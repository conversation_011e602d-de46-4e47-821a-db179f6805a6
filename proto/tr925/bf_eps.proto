syntax = "proto3";
package tr925;

option optimize_for = LITE_RUNTIME;

//--以下为中继参数配置相关Proto
//具体参数含义请参考 TR925_中继写频参数_V1.docx

// 写频指令说明
// rpc.cmd=5
// rpc.para_bin=RepeatorOPerationInfo
// rpc.para_int 为后台操作id,中继回应时需要填写请求时的值

// 操作命令
enum RepeaterOperation {
  None = 0;
  // 5:按table_id查询表
  Query = 5;
  // 6:添加/修改
  InsertUpdate = 6;
  // 7:删除
  Delete = 7;
  // 8:中继进程重启
  Restart = 8;
  // 9:中继重新上电重启
  PowerRestart = 9;
  // 10:查询中继当前信道信息
  RepeaterCurChInfo = 10;
  // 11:切换中继当前信道
  RepeaterCurChSet = 11;
  // 12:切换中继当前发射功率
  RepeaterCurPowerSet = 12;
  // 13:修改中继当前信道信息
  RepeaterCurChChange = 13;
}

// 表结构枚举
enum TableId {
  None = 0;
  RepeaterInfo = 1;
  RepeaterCommonSetting = 2;
  RepeaterKeyFunctionSetting = 3;
  RepeaterMenuSetting = 4;
  RepeaterPresetSms = 5;
  RepeaterPositionSetting = 6;
  RepeaterBdSetting = 7;
  RepeaterBdContact = 8;
  RepeaterIpSetting = 9;
  RepeaterServerSetting = 10;

  RepeaterDmrSetting = 13;
  RepeaterDmrContact = 14;
  RepeaterDmrContactGroup = 15;
  RepeaterMainZone = 16;
  RepeaterSubZone = 17;
  RepeaterUserZone = 18;
  RepeaterOneChannel = 19;
}

//中继写频操作参数,打包在 rpc.para_bin 中
message RepeatorOperationInfo {
  RepeaterOperation operation = 1;
  //写频表ID
  TableId table_id = 2;
  //写频表内对象索引
  int32 obj_index = 3;
  //写频表内对象总数
  int32 obj_num = 4;
  //TR925的区域和信道使用 其它表默认0值
  ZoneId zoneId = 5;
}

// 信道区域信道
// BF-TR925系列使用,设备型号：TR092500、TR092501和TR09250M
message ZoneId {
  // 一级区域ID
  int32 main_zoneId = 1;
  // 二级区域ID
  int32 sub_zoneId = 2;
  // 三级区域ID
  int32 user_zoneId = 3;
}

// 查询中继当前信道信息
// rpc.para_bin.operation = 10
message RepeaterCurChInfo {
  ZoneId zoneId = 1;
  RepeaterOneChannel channelInfo = 2;
  // 自定义功率等级
  int32 curstom_powerLv = 3;
}

// 切换中继当前信道
// rpc.para_bin.operation = 11
message RepeaterCurChSet {
  ZoneId zoneId = 1;
  int32 ch_id = 2;
}

// 切换中继当前发射功率
// rpc.para_bin.operation = 12
message RepeaterCurPowerSet {
  // 发射功率类型，不同产品定义不同
  // BF8100: 0=低功率 1=高功率
  // BF-TR925系列: 0=低功率 1=中功率 2=高功率 3自定义功率
  int32 tx_power = 1;
  // 自定义背负功率等级，tx_power=3(自定义功率)时使用
  int32 curstom_powerLv = 2;
  // 自定义车载功率等级,tx_power=3(自定义功率)时使用
  int32 custom_vehicle_plv = 3;
}

// 写频-设备信息表
// rpc.para_bin.tableid=1
message RepeaterInfo {
  //设备版本
  string version = 1;
  fixed32 low_frequency = 2;
  fixed32 high_frequency = 3;
  bytes sn = 4;
  string device_model = 5;
}

// 写频-常规设置表
// rpc.para_bin.tableid=2
message RepeaterCommonSetting {
  // unicode字符
  bytes dev_name = 1;
  // 开机密码错误阈值
  int32 poweron_pwd_threshold = 2;
  // 开机界面: 0=关 1=开
  int32 poweron_ui_enable = 3;
  // 开机密码
  string poweron_pwd = 4;
  // 信道挂起时间 ms
  int32 ch_hang_time = 8;
  // 单呼挂起时间 ms
  int32 group_hang_time = 9;
  // 组呼挂起时间 ms
  int32 pravite_hang_time = 10;
  // 紧急呼叫挂起时间
  int32 emer_hang_time = 11;
  // 自动发送信标时长 ms
  int32 auto_signal_durtion = 13;
  // 自动发送信标间隔 ms
  int32 auto_signal_interval = 14;
  // 模拟呼叫挂起时间 ms
  int32 analog_hang_time = 12;
  // 提示音开关 0=关 1=开
  int32 tone_enable = 5;
  // 语言环境 0=中文 1=英文
  int32 language_type = 7;
  // LED开关 0=关 1=开
  int32 led_enable = 6;
  // 指定开机区域/信道
  int32 select_zone_ch = 15;
  // 默认1/2/3级区域ID
  ZoneId default_zoneid = 16;
  // 默认信道ID
  int32 default_channel = 17;
}

// 按键功能枚举
enum RepeaterKey {
  // 无功能 0x00
  None = 0x00;
  // 音量加 0x20
  VolumeUp = 0x20;
  // 音量减 0x21
  VolumeDown = 0x21;
  // 信道上调 0x22
  ChannelUp = 0x22;
  // 信道下调 0x23
  ChannelDown = 0x23;
  // 信道设置		0x30
  ChannelSetting = 0x30;
  // 功率切换		0x31
  PowerSwitch = 0x31;
  // 监听开/关	0x32
  MonitorSwitch = 0x32;
  // 区域上调		0x33
  AreaUp = 0x33;
  // 区域下调		0x34
  AreaDown = 0x34;
  // 静噪切换		0x35
  SquelchSwitch = 0x35;
  // 通讯录			0x36
  AddressBook = 0x36;
  // 预制短信		0x37
  PreMadeSms = 0x37;
  // 呼叫记录		0x38
  CallRecord = 0x38;
  // 根目录列表	0x39
  RootDirList = 0x39;
  // 待机界面		0x40
  StandbyInterface = 0x40;
  // 误码测试-发射	0x41
  ErrorTestFire = 0x41;
  // 误码测试-接收	0x42
  ErrorTestReceive = 0x42;
}

// rpc.para_bin.tableid=3
message RepeaterKeyFunctionSetting {
  int32 long_press_time = 1;
  // short固定值0x21 音量减 不允许修改
  RepeaterKey key_01_short_press_func = 2;
  RepeaterKey key_01_long_press_func = 3;
  // short固定值0x20 音量加 不允许修改
  RepeaterKey key_02_short_press_func = 4;
  RepeaterKey key_02_long_press_func = 5;
  // short固定值0x23 信道下调 不允许修改
  RepeaterKey key_03_short_press_func = 6;
  RepeaterKey key_03_long_press_func = 7;
  // short固定值0x22 信道上调 不允许修改
  RepeaterKey key_04_short_press_func = 8;
  RepeaterKey key_04_long_press_func = 9;

  // 数字键0~9, 配置数字键长按时，快捷呼叫DMR联系人对应的ID索引地址
  bytes num_key_long_press_func = 10;
}

// 写频-菜单配置表
// 以下成员/位均为开关 0=不在菜单界面显示  1=显示
// rpc.para_bin.tableid=4
message RepeaterMenuSetting {
  int32 hang_time = 1;
  int32 set_enable = 2;
  // 菜单-设备设置
  // bit0=设备设置 bit1=语言设置 bit2=LED指示 bit3=背光灯
  // bit4=提示音静音 bit5=键盘锁 bit6=开机密码 bit7=开机界面
  // bit8=RTC bit9~bit15=保留
  int32 dev_set = 3;
  // 菜单-信道设置
  // bit0=信道设置 bit1=信道名称 bit2=发射限时 bit3=功率等级
  // bit4=发射频率 bit5=接收频率 bit6=静噪等级 bit7=保留
  // bit8=保留 bit9=亚音频 bit10=彩色码 bit11=中继时隙
  // bit12=发射联系人 bit13=挂起时间 bit14~15=保留
  int32 ch_set = 4;
  // 菜单-设备信息
  // 设备信息显示使能
  int32 dev_info_enable = 5;
  // bit0=本机名称 bit1=本机号码 bit2=中继号码 bit3=北斗号码
  // bit4=固件版本 bit5=CP版本 bit6~7=保留
  int32 dev_info = 6;
  // 菜单区域开关
  int32 zone_enable = 7;
  // 菜单通讯录开关
  int32 contact_enable = 8;
  // 菜单-通讯录-联系人分组
  // bit0=联系人分组 bit1=新建联系人 bit2=删除联系人 bit3~7=保留
  int32 contact_group = 9;
  // 菜单-通讯录-DMR通讯录
  // bit0=DMR通讯录 bit1=所有联系人 bit2=常用联系人 bit3=查看联系人
  // bit4=编辑联系人 bit5=新建联系人 bit6=删除联系人 bit7=保留
  int32 contact_dmr = 10;
  // 菜单-通讯录-手动拨号
  // bit0=手动拨号 bit1=单呼拨号 bit2=组呼拨号 bit3~7=保留
  int32 dial_visible = 11;
  // 菜单-通讯录-北斗通讯录
  // bit0=北斗通讯录 bit1=所有联系人 bit2=查看联系人 bit3=编辑联系人
  // bit4=新建联系人 bit5=删除联系人 bit6=发送短信 bit7=保留
  int32 contact_bd = 12;
  // 菜单短信开关
  int32 sms_enable = 13;
  // 菜单-短信-DMR短信
  // bit0=DMR短信 bit1=新建短信 bit2=预制短信 bit3=收件箱
  // bit4=发件箱 bit5~7=保留
  int32 sms_dmr = 14;
  // 菜单-短信-北斗短信
  // bit0=北斗短信 bit1=新建短信 bit2=预制短信 bit3=收件箱
  // bit4=发件箱 bit5=远程设备禁用 bit6=远程设备启用 bit7=保留
  int32 sms_bd = 15;
  // 菜单附件开关
  int32 annex_enbale = 16;
  // 菜单-附件-北斗导航
  // bit0=北斗导航 bit1=北斗开关 bit2=北斗定位信息 bit3~7=保留
  int32 addition_bd = 17;
}

// 写频-预制短信表
// rpc.para_bin.tableid=5
message RepeaterPresetSms {
  int32 sms_id = 1;
  int32 sms_len = 2;
  // unicode 字符
  bytes sms_content = 3;
}

// 写频-卫星定位设置
// rpc.para_bin.tableid=6
message RepeaterPositionSetting {
  int32 pos_enable = 1;
  // 时区枚举 详见 TR925R_中继写频参数_V1.docx
  int32 time_zone_id = 2;
  // 时区设置枚举 详见 TR925R_中继写频参数_V1.docx
  int32 time_zone_set = 3;
  int32 speed_unit = 4;
}

// 写频-北斗设置
// rpc.para_bin.tableid=7
message RepeaterBdSetting {
  // 北斗模块开关
  int32 bd_enable = 1;
  // 北斗目标ID
  int32 bd_target_id = 2;
  // 北斗网关ID
  fixed32 bd_gate_id = 3;
  // 0=不允许被远端启用/禁用 1=允许被远端启用/禁用
  int32 remote_ctrl_enable = 4;
  // 远程控制UI界面密码
  string remote_ui_pwd = 5;
  // 远程设备密码
  string remote_dis_code = 6;
  // 远程设备启用码
  string remote_en_code = 7;
}

// 写频-北斗联系人
// rpc.para_bin.tableid=8
message RepeaterBdContact {
  // 索引ID 0-99
  int32 index_id = 1;
  // 名称排序ID 0-99 按照字母顺序排序
  int32 sort_id = 2;
  // 北斗号码
  fixed32 bd_num = 3;
  // unicode 字符
  bytes user_name = 4;
  // 联系人是否正在被使用
  int32 in_use = 5;
}

// 写频-网络设置表
// rpc.para_bin.tableid=9
message RepeaterIpSetting {
  // 网络开关
  int32 ip_enable = 1;
  int32 ip_mode = 2;
  fixed32 ip_addr = 3;
  fixed32 ip_mask = 4;
  fixed32 ip_gateway = 5;
  fixed32 ip_dns = 6;
}

// 写频-服务器设置表
// rpc.para_bin.tableid=10
message RepeaterServerSetting {
  string server_addr = 1;
  int32 server_port = 2;
  int32 local_port = 3;
}

// 写频-DMR基础配置表
// rpc.para_bin.tableid=13
message RepeaterDmrSetting {
  // bit0~18=设备ID bit19~23=系统ID bit24~bit31=保留(0)
  fixed32 dmrid = 1;
  fixed32 repeator_id = 2;
  // 呼叫优先级 0-3 0表示无优先级
  int32 priority_level = 3;
  // 中继插话挂起时间 单位S
  int32 rci_hold_time = 4;
}

// 写频-DMR联系人表
// rpc.para_bin.tableid=14
message RepeaterDmrContact {
  int32 contact_id = 1;
  int32 sort_id = 2;
  fixed32 caller_id = 3;
  // unicode 字符
  bytes user_name = 4;
  int32 in_use = 5;
}

// 写频-DMR联系人分组表
// rpc.para_bin.tableid=15
message RepeaterDmrContactGroup {
  int32 group_id = 1;
  // unicode 字符
  bytes user_name = 2;
  // 对应DMR联系人通讯录ID
  bytes contact_array = 3;
}

// 写频-一级区域表
// rpc.para_bin.tableid=16
message RepeaterMainZone {
  int32 id = 1;
  bytes name = 2;
}

// 写频-二级区域表
// rpc.para_bin.tableid=17
message RepeaterSubZone {
  int32 id = 1;
  bytes name = 2;
  int32 main_id = 3;
}

// 写频-三级区域表
// rpc.para_bin.tableid=18
message RepeaterUserZone {
  int32 id = 1;
  bytes name = 2;
  int32 ch_num = 3;
  int32 sub_id = 4;
  bytes ch_id = 5;
}

// 写频-信道信息表
// rpc.para_bin.tableid=19
message RepeaterOneChannel {
  int32 ch_id = 1;
  bytes ch_name = 2;
  int32 ch_type = 3;
  int32 colour_codes = 4;
  fixed32 rx_frequency = 5;
  fixed32 tx_frequency = 6;
  int32 tx_power = 7;
}

//可插话的中继要求后台模拟一个相应的手台
message SimulateDevice {
  //目前必须与中继ID一致
  fixed32 dmrid = 1;

  //当前信道号,与中继LED上显示的一致
  int32 current_ch = 2;

  //请求动作
  //0: 请求模拟一台手台,中继登录后发送
  //1: 信道号改变
  int32 action = 3;

}

