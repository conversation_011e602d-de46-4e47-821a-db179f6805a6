syntax = "proto3";

package bfdx_proto;

message CommonReq {
  // sid
  string Sid = 1;
  //str data
  string StrData = 2;
  //int data
  fixed32 IntData = 3;
  //binary data
  bytes BinData = 4;
}

enum RespCode {
  // unused
  RespCode_Unused = 0;
  // ok
  RespCode_OK = 1;
  // common err code
  RespCode_Err = 2;
  // bad request
  RespCode_Bad_Request = 3;
  //busy
  RespCode_Busy = 4;
  //can't match board for target
  RespCode_Cant_Match_Board = 5;
  //not found
  RespCode_Not_Found = 6;
}

message CommonResp {
  RespCode Code = 1;
  // resp info
  string RespInfo = 2;
  //str data
  string StrData = 3;
  //int data
  fixed32 IntData = 4;
  //binary data
  bytes BinData = 5;
}

message Gateway8100SubsInfo {
  //all subscribe group dmrid
  repeated fixed32 DMRIDs = 1;
}

