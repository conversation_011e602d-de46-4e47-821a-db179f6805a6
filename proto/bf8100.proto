syntax = "proto3";
package bfkcp;

option optimize_for = LITE_RUNTIME;

//系统中所有的消息交互都以此为包装
message rpc_cmd {

  // sequence no
  int32 seq_no = 2;

  // session id
  int64 sid = 3;

  // rpc command code
  int32 cmd = 5;

  // response code
  int32 res = 8;

  // command body
  bytes body = 10;

  // optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
  string para_str = 11;

  // optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
  bytes para_bin = 12;

  // optional int64 parameter
  int64 para_int = 13;
};

//登录请求   rpc.cmd=100
message login {
  //设备dmrid  android端可不填
  fixed32 device_dmrid = 1;
  //设备名字  android端填写username
  string device_name = 2;
  //登录类型,0:中继台　1:控制台程序 2:控制台程序/网关终端  3:android端登录 23:公网poc登录
  int32 login_type = 3;
  //设备型号    android端可不填
  // BF8100项目:TR805005,双频中继，2个时隙
  // BF-TR925项目:TR092500,双频中继，2个时隙
  // BF-TR925R项目:TR092501，单频中继，1个时隙
  // BF-TR925D项目:TR09250M，单频中继，1个时隙
  string device_model = 4;
  //密码检验值   android端填写password/sid
  string password = 5;
  //密码检验方法
  // 0:password=hex(device_dmrid/1023)
  // 1:password=base64(md5(hex(device_dmrid)+device_password))
  // 10: pc
  // 控制台按网页用户登录方式登录,password=base64(sha256(time_str+base64(sha256(user_name+user_pass))))

  //andoid端，公网poc端：
  //11:使用密码登录,password=base64(sha256(time_str+base64(sha256(user_name+user_pass))))
  //12:使用sid登录,首次登录
  //13:使用sid登录，断线重连登录
  int32 password_method = 6;

  // password_method=10/11 时需要的参数
  //取当前时间(utc)格式为: yyyy-mm-dd hh:mm:ss
  string time_str = 7;
  //系统号,00-63
  string sys_id = 8;
  //额外要求的功能,每个额外的功能有相应的功能号
  // 8: 中继支持监控功能
  // 107：要求曾经在此中继下注册过的终端发送终端已在其它地方登录的提示，功能号为107
  // 175: 控制器要求发送所有的会话结束信息，功能号为175
  // 375: app 要求转发gps信息，它有地图显示功能
  repeated int32 extra_option = 9;

  //支持的codec编码器，用于后台判断是否支持该编码器以便转发相关数据
  //目前支持的codec编码器：
  //0: dmr ambe  -> bc30
  //1: opus  -> bc10
  //默认不填写，即只支持标准 dmr ambe，null=[0]
  //poc端一般=[1]
  //cm625=[0,1]
  repeated int32 codec = 10;
}

//登录回应的额外信息。放入rpc_cmd.para_bin
message res_login_para_bin {
  bytes validSnCode = 1;
  string imbeSn = 2;
  //0.没有权限  1.具有全呼权限
  int32 isHaveFullCallPerm = 3;
}

//登录回应
//特别的，andoid端 需要主动返回 channels等信息
message res_login {
  //登录回应值
  // 0:登录成功，
  // 1:重复登录
  // 4:密码不对
  // 5:没有指定密码登录
  // 10:登录失败,不存在此设备
  // 44:bad param
  // 303: 用户没有指定设备
  // 404: 此session id不存在,需要换用户名密码登录
  // 500: 服务器内部错误
  int32 res_code = 1;
  // last session id
  int64 sid = 3;
  //服务器配置的挂起时间，单位毫秒,0为无效值
  int32 hangup_time = 4;
  //服务器http端口
  int32 http_port = 5;
  //服务器版本号
  string server_version = 6;
  //配置最后更新时间,utc时间
  //目前只有poc终端有此字段
  string setting_last_update_time = 7;
}

//中继状态获取的请求
//rpc.cmd=185
//rpc.res=0
//rpc.para_int = 中继 device_dmrid

//中继状态获取的应答
//rpc.cmd=185
//rpc.res=1
//rpc.body=res_repeater_state
message res_repeater_state {
  //设备dmrid
  fixed32 device_dmrid = 1;

  //信道ID
  int32 channel_id = 2;

  //接收频率,Mhz
  fixed32 rx_frequency = 3;

  //发射频率,Mhz
  fixed32 tx_frequency = 4;

  //功率值,W
  int32 power_value = 5;

  //中继本地IP地址
  fixed32 ip_addr = 6;

  //电压值,mV
  int32 vol_value = 7;

  //温度值,单位 0.1摄氏度
  int32 tmp_value = 8;

  //温度状态
  //0:正常
  //1:温度过高
  int32 tmp_err = 9;

  //天线(驻波)状态
  //0:正常
  //1:异常
  int32 ant_err = 10;

  //GPS同步状态
  //0:未安装
  //1:未同步
  //2:已同步
  int32 gps_err = 11;

  //电压状态
  //0:正常
  //1:电压过高
  //2:电压过低
  int32 vol_err = 12;

  //接收pll异常
  //0:正常
  //1:异常
  int32 rx_pll_err = 13;

  //发射pll异常
  //0:正常
  //1:异常
  int32 tx_pll_err = 14;

  //风扇异常
  //0:正常
  //1:异常
  int32 fan_err = 15;

  //信号干扰
  //0:无信号干扰
  //1:有信号干扰
  int32 signal = 16;

  //驻波值,单位 0.1
  int32 ant_value = 17;
}

//中继异常状态上报
//rpc.cmd=188
//rpc.res=0
//rpc.body=repeater_err_status
message repeater_err_status {
  //设备dmrid
  fixed32 device_dmrid = 1;

  //温度状态
  //0:正常
  //1:温度过高
  int32 tmp_err = 2;

  //天线(驻波)状态
  //0:正常
  //1:异常
  int32 ant_err = 3;

  //GPS同步状态
  //0:未安装
  //1:未同步
  //2:已同步
  int32 gps_err = 4;

  //电压状态
  //0:正常
  //1:电压过高
  //2:电压过低
  int32 vol_err = 5;

  //接收pll异常
  //0:正常
  //1:异常
  int32 rx_pll_err = 6;

  //发射pll异常
  //0:正常
  //1:异常
  int32 tx_pll_err = 7;

  //风扇异常
  //0:正常
  //1:异常
  int32 fan_err = 8;

  //信号干扰
  //0:无信号干扰
  //1:有信号干扰
  int32 signal = 9;
}

//手台上传命令，bcxx,cdxx等
message device_send {
  //发起方 中继设备ID
  fixed32 repeater_dmrid = 1;
  //手台上传的fsk 内容
  bytes fsk = 3;

  //从机中继ID(针对同播控制器)
  fixed32 source_repeater_dmrid = 4;
}

//服务器下发的cbxx,dcxx等命令
message server_send {
  //下发的fsk 内容
  bytes fsk = 3;
}

// rpc.cmd=71,72
//手台申请话权
message bc71 {
  //发起方 中继设备ID
  fixed32 repeater_dmrid = 1;
  //目标id
  fixed32 target_dmrid = 2;
  //源id
  fixed32 source_dmrid = 3;
  //场强
  int32 field_intensity = 4;
  //语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
  int32 support_digital = 5;
  //语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
  int32 support_analog = 6;
  //时隙 0：时隙1，　1：时隙2
  int32 time_slot_no = 7;
  //通话优先级
  // 0：普通通话， 1：优先级1
  // 2: 优先级2，  3: 优先级3
  // 4: 紧急通话
  int32 priority = 8;

  //话音申请类型
  // 0:默认的正常手台语音申请
  // 1:手台打电话申请
  // 2:电话网关外线语音申请(被动)
  // 3:手台报警后的自动语音申请
  // 4:后台发监听,手台自动监听的语音申请
  // 5:电话网关收到手台请求的电话后的语音申请
  int32 sound_type = 9;

  //电话号码,电话相关业务使用
  string phone_no = 10;

  //双工,半双工通话类型
  //0: 半双工
  //1: 全双工
  int32 call_duplex = 14;

  //从机中继ID(针对同播控制器)
  fixed32 source_repeater_dmrid = 15;

  //如果涉及到抢占，控制器希望打断自己上面特定的会话时，可以通过此字段指定要打断的会话目标dmrid,默认不需要此功能
  //虚拟集群里面，如果时隙已经占满，则希望抢占当前时隙的会话
  fixed32 prefer_interrupt_target_dmrid = 16;
}

// rpc.cmd=171
//申请话权回应
message cb71 {
  //发起方 中继设备ID
  fixed32 repeater_dmrid = 1;

  //目标id
  fixed32 target_dmrid = 2;
  //源id
  fixed32 source_dmrid = 3;
  //应答的请求指令号
  int32 req_no = 4;
  //语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
  int32 support_digital = 5;
  //语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
  int32 support_analog = 6;
  //时隙 0：时隙1，　1：时隙2
  int32 time_slot_no = 7;
  //通话优先级
  int32 priority = 8;

  //应答结果
  // 0x00：时隙占有，准备接收语音呼叫
  // 0x01：允许联网呼叫
  // 0x02：允许呼叫，中继与服务器断开
  // 0x03: 手台电话申请成功
  // 0x10: 组呼并入成功，集群模式下才有
  // 0x80=128：拒绝呼叫，对方不在线
  // 0x81=129：拒绝呼叫，对方在通话中
  // 0x82=130：拒绝呼叫，中继信道忙
  // 0x83=131：拒绝呼叫，被优先级更高手台抢占时隙资源
  // 0x84=132: 拒绝呼叫，当前有更高优先级手台在通话中
  // 0x85=133: 拒绝呼叫,后台已经释放了此手台的通话资源
  // 0x86=134: 未登录,请先登录
  // 0x87=135: 无电话网关可用,电话申请失败
  // 0x88=136: 电话网关忙,电话申请失败
  // 0x89=137: 电话黑名单,电话申请失败
  // 0x8A=138: 系统授权已经过期，呼叫失败
  // 0x90=144:
  // 0x91=145: 后台数据库查询错误
  // 0x92=146: 电话号码错误
  // 0x93=147: 控制台登录号码和申请话权号码不一致,申请失败
  // 0x94=148: 拒绝呼叫，临时组或任务组已失效
  // 0xA0=160: 拒绝呼叫, 归属组无其它成员在线，集群模式下才有
  // 0xA1=161: 拒绝呼叫, 手台所属中继错误，集群模式下才有
  // 0xA2=162: 拒绝呼叫, 手台所属中继错误，集群模式下才有
  int32 result = 9;

  //被更高优先级抢了语音的设备dmrid
  //当一个人A在通话中,但是有更高优先级的用户B发了bc71,
  //则会先给A发cb71.result=0x83,interrupt_dmrid=A的dmrid
  //然后给B发cb71.result=0x01
  fixed32 interrupt_dmrid = 10;

  //话音申请类型
  // 0:默认的正常手台语音申请
  // 1:手台打电话申请
  // 2:电话网关外线语音申请(被动)
  // 3:手台报警后的自动语音申请
  // 4:后台发监听,手台自动监听的语音申请
  // 5:电话网关收到手台请求的电话后的语音申请
  int32 sound_type = 11;

  //电话号码,电话相关业务使用
  string phone_no = 12;

  //双工,半双工通话类型
  //0: 半双工
  //1: 全双工
  int32 call_duplex = 13;

  //从机中继ID(针对同播控制器)
  fixed32 source_repeater_dmrid = 14;
}

// rpc.cmd=73
//抢断广播命令
message bc73 {
  //发起方 中继设备ID
  fixed32 repeater_dmrid = 1;
  //目标id,被打断语音的发起人
  fixed32 target_dmrid = 2;
  //源id
  fixed32 source_dmrid = 3;
  //时隙 0：时隙1，　1：时隙2
  int32 time_slot_no = 4;
  //从机中继ID(针对同播控制器)
  fixed32 source_repeater_dmrid = 5;
}

// rpc.cmd=175
//后台通知会话需要立即结束
//不管是发起方还是接收方,此会话都必须立即结束
message cb75 {
  //会话目标id
  fixed32 target_dmrid = 2;
  //会话源id
  fixed32 source_dmrid = 3;

  //会话结束原因
  // 0:会话正在被销毁,原因未知
  // 1:被抢占
  // 2:开始后指定时间内无语音
  // 3:语音会话中无语音超时
  // 4:会话已经销毁
  // 5:BC15没抢到话权
  int32 reason = 5;
}

// rpc.cmd=15
message bc15 {
  //发起方 中继设备ID
  fixed32 repeater_dmrid = 1;
  //目标id
  fixed32 target_dmrid = 2;
  //源id
  fixed32 source_dmrid = 3;
  //场强
  int32 field_intensity = 4;
  //语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
  int32 support_digital = 5;
  //语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
  int32 support_analog = 6;
  //时隙 0：时隙1，　1：时隙2
  int32 time_slot_no = 7;
  // 通话类型
  // 0：本地通话, 1：联网通话
  int32 call_type = 9;
  //通话优先级
  // 0：普通通话， 1：优先级1
  // 2: 优先级2，  3: 优先级3
  // 4: 紧急通话
  int32 priority = 8;
  // 优先打断标志
  // 0：不支持优先打断，1支持优先打断
  int32 support_interrupt = 10;
  // Call Status，通话状态
  // 0：语音结束, 1：语音开始
  int32 call_status = 11;

  //话音类型
  // 0:默认的正常手台语音申请
  // 1:手台电话会话语音
  // 2:电话网关过来的开始结束指令
  int32 sound_type = 12;

  //电话号码,电话相关业务使用,手台上来的bc15是没有此字段的
  string phone_no = 13;

  //通话类型
  //0: 半双工
  //1: 全双工
  int32 call_duplex = 14;
  //从机中继ID(针对同播控制器)
  fixed32 source_repeater_dmrid = 15;

  //通话开始时间，服务器填写,目前只有app会用到
  //Unix time, the number of seconds elapsed since January 1, 1970 UTC
  fixed64 start_time = 16;
}

//rpc.cmd=10
message bc10 {

  //目标id
  fixed32 target_dmrid = 2;
  //源id
  fixed32 source_dmrid = 3;

  // 通话类型
  // 0：本地通话, 1：联网通话
  int32 call_type = 5;

  //通话优先级
  // 0：普通通话， 1：优先级1
  // 2: 优先级2，  3: 优先级3
  // 4: 紧急通话
  int32 priority = 6;
  // 优先打断标志
  // 0：不支持优先打断，1支持优先打断
  int32 support_interrupt = 7;
  // Pld Seq,语音数据帧序列号
  fixed32 frame_no = 8;
  //60ms语音帧数据, 一般为60ms发送一次，只有opus_data_1有效
  bytes opus_data_1 = 9;
  //网络不好时，可以一次发两个60ms的语音帧数据，opus_data_1和opus_data_2可以同时存在
  bytes opus_data_2 = 10;
}

//rpc.cmd=30
message bc30 {
  //发起方 中继设备ID
  fixed32 repeater_dmrid = 1;
  //目标id
  fixed32 target_dmrid = 2;
  //源id
  fixed32 source_dmrid = 3;
  //时隙 0：时隙1，　1：时隙2
  int32 time_slot_no = 4;
  // 通话类型
  // 0：本地通话, 1：联网通话
  int32 call_type = 5;
  //通话优先级
  // 0：普通通话， 1：优先级1
  // 2: 优先级2，  3: 优先级3
  // 4: 紧急通话
  int32 priority = 6;
  // 优先打断标志
  // 0：不支持优先打断，1支持优先打断
  int32 support_interrupt = 7;
  // Pld Seq,语音数据帧序列号
  fixed32 frame_no = 8;
  //语音帧数据
  bytes ambe_data = 9;

  //语音类型
  //0:手台产生的语音
  //2:电话网关送过来的语音
  int32 sound_type = 10;

  //从机中继ID(针对同播控制器)
  fixed32 source_repeater_dmrid = 11;
}

//rpc.cmd=33
//手台输入dtmf
message dtmf {
  //发起方 中继设备ID
  fixed32 repeater_dmrid = 1;
  //目标id
  fixed32 target_dmrid = 2;
  //源id
  fixed32 source_dmrid = 3;

  //dtmf 字符串
  string dtmf_str = 6;

  //从机中继ID(针对同播控制器)
  fixed32 source_repeater_dmrid = 7;
}

//rpc.cmd=34
//结束电话通话
message end_call {
  //发起方 中继设备ID
  fixed32 repeater_dmrid = 1;
  //目标id
  fixed32 target_dmrid = 2;
  //源id
  fixed32 source_dmrid = 3;

  //话音类型
  // 1:手台电话会话过来的
  // 2:电话网关过来的
  int32 sound_type = 12;

  //电话号码,电话相关业务使用
  string phone_no = 13;

  //从机中继ID(针对同播控制器)
  fixed32 source_repeater_dmrid = 14;
}

//rpc.cmd=35
//电话转接命令,发送给电话网关
message phone_transfer {
  //电话的dmrid(当前的讲话者)
  fixed32 phone_dmrid = 2;
  //当前的讲话目标
  fixed32 now_target = 3;
  //要转接到的目标dmrid
  fixed32 transfer_target = 4;
}

//rpc.cmd=36
//呼叫方面指示结束会话，不需要等待挂起
//rpc.body=end_call

//rpc.cmd=88
//后台手动重启中继/网关, 可能配置已经改变
//中继/网关需要有配置项指示是否启用远程重启
//rpc.body=空
//rpc.para_int=中继/网关的dmrid
//rpc.para_str=操作的用户名
//操作用户需要有远程操作权限才能发送此命令

//手台频道数据
message OneChannelItem {
  //频道号
  int32 no = 1;
  //默认发射组dmrid
  string sendGroup = 2;
  //接收组dmrid列表
  repeated string listenGroup = 3;
  //频道所在区域rid
  string zoneRid = 4;
}
//手台的频道相关数据
// rpc.cmd=300
message Channels {
  repeated OneChannelItem channels = 1;
  string device_dmrid = 2;
  int32 device_priority = 3;
}

//获取设备的名称
// rpc.cmd=303,rpc.para_str=逗号分隔的dmrid列表
//回应303时,rpc.body=bfdx_proto.DbDeviceList

//获取群组的名称
// rpc.cmd=305,rpc.para_str=逗号分隔的dmrid列表
//回应305时,rpc.body=bfdx_proto.DbOrgList

//获取有权限的设备的名称
// rpc.cmd=307
//回应307时,rpc.body=bfdx_proto.DbDeviceList

//获取有权限的群组的名称
// rpc.cmd=309
//回应309时,rpc.body=bfdx_proto.DbOrgList

// pc device 更新收听组
// rpc.cmd=311
// rpc.body=OneChannelItem

// rpc.cmd=201
//电话网关向后台查询短号对应的dmrid
message PhoneAdapterShortNo2DmridReq {
  string short_no = 1;
  int32 opt = 2;
  fixed32 dmrid = 3;
}

//后台回应电话网关查询短号
message PhoneAdapterShortNo2DmridRes {
  //结果码
  // 0:成功获得对应dmrid
  // 1:数据库没有此短号
  // 2:数据库出错,无法查询
  // 3:marshal/unmarshal error
  int32 result = 1;

  fixed32 dmrid = 2;

  //err info
  string info = 3;
}

//rpc.cmd=203
//电话网关查询是否是黑白名单
//req.body=PhoneAdapterShortNo2DmridReq
//PhoneAdapterShortNo2DmridReq.short_no=电话号码
//PhoneAdapterShortNo2DmridReq.opt 1:查询打入黑白名单 2:查询打出黑白名单
//PhoneAdapterShortNo2DmridReq.dmrid 电话网关电话设备的dmrid

//后台回应黑白名单查询
//res.body=PhoneAdapterShortNo2DmridRes
//PhoneAdapterShortNo2DmridRes.result 回应值如下:
//-1 or <0:后台出错
// 1:在黑名单里面,禁止通话
// 2:在白名单里面,允许通话
// 3:没有启用黑白名单,允许通话
// 4:不在白名单里面,但是白名单启用了,禁止通话

//rpc.cmd=204
//后台查询网关/网关上报 当前空闲/忙/故障等工作状态
//网关登录后需要向后台汇报当前状态
//后台查询时 rpc.body=空
//后台查询时 rpc.para_int=要查询的电话设备的dmrid
//网关回应时 rpc.body=PhoneAdapterShortNo2DmridRes
//网关回应时 PhoneAdapterShortNo2DmridRes.dmrid为网关电话设备的dmrid(不是登录时的中继dmrid)
//网关回应时 PhoneAdapterShortNo2DmridRes.result为下列值:
// 1:当前网关空闲
// 2:当前网关忙碌
// 3:当前网关故障

//rpc.cmd=205
//查询与返回电话网关中电话配置信息
message PhoneLineSetting {
  //动作码
  //1: 网关查询电话配置
  //2: 后台回应配置信息
  //<0:后台出错了
  int32 action_code = 1;

  //以后三个为电话线配置信息,数组一一对应
  //电话线位置,
  repeated int32 line_pos = 2;
  //电话号码
  repeated string line_phone_no = 3;
  //电话对应的dmrid
  repeated fixed32 line_dmrid = 4;
}

//网关终端查询当前信道信息
//rpc.cmd=206
//rpc.para_int=网关终端的dmrid
//rpc.res=要查询的信道号,从1开始

//后台回应网关终端查询信道信息
//rpc.cmd=206
//rpc.body=Channels
//rpc.res=0:为成功 1:失败

//rpc.cmd=320
//网关终端请求群组(收听组)数据回应
message ex_one_org {
  //@table uuid primary key
  //行ID
  string rid = 1;

  //@table varchar(64) unique not null
  //组织机构编号
  string org_self_id = 3;

  //@table varchar(32) unique not null
  //机构名称,缩写
  string org_short_name = 5;

  //@table varchar(256)
  //机构名称,全称
  string org_full_name = 6;

  //@table text
  //机构详细描述
  string note = 7;

  //@table int default 2
  //2:真实机构,1:虚拟机构 虚拟机构没有真实设备和用户,只是引用真实机构的数据,相当于额外的分组
  int32 org_is_virtual = 8;

  //@table varchar(8) unique
  //DMR ID,可用作组呼的ID
  string dmr_id = 9;

  //@table uuid not null default '11111111-1111-1111-1111-111111111111'
  //此组织的上级机构
  string parent_org_id = 11;
}

//rpc.cmd=330
//网关终端请求所有终端数据回应
message ex_one_device {

  //@table uuid not null REFERENCES db_org(rid) ON DELETE CASCADE default '00000000-0000-0000-0000-000000000000'
  //设备所属的群组
  string org_id = 3;

  //@table varchar(16) not null unique
  //设备名称
  string self_id = 4;

  //@table varchar(16) not null unique
  //设备DMR-ID
  string dmr_id = 5;

  //@table text default ''
  //对讲机所属的虚拟群组,逗号分隔的群组rid
  string vir_orgs = 6;

  //@table text default ''
  //设备备注信息
  string note = 8;

  //@table int not null default 0
  //设备类型 0:对讲机手台 1：车台 3:电话网关设备 4:中继虚拟终端 5:互联网关终端
  int32 device_type = 9;

  //@table int
  //优先级
  int32 priority = 12;
}

//rpc.cmd=180
//查询ambe 序列号
message ambe_serial_code { repeated int32 code = 1; }

//rpc.cmd=333
//在线终端信息
message ex_oneline_devices {
  //在线的终端dmrid
  repeated int32 dmr_id = 1;
  //终端对应的最后数据时间time.unix(单位:s)
  repeated int64 last_data_time = 2;
}

//rpc.cmd=3
//只能上行是3,下行的cmd=3已经被用于系统后台发短信
//物联网数据
message iot_data {
  //指令
  sint32 cmd = 1;
  //设备类型
  sint32 dev_type = 2;
  //终端设备ID,iot发射终端
  bytes dev_id = 3;
  //指令参数
  bytes cmd_param = 4;
  //iot接收设备ID
  bytes recv_dev_id = 9;
  //接收时间,unix epoch时间(秒)
  sint64 recv_time = 10;

  //后台接收此数据的控制器ID，(控制器上传时不需要填写此字段)
  string kcp_recv_dev_id = 11;
}

//rpc.cmd=8
//广播给app终端
message dev_data_info {
  //广播代码
  //0:主动上线
  //1:主动下线
  //3:超时下线
  //14:断线下线
  //8：讲话
  //10: gps data data=bfdx_proto.Gps84
  int32 code = 1;
  //源id
  uint32 src_dmrid = 2;
  //目标id
  uint32 dst_dmrid = 3;
  //数据
  bytes data = 4;
}

message addr_book {
  //1.组呼  2.单呼
  int32 type = 1;
  //0.ok  -1.query err  -2.marshal err
  int32 code = 2;
  //code=1,body=db_org
  //code=2,body=db_device
  bytes body = 3;
  //para_str=dmridHex
  string para_str = 4;
}

//返回查询通讯录结果
//rpc.cmd = 315
//rpc.res  0.ok  -1.db err  -2.marshal err   -3.需要查询的dmrid为空
message addr_book_list { repeated addr_book addr_book_list = 1; }

//从kcp通道更新地图url token
//rpc.cmd=1440
//rpc.res 0=ok
//rpc.ParaInt=token (uint32)

//rpc.cmd=107
//服务器通知曾经在此中继上的终端已在其它控制器/中继登录
//rpc.para_int=dmrid

//虚拟集群手台注册请求
//rpc_cmd.cmd = 108
//rpc_cmd.res = 0
//rpc_cmd.body=bc40_req
message bc40_req {
  /**手台ID**/
  fixed32 dev_dmrid = 1;
  /**手台归属组ID**/
  fixed32 group_dmrid = 2;
  /**无线终端的场强值,0~4**/
  int32 riss = 3;
  /**手台状态信息 6字节**/
  bytes status = 4;
  /**本次开关机事件,0:关机,1:正常开机,2:电池开机,3:低压复位开机 8：信道切换注册**/
  int32 power_event = 5;
  /**上次开关机事件,1:正常开关机,2:异常开关机,3:低压关机/复位开机**/
  int32 last_power_event = 6;
  /**0:主信道即选定发起注册,1:漫游信道发起注册**/
  int32 roaming = 7;
  /**虚拟集群下主中继在基站内的序列号**/
  int32 bs_index = 8;
}

//虚拟集群手台注册应答
//rpc_cmd.cmd = 108
//rpc_cmd.res = 1
//rpc_cmd.body=bc40_resp
message bc40_resp {
  /**手台ID**/
  fixed32 dev_dmrid = 1;
  /**应答结果码 0: 注册成功 1：系统无此手台信息 2:归属组错误 3:终端类型配置错误 4:未知错误 10:关机成功**/
  int32 res_code = 2;
  //当前系统时间，unix时间，单位为秒
  // the number of seconds elapsed since January 1, 1970 UTC
  int64 sys_time = 3;
}

//msg for mesh gateway
//rpc_cmd.cmd = 500=Query8100DmridInfo
//rpc_cmd.para_int = dmrid
//rcp_cmd.cmd = 501=QueryAll8100DmridInfo
//rpc_cmd.para_int = 0: all group and radio info 1:query group info,2:query radio info
message Bf8100DmridInfo {
  //dmrid
  fixed32 DmrID = 1;
  //dmrid name
  string Name = 2;
  //parent org uuid, 所属单位的uuid
  string OrgUUID = 4;
  //self uuid
  string MyUUID = 5;
}

/** 卫星定位数据类型 **/
enum MESH_GPS_DATA_TYPE {
  /**0:无**/
  ST_NONE = 0;
  /**1:功能键**/
  ST_FUNCTION_KEY = 1;
  /**2:开机**/
  ST_POWER_ON = 2;
  /**3:关机**/
  ST_POWER_OFF = 3;
  /**4:定时**/
  ST_TIME = 4;
  /**5:距离超出**/
  ST_DISTANCE = 5;
  /**6:通过菜单发送**/
  ST_FUNCTION_MENU = 6;
  /**7:开机密码错误自毙**/
  ST_PWDERR = 7;
  /**8:收到对讲机遥毙**/
  ST_DEVICE_DISABLE = 8;
  /**9:收到远程监听**/
  ST_REMOTE_MONITOR = 9;
  /**10:超出PTT次数**/
  ST_PTT_BEYOND = 10;
  /**11:超出连接次数**/
  ST_LINK_BEYOND = 11;
  /**12:被GPS查询发送**/
  ST_GPS_QUERY = 12;
  /**13:发起警报发送**/
  ST_TX_ALARM = 13;
}

/** 定位数据 **/
message mesh_gps_info_t {

  /**数据源ID**/
  uint32 source_id = 1;

  /**目标ID**/
  uint32 target_id = 2;

  /**GPS数据**/
  uint32 hour = 3;   /**小时,0~23**/
  uint32 minute = 4; /**分钟,0~59**/
  uint32 second = 5; /**秒,0~59**/
  uint32 day = 6;    /**日,1~31**/
  uint32 month = 7;  /**月,1~12**/
  uint32 year = 8;   /**年,0~99**/

  uint32 available = 9;     /**1:有效,0:无效**/
  uint32 latitude = 10;     /**纬度,DDMM.MMMM *10000  **/
  uint32 northOrSouth = 11; /**1:North,0:South**/
  uint32 longitude = 12;    /**经度,DDDMM.MMMM *10000 **/
  uint32 eastOrWest = 13;   /**1:East,0:West**/

  uint32 speed = 14;     /**速度,0~999,节**/
  uint32 direction = 15; /**方位角,0~360**/
  int32 altitude = 16;   /**(有正负号)海拔高度,0~9999,米**/

  /** (预留)GPS数据类型 **/
  MESH_GPS_DATA_TYPE gps_data_type = 17;
}

//mesh send gps info to server
//rpc_cmd.cmd = 502=MeshSendGpsInfo
//rpc_cmd.body = MeshGpsInfo
message MeshGpsInfo {
  //dev dmrid in 8100
  fixed32 DmrID = 1;
  //gps info
  mesh_gps_info_t GpsInfo = 2;
}

message PocCommand {
  int32 cmd = 1;
  //seq_no
  int64 seq_no = 2;
  bytes body = 3;
  //para_str
  string para_str = 4;
  //para_bin
  bytes para_bin = 5;
  //para_int
  int64 para_int = 6;
}

message PocDefaultGroup {
  fixed32 default_send_group_dmrid = 1;
  //最多256个，如果多于256个，则应该分包发送多次
  repeated fixed32 default_listen_group_dmrids = 2;

}

message PocSubscribleUpdateOption {
  // 序号
  int32 frame_no = 1;
  // 帧类型
  // 0: 一次性更新
  // 1: 开始批量更新
  // 2: 处于批量更新中
  // 3: 批量更新结束
  int32 frame_type = 2;
}

message PocConfig {
  //can edit subscription local
  //终端是否可以本地编辑收听组,0:不可以,1:可以
  int32 can_edit_subscription_local = 1;
  //reliable gps transfer
  //是否启用可靠的gps传输,0:不启用,1:启用
  int32 rgps = 2;
}
