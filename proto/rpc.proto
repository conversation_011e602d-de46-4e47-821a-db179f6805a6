syntax = "proto3";
package bfdx_proto;

enum request_command{
  UNKNOW = 0;
  CLIENT_LOGIN_REQUEST = 10;
  CLIENT_LOGIN_RESPONSE = 11;

  DB_ORG_OPERATION = 20;
  DB_POSITION_OPERATION = 22;

};

//系统中所有的消息交互都以此为包装
message rpc_cmd{
  //uuid of request
  string req_id = 1;

  //seq no
  int32 seq_no = 2;

  //sys id
  string sys_id = 3;

  //session id(uuid)
  string sid = 4;

  //req or response, 0:request 2:response  3:broadcast
  int32 req_or_response = 5;

  //the orig req id of response
  string orig_req_id = 6;

  //res info
  string res_info = 7;

  //request command
  int32 command = 8;

  //body compress method
  //0:no compress 1:zlib inflate deflate
  int32 compress_method = 9;

  //command body
  bytes body = 10;

  //rpc opt,额外的信息,一般不会有,有些特殊命令里面可能用到
  string opt = 11;

  //rpc opt int64
  int64 opt_int = 12;

};

//rpc接口,系统目前都是以消息形式通过nats调用
service bfdx {
  rpc server_call (rpc_cmd) returns (rpc_cmd);
}

//GETBY 获取数据时,rpc_cmd.OrigReqId为sql语句select后的条件(列名), rpc_cmd.ResInfo为返回结果包含的列名
//rpc_cmd.opt=列的排序要求,对应于sql的order by后面的内容
//如: select dmr_id from db_device where rid=xxx
//则rpc_cmd.OrigReqId=rid, rpc_cmd.ResInfo=dmr_id
//注意条件列中必须有唯一列

//PUPDATE 更新数据时,rpc_cmd.OrigReqId为sql语句update后的条件(列名), rpc_cmd.ResInfo为需要修改的列名
//如: update db_device set dmr_id=xxx where rid=yyy
//则rpc_cmd.OrigReqId=rid, rpc_cmd.ResInfo=dmr_id
