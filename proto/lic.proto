syntax = "proto3";
package bfdx_proto;

//cpu info
message cpu_info {
  string brand_name = 1;
  string vendor = 2;
  int32 physical_cores = 3;
  int32 logical_cores = 4;
}
//sys info
message sys_info {
  string bf_sw_name = 1;
  string bf_sw_version = 2;
  cpu_info cpuinfo = 3;
  //total memory size
  uint64 memory = 4;
  //all interface mac address
  repeated bytes macs = 5;
}

message lic_8100_content {
  //uint32 mod-rfid
  uint32 mod_rfid = 1;
  //uint32 mod-record
  uint32 mod_record = 2;
  //uint32 mod-phone-gateway
  uint32 mod_phone_gateway = 3;
  //uint32 mod-dispatch
  uint32 mod_dispatch = 4;
  //uint32 mod-traditional-dmr
  uint32 mod_traditional_dmr = 5;
  //uint32 max-devices
  uint32 max_devices = 6;
  //uint32 max-users
  uint32 max_users = 7;
  //uint32 max-controllers
  uint32 max_controllers = 8;
  //uint32 mod-svt
  uint32 mod_svt = 9;
}

//lic request
message lic_request {
  //系统信息
  sys_info sysinfo = 1;
  //项目名称
  string proj_name = 2;
  //request time(utc)
  string request_time = 3;
  //first run time, unix_epoch
  int64 first_run_time = 4;
  //请求授权的系统识别ID
  //1=8100
  uint32 system_code = 5;
  //请求授权中的用户填写的需要授权的信息
  //8100时=lic_8100_content
  bytes user_lic_content = 6;
}

//lic request file
message lic_request_file {
  string proj_name = 2;
  string key_prefix = 3;
  //note
  string note = 6;
  //data =base64(lic_request)
  string data = 8;
  //base64(rsa_enc(sha256(data),public_key))
  string data_ver = 9;
  
}

//lic content
message lic_content {
  string bf_sw_name = 1;
  //模块授权时为 module_name -> 1,建议统一为 mod-xxxx -> 1,没有授权时为 mod-xxxx -> 4
  //数据授权时为 data_name -> uint32_value,建议为 max-xxxx -> value
  //0值为无限制，对于模块而言为可用，对于数量而言则为不加限制，此值为向后兼容定义
  map<string, uint32> licenses = 2;
  //到期时间(yyyy-mm-dd HH:MM:SS)，utc
  string expire_time = 3;
}

//lic response
message lic_response {
  //系统信息
  sys_info sysinfo = 1;
  //项目名称
  string proj_name = 2;
  //lic
  lic_content lic = 3;
  //issue time utc
  string issue_time = 6;
}

message lic_response_file {
  string proj_name = 2;
  string key_prefix = 3;
  //issue time utc
  string issue_time = 6;
  //data =base64(lic_response)
  string data = 8;
  //sign=base64(rsa.signpss(sha256(data),private_key))
  string sign = 9;
}

