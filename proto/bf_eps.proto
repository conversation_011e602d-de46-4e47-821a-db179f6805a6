syntax = "proto3";
package bfkcp;

option optimize_for = LITE_RUNTIME;

//--以下为中继参数配置相关Proto
//具体参数含义请参考 TR8100_中继写频参数.docx

// rpc.res 指令附加代码说明
// 0:请求
// 1:应答/操作成功
// -1:失败,其它原因情况
// -2:失败,对象不存在(查询/添加/修改/删除)
// -3:失败,删除对象为当前信道
// -4:失败,参数有误

// 写频指令说明
// rpc.cmd=5
// rpc.para_bin=RepeatorOPerationInfo
// rpc.para_int 为后台操作id,中继回应时需要填写请求时的值

//中继写频操作参数,打包在 rpc.para_bin 中
message RepeatorOperationInfo {
  //操作命令
  // 5:按table_id查询表
  // 6:添加/修改 
  // 7:删除 
  // 8:中继进程重启 
  // 9:中继重新上电重启
  //10:查询中继当前信道信息
  //11:切换中继当前信道
  //12:切换中继当前发射功率
  int32 operation = 1;
  //写频表ID
  int32 table_id = 2;
  //写频表内对象索引
  int32 obj_index = 3;
  //写频表内对象总数
  int32 obj_num = 4;
}

// 信道区域信道
// BF-TR925系列使用,设备型号：TR092500、TR092501和TR09250M
message ZoneId {
  // 一级区域ID
  int32 main_zoneId = 1;
  // 二级区域ID
  int32 sub_zoneId = 2;
  // 三级区域ID
  int32 user_zoneId = 3;
}

// 查询中继当前信道信息
// rpc.para_bin.operation = 10
message RepeaterCurChInfo {
  ZoneId zoneId = 1;
  RepeaterOneChannel channelInfo = 2;
  
// 自定义功率等级
//  int32 curstom_powerLv = 3;
}

// 切换中继当前信道
// rpc.para_bin.operation = 11
message RepeaterCurChSet {
    
  ZoneId zoneId = 1;
  int32 ch_id = 2;
}

// 切换中继当前发射功率
// rpc.para_bin.operation = 12
message RepeaterCurPowerSet {
  // 发射功率类型，不同产品定义不同
  // BF8100: 0=低功率 1=高功率
  // BF-TR925系列: 0=低功率 1=中功率 2=高功率 3自定义功率
  int32 tx_power = 1;
  // 预留 兼容TR925使用 默认0
  int32 curstom_powerLv = 2;
  // 预留 兼容TR925使用 默认0
  int32 custom_vehicle_plv = 3;
}
  
// 写频-设备信息表
// rpc.para_bin.tableid=1
message RepeaterInfo {
  //设备版本
  string version = 1;
  fixed32 low_frequency = 2;
  fixed32 high_frequency = 3;
  // 兼容TR925 8100项目为32字节字符串
  bytes sn = 4;
  string device_model = 5;
}

// 写频-常规设置表
// rpc.para_bin.tableid=2
message RepeaterCommonSetting {
  string dev_name = 1;
  fixed32 dmrid = 2;
  fixed32 repeater_id = 3;
  int32 hang_time = 4;
  int32 sound_tip = 5;
  int32 squelch_level = 6;
  int32 default_channel = 7;
}

// 按键功能枚举
// 定义类型	值
// 无功能	0
// 信道向上切换	1
// 信道向下切换	2
// 高低功率切换	3
// 恢复默认IP地址	4
// 恢复默认IP地址	5
// 写频-按键功能设置表
// rpc.para_bin.tableid=3
message RepeaterKeyFunctionSetting {
  int32 long_press_time = 1;
  int32 key_01_short_press_func = 2;
  int32 key_01_long_press_func = 3;
  int32 key_02_short_press_func = 4;
  int32 key_02_long_press_func = 5;
  int32 key_03_short_press_func = 6;
  int32 key_03_long_press_func = 7;
  int32 key_04_short_press_func = 8;
  int32 key_04_long_press_func = 9;
}

// 写频-网络设置表
// rpc.para_bin.tableid=4
message RepeaterIpSetting {
  int32 ip_mode = 1;
  fixed32 ip_addr = 2;
  fixed32 ip_mask = 3;
  fixed32 ip_gateway = 4;
  fixed32 ip_dns = 5;
}

// 写频-服务器设置表
// rpc.para_bin.tableid=5
message RepeaterServerSetting {
  string server_addr = 1;
  int32 server_port = 2;
  int32 local_port = 3;
}

// 写频-信道信息表
// rpc.para_bin.tableid=6
message RepeaterOneChannel {
  int32 ch_id = 1;
  string ch_name = 2;
  int32 ch_type = 3;
  int32 colour_codes = 4;
  fixed32 rx_frequency = 5;
  fixed32 tx_frequency = 6;
  int32 tx_power = 7;
}

//可插话的中继要求后台模拟一个相应的手台
message SimulateDevice {
  //目前必须与中继ID一致
  fixed32 dmrid = 1;

  //当前信道号,与中继LED上显示的一致
  int32 current_ch = 2;

  //请求动作
  //0: 请求模拟一台手台,中继登录后发送
  //1: 信道号改变
  int32 action = 3;
}

//----以下为同播控制器新增协议

/** 配置指令枚举**/
enum EPDB_CTRL_CLIENT_CMD {
  NONE = 0;            /**无**/
  CLIENT_INFO = 1;     /**服务器获取从机中继信息 **/
  CH_CONFIG = 2;       /**设置信道信息（服务器获取信道信息）**/
  CH_SWITCH = 3;       /**切换信道**/
  TX_POWER_SWITCH = 4; /**切换发射功率**/
  DEVICE_REBOOT = 5;   /**重启中继**/
  CFCB_SYS_ADD = 6;    /**加入同频同播系统**/
  CFCB_SYS_REMOVE = 7; /**移除同频同播系统**/
  SVT_RELAY_INFO = 8; 	 	/**虚拟集群中继信息**/
  SVT_RELAY_CFG = 9; 	 	/**虚拟集群中继配置**/
  SVT_RELAY_STATUS = 10; 	/**虚拟集群中继状态**/

}

//从机中继配置操作参数,打包在 rpc_cmd.body 中
message RepeatorConfig {
  uint32 device_id = 1;      /** 从机设备ID **/
  bytes ConfigData_body = 2; /**参数内容 [max_size:100]**/
  int32 data_mark = 3; 	   /** 标识 0=控制器 1=服务器源 2=CPS源**/
}

/**控制从机 信道配置**/
//rpc_cmd.cmd = 5
//rpc_cmd.body = RepeatorConfig
//rpc_cmd.para_int = 2
//rpc_cmd.res = 0//配置
//RepeatorConfig.body = RepeatorConfigChInfo
message RepeatorConfigChInfo {
  uint32 ch_type = 1; /**信道类型 **/
  bytes ch_name = 2;  /**信道名称 [max_size:34] **/
  uint32 tx_power = 3; /**设置发射功率（低:0、中:1、高:2、自定义:3）**/
  uint32 custom_power = 4; /**自定义功率大小 1~50 **/
  fixed32 rx_freq = 5;     /**接收频率 **/
  fixed32 tx_freq = 6;     /**发射频率 **/
  uint32 cc = 7;           /**色码 0~15**/
}

/**控制从机 信道切换**/
//rpc_cmd.cmd = 5
//rpc_cmd.body = RepeatorConfig
//rpc_cmd.para_int = 3
//RepeatorConfig.body = RepeatorConfigChSwitch
message RepeatorConfigChSwitch {
  uint32 zone_id = 1; /**区域ID 1~127**/
  uint32 ch_id = 2;   /**信道ID 1~127**/
}

/**控制从机 发射功率切换**/
//rpc_cmd.cmd = 5
//rpc_cmd.body = RepeatorConfig
//rpc_cmd.para_int = 4
//rpc_cmd.res = 0	    //配置 RepeatorConfig.ConfigData_body = RepeatorConfigTxPowerSwitch
//rpc_cmd.res = 1	    //请求 RepeatorConfig.ConfigData_body 为空
//rpc_cmd.res = 2	    //应答 RepeatorConfig.ConfigData_body = RepeatorConfigTxPowerSwitch
message RepeatorConfigTxPowerSwitch {
  uint32 tx_power = 1;      /**功率等级 （低:0、中:1、高:2、自定义:3）**/
  uint32 custom_power = 2;  /**自定义功率值 1~50**/
}

/**上报从机注册（注销）信息**/
//rpc_cmd.cmd = 102
//rpc_cmd.body = ClientRepeatorInfoReport
message ClientRepeatorInfoReport {
  uint32 device_id = 1; /**从机设备ID**/
  uint32 ip_addr = 2;   /**从机IP地址**/
  uint32 rx_freq = 3;   /**接收频率**/
  uint32 tx_freq = 4;   /**发射频率**/
  int32 is_reg = 5; /**注册或注销- 1:注册, 0:注销 2:控制器重新上线后同步在线中继信息**/
  int32 cfcb_disable = 6; /**1:移出同频同播系统, 0:正常使用同频同播系统**/
  string device_model = 7; //中继的型号
  int32 dualslot_ipmc = 8; /**是否双时隙IP互联 1:是, 0:否**/
  int32 slot_id = 9;       /**当前工作时隙**/
}

/**其他指令说明**/
/**服务器请求获取中继信息**/
//rpc_cmd.cmd = 5
//rpc_cmd.body = RepeatorConfig
//RepeatorConfig.device_id = 中继ID
//rpc_cmd.para_int = 1
//rpc_cmd.res = 1//请求

/**同播控制器 回复 服务器 中继注册（注销）信息**/
//rpc_cmd.cmd = 5
//rpc_cmd.body = RepeatorConfig
//RepeatorConfig.body = ClientRepeatorInfoReport
//rpc_cmd.para_int = 1
//rpc_cmd.res = 2//回复

/**服务器请求获取中继信道配置**/
//rpc_cmd.cmd = 5
//rpc_cmd.body = RepeatorConfig
////RepeatorConfig.device_id = 中继ID
//rpc_cmd.para_int = 2
//rpc_cmd.res = 1//请求


/**同播控制器 回复 服务器 中继信道配置**/
//rpc_cmd.cmd = 5
//rpc_cmd.body = RepeatorConfig
//rpc_cmd.para_int = 2
//rpc_cmd.res = 2//回复
//RepeatorConfig.body = RepeatorConfigChInfo

//----以下为虚拟集群控制器新增协议
//虚拟集群中所有的消息交互都以此为包装
//中继<->控制器
message rpc_cmd_svt {

  // sequence no
  int32 seq_no = 2;

  // session id
  int64 sid = 3;

  // rpc command code
  int32 cmd = 5;

  // response code
  int32 res = 8;

  // command body
  bytes body = 10;

  // optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
  string para_str = 11;

  bytes para_bin = 12;

  // optional int64 parameter
  int64 para_int = 13;
  
  // 虚拟集群 控制器联网/断网
  int32 contoller_network = 14; 
  
  // 虚拟集群 自由中继逻辑信道号
  int32 freerelay_chnum = 15;
  
  // 虚拟集群 站内中继注册状态
  int32 reg_status = 16;
  
  // 虚拟集群 站内各中继忙闲状态
  int32 busy_status = 17;

  // 虚拟集群 中继各模块状态
  int32 module_status = 18;
};

// rpc.cmd=171
// 控制器->中继 申请话权回应
message cb71_svt {
  //发起方 中继设备ID
  fixed32 repeater_dmrid = 1;

  //目标id
  fixed32 target_dmrid = 2;
  //源id
  fixed32 source_dmrid = 3;
  //应答的请求指令号
  int32 req_no = 4;
  //语音传输类型,是否支持数字传输 0/1：不支持/支持数字传输
  int32 support_digital = 5;
  //语音传输类型,是否支持模拟传输 0/1：不支持/支持模拟传输
  int32 support_analog = 6;
  //时隙 0：时隙1，　1：时隙2
  int32 time_slot_no = 7;
  //通话优先级
  int32 priority = 8;
  //应答结果
  // 0x00：时隙占有，准备接收语音呼叫
  // 0x01：允许联网呼叫
  // 0x02：允许呼叫，中继与服务器断开
  // 0x03: 手台电话申请成功
  // 0x10: 组呼并入成功，集群模式下才有
  // 0x80=128：拒绝呼叫，对方不在线
  // 0x81=129：拒绝呼叫，对方在通话中
  // 0x82=130：拒绝呼叫，中继信道忙
  // 0x83=131：拒绝呼叫，被优先级更高手台抢占时隙资源
  // 0x84=132: 拒绝呼叫，当前有更高优先级手台在通话中
  // 0x85=133: 拒绝呼叫,后台已经释放了此手台的通话资源
  // 0x86=134: 未登录,请先登录
  // 0x87=135: 无电话网关可用,电话申请失败
  // 0x88=136: 电话网关忙,电话申请失败
  // 0x89=137: 电话黑名单,电话申请失败
  // 0x90=144:
  // 0x91=145: 后台数据库查询错误
  // 0x92=146: 电话号码错误
  // 0x93=147: 控制台登录号码和申请话权号码不一致,申请失败
  // 0x94=148: 拒绝呼叫，临时组或任务组已失效
  // 0xA0=160: 拒绝呼叫, 归属组无其它成员在线，集群模式下才有
  // 0xA1=161: 拒绝呼叫, 手台所属中继错误，集群模式下才有
  // 0xA2=162: 拒绝呼叫, 手台所属中继错误，集群模式下才有
  int32 result = 9;

  //被更高优先级抢了语音的设备dmrid
  //当一个人A在通话中,但是有更高优先级的用户B发了bc71,
  //则会先给A发cb71.result=0x83,interrupt_dmrid=A的dmrid
  //然后给B发cb71.result=0x01
  fixed32 interrupt_dmrid = 10;

  //话音申请类型
  // 0:默认的正常手台语音申请
  // 1:手台打电话申请
  // 2:电话网关外线语音申请(被动)
  // 3:手台报警后的自动语音申请
  // 4:后台发监听,手台自动监听的语音申请
  // 5:电话网关收到手台请求的电话后的语音申请
  int32 sound_type = 11;

  //电话号码,电话相关业务使用
  string phone_no = 12;

  //双工,半双工通话类型
  //0: 半双工
  //1: 全双工
  int32 call_duplex = 13;
  
  // 会话 中继逻辑信道号
  int32 relay_chnum = 14;
  
  // 会话类型
  // 0：本地
  // 1：联网
  int32 call_type = 15;
  
  // 标识 是否移除自由中继
  // 0：否
  // 1：是
  int32 move_freerelay = 16;
}

//rpc.cmd=104
//中继登录请求 虚拟集群
//中继->控制器
message svt_login {
  // 设备dmrid
  fixed32 device_dmrid = 1;
  // 设备型号(6字节型号 + 2字节功能码)
  bytes device_code = 2;
  // 接收频率
  fixed32 ulRxFre = 3;
  // 发射频率
  fixed32 ulTxFre = 4;
}

//rpc.cmd=104
//中继登录回应 虚拟集群
//控制器->中继
message res_svt_login {
  // 登录回应值
  // 0：登录成功
  // 1：重复登录
  // 2：中继频率错误
  // 3：站内存在重复归属组
  // 10：登录失败,不存在此设备。
  // 303：用户没有指定设备
  int32 res_code = 1;
  // 中继设备序号
  int32 ucSeqId = 2;
  // 中继设备信道号
  int32 ucChNum = 3;
  // 控制器设备id
  fixed32 device_id = 4;
  // 心跳时间
  int32 usHbPeriod = 5;
  // 时间信息
  int32 ulTime = 6;
  // 是否统一配置
  int32 ubUnityCfg = 7;
  // 信道挂起时间
  int32 usChHangTime = 8;
  // 单呼挂起时间
  int32 usPrivateHangTime = 9;   
  // 组呼挂起时间
  int32 usGroupHangTime = 10;    
  // 紧急呼叫挂起时间
  int32 usEmerHangTime = 11; 
  // 自动发送信标时长    
  int32 usAutoSignalDurtion = 12;    
  // 自动发送信标间隔
  int32 usAutoSignalInterval = 13;  
  // 鉴权开关
  int32 ubAuthEnable = 14;
  // 鉴权秘钥
  bytes ucnAuthKey = 15; 
}

//rpc.cmd=106
//控制器 会话信息广播
//控制器->中继
message svt_session {
  // 会话信息条数
  int32 ucNum = 1;
  // 会话信息结构体数据长度
  int32 ucSizeof = 2;
  // 会话信息内容
  bytes ucnData = 3;
}

/**(虚拟集群) 中继设备注册（注销）信息**/
/**控制器->服务器主动上报中继信息**/
//rpc_cmd.cmd = 103
//rpc_cmd.body = SvtRepeatorInfoReport
//rpc_cmd.res = 0		//上报

/**远程查询中继信息指令**/
//rpc_cmd.cmd = 5
//rpc_cmd.body = RepeatorConfig
//rpc_cmd.para_int = 8	//SVT_RELAY_INFO
//rpc_cmd.res = 1		//请求 RepeatorConfig.ConfigData_body 为空
//rpc_cmd.res = 2		//应答 RepeatorConfig.ConfigData_body = SvtRepeatorInfoReport
message SvtRepeatorInfoReport {
  fixed32 device_id = 1; 	/**中继设备ID**/
  fixed32 ip_addr = 2;   	/**中继IP地址**/
  fixed32 rx_freq = 3;   	/**接收频率**/
  fixed32 tx_freq = 4;   	/**发射频率**/
  int32 is_reg = 5; 		/**注册或注销- 1:注册, 0:注销 2:控制器重新上线后同步在线中继信息**/
  bytes device_code = 6;	/**设备型号(6字节型号 + 2字节功能码)**/
  fixed32 ctrl_id = 7;   				/**中继所属控制器ID**/
  fixed32 module_status = 8;   	/**中继模块状态**/
}

/**控制器->服务器主动上报扩展组信息**/
//rpc_cmd.cmd = 109
//rpc_cmd.body = SvtExtendedSubscribles
//rpc_cmd.res = 0		//上报

message SvtExtendedSubscribles {
  //0: 添加扩展组 1：删除扩展组
  int32 code = 1;
  /**扩展组ID**/
  repeated fixed32 subscribles=2; 	
  
}

/**(虚拟集群)中继常规参数配置**/
//rpc_cmd.cmd = 5
//rpc_cmd.body = RepeatorConfig
//rpc_cmd.para_int = 9	//SVT_RELAY_CFG
//rpc_cmd.res = 0	    //配置 RepeatorConfig.ConfigData_body = SvtRepeatorCfgPara
//rpc_cmd.res = 1	    //请求 RepeatorConfig.ConfigData_body 为空
//rpc_cmd.res = 2	    //应答 RepeatorConfig.ConfigData_body = SvtRepeatorCfgPara
message SvtRepeatorCfgPara {
  int32 ch_hang_time = 1; 		    /**信道挂起时间 1000~8000 **/
  int32 private_hang_time = 2; 		/**单呼挂起时间 0~7000**/
  int32 group_hang_time = 3; 		/**组呼挂起时间 0~7000**/
  int32 emer_hang_time = 4; 		/**紧急呼叫挂起时间 0~7000**/
  int32 auto_signal_durtion = 5; 	/**自动发送信标时长 300~600**/
  int32 auto_signal_interval = 6; 	/**自动发送信标间隔 0~300**/
  fixed32 rx_freq = 7;     		    /**接收频率 **/
  fixed32 tx_freq = 8;     		    /**发射频率 **/
  int32 cc = 9;           		    /**色码 0~15**/
  int32 ubAuthEnable = 10;		    /**鉴权开关**/
  bytes ucnAuthKey = 11; 		    /**鉴权秘钥**/
}

/**(虚拟集群)中继设备状态信息**/
//rpc_cmd.cmd = 5  
//rpc_cmd.body = RepeatorConfig
//rpc_cmd.para_int = 10	//SVT_RELAY_STATUS
//rpc_cmd.res = 1	    //请求 RepeatorConfig.ConfigData_body为空
//rpc_cmd.res = 2	    //应答 RepeatorConfig.ConfigData_body = SvtRelayDevStatReport
//rpc_cmd.res = 3	    //上报 RepeatorConfig.ConfigData_body = SvtRelayDevStatReport
message SvtRepeatorDevStatReport {
  fixed32 device_id = 1;   	    /**从机(中继)设备ID**/
  int32 ant_status = 2;	  	    /**天线状态**/	
  int32 temp_status = 3;    	/**温度状态**/
  int32 gps_status = 4;   	    /**GPS同步状态**/
  int32 fan_status = 5;   	    /**风扇状态**/
  int32 tx_status = 6;	  	    /**发射状态**/		
  int32 rx_status = 7;	  	    /**接收状态**/	
  int32 vol_status = 8;   	    /**电压状态**/
  int32 pll_status = 9;   	    /**PLL锁定状态**/
  int32 session_slot1 = 10;   	/**时隙1会话状态**/
  int32 session_slot2 = 11;   	/**时隙2会话状态**/
  int32 free_relay = 12;      	/**当前是否是自由中继**/
}

// 远程控制命令 公共结果码, 如等待配置/请求结果
// rpc_cmd.res = -1	    //失败
// rpc_cmd.res = 1		//成功
