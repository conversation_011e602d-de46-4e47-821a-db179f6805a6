syntax = "proto3";
package bfdx_proto;

//一个通用的gps84定位数据
message gps84{
  string gps_time = 1;
  int32 av = 2; //invalid=0,gps valid =1,last_gps_data=2
  double lat = 3;
  double lon = 4;
  double speed = 5; //km/h
  int32 direction = 6; //north=0
  int32 altitude = 7;
}

//上来的BC命令通用数据头
message bcxx_head{
  string xx = 1;
  int32 b_dic = 2;
  int32 cmd = 3;
  string sys_id = 4;
  int32 gi_flag = 5;
  string c_con_ch = 6;
  string con_ch = 7;
  string m_id_t = 8;
  string m_id_s = 9;
  string cmd_time = 10;
  int32 m_tx_e = 11;
  string ms_status = 12;
  bytes orig_data = 13;
}

//收到重复的命令
//rpc_cmd.command=1101
message cmd_repeate_received{
  string dmr_id = 1;
  string cmd = 2; //bcxx
  string c_dic = 3;
  string sys_id = 4;
  bytes  cmd_bytes = 5;
}

//收到还没登录的对讲机上来的数据
//rpc_cmd.command=1100
message not_register_device_cmd{
  string dmr_id = 1;
  bytes cmd_bytes = 2;
  string received_controller_dmr_id = 3;
}

//收到还没登记到数据库的控制器信息
//rpc_cmd.cmd=1102
//nats.subject=sys_id.new_controller
message new_controller{
  string dmr_id = 1;
  string ip_info = 2;
  string model = 3;
  string device_name = 4;
}


message bc00{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 c_dic = 3;
  int32 cbxx = 4;
}

message bc01{
  bcxx_head head = 1;
  gps84 gps = 2;
}

message bc02{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 y_n = 3;
  int32 time = 4;
  int32 size = 5;
}

message bc03{
  bcxx_head head = 1;
  gps84 gps = 2;
}

message bc04{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 tp = 3;
  int32 y_n = 4;
  int32 pen_n = 5;
  int32 time = 6;
  double min_lat = 7;
  double min_lon = 8;
  double max_lat = 9;
  double max_lon = 10;
}

message bc05{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 tp = 3;
  int32 y_n = 4;
  int32 time = 5;

  double lat = 6;
  double lon = 7;

  string lat_dif = 8;
  string lon_dif = 9;
}

message bc06{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 tp = 3;
  int32 y_n = 4;
  int32 time = 5;
}

message bc07{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 y_n = 3;
  int32 jt_time = 4;
  int32 dw_time = 5;
}
message bc08{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 y_n = 4;
  int32 jt_ch = 5;
  int32 time = 6;
}
message bc09{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 y_n = 3;
  int32 st = 4;
}

message bc10{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 c_dic = 3;
  int32 cbxx = 4;
}

message bc11{
  bcxx_head head = 1;
  int32 y_n = 2;
  bytes c_tp = 3;
  int32 a_tp = 4;
  int32 dd_ch = 5;
}


message bc12{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 p_s = 3;
  int32 lp_s = 4;
  string ls_time = 5;
  string sell_id = 6;
  string licence = 7;
  /**手台归属组ID**/
  fixed32 dev_group = 8;
  /**0:主信道即选定发起注册,1:漫游信道发起注册**/
  int32 roaming = 9;
  bool from_bc40 = 10;
}

message bc13{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 type = 3;
  int32 land_bs = 4;
  int32 land_ch = 5;
  /**手台归属组ID**/
  fixed32 dev_group = 6;
  /**0:主信道即选定发起注册,1:漫游信道发起注册**/
  int32 roaming = 7;
  bool from_bc40 = 8;
}

message bc14{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 land_bs = 3;
  int32 audio_cm = 4;
}

message bc15{
  bcxx_head head = 1;
  int32 voice_d_type = 2;
  int32 voice_a_type = 3;
  int32 call_type = 4;
  int32 call_status = 5;
}

message bc16{
  bcxx_head head = 1;
  gps84 gps = 2;
}

message bc17{
  bcxx_head head = 1;
  int32 y_n = 2;
  string user_st = 3;
  int32 user_tp = 4;
  string user_addr_id = 5;
  string license = 6;
  string user_show_id = 7;
}

message bc18{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 alarm = 3;

  string db_rid = 4; //数据库db_alarm_historyYYYYMM里面对应的rid
}

message bc19{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 y_n = 3;
}
message bc20{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 y_n = 3;
  int32 link_tp = 4;
  int32 a_tp = 5;
  string net_id = 6;
}

message bc21{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 c_dic = 3;
  int32 cbxx = 4;
}
message bc22{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 c_dic = 3;
  int32 cbxx = 4;
  int32 y_n = 5;
  int32 check_st = 6;
}
message bc23{
  bcxx_head head = 1;
  string con_status = 2;
  int32 c_dic = 3;
  int32 cbxx = 4;
}
message bc25{
  bcxx_head head = 1;
  int32 y_n = 2;
  int32 point_n = 3;
  string point_card = 4;
  double lat = 5;
  double lon = 6;
  string lat_dif = 7;
  string lon_dif = 8;
}
message bc26{
  bcxx_head head = 1;
  int32 y_n = 2;
  int32 ch = 3;
  int32 code_tp = 4;
  bytes ch_name = 5;
}
message bc28{
  bcxx_head head = 1;
  int32 y_n = 2;
  int32 code_tp = 3;
  int32 c_byte = 4;
  bytes data = 5;
}
message bc29{
  bcxx_head head = 1;
  int32 y_n = 2;
  string key_id = 3;
}

message bc31 {
  bcxx_head head = 1;

  string sms_type = 2;
  // 短信内容
  string sms_content = 3;
  // 短信序号,uint16(hex)
  string sms_no = 4;
}

message bc38 {
  bcxx_head head = 1;
  string m_group_id = 3;
}
message bc39 {
  bcxx_head head = 1;
  string m_group_id = 3;
}

message bc42{
  bcxx_head head = 1;
  //code uint8: 10:关闭状态 11:开启状态 14:不支持/不适用
  int32 code = 2;
}

//bc30 not for system

message dc00{
  bcxx_head head = 1;
  gps84 gps = 2;
  int32 c_dic = 3;
  int32 cdxx = 4;

}

message dc01_one_rfid{
  string read_time = 1;
  string rfid_id = 2;
  int32 rfid_type = 3;
  int32 db_type = 4;//1,2,3: 线路巡查点 100:人员身份卡 -1:未知卡点 -2:数据库未连接
}
message dc01{
  bcxx_head head = 1;
  //对讲机里面的积压点数量
  int32 backlog_points = 2;

  repeated dc01_one_rfid rfids = 4;

}


//client radio command, cbxx

message cbxx_target{
  //目标群组列表,群组dmrid
  repeated string target_groud = 1;
  //目标设备列表,设备dmrid
  repeated string target_device = 2;
}

message cb01{
  cbxx_target target = 1;
  int32 time = 2;
  int32 size = 3;
  int32 count = 4;
}

message cb02{
  cbxx_target target = 1;
  int32 y_n = 3;
  int32 time = 4;
  int32 size = 5;
}

message cb03{
  cbxx_target target = 1;
  double min_lat = 7;
  double min_lon = 8;
  double max_lat = 9;
  double max_lon = 10;
}

message cb04{
  cbxx_target target = 1;

  int32 y_n = 4;
  int32 pen_n = 5;
  int32 time = 6;
  double min_lat = 7;
  double min_lon = 8;
  double max_lat = 9;
  double max_lon = 10;
}

message cb05{
  cbxx_target target = 1;
  int32 y_n = 4;
  int32 time = 5;

  double lat = 6;
  double lon = 7;

  string lat_dif = 8;
  string lon_dif = 9;
}

message cb06{
  cbxx_target target = 1;

  int32 y_n = 4;
  int32 time = 5;
  string lat_dif = 8;
  string lon_dif = 9;
}

message cb07{
  cbxx_target target = 1;

  int32 y_n = 3;
  int32 jt_time = 4;
  int32 dw_time = 5;
}

message cb08{
  cbxx_target target = 1;

  string src = 2;

  int32 y_n = 4;
  int32 jt_ch = 5;
  int32 time = 6;
}

message cb09{
  cbxx_target target = 1;

  int32 y_n = 3;
  int32 st = 4;
}

message cb10{
  cbxx_target target = 1;

}

message cb11{
  cbxx_target target = 1;

  int32 y_n = 2;
  bytes c_tp = 3;
  int32 a_tp = 4;
  int32 dd_ch = 5;

  string initiator = 6;//中心下发的话为00000000,手动发起的话则为手台的dmr-id
  bytes  orig_cmd = 7;//手台发起时,这里面是根据手台上来的原始数据转换过来的命令,其中目标控制器为XXXXXXXX
}

message cb12{
  cbxx_target target = 1;
  int32 type = 2;
  int32 bs_id = 3;
  int32 bs_n = 4;
  int32 ch_id = 5;
  int32 ch_n = 6;
  string con_status = 7;
  int32 myxb_TT = 8;
  string c_bs_n = 9;
  int32 ch_dd_st = 10;
}

message cb17{
  cbxx_target target = 1;
  int32 y_n = 2;
  string user_st = 3;
  int32 user_tp = 4;
  string user_addr_id = 5;
  string license = 6;
  string user_show_id = 7;
}

message cb19{
  cbxx_target target = 1;
  int32 type = 2;
  string new_duty = 3;
  string old_duty = 4;
}

message cb20{
  cbxx_target target = 1;

  int32 y_n = 3;
  int32 link_tp = 4;
  int32 a_tp = 5;
  string net_id = 6;
}
message cb21{
  cbxx_target target = 1;

  int32 y_n = 3;
  int32 dd_ch = 4;
}
message cb24{
  cbxx_target target = 1;

  int32 y_n = 2;
  string data = 3;
  int32 code_tp = 4;

  //预定发送时间(utc),如果是立即发送,填空字符串
  string schedule_send_time = 5;
}

message cb25{
  cbxx_target target = 1;

  int32 y_n = 2;
  int32 point_n = 3;
  string point_card = 4;
  double lat = 5;
  double lon = 6;
  string lat_dif = 7;
  string lon_dif = 8;
}

message cb26{
  cbxx_target target = 1;

  int32 y_n = 2;
  int32 ch = 3;
  string ch_name = 5;
}

message cb42{
  cbxx_target target = 1;
  //code [uint8]: 10:关闭 11:开启 12:查询
  int32 code = 2;
}

//客户端调用cbxx命令时
//服务器返回发送的命令和序号,以便客户端识别手台回应命令
message cbxx_send_stub{
  string cbxx = 1; //发送的命令
  repeated string target_group = 2; //每一个目标群组的dmr-id
  repeated string target_group_seq_no = 3;//目标群组对应的发送序号
  repeated string target_device = 4;//每一个设备dmr-id
  repeated string target_device_seq_no = 5;//每一个设备rid发送命令的序号
}

//控制器交互命令
message cc01{
  //=1 控制器连接注册成功
  //=2 控制器连接已断开
  //=3 控制器报警
  //=4 设置控制器参数
  //=5 查询控制器参数
  //=6,控制器在线监测

  //=80,客户端查询控制器状态，此时dmr_id为逗号分隔的控制器 id列表
  //=103, cc03
  int32 action_code = 1;
  string dmr_id = 2;
  string ccxx_str = 3;
}

//8100广播app请求特定终端的gps数据广播
//same as req_gps_permission in app_proto.proto
//rpc.cmd=8181, server to web client
//kcp rpc.cmd=81, app to server
message cc81 {
  //目标终端dmr_id
  uint32 target_dmr_id = 1;
  //发起请求的终端dmr_id
  uint32 apply_dmr_id = 2;
}

// apply response for device gps info permission
// kcp rpc.cmd=82, server to app
message res_gps_permission {
  //response code
  //0:ok 4:reject
  int32 code = 1;
  //device dmrid
  uint32 dmrid = 2;
  //who grant the permission, user rid
  string grant_user_rid = 3;
  //grant user name
  string grant_user_name = 4;
  //permission expire time
  string expire_time = 5;
}

//8100中继状态查询/通知
//rpc.cmd=83
message cc83 {
  //1:查询中继在线状态,
  //2:回应查询 1,返回在线的中继
  //3:中继上线通知
  //4:中继下线通知
  //11:查询手台在线状态
  //12:回应查询11,返回在线的手台
  //13:手台上线通知
  //14:手台下线通知
  int32 action_code = 1;
  //action_code=2, 这里是在线的中继列表
  repeated string hex_dmrids = 2;
  //相应的型号信息
  repeated string models = 3;
  //相应的额外功能，逗号分隔
  //8:中继支持监控数据查询的报告
  repeated string functions = 4;
}

//收到未知物联网设备上来的数据
//rpc_cmd.command=1110
message unknown_iot_device_cmd{
  //未知设备类型  200:基站巡查点 1:对讲机 2:工牌 3:物卡 4：烟感  5：设备开关盒  6：信标开关盒
  int32 dev_type = 1;
  //未知设备的dmrid或rid  基站巡查点->rfid  对讲机等->dmrid
  string dev_id = 2;
}

//越界报警.对应db_iot_restriction 1.离开只允许进入基站巡查点 2.进入禁止进入基站巡查点
message over_step_base_station{
  //设备类型  1:对讲机 2:工牌 3:物卡 4：烟感  5：设备开关盒  6：信标开关盒
  int32 dev_type = 1;
  //设备id
  string dev_id = 2;
  //越界类型 1.离开只允许进入基站巡查点 2.进入禁止进入基站巡查点
  int32 over_step_type = 4;
  //
}

// sip协议终端可以动态修改自己的收听组
message SipGroupSubscribeOperation {
  // 操作命令
  // 1:添加
  // 2:删除
  // 3:查询当前收听组（内存）
  // 4:查询当前收听组（数据库配置）
  // 5:替换当前收听组为dmrids（内存）
  // 6:替换当前收听组为dmrids（数据库配置）
  // 7:清空当前收听组（内存）
  // 101: response to 1
  // 102: response to 2
  // 103: response to 3
  // 104: response to 4
  // 105: response to 5
  // 106: response to 6
  // 107: response to 7
  int32 operation = 1;
  // 收听组dmrid，每个为8位HEX字符串
  repeated string dmrids = 2;
  // response code
  // 0:成功 1:失败
  int32 res = 3;
  // response message when err
  string res_msg = 4;
}
