syntax = "proto3";
package bfdx_proto;
import "db.proto";
option optimize_for = LITE_RUNTIME;

/*
msID: 定义为 uint32 类型，则对应prochat内部的终端ID; 定义为 string 类型，则对应专网系统的DMRID;
groupID：与msID类似，定义为 uint32 类型，则对应prochat内部的组ID; 定义为 string 类型，则对应专网系统的组ID;
*/

//消息版本类型
enum MessageVersion {
  MV_Unknow = 0;
  MV_VERSION_1 = 1;
}

//消息服务类型
enum ServiceType {
  ST_Unknow = 0;
  ST_SG_CALL = 1;
}

//专网网关与公网网关消息
enum MessageType {
  MT_Unknow = 0;
  MT_SG_LOGIN_REQUEST = 2000;
  MT_GS_LOGIN_RESPONSE = 2001;
  MT_SG_HEARTBEAT_REPORT = 2002;
  MT_GS_HEARTBEAT_CONFIRM = 2003;
  MT_SG_GROUP_LIST = 2004;
  MT_SG_MS_LIST = 2005;
  MT_SG_MS_ONLINE_STATUS_UPDATE = 2006;
  MT_SG_MS_UPDATE = 2007;
  MT_SG_GROUP_UPDATE = 2008;
  MT_SG_GET_MS_LIST_REQUEST = 2009;
  MT_GS_GROUP_TYPE_UPDATE = 2010;
  MT_SG_CALLING_START = 2011;
  MT_SG_CALLING_STOP = 2012;
  MT_SG_CALLED_STOP = 2013;
  MT_SG_LOCATION_REPORT = 2014;
  MT_SG_SMS_NOTIFY = 2015;
  MT_GS_MS_LIST = 2016;
  MT_GS_MS_UPDATE = 2017;
  MT_GS_CREATE_TEMP_GROUP = 2018;
  MT_GS_TEMP_GROUP_UPDATE_MEMBER_REQUEST = 2019;
  MT_SG_TEMP_GROUP_MEMBER_UPDATE = 2020;
  MT_GS_DELETE_TEMP_GROUP = 2021;
  MT_SG_GET_MS_ONLINE_STATUS_REQUEST = 2022;
}

message MTSG_LoginRequest {
  string loginName = 1;
  bytes authRandom = 2;
  bytes authCode = 3;
}

message MTGS_LoginResponse {
  string login_name = 1;
  //0: 成功
  //1: 用户不存在
  //2: 密码错误
  //3: 服务器错误
  int32 result = 2;
  uint32 gwID = 3;
  string receiveIP = 4;
  uint32 receivePort = 5;
  uint32 random = 6;
}

message MTSG_HeartbeatReport {
  uint32 gwID = 1;
  //附加数据，留作扩展使用
  bytes attachData = 2;
}

message MTGS_HeartbeatConfirm {
  uint32 gwID = 1;
  //附加数据，留作扩展使用
  bytes attachData = 2;
}

message MTSG_GroupList {
  uint32 gwID = 1;
  uint32 listEnd = 2;
  repeated db_org groupList = 3;
}

message MTSG_MsList {
  uint32 gwID = 1;
  uint32 listEnd = 2;
  repeated db_device msList = 3;
}

message MsStatusInfo{
  string msID = 1;
  // 0: 离线, 1: 在线 2：表示动作，设备上线
  uint32 onlineStatus = 2;
}

message MTSG_MsOnlineStateUpdate{
  uint32 gwID = 1;
  repeated MsStatusInfo msStatusList = 2;
}

message MTSG_GetMsOnlineStatusRequest{
  uint32 gwID = 1;
  // 0:填写msIDList，1：不填写msIDList，获取所有终端状态
  uint32 getAll = 2;
  repeated string msIDList = 3;
}

message MTSG_MsUpdate {
  uint32 gwID = 1;
  uint32 operate = 2;
  string msID = 3;
  db_device msInfo = 4;
}

// 0：添加群组
// 1：更新群组
// 2：删除群组
// 对动态组的增删改操作也发送这个消息
message MTSG_GroupUpdate {
  uint32 gwID = 1;
  uint32 operate = 2;
  string groupID = 3;
  db_org groupInfo = 4;
}

message MTSG_GetMsListRequest {uint32 gwID = 1;}

// 0：纯专网组
// 1：纯公网组
// 2：混合组
message GroupTypeInfo {
  string groupID = 1;
  uint32 groupType = 2;
}

message MTGS_GroupTypeUpdate {
  uint32 gwID = 1;
  repeated GroupTypeInfo groupList = 2;
}

message MTSG_CallingStart {
  uint32 gwID = 1;
  uint32 callType = 2;
  string callingMsID = 3;
  string callingMsName = 4;
  string calledTargetID = 5;
  uint32 priority = 6;
  uint32 callingSeq = 7;
}

message MTSG_CallingStop {
  uint32 gwID = 1;
  uint32 callType = 2;
  string callingMsID = 3;
  string calledTargetID = 4;
}

message MTSG_CalledStop {
  uint32 gwID = 1;
  string callingMsID = 2;
  //0-离线
  //1-切换群组
  //5-主呼设备在呼叫中
  uint32 stopReason = 3;
}

message LocationInfo {
  string msID = 1;
  uint32 coordinateSystem = 2;
  double latitude = 3;
  double longitude = 4;
  double speed = 5;
  double altitude = 6;
  uint32 direction = 7;
  uint64 time = 8;
}

message MTSG_LocationReport {
  uint32 gwID = 1;
  repeated LocationInfo locationList = 2;
}

message TargetInfo {
  uint32 targetType = 1;
  string targetID = 2;
}

message SMSInfo {
  string sendMsID = 1;
  repeated TargetInfo targetList = 2;
  string content = 3;
  uint64 time = 4;
}

message MTSG_SMSNotify {
  uint32 gwID = 1;
  repeated SMSInfo smsList = 2;
}

message MsInfo {
  uint32 msID = 1;
  uint32 msType = 2;
  string deviceId = 3;
  uint32 priority = 4;
}

message MTGS_MsList {
  uint32 gwID = 1;
  uint32 listEnd = 2;
  repeated MsInfo msList = 3;
}

message MTGS_MsUpdate {
  uint32 gwID = 1;
  uint32 operate = 2;
  string msID = 3;
  MsInfo msInfo = 4;
}

message MTGS_CreateTempGroup {
  uint32 gwID = 1;
  uint32 groupID = 2;
  string groupName = 3;
  repeated string memberList = 4;
}

// 0: 添加成员
// 2： 删除成员
message MTGS_TempGroupUpdateMemberRequest {
  uint32 gwID = 1;
  uint32 groupID = 2;
  uint32 operate = 3;
  repeated string memberList = 4;
}

message MTSG_TempGroupMemberUpdate {
  uint32 gwID = 1;
  uint32 groupID = 2;
  repeated db_dynamic_group_detail memberList = 3;
}

message MTGS_DeleteTempGroup {
  uint32 gwID = 1;
  uint32 groupID = 2;
}
