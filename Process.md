# 前端与后端的交互过程

#### 前端交互流程图

```flow
start=>start: Web Client Request
enpbf=>operation: google protobuf encode
nats=>operation: Nats Server
res=>condition: Has Response or Not?
depbf=>operation: google protobuf decode
process=>operation: Process response data
end=>end: End

start->enpbf->nats->res
res(no)->end
res(yes)->depbf->process->end
```

#### google protobuf 协议说明

- 前端与后台的交互是使用google protobuf协议，将需要发送的指令先序列化成[Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array)，再将该指令再序列化成统一格式的消息rpc_cmd，使用nats服务器publish/subscribe模式将rpc_cmd序列化后的Uint8Array数据发布到nats服务器上的主题事件，后端服务器收到相关事件后，会将处理结果再以特定的消息subject发送到nats服务器，由前端订阅对应的subject事件进行相关逻辑处理。

- 当前使用的[Nats](https://nats.io/)服务器交互，传输的是base64字符串，所以在调用Nats server publish事件前，需要将Uint8Array数组编码成base64字符串

- 前端使用protobufjs处理protobuf协议消息的序列化和反序列化，模块地址：[protobufjs](https://github.com/dcodeIO/ProtoBuf.js/)

#### 登录交互

1. 连接服务器: 使用[webSocket-nats](https://github.com/isobit/websocket-nats)模块连接到后端指定的服务器地址

2. 发起登录: 根据协议里的登录请求类消息生成一个类对象，按对象结构类型赋值，再调用类的encode、finish方法序列化成Uint8Array。 之后，再生成一个rpc_cmd类对象，将登录的请求消息的Uint8Array赋值给rpc_cmd.body, 按类型设置相关的命令参数，生成Uint8Array,再publish出去。最后，以登录响应类消息名称反序列化服务器返回的数据，再根据得到的响应数据作出对应的逻辑流程处理。

#### 登录之后，bcxx/cbxx/ccxx指令之前

1. 登录后，根据登录用户的数据，向服务器请求自己拥有权限的所有组织、设备、控制器、用户等数据，处理各数据之间的关系，保存在全局变量中，以便随时使用。

2. 开始监听登录用户有权限的组织的nats主题事件.

3. 开始监听nats服务器的相关的bcxx/cbxx/ccxx等指令以命令号为subject的主题事件。

#### 监听并处理各种bcxx/cbxx/ccxx指令

- bcxx，为手台(对讲机)发送到服务器的指令。如手台上传的开/关机、定位、语音、报警等指令。
  1. 读取指令数据，以对应的指令消息名称进行反序列化，得到对应指令的对象数据。
  2. 读取得到的对象数据相关属性，判断指令是否为合法、有效，同步更新设备状态数据(设备的最后状态信息、gps定位数据、设备锁机状态等)、前端UI用户体验(消息通知、数据表格更新、关系列表树同步等)

- cbxx, 为服务器发送到手台的指令。如果开启手台的定位、语音监控、锁机/禁听、电子围栏等指令。前端发到服务器的也是cbxx指令，再由服务器转发对应目标。
  1. 根据需要发送的指令名称，生成protobuf类对象，设置相关命令参数，序列化后，以不同的主题名称发送到nats服务器，服务器会返回相关的事件响应。前端需要提前处理好相关的超时、响应事件的监听。前端的cbxx类指令通常是监听bc00指令得到响应。

- ccxx， 为服务器与控制器的交互指令，如控制器在线监听、故障警报等处理，前端处理的是在线监听和会议室的处理，bf8100已经取消控制器，几本处理在线监听就可以了。
