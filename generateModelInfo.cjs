/* eslint-disable */

const { writeFileSync } = require('fs')

// 中继暂时没有的机型数据
const customRepeaterModelInfo = [
  {
    modelName: 'BF-TR8050E',
    modelCode: ['TR805005'],
  },
  // TR900M
  {
    modelName: 'TR900M',
    modelCode: ['TR900M'],
  },
  // TR900M 法语版本
  {
    modelName: 'BF-TR900MFR',
    modelCode: ['TR90FR'],
  },
  {
    modelName: 'BF-TR900M(R7F)',
    modelCode: ['RR900M'],
  },
  // TR925M
  {
    modelName: 'TR925M',
    modelCode: ['TR925M'],
  },
  // DZ1480
  {
    modelName: 'BF-TS908',
    modelCode: ['DZ1480'],
  },
  // TR900L
  {
    modelName: 'TR900L',
    modelCode: ['TR900L'],
  },
  // TR925D
  {
    modelName: 'TR925D',
    modelCode: ['TR925D'],
  },
  // TR925R
  {
    modelName: 'TR925R',
    modelCode: ['TR925R'],
  },
  /* 虚拟集群控制器和中继 */
  // DZ148SVT
  {
    modelName: 'DZ148SVT',
    modelCode: ['DZ1481'],
  },
  {
    modelName: 'TR900SVT',
    modelCode: ['TR900SVT'],
  },
  {
    modelName: 'TR850SVT',
    modelCode: ['TR850SVT'],
  },
  // 与TR925R机型协议一致
  {
    modelName: 'BF-TR8500M',
    modelCode: ['TR850M'],
  },
  {
    modelName: 'BF-TR8500M(R7F)',
    modelCode: ['RR850M'],
  },
  {
    modelName: 'BR1050(SDC)',
    modelCode: ['BR105M'],
  },
  {
    modelName: 'TR8500SVT',
    modelCode: ['TR850S'],
  },
  {
    modelName: 'TR8500SVT',
    modelCode: ['RR850S'],
  },
  {
    modelName: 'BR1050SVT',
    modelCode: ['BR105S'],
  },
  {
    modelName: 'TR900SVT',
    modelCode: ['TR900S'],
  },
  {
    modelName: 'TR900SVT',
    modelCode: ['RR900S'],
  },
]

// 终端机型信息，包括车载台
const customDeviceModelInfo = [
  {
    modelName: 'BF-TD510(SDC)',
    modelCode: ['510SDC00', '510R7F00'],
  },
  {
    modelName: 'BF-TD510(SVT)',
    modelCode: ['510SVT00'],
  },
  // 511SDC机型最后一位包含了不同的功能配置，采用了基础数值(0x30)作为最后一位为配置机型名称
  {
    modelName: 'BF-TD511(SDC)',
    modelCode: ['511SDC00', '511S7000', '511R7F00'],
  },
  // 功能继承于TD511(SDC)，主要多了加密功能及相关的数据
  {
    modelName: 'BF-TD511(SDC)FR',
    modelCode: ['511FR000', '511FRR00'],
  },
  // 功能继承于TD511(SDC)，添加虚拟集群相关功能配置
  {
    modelName: 'BF-TD511(SVT)',
    modelCode: ['511SVT00'],
  },
  // BF-TD818(SDC)与BF-TD818(SVT)同数据结构，屏蔽SVT相关的配置，涉及信道、SVT集群、SVT站点等配置
  {
    modelName: 'BF-TD818(SDC)',
    modelCode: ['818SDC00'],
  },
  // BF-TD818SVT，该机型为BF-TD511SVT(R7F)的包壳机，除模拟信道亚音与511SVT有区别外，其它参数均与511SVT一致
  {
    modelName: 'BF-TD818(SVT)',
    modelCode: ['818SVT00'],
  },
  // TD081000,TD081100
  {
    modelName: 'BF-TD800(SDC)',
    modelCode: ['TD081000', 'TD081100'],
  },
  // BF-TD880(SDC)，实际就是BF-TD511(SDC)，只是机型码不一样
  {
    modelName: 'BF-TD880(SDC)',
    // 880SC100(S70), 880SC200(R7F)
    modelCode: ['880SC100', '880SC200'],
  },
  // BF-TD880(SVT)，实际就是BF-TD511(SVT)，只是机型码不一样
  {
    modelName: 'BF-TD880(SVT)',
    modelCode: ['880SVT00'],
  },
  /*
    取消该机型进入系统
   // TD081100
    {
      modelName: 'BF-TD811',
      modelCode: ['TD081100'],
    },
    */
  // 825SDC00
  {
    modelName: 'BF-TM8250(SDC)',
    modelCode: ['825SDC00'],
  },
  // 825SDC00 法语版本
  {
    modelName: 'BF-TM8250(SDC)FR',
    modelCode: ['8250FR00'],
  },
  // TM8250(SDC) R7F，基于TD930SDC_R7F功能移植
  {
    modelName: 'BF-TM8250(SDC)R7F',
    modelCode: ['R82500S0'],
  },
  // TD910SDC
  {
    modelName: 'BF-TD910(SDC)',
    // 芯片型号：sdc, s70, r7f
    modelCode: ['DP109SDC', '109PSDC0', 'DP109R7F'],
  },
  // 109PSDC0
  {
    modelName: 'BF-TD910P(SDC)',
    // 芯片型号：sdc, r7f
    modelCode: ['109PSDC1', '109PR7F1'],
  },
  // TD920, SDC只是其中一个选配功能
  {
    modelName: 'BF-TD920',
    modelCode: ['DP140100'],
  },
  // TD930SDC
  {
    modelName: 'BF-TD930(SDC)',
    modelCode: ['930SDC00'],
  },
  // TD930SDC R7F芯片
  {
    modelName: 'BF-TD930(SDC)R7F',
    modelCode: ['R930SDC0', 'D930SD'],
  },
  {
    modelName: 'BP750(SDC)',
    modelCode: ['R935SDC0', 'D935SD'],
  },
  // TD930SVT
  {
    modelName: 'BF-TD930(SVT)',
    modelCode: ['930SVT00'],
  },
  // TD930SVT R7F芯片
  {
    modelName: 'BF-TD930(SVT)R7F',
    modelCode: ['R930SVT0', 'D930SV'],
  },
  {
    modelName: 'BP750(SVT) ',
    modelCode: ['R935SVT0', 'D935SV'],
  },
  {
    modelName: 'BP660',
    modelCode: ['BP660100'],
  },
  {
    modelName: 'BF-BP860SDC',
    modelCode: ['D860SD00'],
  },
  {
    modelName: 'BF-BP860SVT',
    modelCode: ['D860SV00'],
  },
  // 基于TM8250(SDC)R7F功能移植
  {
    modelName: 'BF-TM8250(SVT)R7F',
    modelCode: ['R82500V0'],
  },
  {
    modelName: 'BP610',
    modelCode: ['BP660200'],
  },
  {
    modelName: 'BP620',
    modelCode: ['BP660300'],
  },
]

// 注册到系统的所有机型信息
const modelInfoList = [...customDeviceModelInfo, ...customRepeaterModelInfo]

// 生成数据对象源，{[modelCode]:modelData}
const ModelInfo = modelInfoList
  .map(item => {
    const modelInfo = []
    const modelCodes = Array.isArray(item.modelCode)
      ? item.modelCode
      : [item.modelCode]
    for (let i = 0; i < modelCodes.length; i++) {
      const model = modelCodes[i]
      modelInfo.push({
        [model]: item,
      })
    }
    return modelInfo
  })
  .flat()
  .reduce((p, c) => {
    return Object.assign(p, c)
  }, {})

// write es6 module
const es6Module = `/***
 * Generate module in ${new Date()}
 * Don't edit this file, please!
 * https://wiki.bfdx.com/index.php/%E6%9C%BA%E5%9E%8B%E4%BF%A1%E6%81%AF
 ***/

/**
 * @typedef OneModelInfo
 * @property {string} modelName 机型名称
 * @property {string[]} modelCode 机型码列表
 * @property {(model: string) => boolean} [checkModel] 检查机型码是否匹配机型信息
 */

/**
 * 生成带"checkModel"方法的机型信息
 * @param {{[key:string]: OneModelInfo}} models
 * @returns {{[key:string]: OneModelInfo}}
 */
function generateModelCheckModelMethod(models) {
  for (const key in models) {
    const modelInfo = models[key]
    Object.assign(modelInfo, {
      checkModel: (model) => checkModel(modelInfo.modelCode, model),
    })
  }
  return models
}

/**
 * 带"checkModel"方法的机型信息集合
 * @type {{[key:string]: OneModelInfo}}
 */
const ModelInfo = generateModelCheckModelMethod(${JSON.stringify(ModelInfo)})

/**
 * 通过机型码查找机型信息
 * @param {string} modelCode 机型码
 * @returns {OneModelInfo | undefined} 机型信息
 */
function getModel(modelCode) {
  if (!modelCode) return undefined

  const model = ModelInfo[modelCode]
  // 机型码后面2位不固定的，才需要遍历所有机型信息，调用'checkModel'来检查是否匹配机型
  if (!model) {
    for (const key in ModelInfo) {
      const data = ModelInfo[key]
      if (data.checkModel(modelCode)) {
        return data
      }
    }
  }

  return model
}

/**
 * 通用的检查机型码兼容判断方法，只要检查的机型码前6位匹配即可，后2位为功能码
 * @param {Array<string>} modelList 兼容的机型码列表
 * @param {string} model 需要检查的机型码
 * @returns {boolean}
 */
export function checkModel(modelList, model) {
  const model6bit = model.slice(0, 6)
  return modelList.some(m => m.startsWith(model6bit))
}

/**
 * 检测读频的机型是否与界面一致，一致则返回true，否则返回false
 * @param {string} modelCode: 读取终端数据的机型码
 * @param {string} expectedModel: 系统内置的机型码
 * @returns {boolean}
 */
export function checkDeviceModel(modelCode, expectedModel) {
  const model = getModel(expectedModel)
  // 查找不到机型信息，非法机型
  if (!model) {
    return false
  }

  return checkModel(model.modelCode, modelCode)
}

/**
 * 获取机型码对应的机型名称方法
 * @param {string} modelCode
 * @returns {string}
 */
export function getModelName(modelCode) {
  const model = getModel(modelCode)
  return model?.modelName ?? modelCode
}

export default {
  getModelName,
  checkModel,
  checkDeviceModel,
}
`
writeFileSync('src/writingFrequency/modelInfo.js', es6Module)
