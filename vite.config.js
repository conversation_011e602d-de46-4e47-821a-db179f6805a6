/* eslint-disable no-undef */

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { viteCommonjs } from '@originjs/vite-plugin-commonjs'
import path from 'path'
import fs from 'fs'
import { version } from './package.json'
import { nodePolyfills } from 'vite-plugin-node-polyfills'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import eslintPlugin from 'vite-plugin-eslint'
import tailwindcss from '@tailwindcss/vite'
import postcsspxtorem from 'postcss-pxtorem'

// 获取版本编译时间
function clientBuildTime() {
  const isoString = new Date().toISOString()
  return isoString.slice(0, 10) + ' ' + isoString.slice(11, 19)
}

// 获取git版本hash
function clientGitTag() {
  const gitHEAD = fs.readFileSync('.git/HEAD', 'utf-8').trim()
  if (gitHEAD.startsWith('ref:')) {
    const ref = gitHEAD.slice(4).trim()
    return fs.readFileSync(`.git/${ref}`, 'utf-8').trim()
  }
  return gitHEAD
}

export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '')
  const isProduction = env.NODE_ENV === 'production'

  // 配置代理服务器
  function getProxy() {
    const protocol = env.VITE_protocol
    const hostname = env.VITE_hostname
    const webPort = env.VITE_webPort
    const ProxyHref = `${protocol}://${hostname}:${webPort}`
    const context = [
      '/myip',
      '/download_sound',
      '/download_sound_wav',
      '/sys_ini_config',
      '/repeater-op-no',
      '/server_version',
      '/gmap',
      '/bf8100/assets/software/',
      '/bfdx_proto.Prochat/',
      '/active_sys_ids',
    ]

    return context.reduce((proxy, ctx) => {
      const src = Array.isArray(ctx) ? ctx[0] : ctx
      const basePath = Array.isArray(ctx) ? ctx[1] || '/' : '/'

      proxy[src] = {
        target: ProxyHref,
        changeOrigin: true,
        secure: false,
        rewrite: p => p.replace(new RegExp(`^${src}`), path.join(basePath, src)),
        headers: {
          Origin: ProxyHref,
          Reference: ProxyHref,
        },
      }
      return proxy
    }, {})
  }

  return {
    base: '/',
    build: {
      outDir: `./build/webclient-${version}`,
      assetsDir: '',
      sourcemap: !isProduction,
      minify: isProduction ? 'terser' : false,
      rollupOptions: {
        output: {
          assetFileNames: assetInfo => {
            let extType = assetInfo.name.split('.').at(-1)
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
              extType = 'img'
            }
            return `${extType}/[name]-[hash][extname]`
          },
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
        },
      },
    },
    plugins: [
      vue(),
      viteCommonjs(),
      nodePolyfills({
        // Whether to polyfill specific globals.
        globals: {
          Buffer: true, // can also be 'build', 'dev', 'runtime', 'always'
          process: true,
        },
        include: ['crypto', 'stream', 'util', 'path'],
      }),
      AutoImport({
        dts: false,
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        dts: false,
        resolvers: [ElementPlusResolver()],
      }),
      eslintPlugin({
        // 配置项 (可选)
        fix: true, // 自动修复部分 ESLint 问题 (可选)
        cache: false, // 禁用缓存，确保每次都进行 lint 检查
        // include: ['./src/**/*.{js,jsx,ts,tsx,vue}'], // 指定要 lint 的文件 (可选)
        // exclude: ['./node_modules/**'], // 排除不需要 lint 的目录 (可选)
      }),
      tailwindcss(),
    ],
    css: {
      postcss: {
        plugins: [
          postcsspxtorem({
            rootValue: 16,
            propList: ['*'],
            include: /node_modules\/element-plus/i,
          }),
        ],
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '~': __dirname,
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    define: {
      CLIENT_BUILD_TIME: `"${clientBuildTime()} UTC"`,
      CLIENT_GIT_TAG: `"${clientGitTag()}"`,
    },
    server: {
      open: true,
      host: true,
      proxy: getProxy(),
    },
  }
})
