/*
 * BF-8100系统web界面定位配置
 */

// 系统写频功能，支持的设备型号
const modelConfig = {
  // 机型码与机型名称分隔符，重新自定义机型名称与分隔符冲突时使用
  // 比较特殊的机型名称包含了冒号(":")，可以替换为"#", "@", "$"等符号
  modelSeparator: ':',

  // 定义支持的机型时，可重新自定义机型名称，用"modelSeparator"配置的符号分隔，如: "510SDC00: BF-TD510(SDC)"
  // 对讲终端型号，包含对讲机、车载台，支持的型号如下:
  // TD081000/TD081100: BF-TD800(SDC),
  // 510SDC00: BF-TD510(SDC),
  // 511SDC00: BF-TD511(SDC), 511FR000: BF-TD511(SDC)FR, 511SVT00: BF-TD511(SVT),
  // 825SDC00: BF-TM8250(SDC), 8250FR00: BF-TM8250(SDC)FR,
  // DP109SDC: BF-TD910(SDC), 109PSDC0: BF-TD910P(SDC),
  // 930SDC00: BF-TD930(SDC), 930SVT00: BF-TD930(SVT),
  // 880SC100/880SC200: BF-TD880(SDC), 880SVT00: BF-TD880(SVT),

  // 终端型号配置参考
  // device: ['511FR000', '8250FR00'],
  device: [],

  // 对讲机写频功能的常规设置，语言类型选项是否可设置
  // 默认可设置，当deviceNoLocale参数设置为true时，隐藏语言类型选项
  // 参数值：true, false
  deviceNoLocale: false,

  // 中继型号，支持的型号如下：
  // TR805005: BF-TR8050E, TR900M: TR900M, TR90FR: BF-TR900MFR, TR925M: TR925M,
  // DZ1480: BF-TS908(同播控制器),
  // DZ1481: DZ1481(虚拟集群控制器),
  // TR850M: TR8500M,

  // 中继型号配置参考
  // repeater: ['TR90FR'],
  repeater: [],
}

// 系统语言支持
const languages = {
  // 支持的语言，当前支持"中文(zh-cn)","英文(en)","法文(fr)"
  // supports: ['zh-cn', 'de'],
  // 切换语言菜单中显示的文本，顺序与supports一致
  // displayLabels: ['中文', '德语'],
  // 默认显示的语言，默认跟随浏览器语言
  default: '',
}

// 菜单选项隐藏配置，当前只支持帮助菜单的显示/隐藏控制
// 参数值：true(显示), false(隐藏)，默认为true
const menuControl = {
  // 帮助菜单配置
  helpMenu: {
    // 系统说明文档
    // systemFile: true,
    // 关于公司官网
    // aboutBF: true,
    // 客户端运行日志
    // notes: true,
    // 版本信息
    // version: true,
    // 相关软件下载
    // software: true,
  },
  // 命令菜单配置
  commandMenu: {
    // 改善命令
    // sendCommand: true,
    // 导出通讯录
    // contacts: true,
    // 中继写频
    // repeaterCommand: true,
    // 对讲机写频
    // terminalCommand: true,
  },
}

// 网站标题配置
// 网页标签左侧的图标，固定名为favicon.ico，放在客户端根目录下，需要变更的，直接替换即可
const siteConfig = {
  // 网站标题，优先使用该参数，不设置则默认使用系统内i18n翻译的标题
  siteTitle: '',

  // 配置系统顶部的滚动的标题
  // 优先使用系统设置中设置的滚动标题，系统中没有设置时才使用该参数，不设置则使用系统内i18n翻译的标题
  moveTitle: '',

  // 登录背景图名称格式: login.{language}.{ext} 例如: login.zh-cn.jpg
  // 登录背景图后缀，jpg,png,...
  loginBackgroundImageExt: 'jpg',

  // logo配置，使用指定的后缀，jpg,png,...
  logoExt: 'png',
}

// 地图配置
const mapConfig = {
  // 使用天地图或谷歌地图, [tianditu, google]
  // mapName: 'google',
  // 地图可能需要的token，目前只有天地图需要
  // token: '9b0b02390ca05e91ce5e113274103357',
  tilesMaxzoom: 18,
  tilesMinzoom: 0,
  maxZoom: 23,
  minZoom: 1,
  defaultZoom: 15,
  tile: 0,
  mapCenter: [118.62188333333333, 25.003556666666668],
}

// 系统的一些全局配置
const sysConfig = {
  maxAgeOfOnline: 30,
  maxAgeOfGpsTime: 30,
  maxAgeOfDataTime: 10080,
}

// 存在到全局变量中
window.bfglob = Object.assign(window.bfglob || {}, {
  modelConfig,
  languages,
  menuControl,
  siteConfig,
  mapConfig,
  sysConfig,
})
