﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace BFDX
{
    /// <summary>
    /// 序列号用的数字
    /// </summary>
    public class SnNumber
    {
        private static readonly string Characters;
        /// <summary>
        /// 进制
        /// </summary>
        public static int BaseLength
        {
            get
            {
                if (Characters != null)
                    return Characters.Length;
                else
                    return 0;
            }
        }

        static SnNumber()
        {
            string tmp = string.Empty;

            var excludes = new List<char> {
                '1', '3', '5', 'A', 'G', 'H', 'K', 'P', 'Q', 'R', 'U', 'V', //旧的商品码的头两个数字
                'I', 'O', 'Z', //阅读易混淆的
                'E', 'U', 'B', 'D', 'J', 'Z', 'M','N', //发音易混淆的
                '4', //其他
            }.Distinct();

            List<char> chars = new List<char>();
            for (byte i = 0x30; i <= 0x39; i++) //0-9
            {
                if (!excludes.Contains((char)i))
                {
                    tmp += (char)i;
                }
            }
            for (byte i = 0x41; i <= 0x5A; i++)//A-Z
            {
                if (!excludes.Contains((char)i))
                {
                    tmp += (char)i;
                }
            }

            Characters = tmp;
        }

        public static int MaxValue(int length)
        {
            double pow = Math.Pow(BaseLength, length);
            if (pow > int.MaxValue)
            {
                throw new ArgumentOutOfRangeException();
            }
            else
            {
                return (int)pow;
            }
        }

        /// <summary>
        /// 十进制数字转换为指定的进制形式字符串
        /// </summary>
        /// <param name="number"></param>
        /// <returns></returns>
        public static string ToString(long number, int len = 0)
        {
            string value;
            if (number == 0)
            {
                value = Characters[0].ToString();
            }
            else
            {
                List<string> result = new List<string>();
                long t = number;

                while (t > 0)
                {
                    var mod = t % BaseLength;
                    t = Math.Abs(t / BaseLength);
                    var character = Characters[Convert.ToInt32(mod)].ToString();
                    result.Insert(0, character);
                }

                value = string.Join("", result.ToArray());
            }

            if (len > 0)
            {
                return value.PadLeft(len, Characters[0]);
            }
            else
            {
                return value;
            }
        }

        /// <summary>
        /// 指定字符串转换为十进制的数字形式
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static long FromString(string str)
        {
            long result = 0;
            int j = 0;
            foreach (var ch in new string(str.ToCharArray().Reverse().ToArray()))
            {
                if (Characters.Contains(ch))
                {
                    result += Characters.IndexOf(ch) * ((long)Math.Pow(BaseLength, j));
                    j++;
                }
                else
                {
                    throw new ArgumentOutOfRangeException(string.Format("'{0}'不是有效的序列号字符！", ch));
                }
            }
            return result;
        }

        public static bool IsSnNumber(char c)
        {
            return Characters.Contains(c);
        }

        /// <summary>
        /// 将8个字符的序列号转为16个10进制的数字。
        /// </summary>
        /// <param name="sn"></param>
        /// <returns></returns>
        public static string CovertToDecString(string sn)
        {
            string hex = string.Empty;
            foreach (char ch in sn.ToCharArray())
            {
                hex += ((byte)ch).ToString("D2");
            }
            return hex;
        }

        /// <summary>
        /// 将32个10进制字符的序列号转为8个SnNumber字符。
        /// </summary>
        /// <param name="sn"></param>
        /// <returns></returns>
        public static string CovertToSnNumberString(string sn)
        {
            if (string.IsNullOrWhiteSpace(sn) || sn.Length != 32)
            {
                throw new ArgumentException("sn必须是32个字符！");
            }

            try
            {
                string temp = sn.Substring(11, 4) + sn.Substring(18, 6) + sn.Substring(26, 6);
                string real = string.Empty;
                char ch;
                for (int i = 0; i < temp.Length; i += 2)
                {
                    ch = (char)(Convert.ToInt32(temp.Substring(i, 2), 10));
                    if (IsSnNumber(ch))
                    {
                        real += ch;
                    }
                    else
                    {
                        return null;
                    }
                }
                if (real.Length == 8)
                {
                    return real;
                }
                else
                {
                    return null;
                }
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 尝试将32个10进制字符的序列号转为8个SnNumber字符,如果失败则返回原值。
        /// </summary>
        /// <param name="sn"></param>
        /// <returns></returns>
        public static string TryCovertToSnNumberString(string sn)
        {
            if (string.IsNullOrWhiteSpace(sn) || sn.Length != 32)
            {
                return sn;
            }

            string real = CovertToSnNumberString(sn);
            if (!string.IsNullOrWhiteSpace(real))
            {
                return real;
            }
            else
            {
                return sn;
            }
        }

    }
}
