### 电话网关相关数据与操作说明

#### 相关的数据管理

- 设备管理
  - 新增电话设备类型，电话网关专用
  - 添加电话设备类型数据时，可以选择使用一个黑白名单规则(详见电话黑白名单)
  - ![电话设备类型](./gateway-device-type.png)
- 控制器管理
  - 新增电话网关类型，需要设置电话设备和电话号码信息
  - 在控制器类型右侧的按钮，需要类型为电话网关时才能点击
  - 在进入设置电话信息窗口后，点击编辑按钮后设置电话设置和电话号码，保存后关闭窗口
  - 添加电话网关控制器时需要控制器添加成功后才会添加设置好的电话信息
  - ![电话网关设置相关电话信息](./controller-set-gateway.png)

- 短号映射
  - 上级为添加数据的登录用户的上级单位
  - 一个短号只能有一个使用单位或设备(二选一)
  - ![一个短号只能有一个使用单位或设备](./short-number-add.png)
  - 一个单位或设备可以同时有多个短号
  - ![可以有多个短号](./short-number-data.png)
  - 短号的所属单位为使用的单位或设备的上级单位
  - 每个用户都能看到系统内的所有短号，但只能编辑拥有权限的短号(没有权限的短号为斜休、黄色字体，如下图)
  - ![没有权限的数据](./short-number-no-perm.png)

- 电话黑白名单
  - 上级为添加数据的登录用户的上级单位
  - 黑白名单分为拨入/拨出名单
  - 黑白名单设置后，需要明确选中启用选择框才会生效
  - 如果名单中没有数据，会自动取消且禁用启用功能
  - 黑白名单需要在设备管理中与电话设备关联
  - 黑白名单的号码互相排斥，只能在黑名单或白名单中，不能同时存在于黑/白名单
  - 电话号码的输入，可以将鼠标移到左侧的问号图标，会显示相关规则说明(规则如下)
  - ![电话号码规则说明](./telephone-number-info.png)

- 电话设备授权
  - 上级为添加数据的登录用户的上级单位
  - 单位/设备要使用某个电话设备，需要授权
  - 单位/设备默认拥有本单位的电话设备使用权限(不包含下级单位的电话设备)
  - 一个电话设备可以同时被多个单位/设备授权使用

#### 相关操作

- 电话拨入
  - 使用电话拨打网关电话号码，按相关语音提示操作
  - 二次拨号，输入短号按#号转接
- 手台拨出
  - 直接拨打电话号码
  - 手台电话簿功能暂无
- 详细的操作流程待下版本文档
