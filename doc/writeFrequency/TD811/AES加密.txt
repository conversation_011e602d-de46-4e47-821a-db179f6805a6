static int32 _bf_aes_padding_encode(uint8 * ptData,uint32 *pulDataLen)
{
	uint8 ucTemp = (uint8)(BF_AES_KEY_BYTE_LEN - (*pulDataLen%BF_AES_KEY_BYTE_LEN));
	uint32 i = 0;
	
	for(i=0; i<ucTemp; i++)	
		ptData[*pulDataLen+i] = ucTemp;

	*pulDataLen = *pulDataLen+ucTemp;
		
	return SUCC;
}

static int32 _bf_aes_padding_decode(uint8 * ptData,uint32 *pulDataLen)
{
	uint32 i = 0;
	uint8 ucTemp = ptData[(*pulDataLen) - 1];

	if((ucTemp > BF_AES_KEY_BYTE_LEN)||(0 == ucTemp))
		return FAIL;

	for(i=0; i<ucTemp; i++)
	{
		if(ucTemp != ptData[(*pulDataLen)-1-i])
			return FAIL;
	}

	*pulDataLen = *pulDataLen - ucTemp;
	
	return SUCC;
}