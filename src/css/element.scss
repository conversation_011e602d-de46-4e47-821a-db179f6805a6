.el-menu {
  --el-menu-item-height: 40px;
  --el-menu-base-level-padding: 12px;
  --el-menu-icon-width: 16px;

  &.el-menu--horizontal {
    --el-menu-horizontal-height: 40px;
    --el-menu-border-color: transparent;
    --el-menu-bg-color: transparent;
    --el-menu-hover-bg-color: transparent;
    --el-menu-text-color: #fff;
    --el-menu-hover-text-color: #fff;
    --el-menu-active-color: #fff;
  }

  &.el-menu--vertical {
    --el-menu-bg-color: #545c64;
    --el-menu-text-color: #fff;
    --el-menu-hover-bg-color: rgb(67, 74, 80);
    --el-menu-item-height: 56px;
  }

  &.el-menu--vertical .is-active {
    color: #ffd04b;
  }
}

// 子级menu菜单
.header-top-submenu.el-menu--horizontal .el-menu {
  --el-menu-active-color: #20a0ff;
  --el-menu-hover-text-color: #303133;
  --el-menu-hover-bg-color: #ccc;
}

// 对话框header和footer拥有边框线，需要去掉padding
.el-overlay-dialog .el-dialog {
  padding: 0;

  & > * {
    padding: 12px;
  }

  .el-dialog__header {
    border-bottom: 1px solid var(--el-border-color);
  }
}

// 警告消息框等按钮样式
.el-overlay-message-box .el-message-box {
  .el-message-box__btns {
    justify-content: center;

    .el-button {
      width: 36%;
    }
  }
}

// 使用拖拽自定义指令的弹窗的部分样式
.drag-dialog-modal {
  inset: unset !important;

  .el-overlay-dialog {
    height: 0;
    overflow: visible;
  }
}

.el-dialog.drag-dialog {
  .el-dialog__header {
    padding: 10px;
    text-align: left;
    font-size: 18px;
  }
  .el-dialog__header:hover {
    cursor: move !important;
  }
}

// 表单组件下面的input-number需要宽度100%, 仅限于form-item下第一个就为input-number的，还存在多个input-number组合在一个form-item里面的
.el-form .el-form-item .el-form-item__content > .el-input-number:first-child {
  width: 100%;
}
