.writer-frequency-wrap {
  .roam-form,
  .scan-form {
    .el-row.el-row--flex {
      height: 100%;
      align-content: flex-start;

      .transfer-wrapper {
        margin-bottom: 8px;
        height: 80%;
        overflow: auto;
      }
    }
  }

  .transfer-wrapper {
    .el-transfer {
      width: 100%;
      height: 100%;
      display: flex;

      .el-transfer-panel {
        flex: auto;
        width: auto;

        .el-transfer-panel__body {
          height: calc(100% - 40px);
          //max-height: 246px;

          .el-transfer-panel__list {
            height: 100%;

            .el-transfer-panel__item {
              $height: 24px;

              display: block;
              height: $height;
              line-height: $height;
              padding: 0 10px;
              margin-right: 0;

              .el-checkbox__label {
                line-height: $height;
              }
            }
          }
        }
      }

      .el-transfer__buttons {
        flex: none;
        align-self: center;
        padding: 0 15px;
        margin: 0;

        .el-button:first-child {
          margin-bottom: 10px;
        }
      }
    }

    &:not(.zh-cn) {
      .el-transfer .el-transfer-panel {
        min-width: 230px;
      }
    }
  }
}
