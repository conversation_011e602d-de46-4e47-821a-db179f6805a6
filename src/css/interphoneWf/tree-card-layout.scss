.table-no-bg.el-table {
  .el-table__body tr:hover > td {
    background-color: transparent;
  }
}

.tone-volume-form-item {
  /*.el-form-item__label{*/
  /*  min-width:200px ;*/
  /*}*/
  .el-form-item__content {
    display: flex;

    .el-checkbox {
      margin-right: 10px;
    }
  }
}

.TD910-layout {
  flex: auto;
  display: flex;
  height: 50vh;

  .TD910-menu-tree {
    flex: none;
    width: 200px;
    border: 1px solid #ebeef5;
    border-radius: 4px 0 0 4px;

    .fancytree-active .fancytree-node .fancytree-title {
      border-color: #719acb !important;
      background: linear-gradient(
        to bottom,
        #faf9fd 0,
        #c4e8fa 100%
      ) !important;
    }
  }

  .TD910-content {
    max-width: calc(100% - 202px);
    min-height: 200px;
    flex: auto;

    .write-freq-component {
      height: 100%;
      border-radius: 0 4px 4px 0;

      .el-row.el-row--flex {
        flex-wrap: wrap;
      }

      &.patrol-system-container {
        border: 1px solid #dcdfe6;
        position: unset;

        .el-tabs__content {
          padding: 10px;
          overflow: auto;
          height: calc(100% - 29px);
          box-sizing: border-box;
        }
      }

      .el-card__body {
        padding: 10px;
        overflow: auto;
      }

      .general-settings-form {
        width: 100%;
        height: auto;
        overflow: hidden;
      }

      &.buttonDefine-container {
        .el-table {
          .cell {
            padding: 0;

            .el-form-item {
              margin: 0;
            }
          }
        }
      }

      &.encrypt-settings-container {
        .el-table--enable-row-hover
          .el-table__body
          tr:not(.encrypt-enable-row):hover
          > td {
          background-color: transparent;
        }

        .el-table .cell {
          padding: 0;
        }

        .el-card__body {
          display: flex;
          flex-direction: column;

          .encrypt-list-form {
            overflow: auto;
          }
        }
      }

      .channel-setting-form {
        .scramble-form-item .el-form-item__content {
          display: flex;

          .el-checkbox {
            flex: none;
            margin-right: 10px;
          }

          .el-input-number {
            flex: auto;
            width: auto;
          }
        }
      }
    }
  }
}

.el-dialog.is-fullscreen {
  .TD910-layout {
    height: calc(100% - 83px);

    ul.fancytree-container {
      max-height: unset;
    }
  }
}
