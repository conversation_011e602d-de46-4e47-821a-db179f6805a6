@use '@/css/interphoneWf/scan-roam-group.scss' as *;
@use '@/css/interphoneWf/button-define.scss' as *;
@use '@/css/interphoneWf/list-config.scss' as *;

.writer-frequency-wrap {
  & > * {
    width: 100%;
  }

  .deviceInfo-container .el-card__body {
    display: flex;
    justify-content: center;
    height: auto;
  }

  .form-width-xs,
  .form-item-width-xs {
    width: 384px;
  }

  .one-column-box {
    .el-form {
      @extend .form-width-xs;
    }
  }

  .full-width-form.el-form {
    width: 100%;
  }

  .has-tabs-child {
    padding: calc(var(--spacing) * 3);
  }

  .settings-box {
    height: 100%;

    & > .el-form {
      overflow: auto;
      padding: calc(var(--spacing) * 3);
    }

    &.deviceInfo-box {
      display: flex;
      justify-content: center;
      height: auto;
    }

    &.patrol-system-box {
      padding: calc(var(--spacing) * 3);

      .el-form:not(.patrol-system-activeRfid-form) {
        @extend .form-width-xs;
      }

      .el-tabs.patrol-system-tabs {
        height: 100%;
        border-radius: var(--radius-sm);

        & > .el-tabs__content > .el-tab-pane {
          display: flex;
          justify-content: center;
          height: auto;
        }
      }
    }

    &.channel-settings-box {
      padding: calc(var(--spacing) * 3);

      .el-tabs.channel-list-tabs {
        height: 100%;
        border-radius: var(--radius-sm);

        & > .el-tabs__content > .el-tab-pane {
          display: flex;
          justify-content: center;

          &.channel-tab-pane {
            height: 100%;
          }
        }
      }
    }

    &.encrypt-settings-box {
      padding: calc(var(--spacing) * 3);
    }

    &.menu-settings-box {
      .el-form {
        .el-form-item {
          margin-bottom: unset;
        }

        .el-row.has-input-row {
          .el-form-item {
            margin-bottom: 4px;
          }
        }
      }
    }
  }

  .new-device-config-dialog {
    height: 60%;

    .el-dialog__body {
      align-items: center;
      height: calc(100% - 45px);
    }
  }

  #area-channel-table.el-table--striped {
    .el-table__body tr.el-table__row--striped td {
      background-color: rgba(0, 0, 0, 0.05);
    }

    .el-table__body tr:hover > td {
      background-color: #b3e5fc;
    }

    .el-table__body tr.current-row > td {
      background: #03a9f4;
      color: #fff;
    }
  }

  .digit-alarm-list {
    font-size: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 8px;

    .roaming-group-item,
    .scanning-group-item,
    .digit-alarm-item,
    .list-item {
      @extend .label-list-item;

      .list-item-name {
        @extend .label-list-item-name;
      }

      .el-button {
        @extend .label-list-item-close;
      }

      &:hover {
        color: #66b1ff !important;

        .el-button {
          visibility: visible;
        }
      }

      &.selected {
        @extend .label-list-item-name__selected;
      }
    }
  }

  .scanning-group,
  .roaming-group {
    .config-container {
      flex: auto;

      .el-form {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-items: start;
      }

      .el-form-item:not(.transfer-wrapper) {
        @extend .form-item-width-xs;
      }

      .el-form-item.transfer-wrapper {
        flex: none;
        width: 100%;

        .el-transfer {
          min-height: 320px;
          max-height: 560px;

          .el-transfer__buttons {
            display: flex;
            flex-direction: column;
            gap: calc(var(--spacing) * 3);
            align-self: center;

            .el-button {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin: 0;
            }
          }
        }
      }
    }
  }

  .new-device-config-dialog-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3000 !important;

    & ~ .v-modal {
      z-index: 2999 !important;
    }
  }

  .synchronised-time-button {
    margin-left: 6px;
  }

  .el-row.el-row--flex {
    flex-wrap: wrap;
  }

  .no-details {
    .el-table__expand-column {
      .el-table__expand-icon {
        visibility: hidden;
      }
    }
  }

  .buttonDefine-form {
    .el-table .el-form-item {
      margin-bottom: 0;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: transparent;
    }
  }

  // 站点信息配置样式
  .settings-box.site-info-container,
  // 扫描组
  .scanning-group,
  // 漫游组
  .roaming-group,
  // 通讯录群组
  .address-book-group,
  // 数字报警
  .digit-alarm {
    display: flex;
    gap: 1em;

    .label-list {
      @extend .label-list-container;
      display: flex;
      flex-direction: column;
      align-items: center;

      .list-item {
        @extend .label-list-item;
        width: 100%;

        .list-item-name {
          @extend .label-list-item-name;
          text-align: center;
        }

        .list-item-close {
          @extend .label-list-item-close;
        }

        &:hover {
          @extend .label-list-item-name__hover;
        }

        &.selected {
          @extend .label-list-item-name__selected;
        }

        &.disToggle:not(.selected) {
          @extend .label-list-item-name__disToggle;
        }
      }

      .new-list-item {
        @extend .label-new-list-item;
      }
    }

    .config-container {
      @extend .list-config-container;
    }
  }

  // 站点信息配置样式
  .settings-box.site-info-container {
    @extend .label-list-container-wrapper;
  }

  .recording-box {
    .record-box-btn {
      display: flex;
      justify-content: space-between;
    }

    .dataTables_wrapper {
      height: calc(100% - 32px);

      .dataTables_scroll .dataTables_scrollBody {
        min-height: 0vh;
      }
    }
  }

  .tb-checkBox {
    width: 10px;
    height: 10px;
    display: inline-block;
  }

  .el-form .el-form-item.select-input-form-item {
    .el-form-item__content {
      display: flex;
      flex-wrap: nowrap;
    }

    .el-select {
      width: 150px;
      flex: none;

      .el-select__wrapper {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }

    .el-input .el-input__wrapper {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  .el-card.write-freq-component .upside-down-container,
  .roam-config-container {
    display: flex;
    justify-content: center;

    & > .upside-down-form,
    .roam-config-form {
      width: 400px;
    }
  }

  &.write-freq-component {
    .encrypt-settings-container .encrypt-settings__header {
      flex: none;
      display: flex;
      justify-content: space-between;
      align-content: center;

      .encrypt-control {
        flex: auto;
      }
    }

    .table-no-bg.el-table {
      .el-table__body tr:hover > td {
        background-color: transparent;
      }
    }

    .TD910-layout {
      flex: auto;
      display: flex;
      height: 50vh;

      .program-password-container,
      .scan-config-container,
      .roam-config-container,
      .alone-work-container,
      .upside-down-container,
      .signaling-system-container,
      .digital-alert-container,
      .sos-settings-container,
      .patrol-system-container .el-tab-pane,
      .encrypt-settings-container .el-col,
      .gps-settings-container,
      .attachment-settings-container {
        display: flex;
        justify-content: center;

        .program-password-form,
        .upside-down-form,
        .alone-work-form,
        .roam-config-form,
        .scan-config-form,
        .signaling-system-form,
        .digit-alarm-config-form,
        .sos-settings-form,
        .patrol-system-emergency-form,
        .system-configure-form,
        .gpsSettings-form,
        .attachment-settings-form {
          width: 400px;
        }
      }

      .roam-container,
      .address-group-container,
      .scan-container {
        .transfer-wrapper .el-transfer {
          min-height: 250px;
          max-height: 560px;

          .el-transfer__buttons {
            display: flex;
            flex-direction: column;
            gap: calc(var(--spacing) * 3);
            align-self: center;

            .el-button {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin: 0;
            }
          }
        }

        .roma-form-item,
        .scan-group-form-item {
          width: 400px;
          margin-top: 20px;
        }
      }
    }

    .tone-volume-form-item {
      .el-form-item__content {
        display: flex;
        flex-wrap: nowrap;

        .el-checkbox {
          margin-right: 10px;
        }
      }
    }
  }

  .el-tab-pane.address-book,
  .phoneBook-box-container {
    height: 100%;
  }

  .el-tab-pane.encrypt-settings-box {
    .encrypt-list-tabs {
      display: flex;
      justify-content: center;
    }
  }

  // 写频录音页面的表格顶部按钮组样式
  .el-tab-pane.recording-box,
  .el-card.record-list-container {
    .record-box-btn {
      margin-bottom: 0.5rem;
    }
  }

  .el-card {
    .el-form {
      .scan-group-form-item,
      .roma-form-item {
        width: 100%;
      }

      .el-form-item .el-checkbox {
        height: unset;
      }

      .el-row {
        margin: 0 !important;
        .el-col {
          .el-transfer {
            margin-bottom: 10px;
          }
        }
      }
    }
  }
}
