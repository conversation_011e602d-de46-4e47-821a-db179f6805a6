<template>
  <section :class="['upgrade', fullscreenClass]">
    <div v-if="type === 1" id="browser_err">
      <header>
        <h2>该浏览器不支持本系统，请使用以下浏览器的最新版本!</h2>
        <h3>
          This browser does not support this system, please use the latest
          version of the following browser!
        </h3>
      </header>
      <div id="error">
        <a
          href="http://www.google.cn/intl/zh-CN/chrome/browser/desktop/index.html"
        >
          <img src="@/images/browser/Chrome.png" />
        </a>
        <a href="http://www.firefox.com.cn/">
          <img src="@/images/browser/Firefox.png" />
        </a>
        <a href="http://www.opera.com/zh-cn">
          <img src="@/images/browser/Opera.png" />
        </a>
        <a href="http://www.apple.com/cn/safari/">
          <img src="@/images/browser/Safari.png" />
        </a>
      </div>
    </div>
    <div v-else id="compute_err">
      <h2>
        本系统使用高新技术，该客户端电脑配置过低，请先升级电脑配置或使用其他更高配置的电脑客户端。
      </h2>
      <h3>
        The system uses high-tech, the client computer configuration is too low,
        please upgrade the computer configuration or use other higher
        configuration of the computer client.
      </h3>
    </div>
  </section>
</template>

<script>
import { upgradeType } from '@/utils/checkbrowser'

export default {
  name: 'BfUpgrade',
  data() {
    return {
      type: upgradeType(),
    }
  },
  computed: {
    fullscreen() {
      return this.$root.layoutLevel === 0
    },
    fullscreenClass() {
      return this.fullscreen ? 'fullscreen' : ''
    },
  },
}
</script>

<style scoped>
.upgrade {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: #fff;
}

#browser_err,
#compute_err {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50%;
}

#browser_err {
}

#browser_err header {
  font-size: 1.5em;
}

#error {
  display: flex;
  margin-top: 25px;
  align-items: center;
}

#error a {
  flex: auto;
}

#error img {
  width: 50%;
}

h2,
h3 {
  margin-bottom: 12px;
}

/********** 全屏下样式 ************/
.fullscreen.upgrade {
  padding: 20px;
}

.fullscreen #browser_err,
.fullscreen #compute_err {
  width: 80%;
}

.fullscreen #browser_err header h2,
.fullscreen #compute_err h2 {
  font-size: 24px;
}

.fullscreen #browser_err header h3,
.fullscreen #compute_err h3 {
  font-size: 20px;
}

.fullscreen #error img {
  width: 100%;
}
</style>
