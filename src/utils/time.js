import dayjs from 'dayjs'
import isSameOrAfterPlugin from 'dayjs/plugin/isSameOrAfter'
import isSameOrBeforePlugin from 'dayjs/plugin/isSameOrBefore'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)
dayjs.extend(isSameOrBeforePlugin)
dayjs.extend(isSameOrAfterPlugin)

export const DayMask = 'YYYY-MM-DD HH:mm:ss'
export const DateMask = 'YYYY-MM-DD'
export const TimeMask = 'HH:mm:ss'

/**
 * 将BCD码时间字符串转换为dayjs对象
 * @param {string} bcdCodeTime 比如20230302161130=0x20 0x23 0x03 0x02 0x16 0x11 0x30
 * @returns {dayjs.Dayjs}
 */
export function bcdCodeTimeToDayjs(bcdCodeTime) {
  const year = bcdCodeTime.slice(0, 4)
  const month = bcdCodeTime.slice(4, 6)
  const day = bcdCodeTime.slice(6, 8)
  const hour = bcdCodeTime.slice(8, 10)
  const minute = bcdCodeTime.slice(10, 12)
  const second = bcdCodeTime.slice(12, 14)
  return dayjs(`${year}-${month}-${day} ${hour}:${minute}:${second}`)
}

/**
 * 格式化时方法
 * @param {dayjs.ConfigType} t 时间对象
 * @param {string} mask 格式字符串
 */
export function formatTime(t, mask = DayMask) {
  return dayjs(t).format(mask)
}

/**
 * 检查时间是否为正确时间
 * @param {any} t 被检查的时间参数，可以为任意类型
 * @returns {boolean}
 */
export function dateTimeIsValid(t) {
  return dayjs(t).isValid()
}

export function subtractTime(t, v, unit = 'minute') {
  return dayjs(t).subtract(v, unit)
}

export function addTime(t, v, unit = 'minute') {
  return dayjs(t).add(v, unit)
}

// arg1 is same or before arg2
export function isSameOrBefore(aTime, bTime) {
  return dayjs(aTime).isSameOrBefore(dayjs(bTime))
}

export function timeIsBefore(aTime, bTime) {
  return dayjs(aTime).isBefore(dayjs(bTime))
}

export function isSameOrAfter(aTime, bTime) {
  return dayjs(aTime).isSameOrAfter(dayjs(bTime))
}

export function timeIsAfter(aTime, bTime) {
  return dayjs(aTime).isAfter(dayjs(bTime))
}

export function comparisonDayTime(time1, time2) {
  return dayjs(new Date(time1)).isBefore(dayjs(new Date(time2)))
}

const nowLocalTime = (mask = DayMask) => {
  return dayjs().format(mask)
}
const getLocalTimeString = (time, mask = DayMask) => {
  return dayjs(new Date(time)).format(mask)
}
const nowUtcTime = (mask = DayMask) => {
  return dayjs().utc().format(mask)
}
const getUtcTimeString = (time, mask = DayMask) => {
  return dayjs.utc(time).format(mask)
}
const localToUtcTime = (time, mask = DayMask) => {
  return dayjs(time).utc().format(mask)
}

/**
 * 将UTC时间转换为本地时间
 * @param {dayjs.ConfigType} time
 * @param {string} mask
 * @return {string}
 */
export function utcToLocalTime(time, mask = DayMask) {
  return dayjs.utc(time).local().format(mask)
}

// 获取当前时间前的某一天的当前时间，如一天前,duration ,默认30分钟前
const get_current_time_before_one_day_time = (duration = 30, isUtc = false) => {
  const timeNum = isUtc ? new Date(nowUtcTime()).getTime() : Date.now()
  return timeNum - duration * 60 * 1000
}
// 将UTC时间转为本地时间,返回时间毫秒数
const utcTimeToLocalTime = GMT_time => {
  if (GMT_time === '' || typeof GMT_time === 'undefined') {
    return new Date().getTime()
  }
  const shicha = new Date().getTimezoneOffset() * 60 * 1000
  const time = Date.parse(new Date(GMT_time))
  return new Date(time - shicha)
}

export function utcToLocalTimeFormat(time, mask = DayMask) {
  return dayjs.utc(time).local().format(mask)
}

// 比较两个时间的前后,后一个时间大则为 true
const comparison_two_time = (time1, time2) => {
  if (typeof time2 === 'undefined') {
    return false
  } else if (typeof time1 === 'undefined') {
    return true
  }
  time1 = new Date(time1).getTime()
  time2 = new Date(time2).getTime()
  if (time1 < time2) {
    return true
  } else {
    return false
  }
}
// 获取指定时间的一年后时间
const getYearAfterTheSpecifiedTime = date => {
  var strYear = date.getFullYear() + 1
  var strDay = date.getDate()
  var strMonth = date.getMonth() + 1
  if (strMonth < 10) {
    strMonth = '0' + strMonth
  }
  if (strDay < 10) {
    strDay = '0' + strDay
  }
  var datastr = strYear + '-' + strMonth + '-' + strDay
  return new Date(datastr)
}
const getDayBeforeTheSpecifiedTime = (date, hour = 24) => {
  const _date = new Date(date).getTime()
  const _diff = parseFloat(hour) * 60 * 60 * 1000
  return new Date(_date - _diff)
}

export default {
  nowLocalTime,
  getLocalTimeString,
  nowUtcTime,
  getUtcTimeString,
  localToUtcTime,
  get_current_time_before_one_day_time,
  utcTimeToLocalTime,
  comparison_two_time,
  getYearAfterTheSpecifiedTime,
  getDayBeforeTheSpecifiedTime,
}
export { nowLocalTime }
export { getLocalTimeString }
export { nowUtcTime }
export { getUtcTimeString }
export { localToUtcTime }
export { get_current_time_before_one_day_time }
export { utcTimeToLocalTime }
export { comparison_two_time }
export { getYearAfterTheSpecifiedTime }
export { getDayBeforeTheSpecifiedTime }
