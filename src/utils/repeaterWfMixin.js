import { SupportedLang } from '@/modules/i18n'

export default {
  computed: {
    isMobile() {
      return this.$root.layoutLevel === 0
    },
    fullscreen() {
      return this.isMobile
    },
    locale() {
      return this.$i18n.locale
    },
    isCN() {
      return this.locale === SupportedLang.zhCN
    },
    isEN() {
      return this.locale === SupportedLang.enUS
    },
    isEn() {
      return this.isEN
    },
    isFR() {
      return this.locale === SupportedLang.fr
    },
  },
}
