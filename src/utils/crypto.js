import md5 from 'crypto-js/md5'
import base64 from 'crypto-js/enc-base64'
import SHA256 from 'crypto-js/sha256'
import hex from 'crypto-js/enc-hex'

/**
 * Mesh网关控制器密码加密方法
 * @param {string} dmrId 8位大写16进制字符串
 * @param {string} password 明文密码
 * @returns {string} 返回加密后的密码
 */
export function encodeMeshGatewayDevicePassword(dmrId, password) {
  // password=base64(md5(hex(device_dmrid)+device_password))
  return md5(dmrId + password).toString(base64)
}

/**
 * @param {string} str
 * @returns {string} 编码后的base64字符串
 */
export function sha256(str = '') {
  return SHA256(str).toString(base64)
}

/**
 * 转换hex为字节数组
 * @param {string} hexStr 16进制字符串
 * @returns {Array<number>}
 */
export function hexToBytes(hexStr) {
  const bytes = []
  for (let i = 0; i < hexStr.length; i += 2) {
    bytes.push(parseInt(hexStr.slice(i, i + 2), 16))
  }
  return bytes
}

/**
 * 转换hex为Uint8Array
 * @param {string} hexStr 16进制字符串
 * @returns {Uint8Array}
 */
export function hexToUint8Array(hexStr) {
  return Uint8Array.from(hexToBytes(hexStr))
}

/**
 * 对字符串进行md5编码，返回字节数组
 * @param {string} str 需要进行md5编码的字符串
 * @returns {Array<number>}
 */
export function md5ToBytes(str) {
  const hexStr = md5(str).toString(hex)
  return hexToBytes(hexStr)
}

export default {
  sha256,
}
