// src/utils/setRem.ts
const setRem = () => {
  const designWidth = 1920 // 设计基准1920 默认在1920*1080分辨率下设计
  const baseFontSize = 16 // 1rem = 16px
  const clientWidth = document.documentElement.clientWidth
  const html = document.documentElement
  const scale = clientWidth / designWidth
  html.style.fontSize = baseFontSize * scale + 'px'
}

window.addEventListener('resize', setRem)
window.addEventListener('DOMContentLoaded', setRem)

setRem()
