import bfutil from './bfutil'
import bfNotify from './notify'
import { SupportedLang } from '@/modules/i18n'

export default {
  data() {
    return {
      compName: '',
      bfmaxi: false,
      visible: false,
      tabsValue: 'data',
      selOrgList: bfglob.gorgData.getList(),
    }
  },
  methods: {
    openDlgFn() {
      // 发布关闭最小化导航区对应按钮事件
      this.compName && bfglob.emit('closeMiniNavBtn', 'open_' + this.compName)
      if (this.tabsValue === 'data') {
        bfutil.columnsAdjust(this)
      }
    },
    closeDlgFn() {
      bfutil.closeDialogCallback(this)
    },
    openComponentByName() {
      this.visible = true
      this.openDlgFn()
    },
    showDbOperateErrorMsg(msg, type, detailMessage) {
      if (detailMessage) {
        bfNotify.messageBox(msg, type, {
          isVnode: true,
          detailMessage: detailMessage,
          message: msg,
          type,
        })
      } else {
        bfNotify.messageBox(msg, type)
      }
    },
    showAddSuccessMsg() {
      bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
    },
    showAddErrorMsg(detailMessage) {
      const msg = this.$t('msgbox.addError')
      const type = 'error'
      this.showDbOperateErrorMsg(msg, type, detailMessage)
    },
    showUpdateSuccessMsg() {
      bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
    },
    showUpdateErrorMsg(detailMessage) {
      const msg = this.$t('msgbox.upError')
      const type = 'error'
      this.showDbOperateErrorMsg(msg, type, detailMessage)
    },
    showDeleteSuccessMsg() {
      bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
    },
    showDeleteErrorMsg(detailMessage) {
      const msg = this.$t('msgbox.delError')
      const type = 'error'
      this.showDbOperateErrorMsg(msg, type, detailMessage)
    },
  },
  computed: {
    orgTreeSelectOptions() {
      // Define types for tree node
      const root = {
        label: this.$t('dialog.default'),
        value: bfutil.DefOrgRid,
        disabled: false,
        children: [],
      }

      // Early return if no data
      if (!this.selOrgList) {
        return [root]
      }

      // Create a map for faster lookups
      const nodeMap = new Map()
      // nodeMap.set(bfutil.DefOrgRid, root)

      // First pass: create all nodes
      const allNodes = Object.values(this.selOrgList).map(item => ({
        ...item,
        value: item.rid,
        disabled: false,
        children: [],
      }))

      // Build node map for quick access
      allNodes.forEach(node => {
        nodeMap.set(node.value, node)
      })

      const options = [root]
      // Second pass: build tree structure
      allNodes.forEach(node => {
        const org = bfglob.gorgData.get(node.value)
        const parentId = org?.parentOrgId
        // const parentId = org?.parentOrgId || bfutil.DefOrgRid
        const parentNode = nodeMap.get(parentId)

        if (parentNode) {
          parentNode.children.push(node)
        } else {
          // If parent not found, attach to root
          // root.children.push(node)
          options.push(node)
        }
      })

      // 添加没有权限的组织节点
      const noPermOrgData = bfglob.noPermOrgData.getAll()
      const noPermNodes = Object.values(noPermOrgData).map(item => ({
        label: item.orgShortName,
        value: item.rid,
        disabled: true,
        children: [],
        noPerm: true,
      }))
      options.push(...noPermNodes)

      return options
    },
    isMobile() {
      return this.$root.layoutLevel === 0
    },
    fullscreen() {
      return this.bfmaxi ? true : this.isMobile
    },
    locale() {
      return this.$i18n.locale
    },
    isCN() {
      return this.locale === SupportedLang.zhCN
    },
    isEN() {
      return this.locale === SupportedLang.enUS
    },
    isEn() {
      return this.isEN
    },
    isFR() {
      return this.locale === SupportedLang.fr
    },
  },
  watch: {
    fullscreen: {
      handler(newVal) {
        this.dataTable &&
          this.dataTable.name &&
          bfglob.emit(this.dataTable.name, newVal)
      },
    },
  },
  mounted() {
    this.visible = true

    if (this.compName) {
      bfglob.on('open_' + this.compName, this.openComponentByName)
    }
  },
  beforeMount() {
    if (this.compName) {
      bfglob.off('open_' + this.compName, this.openComponentByName)
    }
  },
}
