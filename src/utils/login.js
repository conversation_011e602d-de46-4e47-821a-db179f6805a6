import bfproto from '@/modules/protocol'
import bfCrypto from '@/utils/crypto'
import { getLocalTimeString, nowUtcTime } from '@/utils/time'
import NATS from 'websocket-nats'
import { ProxyServer } from '@/envConfig'

function emitServerStatus(status) {
  bfglob.emit('serverStatus', status)
}

// 读取服务器配置相关参数逻辑，nats连接服务器后请求
// nats断开重连后不请求，需要手动刷新后请求配置(配置对象非响应式数据)
let isFetchSysIniConfig = false

function fetchSysIniConfig() {
  if (isFetchSysIniConfig) {
    return
  }
  isFetchSysIniConfig = true

  setTimeout(() => {
    fetch(`/sys_ini_config?${Date.now()}`)
      .then(res => res.json())
      .then(config => {
        Object.assign(bfglob.sysIniConfig, config)
      })
  }, 0)
}

function onConnectedToServer() {
  bfglob.emit('server.connect')
  emitServerStatus(true)
  fetchSysIniConfig()
}

function onReconnectedToServer() {
  bfglob.emit('server.reconnect')
  emitServerStatus(true)
}

function onDisconnectFromServer() {
  bfglob.emit('server.disconnect')
  emitServerStatus(false)
}

function onServerConnectionError(err) {
  bfglob.emit('server.error', err)
  emitServerStatus(false)
}

function login(msgData) {
  return new Promise((resolve, reject) => {
    const opts = {
      decodeMsgType: 'client_login_response',
    }

    bfproto
      .sendMessage(
        11,
        msgData,
        'client_login_request',
        `login.${bfglob.sysId}`,
        opts,
      )
      .then(rpc_cmd_obj => {
        resolve(rpc_cmd_obj.body)
      })
      .catch(e => {
        reject(e)
      })
  })
}

// 发post请求，body为session id，请求不成功，重新发一次
let IpFetchCount = 0

function fetchClientIp(sid) {
  IpFetchCount++
  fetch('/myip', {
    method: 'POST',
    body: sid,
  }).catch(err => {
    bfglob.console.error('fetchClientIp err:', err)
    if (IpFetchCount > 3) {
      return
    }
    fetchClientIp(sid)
  })
}

function calcServerTimes(serverTimeOffset) {
  clearInterval(bfglob.timeInterval)
  bfglob.timeInterval = setInterval(function () {
    const date = new Date(new Date().getTime() + serverTimeOffset)
    bfglob.emit('serverTime', getLocalTimeString(date))
  }, 1000)
}

// 获取服务器时间
function getServerTimes() {
  const dbSubject = `db.${bfglob.sysId}`
  bfproto
    .sendMessage(1, null, null, dbSubject, null)
    .then(rpc_cmd_obj => {
      bfglob.console.log('getServerTimes res:', rpc_cmd_obj)
      if (rpc_cmd_obj.opt) {
        const serverTimes = new Date(rpc_cmd_obj.opt).getTime()
        const localTimes = new Date().getTime()
        const timeOffset = new Date().getTimezoneOffset() * 60 * 1000
        const serverTimeOffset = serverTimes - timeOffset - localTimes
        bfglob.serverTimeOffset = serverTimeOffset
        bfglob.serverTime = rpc_cmd_obj.opt
        calcServerTimes(serverTimeOffset)
      }
    })
    .catch(err => {
      bfglob.console.warn('获取服务器时间超时', err)
    })
}

let syncServerTimer = null

// 每隔半小时重新获取一次服务器的时间
export function syncServerTime() {
  // 清除之前的定时器
  if (syncServerTimer) {
    clearInterval(syncServerTimer)
    syncServerTimer = null
  }

  // 同步服务器时间
  getServerTimes()

  // 启动定时同步任务
  syncServerTimer = setInterval(
    () => {
      getServerTimes()
    },
    30 * 60 * 1000,
  )
}

export default {
  connectServer() {
    const hostname = ProxyServer.hostname
    const port = ProxyServer.webPort
    const nats_urls = [`ws://${hostname}:${port}/nats`]
    bfglob.console.log('loadWebSocketPort', nats_urls)

    bfglob.server = NATS.connect({ servers: nats_urls })
    bfglob.server.on('connect', onConnectedToServer)
    bfglob.server.on('reconnect', onReconnectedToServer)
    bfglob.server.on('error', err => {
      onServerConnectionError(err)
      setTimeout(this.connectServer, 3000)
    })
    bfglob.server.on('disconnect', onDisconnectFromServer)
  },
  loginByPwd(data) {
    const nowUtc = nowUtcTime()
    const passHash = bfCrypto.sha256(
      nowUtc + bfCrypto.sha256(data.userName + data.password),
    )

    const msgData = Object.assign({}, data, {
      timeStr: nowUtc,
      passHash: passHash,
      sid: bfglob.sessionId,
      loginMethod: 0,
      loginWay: 1,
    })

    return login(msgData)
  },
  loginBySid(data) {
    const nowUtc = nowUtcTime()
    const msgData = Object.assign({}, data, {
      timeStr: nowUtc,
      passHash: '',
      sid: bfglob.sessionId,
      loginMethod: 1,
      loginWay: 1,
    })

    return login(msgData)
  },
  login(data) {
    if (bfglob.sessionId.length > 10) {
      // login by session id
      return this.loginBySid(data)
    } else {
      // login by pw
      return this.loginByPwd(data)
    }
  },
  saveLoginResponse(response) {
    bfglob.sessionId = response.sid
    bfglob.userInfo.rid = response.dbUser.rid
    bfglob.userInfo.orgId = response.dbUser.orgId
    bfglob.isLogin = true
  },
  fetchClientIp,
}
