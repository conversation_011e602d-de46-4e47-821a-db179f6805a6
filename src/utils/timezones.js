const timezones = [
  {
    value: -12,
    text: '(GMT -12:00) Eniwetok, Kwajalein',
    ch: '(GMT -12:00)埃尼威托克,夸贾林岛',
  },
  {
    value: -11,
    text: '(GMT -11:00) Midway Island, Samoa',
    ch: '(GMT -11:00)中途岛,萨摩亚群岛',
  },
  {
    value: -10,
    text: '(GMT -10:00) Hawaii',
    ch: '(GMT -10:00)夏威夷',
  },
  { value: -9, text: '(GMT -9:00) Alaska', ch: '(GMT -9:00)阿拉斯加' },
  {
    value: -8,
    text: '(GMT -8:00) Pacific Time (US & Canada)',
    ch: '(GMT -8:00) Pacific Time (US & Canada)太平洋时间（美国和加拿大）',
  },
  {
    value: -7,
    text: '(GMT -7:00) Mountain Time (US & Canada)',
    ch: '(GMT -7:00) Mountain Time (US & Canada)山地时间（美国和加拿大）',
  },
  {
    value: -6,
    text: '(GMT -6:00) Central Time (US & Canada), Mexico City',
    ch: '(GMT -6:00) Central Time (US & Canada)中部时间（美国和加拿大） ,墨西哥城',
  },
  {
    value: -5,
    text: '(GMT -5:00) Eastern Time (US & Canada), Bogota, Lima',
    ch: '(GMT -5:00) Eastern Time (US & Canada)东部时间（美国和加拿大）,波哥大,利马',
  },
  {
    value: -4,
    text: '(GMT -4:00) Atlantic Time (Canada), Caracas, La Paz',
    ch: '(GMT -4:00) Atlantic Time (Canada)大西洋时间（加拿大）,加拉加斯,拉巴斯',
  },
  { value: -3.5, text: '(GMT -3:30) Newfoundland', ch: '(GMT -3:30)纽芬兰' },
  {
    value: -3,
    text: '(GMT -3:00) Brazil, Buenos Aires, Georgetown',
    ch: '(GMT -3:00)巴西,布宜诺斯艾利斯,乔治城',
  },
  { value: -2, text: '(GMT -2:00) Mid-Atlantic', ch: '(GMT -2:00)大西洋中部' },
  {
    value: -1,
    text: '(GMT -1:00) Azores, Cape Verde Islands',
    ch: '(GMT -1:00)亚速尔群岛,佛得角群岛',
  },
  {
    value: 0,
    text: '(GMT) Western Europe Time, London, Lisbon, Casablanca',
    ch: '(GMT)西欧时间,伦敦,里斯本,卡萨布兰卡',
  },
  {
    value: 1,
    text: '(GMT +1:00) Brussels, Copenhagen, Madrid, Paris',
    ch: '(GMT +1:00)布鲁塞尔,哥本哈根,马德里,巴黎',
  },
  {
    value: 2,
    text: '(GMT +2:00) Kaliningrad, South Africa',
    ch: '(GMT +2:00)加里宁格勒,南非',
  },
  {
    value: 3,
    text: '(GMT +3:00) Baghdad, Riyadh, Moscow, St. Petersburg',
    ch: '(GMT +3:00)巴格达,利雅得,莫斯科,圣彼得堡',
  },
  { value: 3.5, text: '(GMT +3:30) Tehran', ch: '(GMT +3:30)德黑兰' },
  {
    value: 4,
    text: '(GMT +4:00) Abu Dhabi, Muscat, Baku, Tbilisi',
    ch: '(GMT +4:00)阿布扎比,马斯喀特,巴库,第比利斯',
  },
  { value: 4.5, text: '(GMT +4:30) Kabul', ch: '(GMT +4:30)喀布尔' },
  {
    value: 5,
    text: '(GMT +5:00) Ekaterinburg, Islamabad, Karachi, Tashkent',
    ch: '(GMT +5:00)叶卡捷琳堡,伊斯兰堡,卡拉奇,塔什干',
  },
  {
    value: 5.5,
    text: '(GMT +5:30) Bombay, Calcutta, Madras, New Delhi',
    ch: '(GMT +5:30)孟买,加尔各答,马德拉斯,新德里',
  },
  { value: 5.75, text: '(GMT +5:45) Kathmandu', ch: '(GMT +5:45)加德满都' },
  {
    value: 6,
    text: '(GMT +6:00) Almaty, Dhaka, Colombo',
    ch: '(GMT +6:00)阿拉木图,达卡,科伦坡',
  },
  {
    value: 7,
    text: '(GMT +7:00) Bangkok, Hanoi, Jakarta',
    ch: '(GMT +7:00)曼谷,河内,雅加达',
  },
  {
    value: 8,
    text: '(GMT +8:00) Beijing, Perth, Singapore, Hong Kong',
    ch: '(GMT +8:00)北京,珀斯,新加坡,香港',
  },
  {
    value: 9,
    text: '(GMT +9:00) Tokyo, Seoul, Osaka, Sapporo, Yakutsk',
    ch: '(GMT +9:00)东京,首尔,大阪,札幌,雅库茨克',
  },
  {
    value: 9.5,
    text: '(GMT +9:30) Adelaide, Darwin',
    ch: '(GMT +9:30)阿德莱德,达尔文',
  },
  {
    value: 10,
    text: '(GMT +10:00) Eastern Australia, Guam, Vladivostok',
    ch: '(GMT +10:00)东澳大利亚,关岛,符拉迪沃斯托克',
  },
  {
    value: 11,
    text: '(GMT +11:00) Magadan, Solomon Islands, New Caledonia',
    ch: '(GMT +11:00)马加丹,所罗门群岛,新喀里多尼亚',
  },
  {
    value: 12,
    text: '(GMT +12:00) Auckland, Wellington, Fiji, Kamchatka',
    ch: '(GMT +12:00)奥克兰,惠灵顿,斐济,堪察加半岛',
  },
]

export default timezones
