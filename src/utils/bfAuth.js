/**
 * BF8100系统授权模块，在登录成功后进行处理
 * 授权信息在登录响应中一起返回
 * 授权协议：proto/lic.proto proto/client.proto#client_login_response
 */

import dayjs from 'dayjs'
import i18n from '@/modules/i18n'
import bfproto from '@/modules/protocol'
import { Types, warningBox, warningBoxWithOption } from './notify'
import { useAuthChecker } from './storage'
import {
  addTime,
  DateMask,
  dateTimeIsValid,
  isSameOrAfter,
  isSameOrBefore,
  nowLocalTime,
  nowUtcTime,
} from './time'

/**
 *  @typedef {
 *  "mod-dispatch" |
 *  "mod-phone-gateway" |
 *  "mod-record" |
 *  "mod-rfid" |
 *  "mod-traditional-dmr" |
 *  "mod-svt" |
 *  "max-controllers"|
 *  "max-devices" |
 *  "max-users"
 *  } ModuleNames
 */

/**
 * 8100的模块为：mod-rfid:巡查功能, mod-phone-gateway:电话网关 mod-record:录音功能 mod-dispatch:联网调度
 * mod-traditional-dmr: 常规DMR终端接入, mod-svt:虚拟集群
 * 8100的数量限制: max-devices:终端最大数量, max-users:用户最大数量，max-controllers:设备最大数量
 * @readonly
 */
export const LicenseModuleNames = Object.freeze({
  ModDispatch: 'mod-dispatch',
  ModPhoneGateway: 'mod-phone-gateway',
  ModRecord: 'mod-record',
  ModRfid: 'mod-rfid',
  ModTraditionalDmr: 'mod-traditional-dmr',
  ModSvt: 'mod-svt',
  MaxControllers: 'max-controllers',
  MaxDevices: 'max-devices',
  MaxUsers: 'max-users',
})

/**
 * 申请授权页面的授权模块的国际化翻译
 * @readonly
 */
export const LicenseApplicationModuleNames = Object.freeze({
  modDispatch: 'mod-dispatch',
  modPhoneGateway: 'mod-phone-gateway',
  modRecord: 'mod-record',
  modRfid: 'mod-rfid',
  modTraditionalDmr: 'mod-traditional-dmr',
  modSvt: 'mod-svt',
  maxControllers: 'controller',
  maxDevices: 'device',
  maxUsers: 'user',
})

/**
 * 获取被授权的模块的名称国际化翻译索引
 * @param {ModuleNames} moduleName
 * @returns {string} i18n key, eq: 'auth.modules.mod-rfid'
 */
export function getAuthModuleI18nKey(moduleName) {
  return `auth.modules.${moduleName}`
}

/**
 * 授权许可的模块包含的导航菜单信息
 */
export const LicenseMenuRelation = Object.freeze({
  // 巡逻系统
  [LicenseModuleNames.ModRfid]: [
    'linePoint',
    'lines',
    'rules',
    'rfidBatteryAlarm',
    'InspectionHistory',
    'shiftHistory',
    'gpstraceHistory',
    'InsRulesHistory',
    // 'onlineHistory', 'ctrlonlineHistory', 'crudHistory',
  ],
  // 录音模块
  [LicenseModuleNames.ModRecord]: ['soundHistory'],
  // 电话网关
  [LicenseModuleNames.ModPhoneGateway]: [
    'shortNumberMapping',
    'gatewayFilter',
    'gatewayPermission',
    'predefinedPhoneBook',
  ],
  // 联网通话调度
  [LicenseModuleNames.ModDispatch]: [
    'bfSpeaking',
    // 'sendcmd',
    // 'dispatchHistory',
    // 'smsHistory'
  ],

  // 基础数据
  // baseData: [
  //   'orgs', 'jobs', 'users',
  //   'mapPoints', 'devices', 'controllers', 'dynamicGroup',
  //   'alarmHistory',
  //   'contacts', 'repeaterWriteFrequency', 'interphoneWf',
  // ],
  // 空口命令(发送命令)
  // airCommand: ['sendcmd'],
  // 物联网模块
  // iotManager: ['IOT', 'iotDeviceHistory'],
})

/**
 * 通过导航菜单索引找到授权模块的名称
 * @param {string} menuIndex 导航菜单索引
 * @returns {string | undefined} 授权模块的名称
 */
export function findModuleNameByMenuIndex(menuIndex) {
  for (const modKey in LicenseMenuRelation) {
    const mod = LicenseMenuRelation[modKey]
    if (mod.includes(menuIndex)) {
      return modKey
    }
  }
  return undefined
}

/**
 * 保存的授权信息
 * 不挂载到全局对象中，只能在内部访问
 * @type {lic_response}
 */
const license = {}

/**
 * 读取授权信息参数
 * @returns {lic_response}
 */
export function getLicense() {
  // Object.assign(license, {
  //   projName: 'test-license',
  //   issueTime: '2022-06-23 03:00:00',
  //   lic: {
  //     expireTime: '2032-07-19 05:00:00',
  //     licenses: {
  //       'mod-rfid': 1,
  //       'mod-dispatch': 1,
  //       'mod-phone-gateway': 1,
  //       'mod-record': 1,
  //       // 'max-dev': 1000,
  //     },
  //   },
  // })
  return license
}

/**
 * 保存授权信息
 * @param {lic_response} lic
 */
export function saveLicense(lic) {
  Object.assign(license, lic)
  const keys = Object.values(LicenseModuleNames)
  if (!license.lic?.licenses) {
    license.lic.licenses = new Map()
  }
  for (const key in license.lic?.licenses) {
    if (!keys.includes(key)) {
      delete license.lic.licenses[key]
    }
  }

  keys.forEach(val => {
    if (license.lic?.licenses?.[val]) {
      return
    }
    license.lic.licenses[val] = 0
  })
}

/**
 * 获取客户端和服务器端最大的UTC时间字符串
 * @returns {string}
 */
function getLicNowUtcTime() {
  let now = nowUtcTime()
  const serverTime = bfglob.serverTime || now
  if (isSameOrBefore(now, serverTime)) {
    now = serverTime
  }

  return now
}

/**
 * 判断指定的到期时间是否已经过期
 * @param {string} expireTime 检查的时间
 * @returns {boolean} 过期则返回true
 */
function checkExpireTimeIsExpired(expireTime) {
  // 如果当前时间在授权到期时间之前，则授权未过期
  // 为避免服务器电脑或客户端电脑时间被修改,导致到期时间检验错误,取二者时间最大值
  const now = getLicNowUtcTime()
  return !isSameOrBefore(now, expireTime)
}

/**
 * 检查授权许可是否过期
 * @param {lic_response} license 授权许可参数
 * @returns {boolean} true: 过期
 */
export function checkLicenseIsExpire(license) {
  // 不存在授权数据，视为过期
  if (!license.lic) {
    return true
  }

  const expireTime = license.lic.expireTime
  // 无效的时间参数
  if (!dateTimeIsValid(expireTime)) {
    return true
  }
  // 如果当前时间在授权到期时间之前，则授权未过期
  return checkExpireTimeIsExpired(expireTime)
}

/**
 * 判断授权许可是否存在
 * 必须同时有名称和授权内容才证明存在授权许可
 * @param {lic_response} license
 * @returns {boolean} 如果存在则返回true
 */
export function licenseIsExist(license) {
  return license && license.projName && license.lic
}

/**
 * 检测授权许可对象是否存在及是否过期
 * @param {lic_response} license 授权参数
 * @returns {boolean} 授权许可存在且不过期则返回true
 */
export function checkLicenseAuthorized(license) {
  // 检查是否存在授权许可
  if (!licenseIsExist(license)) {
    return false
  }

  // 判断是否授权到期
  return !checkLicenseIsExpire(license)
}

/**
 * 授权是否永久有效
 * 到期时间在百年之后,则视为永久有效
 * @param {string} expireTime 到期时间(utc)
 * @return {boolean} 永久有效则返回true
 */
export function isAuthForEver(expireTime) {
  const now = getLicNowUtcTime()
  const late100Year = addTime(now, 100, 'year').format(DateMask)
  return isSameOrAfter(dayjs(expireTime).format(DateMask), late100Year)
}

/**
 * 计算授权到期的剩余的时间
 * @param expireTime
 * @return {{
 *   days: number,
 *   hours: number,
 * }}
 */
export function calcRemainingExpirationTime(expireTime) {
  const now = getLicNowUtcTime()
  const daysDiff = dayjs.utc(expireTime).diff(dayjs.utc(now), 'day')
  const hoursDiff = dayjs.utc(expireTime).diff(dayjs.utc(now), 'hour')

  return {
    days: daysDiff,
    hours: hoursDiff - daysDiff * 24,
  }
}

/**
 * 判断授权的内容选项是否为模块授权
 * @param {string} moduleName 授权内容选项名称
 * @returns {boolean} 是模块授权则返回true
 */
export function isAuthModule(moduleName) {
  return moduleName.startsWith('mod-')
}

/**
 * 判断授权的内容选项是否为数据授权
 * @param {string} moduleName 授权内容选项名称
 * @returns {boolean} 是数据授权则返回true
 */
export function isAuthData(moduleName) {
  return moduleName.startsWith('max-')
}

/**
 * 通过模块名称,检测模块是否有授权
 * 调用该方法时,默认授权许可对象在有效期内
 * @param {Map<string, number>} licenses 授权许可Map
 * @param {ModuleNames } moduleName 模块名称
 * @returns {boolean} 有授权则返回true
 */
export function checkLicenseWithModuleName(licenses, moduleName) {
  if (!moduleName) {
    return true
  }

  // 检查授权数据是否包含指定的模块
  const licenseItem = licenses[moduleName]

  // 模块授权时为 module_name -> 1,建议统一为 mod-xxxx -> 1,没有授权时为 mod-xxxx -> 4
  // 数据授权时为 data_name -> uint32_value,建议为 max-xxxx -> value
  // 0值为无限制，对于模块而言为可用，对于数量而言则为不加限制，此值为向后兼容定义
  if (licenseItem === 0) {
    return true
  }

  if (isAuthModule(moduleName)) {
    if (licenseItem === 4) {
      return false
    }
    return licenseItem === 1
  }

  if (isAuthData(moduleName)) {
    return licenseItem > 0
  }

  // 其他情况,默认没有权限
  return false
}

/**
 * 检查指定的模块是否有授权
 * @param {lic_response} license 授权许可
 * @param {string} menuIndex 导航菜单索引
 * @returns {boolean} 有授权则返回true
 */
export function checkLicenseModuleAuth(license, menuIndex) {
  // 检查授权许可是否可用
  if (!checkLicenseAuthorized(license)) {
    return false
  }

  // 找到导航菜单对应的模块名称,如果没有对应的模块名称,则默认有权限
  const moduleName = findModuleNameByMenuIndex(menuIndex)
  // 通过模块名称判断是否有授权
  const licenses = license.lic?.licenses ?? {}
  return checkLicenseWithModuleName(licenses, moduleName)
}

/**
 * 获取授权相关的命令的主题
 * @return {string}
 */
function getDbSubject() {
  return `db.${bfglob.sysId}`
}

/**
 * 请求后端生成授权请求
 * rpc_cmd.cmd=1430
 * @param {lic_request_file} licData 只填写proj_name和note
 * @returns {Promise<lic_request_file | undefined>}
 */
export async function requestGenerateLicense(licData) {
  // 返回的授权许可文件内容，不需要解码，直接返回，以便下载
  const msgOpts = {
    // decodeMsgType: 'lic_request_file',
  }

  return await bfproto
    .sendMessage(1430, licData, 'lic_request_file', getDbSubject(), msgOpts)
    .then(rpcCmd => {
      bfglob.console.log('requestGenerateLicense:', rpcCmd)
      if (rpcCmd.resInfo === '+OK') {
        return rpcCmd.body
      } else {
        // if (rpcCmd.resInfo.includes('Err:unkonw db req')) {
        //   warningBox('Not implemented', Types.error)
        // }
        return undefined
      }
    })
    .catch(err => {
      bfglob.console.warn('requestGenerateLicense catch:', err)
      // const t = bfproto.bfdx_proto_msg_T(msgOpts.decodeMsgType, 'bfdx_proto')
      // return t?.create() ?? {}
      return undefined
    })
}

/**
 * 查询当前的授权情况
 * rpc_cmd.cmd=1431
 * @returns {Promise<lic_response | undefined>} 如果proj_name为空表明没有授权
 */
export async function requestCurrentLicense() {
  const msgOpts = {
    decodeMsgType: 'lic_response',
  }

  return await bfproto
    .sendMessage(1431, null, null, getDbSubject(), msgOpts)
    .then(rpcCmd => {
      bfglob.console.log('requestCurrentLicense:', rpcCmd)
      if (rpcCmd.resInfo === '+OK') {
        return rpcCmd.body
      } else {
        // if (rpcCmd.resInfo.includes('Err:unkonw db req')) {
        //   warningBox('Not implemented', Types.error)
        // }
        return undefined
      }
    })
    .catch(err => {
      bfglob.console.warn('requestCurrentLicense catch:', err)
      // const t = bfproto.bfdx_proto_msg_T(msgOpts.decodeMsgType, 'bfdx_proto')
      // return t?.create() ?? {}
    })
}

/**
 * 上传导入的授权文件到服务器
 * rpc_cmd.cmd=1432
 * rpc_cmd.body=lic_response_file
 * @param {Uint8Array} licFile 导入的授权文件
 * @returns {Promise<boolean>} 成功上传到服务器则返回true
 */
export async function uploadLicenseFile(licFile) {
  const opts = {
    // 通过合并的方式, 避开encode处理
    rpcCmdFields: {
      body: licFile,
    },
  }
  return await bfproto
    .sendMessage(1432, null, null, getDbSubject(), opts)
    .then(rpcCmd => {
      bfglob.console.log('uploadLicenseFile:', rpcCmd)
      if (rpcCmd.resInfo === '+OK') {
        return true
      } else {
        // 上传失败
        if (rpcCmd.resInfo.includes('Err:lic file is not ok')) {
          warningBox(i18n.global.t('auth.authFileError'), Types.error)
        }

        return false
      }
    })
    .catch(err => {
      bfglob.console.warn('uploadLicenseFile catch:', err)
      // const t = bfproto.bfdx_proto_msg_T(msgOpts.decodeMsgType, 'bfdx_proto')
      // return t?.create() ?? {}
      return false
    })
}

/**
 * 弹出没有授权警告，确定后进行跳转授权页面，否则退出登录
 * @param {string} message 警告提示消息内容
 * @param {boolean} isExpired
 * @returns {Promise<any>}
 */
export async function noAuthAlertWithLogout(message, isExpired) {
  const res = await warningBoxWithOption(message, {
    // title: undefined,
    type: Types.error,
    center: true,
    showClose: false,
    showCancelButton: true,
    cancelButtonText: i18n.global.t('auth.logout'),
    // cancelButtonClass: 'el-button--danger',
    confirmButtonText: i18n.global.t('auth.applyAuth'),
    customClass: 'no-auth-alert',
  }).catch(err => {
    return ['confirm', 'cancel', 'close'].includes(err) ? err : 'cancel'
  })

  if (res === 'confirm') {
    setTimeout(() => {
      bfglob.emit('openMenuItem', 'secondary/authorization', vm => {
        vm.visible = true
        vm.$nextTick(() => {
          vm.jumpToRequestLicense(isExpired)
        })
      })
    }, 0)
  } else {
    window.location.reload()
  }
}

export function checkLicenseWithAlert() {
  const lic = getLicense()
  let alertText = ''
  let licOk = true
  let isExpired = false
  const isExist = licenseIsExist(lic)
  if (isExist) {
    isExpired = checkLicenseIsExpire(lic)
    if (isExpired) {
      licOk = false
      alertText = i18n.global.t('auth.authExpiredAlert')
    }
  } else {
    licOk = false
    alertText = i18n.global.t('auth.noAuthAlert')
  }

  if (!licOk) {
    bfglob.emit('cancel-login-loading')
    noAuthAlertWithLogout(alertText, isExpired).catch()
  }

  return licOk
}

export function checkRemainingExpirationTime() {
  const license = getLicense()
  const expireTime = license.lic?.expireTime ?? getLicNowUtcTime()
  const remainingExpirationTime = calcRemainingExpirationTime(expireTime)

  // 大于30天,不提醒重新授权
  if (remainingExpirationTime.days > 30) {
    return
  }

  const { loadAuthCheckTime, saveAuthCheckTime } = useAuthChecker()
  const lastTipTime = loadAuthCheckTime()

  const showTips = async () => {
    const res = await warningBoxWithOption(
      i18n.global.t('auth.reapplyAuthTips', remainingExpirationTime),
      {
        // title: undefined,
        type: Types.warning,
        center: true,
        // showClose: false,
        showCancelButton: true,
        cancelButtonText: i18n.global.t('auth.noReminderToday'),
        // cancelButtonClass: 'el-button--danger',
        confirmButtonText: i18n.global.t('auth.applyAuth'),
        customClass: 'no-auth-alert',
        distinguishCancelAndClose: true,
      },
    ).catch(err => {
      return ['confirm', 'cancel', 'close'].includes(err) ? err : 'cancel'
    })

    if (res === 'confirm') {
      setTimeout(() => {
        bfglob.emit('openMenuItem', 'secondary/authorization', vm => {
          vm.visible = true
          vm.$nextTick(() => {
            vm.jumpToRequestLicense(true)
          })
        })
      }, 0)
    } else if (res === 'cancel') {
      saveAuthCheckTime()
    }
  }

  if (lastTipTime) {
    const today = nowLocalTime(DateMask)
    if (lastTipTime !== today) {
      // 今天第一次提醒
      showTips()
    }
  } else {
    // 第一次提醒剩余时间
    showTips()
  }
}
