// 移出任务组/临时组
import i18n from '@/modules/i18n'
import bfproto from '@/modules/protocol'
import bfprocess from '../bfprocess'
import { messageBox } from '../notify'

// 动态组状态  1:正常 10:失效 / 删除中
export const DynamicGroupState = {
  None: 0,
  Normal: 1,
  Expired: 10,
}
export const DynamicGroupType = {
  TempGroup: 0,
  TaskGroup: 1,
}
export const DbOrgIsVirtual = {
  TempGroup: 100,
  TaskGroup: 101,
  FastTempGroup: 102,
}
//临时组：1：正常 2：被优先级高的抢占 10:已失效
//任务组：1:正常/已应答加入，2：被优先级高的抢占， 4:未应答加入 5:已应答退出 6:未应答退出
export const MemberState = {
  None: 0,
  Normal: 1,
  Preempted: 2,
  JoinWithoutAnswer: 4,
  AnsweredExit: 5,
  ExitWithoutAnswer: 6,
  Expired: 10,
  Delete: 11,
}
export const MemberType = {
  Device: 1,
  Group: 2,
  ListenGroupDevice: 3,
}

export function getGroupSubject() {
  return `group.${bfglob.sysId}`
}

// 动态组的变化，服务器会主动发送通知，需要过滤自己的操作结果
function filterSelfNotice(rpcCmd) {
  bfglob.cmdReqId.add(rpcCmd.reqId)
}

// 删除任务组/临时组
export async function removeDynamicGroup(dbOrg, options) {
  if (!dbOrg) {
    return Promise.reject('No target')
  }

  const opts = {
    decodeMsgType: 'db_org',
    ...options,
  }

  return await bfproto.sendMessage(
    1312,
    dbOrg,
    'db_org',
    getGroupSubject(),
    opts,
  )
}

// 加入任务组/临时组
export async function addDynamicGroup(modifyDeviceList) {
  if (!modifyDeviceList) {
    return Promise.reject('No target')
  }

  const options = {
    decodeMsgType: 'modify_device_list',
    beforeSend: filterSelfNotice,
  }

  // 回应rpc_cmd.body=modify_device_list，包含所有加入的组、终端
  return await bfproto.sendMessage(
    1311,
    modifyDeviceList,
    'modify_device_list',
    getGroupSubject(),
    options,
  )
}

// 编辑临时组/任务组 增加成员
export async function addDynamicGroupMember(modifyDeviceList) {
  if (!modifyDeviceList) {
    return Promise.reject('No target')
  }

  const options = {
    decodeMsgType: 'modify_device_list',
    beforeSend: filterSelfNotice,
  }

  return await bfproto.sendMessage(
    1313,
    modifyDeviceList,
    'modify_device_list',
    getGroupSubject(),
    options,
  )
}

// 编辑临时组/任务组 删除成员
export async function removeDynamicGroupMember(modifyDeviceList) {
  if (!modifyDeviceList) {
    return Promise.reject('No target')
  }

  const options = {
    decodeMsgType: 'modify_device_list',
    // beforeSend: filterSelfNotice,
  }

  return await bfproto.sendMessage(
    1314,
    modifyDeviceList,
    'modify_device_list',
    getGroupSubject(),
    options,
  )
}

// 更新动态组成员
export async function modifyDynamicGroupMember(dynamicGroup, add, remove) {
  const modifyMemberList = {
    dynamicGroup,
    addDevices: {
      rows: add.devices || [],
    },
    addGroups: {
      rows: add.groups || [],
    },
    delDevices: {
      rows: remove.devices || [],
    },
    delGroups: {
      rows: remove.groups || [],
    },
  }

  bfglob.console.log('modifyDynamicGroupMember', modifyMemberList)

  const options = {
    decodeMsgType: 'modify_member_list',
    // beforeSend: filterSelfNotice,
  }

  return await bfproto.sendMessage(
    1321,
    modifyMemberList,
    'modify_member_list',
    getGroupSubject(),
    options,
  )
}

// 下个可用的dmrId
export async function nextDynamicGroupDmrId() {
  const rpcCmd = await bfproto.sendMessage(1301, null, null, getGroupSubject())
  if (rpcCmd.resInfo) {
    switch (rpcCmd.resInfo) {
      // case '':
      default:
        bfglob.console.error('[nextTaskGroupDmrId] resInfo:', rpcCmd.resInfo)
        return Promise.reject(rpcCmd.resInfo)
    }
  }
  return rpcCmd.opt || ''
}

// 查询目标dmrId是否可以加入某个动态组,rpc.body=db_dynamic_group_detail
export async function requestTargetCanJoinDynamicGroup(memberCanAdd) {
  if (!memberCanAdd) {
    return Promise.reject('No target')
  }

  return await bfproto.sendMessage(
    1310,
    memberCanAdd,
    'member_can_add',
    getGroupSubject(),
  )
}

// 查询可查看的临时组
export async function requestViewableDynamicGroup(dbOrgList) {
  if (!dbOrgList) {
    return Promise.reject('No target')
  }

  const options = {
    decodeMsgType: 'temp_group_list',
  }
  return await bfproto.sendMessage(
    1315,
    dbOrgList,
    'db_org_list',
    getGroupSubject(),
    options,
  )
}

// 指挥坐席应答加入动态组
export async function pcDeviceAnswerJoinedDynamicGroup(detail) {
  if (!detail) {
    return Promise.reject('No target')
  }

  return await bfproto.sendMessage(
    1319,
    detail,
    'db_dynamic_group_detail',
    getGroupSubject(),
  )
}

const alreadyReplyJoined = new Set()

export async function checkPcDeviceJoinDynamicGroup(data) {
  // 找到自己的坐席数据
  const pcDevice = bfglob.userInfo.setting.voipSpeakInfo.speaker

  if (
    data.dynamicGroupType === DynamicGroupType.TaskGroup &&
    data.deviceDmrid === pcDevice &&
    data.memberState === MemberState.JoinWithoutAnswer
  ) {
    const rpcCmd = await pcDeviceAnswerJoinedDynamicGroup(data).catch()
    if (rpcCmd.resInfo === '+OK') {
      // 已经应答
      if (alreadyReplyJoined.has(data.rid)) {
        return
      }
      alreadyReplyJoined.add(data.rid)
      // 提示用户指挥坐席已经加入任务组
      const pcDeviceName =
        bfglob.gdevices.getDataByIndex(pcDevice)?.selfId ?? pcDevice
      const dynamicGroupName =
        bfglob.gdynamicGroupDetail.getDynamicGroup(data.orgId)?.orgShortName ??
        ''
      messageBox(
        i18n.global.t('dynamicGroup.pcDeviceJoinedTaskGroup', {
          pcDevice: pcDeviceName,
          dynamicGroup: dynamicGroupName,
        }),
      )
    }
  }
}

// 指挥坐席应答退出动态组
export async function pcDeviceAnswerExitDynamicGroup(detail) {
  if (!detail) {
    return Promise.reject('No target')
  }

  return await bfproto.sendMessage(
    1320,
    detail,
    'db_dynamic_group_detail',
    getGroupSubject(),
  )
}

const alreadyReplyExit = new Set()

export async function checkPcDeviceExitDynamicGroup(data) {
  // 找到自己的坐席数据
  const pcDevice = bfglob.userInfo.setting.voipSpeakInfo.speaker

  if (
    data.dynamicGroupType === DynamicGroupType.TaskGroup &&
    data.deviceDmrid === pcDevice &&
    data.memberState === MemberState.ExitWithoutAnswer
  ) {
    const rpcCmd = await pcDeviceAnswerExitDynamicGroup(data).catch()
    if (rpcCmd.resInfo === '+OK') {
      // 已经应答退出
      if (alreadyReplyExit.has(data.rid)) {
        return
      }
      alreadyReplyExit.add(data.rid)

      const pcDeviceName =
        bfglob.gdevices.getDataByIndex(pcDevice)?.selfId ?? pcDevice
      const dynamicGroupName =
        bfglob.gdynamicGroupDetail.getDynamicGroup(data.orgId)?.orgShortName ??
        ''
      messageBox(
        i18n.global.t('dynamicGroup.pcDeviceExitTaskGroup', {
          pcDevice: pcDeviceName,
          dynamicGroup: dynamicGroupName,
        }),
      )
    }
  }
}

export function saveDynamicGroupDetail(data) {
  bfglob.gdynamicGroupDetail.set(data.rid, data)
  // 此成员是终端还是组呼 1:终端 2:组呼 3:因任务组呼加入的终端
  if (data.isDeviceGroup === 2) {
    bfglob.gdynamicGroupDetail.setIndex(data.groupRid, data.rid)
  } else {
    bfglob.gdynamicGroupDetail.setIndex(data.deviceRid, data.rid)
  }
}

// 请求没有权限的动态组或动态组下的组详情对应的db_org数据
export async function fetchNoPermGroup(orgRids) {
  if (!Array.isArray(orgRids) || !orgRids.length) {
    return []
  }
  // 请求没有权限的动态组，并订阅相关动态组事件
  const noPermOrgRows = await bfprocess.fetchNoPermsOrgs(orgRids)
  for (let j = 0; j < noPermOrgRows.length; j++) {
    const oneNoPermOrg = noPermOrgRows[j]
    // 返回的数据中，默认包含上级，但上级可能是有权限，需要过滤
    if (bfglob.gorgData.get(oneNoPermOrg.rid)) {
      continue
    }
    bfprocess.setGlobalNoPermOrgData(oneNoPermOrg)
    // bfglob.emit('dynamic-group-subscribe-server-command', oneNoPermOrg)
  }

  return noPermOrgRows
}

// 请求没有权限的动态组详情终端数据
export async function fetchNoPermDevice(deviceRids) {
  if (!Array.isArray(deviceRids) || !deviceRids.length) {
    return []
  }

  const noPermDeviceRows = await bfprocess.fetchNoPermDevices(deviceRids)
  for (let j = 0; j < noPermDeviceRows.length; j++) {
    const oneNoPermOrg = noPermDeviceRows[j]
    bfprocess.setGlobalNoPermDevData(oneNoPermOrg)
  }

  return noPermDeviceRows
}

export function processDynamicGroup(dbOrg) {
  // 当前动态组存在于本地，则为有权限，反之，则需要向服务器请求无权限的动态组数据
  const localDbOrg = bfglob.gorgData.get(dbOrg.rid)
  if (!localDbOrg) {
    bfglob.emit('add_global_dynamic_group', dbOrg)
    bfprocess.publishTableEvent('dynamicGroupTable', 'add', dbOrg)
  } else {
    // 合并当前的单位数据
    bfglob.emit('update_global_orgData', dbOrg)
    bfprocess.publishTableEvent('dynamicGroupTable', 'update', dbOrg)
  }
  // 需要订阅事件，服务器在收到动态组的数据后，按动态组进行转发
  bfglob.emit('dynamic-group-subscribe-server-command', dbOrg)
}

export async function processDetailList(dataList) {
  const noPermOrgRids = new Set()
  const noPermDevRids = new Set()

  // 处理动态组下的详情数据
  for (let k = 0; k < dataList.length; k++) {
    const data = dataList[k]
    if (data.isDeviceGroup === MemberType.Group) {
      !bfglob.gorgData.get(data.groupRid) && noPermOrgRids.add(data.groupRid)
    } else {
      !bfglob.gdevices.get(data.deviceRid) && noPermDevRids.add(data.deviceRid)

      // 如果终端成员的上级也没有权限，也需要请求回来，以便动态组管理
      if (
        !(
          bfglob.gorgData.get(data.memberOrgId) ||
          bfglob.noPermOrgData.get(data.memberOrgId)
        )
      ) {
        noPermOrgRids.add(data.memberOrgId)
      }

      // 判断该详情是否为自己的指挥坐席，如果是则向服务器应答加入
      await checkPcDeviceJoinDynamicGroup(data).catch()
      await checkPcDeviceExitDynamicGroup(data).catch()
    }
  }

  await fetchNoPermGroup(Array.from(noPermOrgRids))
  await fetchNoPermDevice(Array.from(noPermDevRids))

  bfglob.emit('add_global_dynamic_group_detail', dataList)
}

export async function requestAllDynamicGroup() {
  const allOrgData = bfglob.gorgData.getAll()
  const dbOrgList = {
    rows: Object.keys(allOrgData).map(key => allOrgData[key]),
  }
  const rpcCmd = await requestViewableDynamicGroup(dbOrgList)

  // 清空旧的数据
  bfglob.gdynamicGroupDetail.clear()
  const dynamicGroup = rpcCmd.body.dynamicGroup
  if (!dynamicGroup.length) {
    return
  }

  // 从组成员列表中过滤出动态组对应的成员列表
  for (let i = 0; i < dynamicGroup.length; i++) {
    const dbOrg = dynamicGroup[i]
    processDynamicGroup(dbOrg)
  }

  await processDetailList(rpcCmd.body.members)

  // 发布完成请求动态组数据事件
  setTimeout(() => {
    bfglob.emit('request-dynamic-group-finish')
  }, 0)
}

// 主动查询动态组状态,主动是因为创建动态组后,被通知的客户端无法接收到通知,订阅事件前,通知已经发送
export async function requestDynamicGroupStatus(dynamicGroup) {
  if (!dynamicGroup) {
    return Promise.reject('no target')
  }

  const options = {
    decodeMsgType: 'temp_group_list',
  }

  return await bfproto.sendMessage(
    1322,
    dynamicGroup,
    'db_org',
    getGroupSubject(),
    options,
  )
}
