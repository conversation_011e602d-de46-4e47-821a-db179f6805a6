import dayjs from 'dayjs'
import { DEV } from '@/envConfig'

const TimeMask = 'YYYY-MM-DD HH:mm:ss'

const defaule_config = {
  level: !DEV ? 'error' : 'debug',
  prefix: 'bf-logs',
  timeStamp: false,
}
const Logs = (function () {
  function Logs() {
    this.config = defaule_config
  }

  Logs.prototype.getTimeStamp = function () {
    return dayjs().format(TimeMask)
  }
  Logs.prototype.getArgs = function (args) {
    if (this.config.prefix) {
      args.unshift('[' + this.config.prefix + '] ')
    }
    if (this.config.timeStamp) {
      args.unshift(this.getTimeStamp())
    }
    return args
  }
  Logs.prototype.canLog = function (level) {
    if (this.config.level === 'debug') {
      return true
    }
    if (this.config.level === 'none') {
      return false
    }
    if (this.config.level === level) {
      return true
    }
    return false
  }
  Logs.prototype.print = function (level, args) {
    if (this.canLog(level)) {
      const _args = this.getArgs(args)
      console[level].apply(console, _args)
    }
  }
  Logs.configure = function (context, opts) {
    context.config = Object.assign(context.config, opts)
  }
  Logs.prototype.log = function () {
    const args = []
    for (let _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i]
    }
    this.print('log', args)
  }
  Logs.prototype.warn = function () {
    const args = []
    for (let _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i]
    }
    this.print('warn', args)
  }
  Logs.prototype.error = function () {
    const args = []
    for (let _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i]
    }
    this.print('error', args)
  }
  return Logs
})()

export default Logs
