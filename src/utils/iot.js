import i18n from '@/modules/i18n'
import bfproto, { kcpPackageName } from '@/modules/protocol'
import { createNotification } from '@/utils/notify'
import bfutil, { bin2hex, DefOrgRid, DeviceTypes } from './bfutil'
import { addLinePointActiveDevice, createPopup, deferCloseMarkerPopup, get_popup_content_for_iot_device, mapFlyTo, reduceLinePointActiveDevice } from './map'
import { getUtcTimeString, isSameOrAfter, subtractTime, utcTimeToLocalTime, utcToLocalTimeFormat } from './time'

export const IotDeviceTypes = {
  // 200(0xC8): 基站巡查点
  BsPoint: 0xc8,
  // 预留
  Reserved: 0x00,
  // 对讲机
  InterPhone: 0x01,
  // 工牌
  UserCard: 0x02,
  // 物卡
  ThingsCard: 0x03,
  // 烟感
  SmokeDetector: 0x04,
  // 节能灯开关盒
  EnergySavingLamps: 0x05,
  // 温湿度检测盒
  TemperatureHumidityDetector: 0x06,
  // 异味排风开关盒
  OdorExhaustSwitchBox: 0x07,
  // 预留
  Reserved08: 0x08,
  // 信标开关盒
  BeaconSwitchBox: 0xfb,
  // 信标机
  BeaconMachine: 0xfc,
  // 中继机
  Repeater: 0xfd,
  // 网关机
  GatewayMachine: 0xfe,
  // 所有设备
  AllDevices: 0xff,
}

export const IotDeviceTypeList = Object.entries(IotDeviceTypes)
  .map(([, v]) => v)
  .filter(v => {
    return ![IotDeviceTypes.Reserved, IotDeviceTypes.InterPhone, IotDeviceTypes.Reserved08, IotDeviceTypes.AllDevices].includes(v)
  })

export const IotCommonCmd = {
  Report: genIotCmd(0x11, 0x10),
  GeneralAlarm: genIotCmd(0x21, 0x20),
  AntiDismantlingAlarm: genIotCmd(0x31, 0x30),
  EmergencyAlarm: genIotCmd(0x41, 0x40),
  OverTemperatureAlarm: genIotCmd(0x51, 0x50),
}

export const TemperatureCmd = {
  Report: genIotCmd(0x65, 0x64),
}

export const SmokeDetectorCmd = {
  Heartbeat: genIotCmd(0x43, 0x42),
}

export const EnergySavingLampsCmd = {
  // 交流AC开关控制“开”命令
  ACOn: genIotCmd(0x74, 0x01),
  // 交流AC开关控制“关”命令
  ACOff: genIotCmd(0x74, 0x00),
  // 开关盒防拆报警命令
  SwitchBoxTamperAlarm: genIotCmd(0x31, 0x30),
  // 人感应触发防盗报警命令
  HumanTriggersAntiTheftAlarm: genIotCmd(0x33, 0x32),
  // 声感应触发防盗报警命令
  AcousticTriggersAntiTheftAlarm: genIotCmd(0x35, 0x34),
  // 交流AC断电报警命令
  ACPowerOffAlarm: genIotCmd(0x53, 0x52),
  // 红外设置警戒模式“开启”命令
  InfraredSettingAlertModeOn: genIotCmd(0x74, 0x71),
  // 红外设置警戒模式“关闭”命令
  InfraredSettingAlertModeOff: genIotCmd(0x74, 0x70),
}

export function decodeDevId(b) {
  return bin2hex(b)
}

export function decodeIotDataTime(t) {
  return getUtcTimeString(t * 1000)
}

// 用于上行指令的鉴别区分，位于指令包的起始位置，字段长度为2 bytes。上行指令规定：
// 2个字节的高BIT位都等于0为上行指令（0x00-0x7F）。
export function isUpwardCmd(cmd) {
  return (cmd & 0xff00) >> 15 === 0 && (cmd & 0x00ff) >> 7 === 0
}

// 2个字节的高BIT位都等于1为下行指令（0x80-0xFF）
export function isDownwardCmd(cmd) {
  return (cmd & 0xff00) >> 15 === 1 && (cmd & 0x00ff) >> 7 === 1
}

const ClassNames = {
  Normal: 'normal-report',
  OtherAlarm: 'other-alarm',
  EmergencyAlarm: 'emergency-alarm',
  TemperatureHumidity: 'temperatureHumidity-report',
  NoCommand: 'no-command-warn',
  OffLine: 'offline',
}

const StatusClasses = {
  // 烟感心跳
  [SmokeDetectorCmd.Heartbeat]: ClassNames.Normal,
  // 打卡
  [IotCommonCmd.Report]: ClassNames.Normal,
  // 其他报警，低电
  [IotCommonCmd.GeneralAlarm]: ClassNames.OtherAlarm,
  // 防拆报警
  [IotCommonCmd.AntiDismantlingAlarm]: ClassNames.EmergencyAlarm,
  // 紧急报警
  [IotCommonCmd.EmergencyAlarm]: ClassNames.EmergencyAlarm,
  // 超温域报警(过高或过低)
  [IotCommonCmd.OverTemperatureAlarm]: ClassNames.OtherAlarm,
  // 定时上报温度及湿度
  [TemperatureCmd.Report]: ClassNames.TemperatureHumidity,
  // 节能灯
  [EnergySavingLampsCmd.ACOn]: ClassNames.Normal,
  [EnergySavingLampsCmd.ACOff]: ClassNames.OffLine,
  [EnergySavingLampsCmd.SwitchBoxTamperAlarm]: ClassNames.EmergencyAlarm,
  [EnergySavingLampsCmd.HumanTriggersAntiTheftAlarm]: ClassNames.EmergencyAlarm,
  [EnergySavingLampsCmd.AcousticTriggersAntiTheftAlarm]: ClassNames.EmergencyAlarm,
  [EnergySavingLampsCmd.ACPowerOffAlarm]: ClassNames.OtherAlarm,
  [EnergySavingLampsCmd.InfraredSettingAlertModeOn]: ClassNames.Normal,
  [EnergySavingLampsCmd.InfraredSettingAlertModeOff]: ClassNames.Normal,
  // 长时间无指令警告
  noCmdWarn: ClassNames.NoCommand,
}
const StatusClassList = Object.keys(StatusClasses).map(key => StatusClasses[key])

function getThingsCardStatusClasses(lastCmd) {
  return StatusClasses[lastCmd] ?? 'none'
}

// devType => recvDevId
const LastRecvDevId = new Map()

function getDevDataByType(devId, devType) {
  if (devType === IotDeviceTypes.UserCard) {
    return bfglob.gdevices.getDataByIndex(devId)
  }

  return bfglob.giotDevices.getDataByIndex(devId)
}

function checkLastLinePointPopupIsOpen(recvDevId) {
  const linePoint = bfglob.glinePoints.getDataByIndex(recvDevId)
  if (!linePoint) {
    return
  }

  // 获取Marker，如果正在显示巡查打卡信息，则关闭popup
  const popup = bfglob.glinePoints.getMarker(linePoint.rid)?.getPopup()
  if (popup && popup.isOpen()) {
    popup.remove()
  }
}

function getUserCardInspectPopupHtml(data, linePoint, devData) {
  let devName = devData?.selfId ?? ''
  if (devData?.deviceUser && devData.deviceUser === DefOrgRid) {
    const userName = bfglob.guserData.getUserNameByKey(devData.deviceUser)
    if (userName) {
      devName += `(${userName})`
    }
  }
  const inspectTime = utcToLocalTimeFormat(data.recvTime)
  const action = i18n.global.t('msgbox.areInspections')
  const target = `${linePoint.pointId} / ${linePoint.pointName}`
  return `<p>${inspectTime} ${devName} ${action} ${target}</p>`
}

function showUserCardInspectPopup(data, linePoint, devData) {
  const marker = bfglob.glinePoints.getMarker(linePoint.rid)
  if (!marker) {
    return
  }

  const content = getUserCardInspectPopupHtml(data, linePoint, devData)
  const oldPopup = marker.getPopup()
  const popup = createPopup({
    closeButton: false,
    closeOnClick: true,
    content: content,
    lngLat: marker._lngLat,
  })
  marker.setPopup(popup)
  deferCloseMarkerPopup(marker, oldPopup)
}

export function updateUserCardLinePointMarkerInfo(linePoint, lastCmd) {
  const marker = bfglob.glinePoints.getMarker(linePoint.rid)
  if (!marker) {
    return
  }

  // 更新图标状态，打到图标元素，删除旧的className，再添加当前状态的className
  const el = marker.getElement().querySelector('.map_marker')
  let cLen = el.classList.length
  while (cLen > 0) {
    const cls = el.classList[--cLen]
    if (StatusClassList.includes(cls)) {
      el.classList.remove(cls)
    }
  }
  el.classList.add(getThingsCardStatusClasses(lastCmd))
}

function getUserCardSelfAndParentNames(devData) {
  const parentName = bfglob.gorgData.getShortName(devData.orgId)
  let devName = devData?.selfId ?? ''
  if (devData?.deviceUser && devData.deviceUser === DefOrgRid) {
    const userName = bfglob.guserData.getUserNameByKey(devData.deviceUser)
    if (userName) {
      devName += `(${userName})`
    }
  }

  return [parentName, devName]
}

/* 以下为人员卡指令处理函数 */

// 正常打卡
export function userCardInspect(data, linePoint, devData) {
  // 如果上个打卡的巡查点是否存在，存在则检查是否还在
  const lastRecvDevId = LastRecvDevId.get(IotDeviceTypes.UserCard)
  if (lastRecvDevId) {
    checkLastLinePointPopupIsOpen(lastRecvDevId)
  }

  // 在当前的巡查点上显示打卡信息
  if (bfglob.userInfo.setting.InsOnMapToolips) {
    showUserCardInspectPopup(data, linePoint, devData)
  }
  LastRecvDevId.set(IotDeviceTypes.UserCard, data.recvDevId)

  const [parentName, devName] = getUserCardSelfAndParentNames(devData)

  // 弹出桌面通知
  if (bfglob.userInfo.setting.showInsNotifi) {
    const options = {
      body: `${parentName} / ${devName} ${i18n.global.t('msgbox.areInspections')} ${linePoint.pointName}`,
      tag: devData.rid,
    }
    createNotification(options)
  }

  updateUserCardLinePointMarkerInfo(linePoint, data.cmd)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('msgbox.areInspections')} ${linePoint.pointName}`
  bfglob.emit('addnote', content)
}

// 低电报警
export function userCardLowPowerAlarm(data, linePoint, devData) {
  bfglob.console.log('userCardLowPowerAlarm', data, linePoint, devData)
  updateUserCardLinePointMarkerInfo(linePoint, data.cmd)

  const [parentName, devName] = getUserCardSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.lowPower')}`
  bfglob.emit('addnote', content)
}

// 防折报警
export function userCardAntiFoldAlarm(data, linePoint, devData) {
  bfglob.console.log('userCardAntiFoldAlarm', data, linePoint, devData)
  updateUserCardLinePointMarkerInfo(linePoint, data.cmd)

  const [parentName, devName] = getUserCardSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.antiFoldAlarm')}`
  bfglob.emit('addnote', content)
}

// 紧急报警
export function userCardEmergencyAlarm(data, linePoint, devData) {
  const [parentName, devName] = getUserCardSelfAndParentNames(devData)

  // 设置在地图中心显示
  if (bfglob.userInfo.setting.showAlarmOnMapCenter) {
    mapFlyTo([linePoint.lon, linePoint.lat])
  }
  // 紧急报警弹框处理
  const content = `${parentName} / ${devName} ${i18n.global.t('dataTable.emergency')}`
  if (bfglob.userInfo.setting.alarmPrompt) {
    bfglob.emit('show_alarm_dialog', devData, data.recvTime, data.historyRid)
    const options = {
      body: content,
      tag: devData.rid,
    }
    createNotification(options)
  }
  // 播放紧急报警声音
  if (bfglob.userInfo.setting.alarmVoice) {
    bfglob.emit('play_alarm_voice')
  }

  updateUserCardLinePointMarkerInfo(linePoint, data.cmd)

  // 运行日志
  bfglob.emit('addnote', content)
}

// 超温域报警，过高或过低都报警
export function userCardOverTemperatureZoneAlarm(data, linePoint, devData) {
  bfglob.console.log('userCardOverTemperatureZoneAlarm', data, linePoint, devData)
  updateUserCardLinePointMarkerInfo(linePoint, data.cmd)
  const [parentName, devName] = getUserCardSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.overTemperatureAlarm')}`
  bfglob.emit('addnote', content)
}

export function updateIotDeviceMarkerInfo(devData) {
  // 如果marker popup已经打开，则更新popup内容
  const marker = bfglob.giotDevices.getMarker(devData.rid)
  if (!marker) {
    return
  }

  // 更新图标状态，找到图标元素，删除旧的className，再添加当前状态的className
  const el = marker.getElement().querySelector('.map_marker')
  let cLen = el.classList.length
  while (cLen > 0) {
    const cls = el.classList[--cLen]
    if (StatusClassList.includes(cls)) {
      el.classList.remove(cls)
    }
  }

  let statusCls = getThingsCardStatusClasses(devData.lastCmd)
  // 如果24小时内，没有数据，则显示为黄色警告图标
  if (!isSameOrAfter(utcTimeToLocalTime(devData.lastDataTime), subtractTime(new Date(), 24, 'hour'))) {
    statusCls = getThingsCardStatusClasses('noCmdWarn')
  }
  el.classList.add(statusCls)

  const popup = marker.getPopup()
  if (!popup) {
    return
  }

  if (popup.isOpen()) {
    popup.setHTML(get_popup_content_for_iot_device(devData))
  }
}

function getIotDeviceSelfAndParentNames(devData) {
  const parentName = bfglob.gorgData.getShortName(devData.orgId)
  const devName = devData.devName

  return [parentName, devName]
}

/* 以下为物卡指令处理函数 */

// 正常打卡
export function thingsCardInspect(data, linePoint, devData) {
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iot.normalClockIn')}`
  bfglob.emit('addnote', content)
}

// 低电报警
export function thingsCardLowPowerAlarm(data, linePoint, devData) {
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.lowPower')}`
  bfglob.emit('addnote', content)
}

// 防折报警
export function thingsCardAntiFoldAlarm(data, linePoint, devData) {
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  // 紧急报警弹框处理
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.antiFoldAlarm')}`
  if (bfglob.userInfo.setting.alarmPrompt) {
    bfglob.emit('show_alarm_dialog', devData, data.recvTime, data.historyRid)
    const options = {
      body: content,
      tag: devData.rid,
    }
    createNotification(options)
  }
  // 播放紧急报警声音
  if (bfglob.userInfo.setting.alarmVoice) {
    bfglob.emit('play_alarm_voice')
  }

  // 运行日志
  bfglob.emit('addnote', content)
}

// 物卡没有该指令，预留
export function thingsCardEmergencyAlarm(data) {
  bfglob.console.log('thingsCardEmergencyAlarm', data)
}

// 超温域报警，过高或过低都报警
export function thingsCardOverTemperatureZoneAlarm(data, linePoint, devData) {
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.overTemperatureAlarm')}`
  bfglob.emit('addnote', content)
}

/* 以下为烟感指令处理函数 */

// 正常打卡
export function smokeDetectorInspect(data, linePoint, devData) {
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iot.normalClockIn')}`
  bfglob.emit('addnote', content)
}

// 低电报警
export function smokeDetectorLowPowerAlarm(data, linePoint, devData) {
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.lowPower')}`
  bfglob.emit('addnote', content)
}

// 防折报警
export function smokeDetectorAntiFoldAlarm(data, linePoint, devData) {
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  // 紧急报警弹框处理
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.antiFoldAlarm')}`
  if (bfglob.userInfo.setting.alarmPrompt) {
    bfglob.emit('show_alarm_dialog', devData, data.recvTime, data.historyRid)
    const options = {
      body: content,
      tag: devData.rid,
    }
    createNotification(options)
  }
  // 播放紧急报警声音
  if (bfglob.userInfo.setting.alarmVoice) {
    bfglob.emit('play_alarm_voice')
  }

  // 运行日志
  bfglob.emit('addnote', content)
}

// 烟感烟雾报警
export function smokeDetectorEmergencyAlarm(data, linePoint, devData) {
  bfglob.console.log('smokeDetectorEmergencyAlarm', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  // 紧急报警弹框处理
  const content = `${parentName} / ${devName} ${i18n.global.t('dataTable.emergency')}`
  if (bfglob.userInfo.setting.alarmPrompt) {
    bfglob.emit('show_alarm_dialog', devData, data.recvTime, data.historyRid)
    const options = {
      body: content,
      tag: devData.rid,
    }
    createNotification(options)
  }
  // 播放紧急报警声音
  if (bfglob.userInfo.setting.alarmVoice) {
    bfglob.emit('play_alarm_voice')
  }

  // 运行日志
  bfglob.emit('addnote', content)
}

// 烟感无报警正常心跳，只更新数据状态，不做其他处理
export function smokeDetectorNoSmokeAlarmNormalHeartbeat(data, linePoint, devData) {
  bfglob.console.log('smokeDetectorNoSmokeAlarmNormalHeartbeat', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  // const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  //
  // // 运行日志
  // const content = `${parentName} / ${devName} ${i18n.global.t('iot.normalClockIn')}`
  // bfglob.emit('addnote', content)
}

// 超温域报警，过高或过低都报警
export function smokeDetectorOverTemperatureZoneAlarm(data, linePoint, devData) {
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.overTemperatureAlarm')}`
  bfglob.emit('addnote', content)
}

// 温度、温度上报指令处理方法
export function temperatureHumidityDetectorReport(data, linePoint, devData) {
  // 解析温度、湿度
  decodeTempHumiToIotDevice(devData, data.cmdParam)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iot.tempReport')}`
  bfglob.emit('addnote', content)
}

/* 以下为节能灯处理程序 */

// 心跳方法
export function energySavingLampsReport(data, linePoint, devData) {
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iotDevHistory.report')}`
  bfglob.emit('addnote', content)
}

// 交流AC开关控制“开”命令：（灯亮、排风扇开）
export function energySavingLampsACOn(data, linePoint, devData) {
  console.log('energySavindgLampsACOn', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iot.acOn')}`
  bfglob.emit('addnote', content)
}

// 交流AC开关控制“关”命令：（灯灭、排风扇关）
export function energySavingLampsACOff(data, linePoint, devData) {
  console.log('energySavingLampsACOff', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)

  // 运行日志
  const content = `${parentName} / ${devName} ${i18n.global.t('iot.acOff')}`
  bfglob.emit('addnote', content)
}

// 开关盒防拆报警命令
export function energySavingLampsSwitchBoxTamperAlarm(data, linePoint, devData) {
  bfglob.console.log('energySavingLampsSwitchBoxTamperAlarm', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  // 紧急报警弹框处理
  const content = `${parentName} / ${devName} ${i18n.global.t('dataTable.antiDismantlingAlarm')}`
  if (bfglob.userInfo.setting.alarmPrompt) {
    bfglob.emit('show_alarm_dialog', devData, data.recvTime, data.historyRid)
    const options = {
      body: content,
      tag: devData.rid,
    }
    createNotification(options)
  }
  // 播放紧急报警声音
  if (bfglob.userInfo.setting.alarmVoice) {
    bfglob.emit('play_alarm_voice')
  }

  // 运行日志
  bfglob.emit('addnote', content)
}

// 人感应触发防盗报警命令
export function energySavingLampsHumanTriggersAntiTheftAlarm(data, linePoint, devData) {
  bfglob.console.log('energySavingLampsHumanTriggersAntiTheftAlarm', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  // 紧急报警弹框处理
  const content = `${parentName} / ${devName} ${i18n.global.t('dataTable.infraredSensorAlarm')}`
  if (bfglob.userInfo.setting.alarmPrompt) {
    bfglob.emit('show_alarm_dialog', devData, data.recvTime, data.historyRid)
    const options = {
      body: content,
      tag: devData.rid,
    }
    createNotification(options)
  }
  // 播放紧急报警声音
  if (bfglob.userInfo.setting.alarmVoice) {
    bfglob.emit('play_alarm_voice')
  }

  // 运行日志
  bfglob.emit('addnote', content)
}

// 声感应触发防盗报警命令
export function energySavingLampsAcousticTriggersAntiTheftAlarm(data, linePoint, devData) {
  bfglob.console.log('energySavingLampsAcousticTriggersAntiTheftAlarm', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  // 紧急报警弹框处理
  const content = `${parentName} / ${devName} ${i18n.global.t('dataTable.soundSensorAlarm')}`
  if (bfglob.userInfo.setting.alarmPrompt) {
    bfglob.emit('show_alarm_dialog', devData, data.recvTime, data.historyRid)
    const options = {
      body: content,
      tag: devData.rid,
    }
    createNotification(options)
  }
  // 播放紧急报警声音
  if (bfglob.userInfo.setting.alarmVoice) {
    bfglob.emit('play_alarm_voice')
  }

  // 运行日志
  bfglob.emit('addnote', content)
}

// 交流AC断电报警命令
export function energySavingLampsACPowerOffAlarm(data, linePoint, devData) {
  bfglob.console.log('energySavingLampsACPowerOffAlarm', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  const content = `${parentName} / ${devName} ${i18n.global.t('dataTable.acPowerOffAlarm')}`

  // 运行日志
  bfglob.emit('addnote', content)
}

// 红外设置警戒模式“开启”命令
export function energySavingLampsInfraredSettingAlertModeOn(data, linePoint, devData) {
  bfglob.console.log('energySavingLampsInfraredSettingAlertModeOn', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  const content = `${parentName} / ${devName} ${i18n.global.t('dataTable.turnOnAlertMode')}`

  // 运行日志
  bfglob.emit('addnote', content)
}

// 红外设置警戒模式“关闭”命令
export function energySavingLampsInfraredSettingAlertModeOff(data, linePoint, devData) {
  bfglob.console.log('energySavingLampsInfraredSettingAlertModeOff', data, linePoint, devData)
  // 更新marker
  updateIotDeviceMarkerInfo(devData)

  const [parentName, devName] = getIotDeviceSelfAndParentNames(devData)
  const content = `${parentName} / ${devName} ${i18n.global.t('dataTable.turnOffAlertMode')}`

  // 运行日志
  bfglob.emit('addnote', content)
}

export function genIotCmd(b1, b2) {
  return ((b1 & 0xff) << 8) + (b2 & 0xff)
}

function getHandlerKey(cmd, type) {
  return cmd.toString(16).padStart(4, '0') + type.toString(16).padStart(2, '0')
}

/* 各物联终端指令处理函数映射 */
const IotCmdHandlers = {
  // 人员卡(工牌)上行数据处理函数
  [getHandlerKey(IotCommonCmd.Report, IotDeviceTypes.UserCard)]: userCardInspect,
  [getHandlerKey(IotCommonCmd.GeneralAlarm, IotDeviceTypes.UserCard)]: userCardLowPowerAlarm,
  [getHandlerKey(IotCommonCmd.AntiDismantlingAlarm, IotDeviceTypes.UserCard)]: userCardAntiFoldAlarm,
  [getHandlerKey(IotCommonCmd.EmergencyAlarm, IotDeviceTypes.UserCard)]: userCardEmergencyAlarm,
  [getHandlerKey(IotCommonCmd.OverTemperatureAlarm, IotDeviceTypes.UserCard)]: userCardOverTemperatureZoneAlarm,

  // 物卡上行数据处理函数
  [getHandlerKey(IotCommonCmd.Report, IotDeviceTypes.ThingsCard)]: thingsCardInspect,
  [getHandlerKey(IotCommonCmd.GeneralAlarm, IotDeviceTypes.ThingsCard)]: thingsCardLowPowerAlarm,
  [getHandlerKey(IotCommonCmd.AntiDismantlingAlarm, IotDeviceTypes.ThingsCard)]: thingsCardAntiFoldAlarm,
  [getHandlerKey(IotCommonCmd.EmergencyAlarm, IotDeviceTypes.ThingsCard)]: thingsCardEmergencyAlarm,
  [getHandlerKey(IotCommonCmd.OverTemperatureAlarm, IotDeviceTypes.ThingsCard)]: thingsCardOverTemperatureZoneAlarm,

  // 烟感上行数据处理函数
  [getHandlerKey(IotCommonCmd.Report, IotDeviceTypes.SmokeDetector)]: smokeDetectorInspect,
  [getHandlerKey(IotCommonCmd.GeneralAlarm, IotDeviceTypes.SmokeDetector)]: smokeDetectorLowPowerAlarm,
  [getHandlerKey(IotCommonCmd.AntiDismantlingAlarm, IotDeviceTypes.SmokeDetector)]: smokeDetectorAntiFoldAlarm,
  [getHandlerKey(IotCommonCmd.EmergencyAlarm, IotDeviceTypes.SmokeDetector)]: smokeDetectorEmergencyAlarm,
  [getHandlerKey(SmokeDetectorCmd.Heartbeat, IotDeviceTypes.SmokeDetector)]: smokeDetectorNoSmokeAlarmNormalHeartbeat,
  [getHandlerKey(IotCommonCmd.OverTemperatureAlarm, IotDeviceTypes.SmokeDetector)]: smokeDetectorOverTemperatureZoneAlarm,

  // 温湿度上报
  [getHandlerKey(TemperatureCmd.Report, IotDeviceTypes.TemperatureHumidityDetector)]: temperatureHumidityDetectorReport,

  // 节能灯处理方法集
  [getHandlerKey(IotCommonCmd.Report, IotDeviceTypes.EnergySavingLamps)]: energySavingLampsReport,
  [getHandlerKey(EnergySavingLampsCmd.ACOn, IotDeviceTypes.EnergySavingLamps)]: energySavingLampsACOn,
  [getHandlerKey(EnergySavingLampsCmd.ACOff, IotDeviceTypes.EnergySavingLamps)]: energySavingLampsACOff,
  [getHandlerKey(EnergySavingLampsCmd.SwitchBoxTamperAlarm, IotDeviceTypes.EnergySavingLamps)]: energySavingLampsSwitchBoxTamperAlarm,
  [getHandlerKey(EnergySavingLampsCmd.HumanTriggersAntiTheftAlarm, IotDeviceTypes.EnergySavingLamps)]: energySavingLampsHumanTriggersAntiTheftAlarm,
  [getHandlerKey(EnergySavingLampsCmd.AcousticTriggersAntiTheftAlarm, IotDeviceTypes.EnergySavingLamps)]: energySavingLampsAcousticTriggersAntiTheftAlarm,
  [getHandlerKey(EnergySavingLampsCmd.ACPowerOffAlarm, IotDeviceTypes.EnergySavingLamps)]: energySavingLampsACPowerOffAlarm,
  [getHandlerKey(EnergySavingLampsCmd.InfraredSettingAlertModeOn, IotDeviceTypes.EnergySavingLamps)]: energySavingLampsInfraredSettingAlertModeOn,
  [getHandlerKey(EnergySavingLampsCmd.InfraredSettingAlertModeOff, IotDeviceTypes.EnergySavingLamps)]: energySavingLampsInfraredSettingAlertModeOff,
}

function getIotCmdHandler(cmd, devType) {
  return IotCmdHandlers[getHandlerKey(cmd, devType)]
}

function setupLinePointActiveDevice(linePoint, devData) {
  // 将设备从上个巡查点中移除
  reduceLinePointActiveDevice(linePoint.pointRfid, devData.rid)
  // 将设备更新到新的巡查点上
  addLinePointActiveDevice(linePoint.pointRfid, devData.rid)
}

function updateIotDeviceLastInfo(data, linePoint, devData) {
  switch (data.devType) {
    case IotDeviceTypes.UserCard:
      devData.lastLon = linePoint.lon
      devData.lastLat = linePoint.lat

      linePoint.lastCheckDeviceId = devData.rid
      linePoint.lastCheckUserId = devData.deviceUser || bfutil.DefOrgRid

      // 更新巡查点打卡活动数据
      setupLinePointActiveDevice(linePoint, devData)
      break
    // case IotDeviceTypes.ThingsCard:
    //   devData.lastCmd = data.cmd
    //   break
    // case IotDeviceTypes.SmokeDetector:
    //   devData.lastCmd = data.cmd
    //   break
    // case IotDeviceTypes.TemperatureHumidityDetector:
    //   devData.lastCmd = data.cmd
    //   break
  }

  devData.lastCmd = data.cmd
  devData.lastDataTime = data.recvTime
  devData.lastController = data.kcpRecvDevId

  linePoint.lastCheckTime = data.recvTime
}

/* 接收cmd=3的物联终端指令，入口处理函数*/
export function processIotDataCmd(rpcCmd) {
  const iot_data_type = bfproto.bfdx_proto_msg_T('iot_data', kcpPackageName)
  const body = iot_data_type.decode(rpcCmd.body)
  body.devId = decodeDevId(body.devId)
  body.recvDevId = decodeDevId(body.recvDevId)
  body.recvTime = decodeIotDataTime(body.recvTime)

  const linePoint = bfglob.glinePoints.getDataByIndex(body.recvDevId)
  if (!linePoint) {
    bfglob.console.warn('unknown iot linePoint:', rpcCmd, body)
    return
  }

  const devData = getDevDataByType(body.devId, body.devType)
  if (!devData) {
    bfglob.console.warn('unknown iot device:', rpcCmd, body)
    return
  }

  // 设置最后数据状态
  updateIotDeviceLastInfo(body, linePoint, devData)

  // 找到对应的指令处理方法进行处理
  const handler = getIotCmdHandler(body.cmd, body.devType)
  if (!handler) {
    bfglob.console.warn('unknown iot cmd:', rpcCmd, body)
    return
  }

  // 紧急报警会弹框提示，并需要更新报警历史表，需要历史表的rid
  body.historyRid = rpcCmd.opt

  handler(body, linePoint, devData)
}

const IotCmdLabels = {
  [IotCommonCmd.Report]: () => i18n.global.t('iotDevHistory.report'),
  [IotCommonCmd.GeneralAlarm]: () => i18n.global.t('iotDevHistory.lowPower'),
  [IotCommonCmd.AntiDismantlingAlarm]: () => i18n.global.t('iotDevHistory.remove'),
  [IotCommonCmd.EmergencyAlarm]: () => i18n.global.t('iotDevHistory.emergency'),
  [IotCommonCmd.OverTemperatureAlarm]: () => i18n.global.t('iotDevHistory.overTemperatureAlarm'),
  [SmokeDetectorCmd.Heartbeat]: () => i18n.global.t('iotDevHistory.heartbeat'),
  [TemperatureCmd.Report]: () => i18n.global.t('iot.tempReport'),
  [EnergySavingLampsCmd.ACOn]: () => i18n.global.t('iot.acOn'),
  [EnergySavingLampsCmd.ACOff]: () => i18n.global.t('iot.acOff'),
  [EnergySavingLampsCmd.SwitchBoxTamperAlarm]: () => i18n.global.t('dataTable.antiDismantlingAlarm'),
  [EnergySavingLampsCmd.HumanTriggersAntiTheftAlarm]: () => i18n.global.t('dataTable.infraredSensorAlarm'),
  [EnergySavingLampsCmd.AcousticTriggersAntiTheftAlarm]: () => i18n.global.t('dataTable.soundSensorAlarm'),
  [EnergySavingLampsCmd.ACPowerOffAlarm]: () => i18n.global.t('dataTable.acPowerOffAlarm'),
  [EnergySavingLampsCmd.InfraredSettingAlertModeOn]: () => i18n.global.t('dataTable.turnOnAlertMode'),
  [EnergySavingLampsCmd.InfraredSettingAlertModeOff]: () => i18n.global.t('dataTable.turnOffAlertMode'),
}

export function getIotCmdLabel(cmd) {
  return IotCmdLabels[cmd]?.() ?? (cmd || i18n.global.t('msgbox.unknownCmd'))
}

/* 以下为提示接收新的物联终端，及添加到数据库的操作方法*/
function createBsPointDevice(data) {
  const createHandler = () => {
    bfglob.emit('openMenuItem', 'dataDialog/linePoint', vm => {
      vm.tabsValue = 'add'
      vm.addData = {
        ...vm.addData,
        pointRfid: data.devId,
        pointType: 4,
      }
    })
  }
  const content = `
    <div>${i18n.global.t('dialog.type')}: ${getIotDeviceTypeName(data.devType)}</div>
    <div>${i18n.global.t('iot.devId')}: ${data.devId}</div>
  `
  const contentObj = {
    name: data.devId,
    result: {
      content: content,
      title: i18n.global.t('iot.receiveUnknownIotDevice'),
      btn: true,
      btnCallback: createHandler,
    },
  }
  bfglob.emit('show_bcxx_result', contentObj)
}

function createUserCardDevice(data) {
  const createHandler = () => {
    bfglob.emit('openMenuItem', 'dataDialog/devices', vm => {
      vm.tabsValue = 'add'
      vm.addData = {
        ...vm.addData,
        dmrId: data.devId,
        deviceType: DeviceTypes.UserCard,
      }
    })
  }
  const content = `
    <div>${i18n.global.t('dialog.type')}: ${getIotDeviceTypeName(data.devType)}</div>
    <div>${i18n.global.t('iot.devId')}: ${data.devId}</div>
  `
  const contentObj = {
    name: data.devId,
    result: {
      content: content,
      title: i18n.global.t('iot.receiveUnknownIotDevice'),
      btn: true,
      btnCallback: createHandler,
    },
  }
  bfglob.emit('show_bcxx_result', contentObj)
}

// 物卡、烟感、温度/温度检测使用同一个新终端提示
function createSmokeDetectorDevice(data) {
  const createHandler = () => {
    bfglob.emit('openMenuItem', 'dataDialog/IOT', vm => {
      vm.tabsValue = 'add'
      vm.addData = {
        ...vm.addData,
        devId: data.devId,
        devType: data.devType || IotDeviceTypes.SmokeDetector,
      }
    })
  }
  const content = `
    <div>${i18n.global.t('dialog.type')}: ${getIotDeviceTypeName(data.devType)}</div>
    <div>${i18n.global.t('iot.devId')}: ${data.devId}</div>
  `
  const contentObj = {
    name: data.devId,
    result: {
      content: content,
      title: i18n.global.t('iot.receiveUnknownIotDevice'),
      btn: true,
      btnCallback: createHandler,
    },
  }
  bfglob.emit('show_bcxx_result', contentObj)
}

const CreateIotDeviceHandlers = {
  [IotDeviceTypes.BsPoint]: createBsPointDevice,
  [IotDeviceTypes.UserCard]: createUserCardDevice,
  [IotDeviceTypes.ThingsCard]: createSmokeDetectorDevice,
  [IotDeviceTypes.SmokeDetector]: createSmokeDetectorDevice,
  [IotDeviceTypes.TemperatureHumidityDetector]: createSmokeDetectorDevice,
  [IotDeviceTypes.EnergySavingLamps]: createSmokeDetectorDevice,
}

const IotDeviceTypeName = {
  [IotDeviceTypes.BsPoint]: () => i18n.global.t('dialog.BaseStationPoint'),
  [IotDeviceTypes.UserCard]: () => i18n.global.t('iot.iotInspectDevice'),
  [IotDeviceTypes.ThingsCard]: () => i18n.global.t('iot.thingsCard'),
  [IotDeviceTypes.SmokeDetector]: () => i18n.global.t('iot.smokeDetector'),
  [IotDeviceTypes.TemperatureHumidityDetector]: () => i18n.global.t('iot.tempDetector'),
  [IotDeviceTypes.EnergySavingLamps]: () => i18n.global.t('iot.energySavingLamps'),
}

export function getIotDeviceTypeName(devType) {
  return IotDeviceTypeName[devType]?.() ?? devType
}

/* 接收新的物联终端数据指令 */
export function newIotDeviceCmd(rpcCmd) {
  const unknown_iot_data_type = bfproto.bfdx_proto_msg_T('unknown_iot_device_cmd')
  const body = unknown_iot_data_type.decode(rpcCmd.body)
  bfglob.console.warn('newIotDeviceCmd:', rpcCmd, body)
  const handler = CreateIotDeviceHandlers[body.devType]
  if (!handler) {
    return
  }

  handler(body)

  // 运行日志
  const content = `${i18n.global.t('iot.receiveUnknownIotDevice')} ${i18n.global.t('iot.devId')}: ${body.devId},
  ${i18n.global.t('dialog.type')}: ${getIotDeviceTypeName(body.devType)}`
  bfglob.emit('addnote', content)
}

// 温度、温度字节处理，高字节为整数，低字节为小数(bit7==1时为负数)，保留1位小数
export function decodeTemHum([high, low]) {
  // 符号位
  const sign = low >> 7 === 1 ? -1 : 1
  // 计算小数部分
  const lowVal = (low & 0x7f) / 10

  return (high + lowVal) * sign
}

export function encodeTemHum(value) {
  const val = Math.abs(value)
  // 符号位
  const sign = value < 0 ? 0x80 : 0
  // 高位
  const high = ~~val & 0xff
  // 低位
  const low = ((val - high) * 10) & 0xff

  return [high, low + sign]
}

export function decodeTemp([high, low]) {
  return decodeTemHum([high, low])
}

export function encodeTemp(value) {
  return encodeTemHum(value)
}

export function getTempLabel(value) {
  return value ? `${value.toFixed(1)}°C` : ''
}

export function decodeHumi([high, low]) {
  return decodeTemHum([high, low])
}

export function encodeHumi(value) {
  return encodeTemHum(value)
}

export function getHumiLabel(value) {
  return value ? `${value.toFixed(1)}%` : ''
}

export function decodeTempHumiToIotDevice(device, cmdParam = [0, 0, 0, 0]) {
  const tempBytes = cmdParam.slice(0, 2)
  const humiBytes = cmdParam.slice(2, 4)
  device.temp = decodeTemp(tempBytes)
  device.humi = decodeHumi(humiBytes)
}
