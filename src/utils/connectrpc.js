import { createPromiseClient } from '@connectrpc/connect'
import { createConnectTransport } from '@connectrpc/connect-web'

const transport = createConnectTransport({
  baseUrl: '/',
  useBinaryFormat: true,
  // credentials: 'include',
})
/**
 * @param Service connectrpc Service which is defined in a proto file
 * @return connectrpc client with Service
 */
export function createClient(Service) {
  return createPromiseClient(Service, transport)
}

/**
 * @return headers with sysid and sid
 */
export function createHeaders() {
  const headers = new Headers()
  headers.set('sysid', bfglob.sysId)
  headers.set('sid', bfglob.sessionId)
  return headers
}
