import qwebchannel from 'qwebchannel'
import i18n from '@/modules/i18n'
import bfNotify from './notify'
import { reactive } from 'vue'

let socket = null
let connectCount = 0

const QWebChannel = reactive({
  url: 'ws://127.0.0.1:32100',
  server: null,
  reInitServer(timeInt = 3 * 1000) {
    setTimeout(() => {
      this.initServer()
    }, timeInt)
  },
  initServer(url = this.url) {
    bfglob.console.log('qwebchannel url:', url, socket)
    if (
      socket &&
      (socket.readyState === socket.CONNECTING ||
        socket.readyState === socket.OPEN)
    ) {
      return
    }

    socket = new WebSocket(url)
    socket.onclose = () => {
      if (connectCount < 1) {
        bfNotify.messageBox(
          i18n.global.t('msgbox.UsbServerNotConnected'),
          'warning',
        )
      }
      connectCount++
      this.reInitServer()
    }
    socket.onopen = () => {
      connectCount = 0
      new qwebchannel.QWebChannel(socket, channel => {
        const showVersionWarningInfo = (requiredVersion, version) => {
          const trArgs = {
            required: requiredVersion,
            curVersion: version,
          }
          bfNotify.warningBox(
            i18n.global.t('msgbox.updateUsbServerVersion', trArgs),
            'warning',
          )
        }
        if (channel && channel.objects) {
          // 检测坐席服务程序版本
          channel.objects.dialog &&
            channel.objects.dialog.sl_pro_version(version => {
              bfglob.console.log('sl_pro_version:', version)
              const requiredVersion = 20200703
              if (version < requiredVersion) {
                showVersionWarningInfo(requiredVersion, version)
              } else {
                this.server = channel.objects.dialog
                // 监听服务器正常关闭的信号
                this.server.sg_server_exit.connect(() => {
                  bfglob.console.log('QWebChannel server exit')
                  bfglob.emit('QWebChannelServerExit')
                  this.server = null
                  socket = null
                  this.reInitServer()
                })
              }
            })
        } else {
          bfNotify.warningBox(
            i18n.global.t('msgbox.UsbServerConnectError'),
            'error',
          )
        }
      })
    }
  },
  serverClose() {
    socket.close()
  },
})

QWebChannel.initServer()

export default QWebChannel
