import { processInfoReport } from '@/writingFrequency/repeater/dz1480'
import { processSvtRepeaterInfoReport } from '@/writingFrequency/repeater/DZ148SVT'
import dayJs from 'dayjs'
import { cloneDeep, throttle } from 'lodash'
import i18n from '@/modules/i18n'
import bfproto, { defPackageName, kcpPackageName, ManualSyncOperations, SyncDataMap } from '@/modules/protocol'
import dbCmd from '@/modules/protocol/db.pb.cmd'
import bftree, { getDeviceChannel } from '@/utils/bftree'
import bfutil, {
  assignFixedDeviceLonLat,
  getDbSubject,
  hex2bin,
  MapMarkerTypes,
  randomOffsetLocation,
  toHexDmrId,
  getCommonOrgType,
  deferred,
} from '@/utils/bfutil'
import bfCrypto from '@/utils/crypto'
import bfNotify, { Types, warningBoxWithOption } from '@/utils/notify'
import bfTime, { getUtcTimeString, isSameOrAfter, utcToLocalTimeFormat, nowUtcTime, timeIsBefore, addTime, DateMask } from '@/utils/time'
import { v1 as uuid } from 'uuid'
import {
  checkPcDeviceExitDynamicGroup,
  checkPcDeviceJoinDynamicGroup,
  getGroupSubject,
  MemberState,
  processDetailList,
  processDynamicGroup,
  requestAllDynamicGroup,
  requestDynamicGroupStatus,
  saveDynamicGroupDetail,
} from './dynamicGroup/api'
import { decodeTempHumiToIotDevice, newIotDeviceCmd, processIotDataCmd, TemperatureCmd, updateIotDeviceMarkerInfo } from './iot'
import maputil, { deferCloseMarkerPopup, get_popup_content_for_iot_device, mapFlyTo, showIotMarker } from './map'
import bfUserSettings from '@/utils/userSettings'
import { h, ref } from 'vue'

const orgIconCls = 'icon-organize'
const deviceIconCls = 'icon-interphone'

let dbSubject = `db.${bfglob.sysId}`
let radioSubject = `radio.${bfglob.sysId}`
let isListenCmd83 = false
let orgNodesIsLoaded = false

export function updateGlobalUserSettingEffect(settings, oldSettings) {
  maputil.show_or_hide_mapLinePointNames()
  maputil.show_or_hide_ctrlMarker()
  maputil.checkDeviceMarkerDisplayStatus()
  showIotMarker()

  if (oldSettings.fancytreeSortType !== settings.fancytreeSortType) {
    bfglob.emit('treeSortChildren')
  }
}

export default {
  g_server_command_time: new Date(),
  temp_orgLists: [],
  /**
   * 发布表格数据变更事件
   * @param {string} tableName - 表格名称
   * @param {string} action - 操作类型: 'add', 'update', 'delete'
   * @param {DataRow} rowData - 行数据
   */
  publishTableEvent(tableName, action, rowData) {
    if (!tableName) {
      return
    }
    const eventName = `${action}-${tableName}`
    bfglob.emit(eventName, rowData)
  },
  test_lastDataTime_of_show_marker() {
    if (!bfglob.isLogin) {
      return
    }
    maputil.checkDeviceMarkerDisplayStatus()
  },
  loginedAfterFunc() {
    // 重置命令主题
    dbSubject = `db.${bfglob.sysId}`
    radioSubject = `radio.${bfglob.sysId}`
    setInterval(this.ping_server, 3 * 60 * 1000)

    bfglob.userInfo.isSuper = bfutil.isRootUser()

    bfglob.server.subscribe(bfglob.sessionId, this.OnServerCommand)
    bfglob.server.subscribe(bfglob.sysId + '.new_controller', this.OnServerCommand)
    bfglob.server.subscribe('conf.' + bfglob.sysId, this.OnServerCommand)
    bfglob.server.subscribe('cdc.' + bfglob.sysId, this.OnServerCommand)
    if (bfglob.sysIniConfig.iotEnable) {
      bfglob.server.subscribe(bfglob.sysId + '.unknown_iot_device', this.OnServerCommand)
    }

    // 请求账号权限
    this.get_login_user_setting(bfglob.userInfo.rid, dbCmd.DB_USER_GETBY)
    this.get_privilege_request(bfglob.sessionId, bfglob.userInfo.rid)

    bfglob.once('cancel-login-loading', () => {
      this.queryNoPermissionUnitByDmrId()
    })

    // 获取系统 logo saler_id 滚动文字
    this.getSysLogoAndTitle('clientLogo', dbCmd.DB_SYS_CONFIG_GETBY)
    this.getSysLogoAndTitle('saler_id', dbCmd.DB_SYS_CONFIG_GETBY)
    this.getSysLogoAndTitle('clientTitle', dbCmd.DB_SYS_CONFIG_GETBY)

    // 设置地图的最大、最小级别限制
    bfglob.setMapLevelConfig = (maxZoom, minZoom) => {
      if (!bfglob.isLogin) {
        bfglob.console.warn('[setMapLevelSysConfig]:请先登录再试!')
        return
      }

      if (!maxZoom || maxZoom > 23) {
        maxZoom = 23
      }
      if (!minZoom || minZoom < 1.5) {
        minZoom = 1.5
      }

      const setting = JSON.stringify({
        maxZoom: Math.max(maxZoom, minZoom),
        minZoom: Math.min(maxZoom, minZoom),
      })
      const cmd = bfglob.mapSetting.signale ? dbCmd.DB_SYS_CONFIG_UPDATE : dbCmd.DB_SYS_CONFIG_INSERT
      const rid = bfglob.mapSetting.signale ? bfglob.mapSetting.rid : uuid()

      this.saveSysLogoAndTitle(setting, cmd, 'mapLevel', rid)
    }

    // 设置定时器，检查对讲机地图标记图标是否显示,300000/5 * 60 * 1000/5分钟一次
    setInterval(() => {
      this.test_lastDataTime_of_show_marker()
    }, 300000)

    this.requestMapToken()
    setInterval(
      () => {
        this.requestMapToken()
      },
      9 * 60 * 1000
    )

    // 添加登录成功日志
    var note = bfglob.userInfo.name + ' ' + i18n.global.t('loginDlg.loginSuccess')
    bfglob.emit('addnote', note)
  },
  requestMapToken() {
    const options = {
      encodePackageName: defPackageName,
    }

    bfproto
      .sendMessage(1440, null, null, getDbSubject(), options)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          bfglob.bftk = rpc_cmd_obj.optInt
          bfglob.emit('bftkReady')
        }
      })
      .catch(err => {
        bfglob.console.warn('requestMapToken catch:', err)
      })
  },
  ping_server() {
    if (this.g_server_command_time < new Date()) {
      bfglob.server.sendCommand('PING\r\n')
    }
  },
  OnServerCommand(msg_data, _msg_reply, _msg_subject, _nats_ssid) {
    this.g_server_command_time = new Date()
    try {
      var rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
      // bfglob.console.log('server cmd', rpc_cmd_obj)

      // 需要过滤自己的操作的数据
      if (bfglob.cmdReqId.has(rpc_cmd_obj.origReqId)) {
        bfglob.console.log('[OnServerCommand] self operation', rpc_cmd_obj)
        bfglob.cmdReqId.delete(rpc_cmd_obj.origReqId)
        return
      }

      if (rpc_cmd_obj.reqOrResponse === 2) {
        bfglob.emit(rpc_cmd_obj.origReqId + '', rpc_cmd_obj)
      } else {
        bfglob.emit(rpc_cmd_obj.command + '', rpc_cmd_obj)
      }
    } catch (e) {
      bfglob.console.error('got server cmd process err', e)
    }
  },
  bfdx_subscribe_broadcast_cmd() {
    const orgs = bfglob.gorgData.getAll()
    for (var i in orgs) {
      bfglob.server.subscribe(orgs[i].rid, this.OnServerCommand)
    }
  },
  // 订阅数据操作消息
  listenBaseDataChanged() {
    var that = this
    // 全局单位数据管理
    var add_global_orgData = data => {
      if (!data) {
        return
      }

      bfglob.server.subscribe(data.rid, that.OnServerCommand)

      that.setGlobOrgData(data)

      bfglob.emit('vorgs_table_add_data', data)

      if (getCommonOrgType().includes(data.orgIsVirtual)) {
        // 发布表格数据变更事件
        that.publishTableEvent('orgsTable', 'add', data)
      }

      // 更新组织树列表
      bftree.syncAddOrgNode('bftree', data)
    }
    bfglob.on('add_global_orgData', add_global_orgData)

    var delete_device_parentVirOrgs = orgRid => {
      var devices = bfglob.gdevices.getAll()
      for (var k in devices) {
        const __device = devices[k]
        if (__device.virOrgs) {
          var virOrgs = __device.virOrgs.split(',')
          var virOrgsNames = __device.virOrgsName.split(',')
          for (var z = 0; z < virOrgs.length; z++) {
            if (virOrgs[z] === orgRid) {
              virOrgs.splice(z, 1)
              virOrgsNames.splice(z, 1)
              z--
            }
          }
          __device.virOrgs = virOrgs.join(',')
          __device.virOrgsName = virOrgsNames.join(',')
        }
      }
      bfglob.emit('vdevices_redraw_table')
    }
    var update_global_orgData = data => {
      const oldData = bfglob.gorgData.get(data.rid)
      // 删除旧的索引
      bfglob.gorgData.deleteIndexByKey(oldData.dmrId)
      const t = bfproto.bfdx_proto_msg_T('db_org')
      const d = t.create(data)
      data = Object.assign(d, oldData, data)
      that.setGlobOrgData(data)

      if (oldData.virtual === 1 && data.orgIsVirtual === 2) {
        // 单位性质从虚拟转为真实单位后，删除原虚拟单位下属对讲机数据
        delete_device_parentVirOrgs(data.rid)
        bftree.removeNodeChildren('bftree', data.rid)
      }

      // 更新组织树列表
      bftree.syncUpdateOneOrgNode('bftree', data)

      // 单位的dmrId变化后，需要同步设备数据的信道配置
      if (oldData.dmrId !== data.dmrId) {
        bfglob.emit('update_device_channel_config', data, 1)
      }

      bfglob.emit('vorgs_table_update_data', data)

      if (getCommonOrgType().includes(data.orgIsVirtual)) {
        // 发布表格数据变更事件
        that.publishTableEvent('orgsTable', 'update', data)
      }
    }
    bfglob.on('update_global_orgData', update_global_orgData)

    var delete_global_orgData = data => {
      bfglob.gorgData.delete(data.rid)
      // 删除组织树列表单位节点
      bftree.syncDelOneOrgNode('bftree', data.rid)
      // 删除单位后，需要同步更新所有设备的信的发射组和收听组配置
      bfglob.emit('update_device_channel_config', data)
      // 发布删除数据的消息
      bfglob.emit('vorgs_table_delete_data', data.rid)

      // 发布表格数据变更事件 真实群组才需要去orgTable 删除
      if (getCommonOrgType().includes(data.orgIsVirtual)) {
        that.publishTableEvent('orgsTable', 'delete', data)
        const mapPointData = bfglob.gorgData.getOrgMapMakerPoint(data.rid)
        if (mapPointData) {
          this.publishTableEvent('mapPointsTable', 'delete', mapPointData)
        }
      }
      // 如果删除的单位是当前登录用户的上级，则提示用户，然后跳转到登录页
      if (data.rid === bfglob.userInfo.orgId) {
        const panicLogoutSeconds = ref(10)
        let timer = null
        const logout = () => {
          clearInterval(timer)
          timer = null
          window.location.reload(true)
        }
        ElMessageBox({
          title: i18n.global.t('syncCenter.warning'),
          message: () =>
            h('div', [
              h('p', null, i18n.global.t('syncCenter.orgDeleteAndLogout')),
              h(
                'p',
                {
                  class: 'text-center text-red-600 text-xl',
                },
                `${panicLogoutSeconds.value}s`
              ),
            ]),
          confirmButtonText: i18n.global.t('dialog.confirm'),
          type: 'error',
          center: true,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          showClose: false,
        }).finally(() => {
          logout()
        })
        timer = setInterval(() => {
          panicLogoutSeconds.value--
          if (panicLogoutSeconds.value <= 0) {
            logout()
          }
        }, 1000)
      }
    }
    bfglob.on('delete_global_orgData', delete_global_orgData)

    // 动态组数据同步，实际上也是db_org数据
    const add_global_dynamic_group = data => {
      this.setGlobOrgData(data)
      bfglob.emit('add_one_dynamic_group', data)
    }
    bfglob.on('add_global_dynamic_group', add_global_dynamic_group)

    const update_global_dynamic_group = data => {
      bfglob.emit('update_global_orgData', data)
      bfglob.emit('update_one_dynamic_group', data)
    }
    bfglob.on('update_global_dynamic_group', update_global_dynamic_group)

    const delete_global_dynamic_group = data => {
      bfglob.emit('delete_global_orgData', data)
      bfglob.gdynamicGroupDetail.deleteDataByGroupRid(data.rid)
      bfglob.emit('delete_one_dynamic_group', data)

      // 日志
      setTimeout(() => {
        const content = i18n.global.t('dialog.delete') + data.orgShortName + i18n.global.t('dynamicGroup.title')
        bfglob.emit('addnote', content)
      }, 0)
    }
    bfglob.on('delete_global_dynamic_group', delete_global_dynamic_group)

    // 同步动态组下的成员
    const add_global_dynamic_group_detail = dataList => {
      for (let i = 0; i < dataList.length; i++) {
        const data = dataList[i]
        saveDynamicGroupDetail(data)
        bfglob.emit('add_one_dynamic_group_detail', data)
      }
    }
    bfglob.on('add_global_dynamic_group_detail', add_global_dynamic_group_detail)

    const update_global_dynamic_group_detail = dataList => {
      for (let i = 0; i < dataList.length; i++) {
        const data = dataList[i]
        const oldDetail = bfglob.gdynamicGroupDetail.get(data.rid)
        saveDynamicGroupDetail(data)
        bfglob.emit('update_one_dynamic_group_detail', data, oldDetail)
      }
    }
    bfglob.on('update_global_dynamic_group_detail', update_global_dynamic_group_detail)

    const removeOneDynamicGroupDetail = detail => {
      bfglob.emit('delete_one_dynamic_group_detail', detail)
      // setTimeout(() => {
      bfglob.gdynamicGroupDetail.delete(detail.rid)
      // }, 0)
    }
    const remove_global_dynamic_group_detail = (dataList, force) => {
      const canRemoveStatus = [5, 11]
      // 非删除状态，则更新节点
      const unRemoveList = []

      for (let i = 0; i < dataList.length; i++) {
        const data = dataList[i]
        // force标记为服务器通知要删除的，即不再管成员状态
        if (force) {
          removeOneDynamicGroupDetail(data)
          continue
        }

        // 因收听组被拉入任务组，不能删除，等待接收听组成员的删除
        // 任务组：1:正常/已应答加入，2：被优先级高的抢占， 4:未应答加入 5:已应答退出 6:未应答退出
        if (canRemoveStatus.includes(data.memberState)) {
          removeOneDynamicGroupDetail(data)
          continue
        }

        unRemoveList.push(data)
      }

      unRemoveList.length && bfglob.emit('update_global_dynamic_group_detail', unRemoveList)
    }
    bfglob.on('remove_global_dynamic_group_detail', remove_global_dynamic_group_detail)

    // 全局职位数据管理
    var add_global_jobData = data => {
      that.setGlobUsetTitleData(data)
      bfglob.emit('vjobs_table_add_data', data)
      this.publishTableEvent('jobsTable', 'add', data)
    }
    bfglob.on('add_global_jobData', add_global_jobData)

    var update_global_jobData = data => {
      const oldData = bfglob.gjobsData.get(data.rid)
      // 删除旧的索引
      bfglob.gjobsData.deleteIndexByKey(oldData.titleName)
      const t = bfproto.bfdx_proto_msg_T('db_user_title')
      const d = t.create(data)
      data = Object.assign(d, oldData, data)
      that.setGlobUsetTitleData(data)
      bfglob.emit('vjobs_table_update_data', data)
      this.publishTableEvent('jobsTable', 'update', data)
    }
    bfglob.on('update_global_jobData', update_global_jobData)

    var delete_global_jobData = data => {
      bfglob.gjobsData.delete(data.rid)
      // 发布删除数据的消息
      bfglob.emit('vjobs_table_delete_data', data.rid)
      this.publishTableEvent('jobsTable', 'delete', data)
    }
    bfglob.on('delete_global_jobData', delete_global_jobData)

    // 全局用户数据管理
    var add_global_userData = data => {
      that.setGlobUserData(data)
      bfglob.emit('vusers_table_add_data', data)

      // 发布表格数据变更事件
      that.publishTableEvent('usersTable', 'add', data)
    }
    bfglob.on('add_global_userData', add_global_userData)

    var update_global_userData = data => {
      const oldData = bfglob.guserData.get(data.rid)
      // 删除旧的索引
      bfglob.guserData.deleteIndexByKey(oldData.userRfid)
      const t = bfproto.bfdx_proto_msg_T('db_user')
      const d = t.create(data)
      data = Object.assign(d, oldData, data)
      that.setGlobUserData(data)
      bfglob.emit('vusers_table_update_data', data)

      // 发布表格数据变更事件
      that.publishTableEvent('usersTable', 'update', data)
    }
    bfglob.on('update_global_userData', update_global_userData)

    var delete_global_userData = data => {
      bfglob.guserData.delete(data.rid)
      // 发布删除数据的消息
      bfglob.emit('vusers_table_delete_data', data.rid)

      // 发布表格数据变更事件
      that.publishTableEvent('usersTable', 'delete', data)
    }
    bfglob.on('delete_global_userData', delete_global_userData)

    var pupdate_global_userData = data => {
      const userData = bfglob.guserData.get(data.rid)
      if (!userData) {
        return
      }
      Object.assign(userData, data)

      try {
        // 只有相同的登录用户，才同步设置
        if (data.rid !== bfglob.userInfo.rid) {
          return
        }

        const oldSettings = cloneDeep(bfglob.userInfo.setting)
        bfglob.userInfo.setting = JSON.parse(userData.userSetting)
        const userSettingkeys = Object.keys(bfUserSettings)
        // 用户个人设置参数有变更，才触发相关的设置切换功能
        if (userSettingkeys.some(key => oldSettings[key] !== bfglob.userInfo.setting[key])) {
          updateGlobalUserSettingEffect(bfglob.userInfo.setting, oldSettings)
          bfglob.emit('update_user_settings', bfglob.userInfo.setting, oldSettings)
        }
      } catch (error) {
        bfglob.console.error('pupdate_global_userData parse userSetting error:', error)
      }

      // 按更新数据处理
      bfglob.emit('update_global_userData', userData)
      this.publishTableEvent('usersTable', 'update', userData)
    }
    bfglob.on('pupdate_global_userData', pupdate_global_userData)

    // 全局用户权限数据，权限是以数组的方式储存
    const setPrivelege = data => {
      const privelege = bfglob.guserData.getPrivelege(data.userRid) || []
      privelege.push(data)
      bfglob.guserData.setPrivelege(data.userRid, privelege)
    }
    const add_global_db_user_privelege = data => {
      setPrivelege(data)
      bfglob.emit('add_global_user_privelege', data)
    }
    bfglob.on('add_global_db_user_privelege', add_global_db_user_privelege)
    const update_global_db_user_privelege = data => {
      setPrivelege(data)
      bfglob.emit('update_global_user_privelege', data)
    }
    bfglob.on('update_global_db_user_privelege', update_global_db_user_privelege)
    const delete_global_db_user_privelege = data => {
      const privelege = bfglob.guserData.getPrivelege(data.userRid) || []
      bfglob.guserData.setPrivelege(
        data.userRid,
        privelege.filter(item => item.rid !== data.rid)
      )
      bfglob.emit('delete_global_user_privelege', data)
    }
    bfglob.on('delete_global_db_user_privelege', delete_global_db_user_privelege)

    // 全局设备数据管理
    // 找到对应的用户，更新该用户的联网通话配置数据
    const updateVoipSpeakInfoConf = (rid = '', speaker = '', priority = 0) => {
      const userItem = bfglob.guserData.get(rid)
      if (!userItem) {
        return
      }
      const setting = JSON.parse(userItem.userSetting)
      const voipSpeakInfo = typeof setting.voipSpeakInfo === 'string' ? JSON.parse(setting.voipSpeakInfo) : setting.voipSpeakInfo || {}
      voipSpeakInfo.speaker = speaker
      voipSpeakInfo.priority = priority
      setting.voipSpeakInfo = voipSpeakInfo
      setting.ispUdateVoipSpeakInfo = true

      const setting_str = JSON.stringify(setting)
      this.updateUserSetting(setting_str, dbCmd.DB_USER_PUPDATE, rid, userItem.orgId)
      if (rid === bfglob.userInfo.rid) {
        Object.assign(bfglob.userInfo.setting.voipSpeakInfo, voipSpeakInfo)
        this.noticeServerUserPriorityChange()
        bfglob.emit('reset_voipSpeakInfo_speaker', speaker, priority)
      }
    }
    // 重置对应用户的联网通话配置
    const resetVoipSpeakInfoSpeaker = (deviceUser, priority) => {
      let speaker = ''
      const globDevices = bfglob.gdevices.getAll()
      for (const k in globDevices) {
        const deviceItem = globDevices[k]
        if (deviceItem.deviceType !== 2) {
          continue
        }
        if (deviceItem.deviceUser === deviceUser) {
          speaker = deviceItem.dmrId
          break
        }
      }
      updateVoipSpeakInfoConf(deviceUser, speaker, priority)
    }

    var add_global_deviceData = data => {
      data.updateAt = bfTime.nowUtcTime()
      data.lastDataTime = '2000-01-01 00:00:00'
      data.lastRfidTime = '2000-01-01 00:00:00'
      data.lastGpsTime = '2000-01-01 00:00:00'
      data.lastRfidPerson = '00000000-0000-0000-0000-000000000000'
      data.lastRfid = ''
      data.lastLon = 1000
      data.lastLat = 1000
      data.deviceLockState = 0
      data.lastController = ''
      data.deviceUserName = data.userName = bfglob.gdevices.getUserNameByKey(data.deviceUser)
      data.msStatus = '000000000000'
      data.msStatusBin = [0, 0, 0, 0, 0, 0]

      that.setGlobDeviceData(data)
      bfglob.emit('vdevices_table_add_data', data)

      // 发布表格数据变更事件
      that.publishTableEvent('devicesTable', 'add', data)

      // 将对讲机数据添加到列表树中
      bftree.add_one_device_to_tree('bftree', data)

      // data.deviceUser && data.deviceUser !== "00000000-0000-0000-0000-000000000000"
      if (data.deviceType === 2) {
        updateVoipSpeakInfoConf(data.deviceUser, data.dmrId, data.priority)
      }
    }
    bfglob.on('add_global_deviceData', add_global_deviceData)

    var update_global_deviceData = data => {
      const oldData = bfglob.gdevices.get(data.rid)
      // 删除旧的索引
      bfglob.gdevices.deleteIndexByKey(oldData.dmrId)
      const priorityChanged = oldData.priority !== data.priority
      const t = bfproto.bfdx_proto_msg_T('db_device')
      const d = t.create(data)
      data = Object.assign(d, oldData, data)
      that.setGlobDeviceData(data)
      bfglob.emit('vdevices_table_update_data', data)

      // 发布表格数据变更事件
      that.publishTableEvent('devicesTable', 'update', data)

      bftree.update_device_node_of_tree('bftree', data)

      // 更新 marker 名称和 popup 内容
      const marker = bfglob.gdevices.getMarker(data.rid)
      const content = maputil.get_popup_content_for_device(data)
      marker && marker.updateMapName(data.selfId).updatePopup(content)

      // 如果dmrId变更，且是指挥坐席时也同步更新联网通话 speaker 参数
      if (oldData.dmrId !== data.dmrId && data.deviceType === 2) {
        updateVoipSpeakInfoConf(data.deviceUser, data.dmrId, data.priority)
      }

      // 从其他设备类型变更为指挥坐席
      if (oldData.deviceType !== data.deviceType && data.deviceType === 2) {
        updateVoipSpeakInfoConf(data.deviceUser, data.dmrId, data.priority)
      }
      // 从指挥坐席变更为其他设备类型
      if (oldData.deviceType !== data.deviceType && oldData.deviceType === 2) {
        resetVoipSpeakInfoSpeaker(oldData.deviceUser, data.priority)
      }

      if (oldData.deviceUser !== data.deviceUser) {
        resetVoipSpeakInfoSpeaker(oldData.deviceUser, data.priority)
        updateVoipSpeakInfoConf(data.deviceUser, data.dmrId, data.priority)
      }

      // 优先级变化，同步到指定的用户的配置中
      if (data.deviceUser && priorityChanged) {
        updateVoipSpeakInfoConf(data.deviceUser, data.dmrId, data.priority)
      }
    }
    bfglob.on('update_global_deviceData', update_global_deviceData)

    var delete_global_deviceData = data => {
      // 如果删除的设备为中心通话专用，则重置中心通话专用设备DMRID
      bfglob.gdevices.delete(data.rid)
      // 发布删除数据的消息
      bfglob.emit('vdevices_table_delete_data', data.rid, data)

      // 发布表格数据变更事件
      that.publishTableEvent('devicesTable', 'delete', data)

      bftree.delete_device_node_of_tree('bftree', data)
      bfglob.gdevices.deleteMarker(data.rid)

      // data.deviceUser && data.deviceUser !== "00000000-0000-0000-0000-000000000000"
      if (data.deviceType === 2) {
        resetVoipSpeakInfoSpeaker(data.deviceUser, data.priority)
      }
    }
    bfglob.on('delete_global_deviceData', delete_global_deviceData)

    const pupdate_global_deviceData = data => {
      if (!data) {
        return
      }
      const device = bfglob.gdevices.get(data.rid)
      if (!device) {
        return
      }

      try {
        device.channel = data.channel
        device.channels = bfutil.sortChannels(JSON.parse(data.channel).channels ?? [])
        device.channelLastModifyTime = data.channelLastModifyTime
        device.lastRfConfigTime = data.lastRfConfigTime
      } catch (_) {
        // no-empty
      }

      if (device.dmrId === this.speakInfo?.speaker) {
        this.gotDeviceUpdate(device)
      }

      bfglob.emit('update_global_deviceData', device)
      // 发布信道设置变更事件
      bfglob.emit('device_channel_changed', device)
    }
    bfglob.on('pupdate_global_deviceData', pupdate_global_deviceData)

    // 全局巡查点数据管理
    var add_global_linePointData = data => {
      that.setGlobLinePointData(data)
      bfglob.emit('vlinePoints_table_add_data', data)

      // 发布表格数据变更事件
      that.publishTableEvent('linePointTable', 'add', data)
    }
    bfglob.on('add_global_linePointData', add_global_linePointData)

    var update_global_linePointData = data => {
      const oldData = bfglob.glinePoints.get(data.rid)
      // 删除旧的索引
      bfglob.glinePoints.deleteIndexByKey(oldData.pointRfid)
      const t = bfproto.bfdx_proto_msg_T('db_line_point')
      const d = t.create(data)
      data = Object.assign(d, oldData, data)
      that.setGlobLinePointData(data)
      bfglob.emit('vlinePoints_table_update_data', data)

      // 发布表格数据变更事件
      that.publishTableEvent('linePointTable', 'update', data)

      // 更新 marker 名称和 popup 内容
      const marker = bfglob.glinePoints.getMarker(data.rid)
      const content = maputil.get_popup_content_for_linePoint(data)
      const lonLat = [data.lon, data.lat]
      marker &&
        marker
          .updateMapName(data.pointName)
          .updateImage({
            type: 2,
            data: data.fileContent,
          })
          .updatePopup(content)
          .setLngLat(lonLat)
    }
    bfglob.on('update_global_linePointData', update_global_linePointData)

    var delete_global_linePointData = data => {
      bfglob.glinePoints.delete(data.rid).deleteMarker(data.rid)
      // 发布删除数据的消息
      bfglob.emit('vlinePoints_table_delete_data', data.rid)

      // 发布表格数据变更事件
      that.publishTableEvent('linePointTable', 'delete', data)
    }
    bfglob.on('delete_global_linePointData', delete_global_linePointData)

    // 请求巡查线路详情数据并发布表格数据变更事件
    const requestLineDetailAndPublishEvent = async (data, eventType) => {
      try {
        await bfutil.sleep(100) // 确保数据库数据已经更新
        // 请求巡查线路详情数据
        await that.get_line_detail_for_lineMast(data, dbCmd.DB_LINE_DETAIL_GETBY)
        // 等待处理完成后发布表格数据变更事件
        that.publishTableEvent('linesTable', eventType, data)
      } catch (err) {
        bfglob.console.warn('请求巡查线路详情数据失败，直接发布表格数据变更事件', err)
        // 如果请求失败，仍然发布表格数据变更事件
        that.publishTableEvent('linesTable', eventType, data)
      }
    }

    // 全局巡查线路数据管理
    var add_global_lineMasterData = data => {
      that.setGlobLineMasterData(data)
      bfglob.emit('vlines_table_add_data', data)

      // 请求巡查线路详情数据，然后发布表格数据变更事件
      requestLineDetailAndPublishEvent(data, 'add')
    }
    bfglob.on('add_global_lineMasterData', add_global_lineMasterData)

    var update_global_lineMasterData = data => {
      const oldData = bfglob.glineMaster.get(data.rid)
      const t = bfproto.bfdx_proto_msg_T('db_line_master')
      const d = t.create(data)
      data = Object.assign(d, oldData, data)
      that.setGlobLineMasterData(data)
      bfglob.emit('vlines_table_update_data', data)
      data.pointData = {}
      bfglob.glineMaster.set(data.rid, data)
      // 请求巡查线路详情数据，然后发布表格数据变更事件
      requestLineDetailAndPublishEvent(data, 'update')
    }
    bfglob.on('update_global_lineMasterData', update_global_lineMasterData)

    var delete_global_lineMasterData = data => {
      bfglob.glineMaster.delete(data.rid)
      // 发布删除数据的消息
      bfglob.emit('vlines_table_delete_data', data.rid)

      // 发布表格数据变更事件
      that.publishTableEvent('linesTable', 'delete', data)
    }
    bfglob.on('delete_global_lineMasterData', delete_global_lineMasterData)

    // 全局巡查线路详情数据
    const add_global_db_line_detail = data => {
      bfutil.handle_linePoint_for_lineMast(data)
      bfglob.emit('add_global_line_detail', data)
    }
    bfglob.on('add_global_db_line_detail', add_global_db_line_detail)
    const update_global_db_line_detail = data => {
      const lineItem = bfglob.glineMaster.get(data.lineId)
      if (!lineItem) {
        return
      }
      const key = 'x' + data.rid
      lineItem.pointData[key].pointNo = data.pointNo
      lineItem.pointData[key].aheadTime = data.aheadTime
      lineItem.pointData[key].delayTime = data.delayTime

      bfglob.emit('update_global_line_detail', data)
    }
    bfglob.on('update_global_db_line_detail', update_global_db_line_detail)
    const delete_global_db_line_detail = data => {
      const lineItem = bfglob.glineMaster.get(data.lineId)
      if (!lineItem || !lineItem.pointData) {
        return
      }

      const key = 'x' + data.lineDetailRid
      delete lineItem.pointData[key]

      bfglob.emit('delete_global_line_detail', data)
    }
    bfglob.on('delete_global_db_line_detail', delete_global_db_line_detail)

    // 全局巡查规则数据管理
    var add_global_ruleMasterData = data => {
      that.setGlobRuleMasterData(data, 2)
      bfglob.emit('vrules_table_add_data', data)
      this.publishTableEvent('rulesTable', 'add', data)
    }
    bfglob.on('add_global_ruleMasterData', add_global_ruleMasterData)

    var update_global_ruleMasterData = data => {
      const oldData = bfglob.gruleMaster.get(data.rid)
      const t = bfproto.bfdx_proto_msg_T('db_rfid_rule_master')
      const d = t.create(data)
      data = Object.assign(d, oldData, data)
      that.setGlobRuleMasterData(data, 2)
      bfglob.emit('vrules_table_update_data', data)
      this.publishTableEvent('rulesTable', 'update', data)
    }
    bfglob.on('update_global_ruleMasterData', update_global_ruleMasterData)

    var delete_global_ruleMasterData = data => {
      bfglob.gruleMaster.delete(data.rid)
      // 发布删除数据的消息
      bfglob.emit('vrules_table_delete_data', data.rid)
      this.publishTableEvent('rulesTable', 'delete', data)
    }
    bfglob.on('delete_global_ruleMasterData', delete_global_ruleMasterData)

    // 全局地图标记点数据管理
    var add_global_mapPointData = data => {
      that.setGlobMapPointData(data)
      bfglob.emit('vmapPoints_table_add_data', data)

      // 发布表格数据变更事件
      that.publishTableEvent('mapPointsTable', 'add', data)
    }
    bfglob.on('add_global_mapPointData', add_global_mapPointData)

    var update_global_mapPointData = data => {
      const oldData = bfglob.gmapPoints.get(data.rid)
      const t = bfproto.bfdx_proto_msg_T('db_map_point')
      const d = t.create(data)
      data = Object.assign(d, oldData, data)
      that.setGlobMapPointData(data)
      bfglob.emit('vmapPoints_table_update_data', data)

      // 发布表格数据变更事件
      that.publishTableEvent('mapPointsTable', 'update', data)

      // 更新 marker 名称和 popup 内容
      const marker = bfglob.gmapPoints.getMarker(data.rid)
      const content = maputil.get_popup_content_for_mapPoint(data)
      const lonLat = [data.lon, data.lat]
      marker &&
        marker
          .updateMapName(data.pointName)
          .updateImage({
            type: 3,
            imgOrColorPoint: data.imgOrColorPoint,
            data: data.imgOrColorPoint === 1 ? data.fileContent : data.colorRGB,
            width: data.imgOrColorPoint === 1 ? data.markerWidth : 16,
            height: data.imgOrColorPoint === 1 ? data.markerHeight : 16,
          })
          .updatePopup(content)
          .setLngLat(lonLat)
    }
    bfglob.on('update_global_mapPointData', update_global_mapPointData)

    var delete_global_mapPointData = data => {
      bfglob.gmapPoints.delete(data.rid).deleteMarker(data.rid)
      // 发布删除数据的消息
      bfglob.emit('vmapPoints_table_delete_data', data.rid)

      // 发布表格数据变更事件
      that.publishTableEvent('mapPointsTable', 'delete', data)
    }
    bfglob.on('delete_global_mapPointData', delete_global_mapPointData)

    // 全局控制器数据管理
    var add_global_controllerData = data => {
      that.setGlobControllerData(data)
      // 添加控制器最后数据默认信息
      data.lastDataTime = '2000-01-01 00:00:00'
      data.connected = 0
      data.ctrlStats = -1

      // 在地图上创建控制器 marker
      const createMapMarker = data => {
        if (bfglob.map) {
          bfglob.gcontrollers.setMarker(
            data.rid,
            maputil.mapMarker({
              type: MapMarkerTypes.Controller,
              id: data.rid,
              markerName: data.selfId,
              className: 'iconfont icon-base-station',
              // className: 'iconfont icon-signal',
              lngLat: [data.lon, data.lat],
              data: data,
            })
          )
          maputil.show_or_hide_ctrlMarker()
        } else {
          setTimeout(() => {
            createMapMarker(data)
          }, 200)
        }
      }
      createMapMarker(data)
      bfglob.emit('vcontrollers_table_add_data', data)
      bfglob.emit('update_controller_stats', data)

      // 发布表格数据变更事件
      that.publishTableEvent('controllersTable', 'add', data)
    }
    bfglob.on('add_global_controllerData', add_global_controllerData)

    var update_global_controllerData = data => {
      const oldData = bfglob.gcontrollers.get(data.rid)
      // 删除旧的索引
      bfglob.gcontrollers.deleteIndexByKey(oldData.dmrId)
      const t = bfproto.bfdx_proto_msg_T('db_controller')
      const d = t.create(data)
      data = Object.assign(d, oldData, data)
      that.setGlobControllerData(data)
      bfglob.emit('vcontrollers_table_update_data', data)
      bfglob.emit('update_controller_stats', data)

      // 发布表格数据变更事件
      that.publishTableEvent('controllersTable', 'update', data)

      // 更新 marker 名称和 popup 内容
      const marker = bfglob.gcontrollers.getMarker(data.rid)
      const content = maputil.get_popup_content_for_controller(data)
      const lonLat = [data.lon, data.lat]
      marker && marker.updateMapName(data.selfId).updatePopup(content).setLngLat(lonLat)
    }
    bfglob.on('update_global_controllerData', update_global_controllerData)

    var delete_global_controllerData = data => {
      bfglob.gcontrollers.delete(data.rid).deleteMarker(data.rid)
      // 发布删除数据的消息
      bfglob.emit('vcontrollers_table_delete_data', data.rid)
      bfglob.emit('update_controller_stats', data)

      // 发布表格数据变更事件
      that.publishTableEvent('controllersTable', 'delete', data)
    }
    bfglob.on('delete_global_controllerData', delete_global_controllerData)

    // 全局短号数据映射管理
    const add_global_db_phone_short_no = data => {
      that.setGlobShortNoData(data)
      bfglob.emit('add_global_short_no', data)
      that.publishTableEvent('shortMumberMappingTable', 'add', data)
    }
    bfglob.on('add_global_db_phone_short_no', add_global_db_phone_short_no)
    const update_global_db_phone_short_no = data => {
      const old_data = bfglob.gshortNo.get(data.rid)
      // 删除旧的索引
      bfglob.gshortNo.deleteIndexByKey(old_data.shortNo)
      const t = bfproto.bfdx_proto_msg_T('db_phone_short_no')
      const d = t.create(data)
      const new_data = Object.assign(d, old_data, data)
      that.setGlobShortNoData(new_data)
      bfglob.emit('update_global_short_no', new_data)
      that.publishTableEvent('shortMumberMappingTable', 'update', new_data)
    }
    bfglob.on('update_global_db_phone_short_no', update_global_db_phone_short_no)
    const delete_global_db_phone_short_no = data => {
      bfglob.gshortNo.delete(data.rid)
      bfglob.emit('delete_global_short_no', data)
      that.publishTableEvent('shortMumberMappingTable', 'delete', data)
    }
    bfglob.on('delete_global_db_phone_short_no', delete_global_db_phone_short_no)

    // 全局网关管理
    const add_global_db_phone_gateway_filter = data => {
      that.setGlobGatewayFilterData(data)
      bfglob.emit('add_global_phone_gateway_filter', data)
      this.publishTableEvent('gatewayTable', 'add', data)
    }
    bfglob.on('add_global_db_phone_gateway_filter', add_global_db_phone_gateway_filter)
    const update_global_db_phone_gateway_filter = data => {
      const old_data = bfglob.gatewayFilter.get(data.rid)
      // 删除旧的索引
      bfglob.gatewayFilter.deleteIndexByKey(old_data.name)
      const t = bfproto.bfdx_proto_msg_T('db_phone_gateway_filter')
      const d = t.create(data)
      const new_data = Object.assign(d, old_data, data)
      that.setGlobGatewayFilterData(new_data)
      bfglob.emit('update_global_phone_gateway_filter', data)
      this.publishTableEvent('gatewayTable', 'update', new_data)
    }
    bfglob.on('update_global_db_phone_gateway_filter', update_global_db_phone_gateway_filter)
    const delete_global_db_phone_gateway_filter = data => {
      bfglob.gatewayFilter.delete(data.rid)
      bfglob.emit('delete_global_phone_gateway_filter', data)
      this.publishTableEvent('gatewayTable', 'delete', data)
    }
    bfglob.on('delete_global_db_phone_gateway_filter', delete_global_db_phone_gateway_filter)

    // 全局网关授权管理
    const add_global_db_phone_gateway_permission = data => {
      that.setGlobGatewayPermissionData(data)
      bfglob.emit('add_global_phone_gateway_permission', data)
      this.publishTableEvent('gatewayPermissionTable', 'add', data)
    }
    bfglob.on('add_global_db_phone_gateway_permission', add_global_db_phone_gateway_permission)
    const update_global_db_phone_gateway_permission = data => {
      const old_data = bfglob.gatewayPermission.get(data.rid)
      // 删除旧的索引
      bfglob.gatewayPermission.deleteIndexByKey(old_data.name)
      const t = bfproto.bfdx_proto_msg_T('db_phone_gateway_permission')
      const d = t.create(data)
      const new_data = Object.assign(d, old_data, data)
      that.setGlobGatewayPermissionData(new_data)
      bfglob.emit('update_global_phone_gateway_permission', data)
      this.publishTableEvent('gatewayPermissionTable', 'update', new_data)
    }
    bfglob.on('update_global_db_phone_gateway_permission', update_global_db_phone_gateway_permission)
    const delete_global_db_phone_gateway_permission = data => {
      bfglob.gatewayPermission.delete(data.rid)
      bfglob.emit('delete_global_phone_gateway_permission', data)
      this.publishTableEvent('gatewayPermissionTable', 'delete', data)
    }
    bfglob.on('delete_global_db_phone_gateway_permission', delete_global_db_phone_gateway_permission)

    // 全局电话网关设备关系管理
    const add_global_db_controller_gateway = data => {
      that.setGlobControllerGatewayData(data)
      bfglob.emit('add_global_controller_gateway', data)
    }
    bfglob.on('add_global_db_controller_gateway', add_global_db_controller_gateway)
    const update_global_db_controller_gateway = data => {
      const old_data = bfglob.gcontrollerGateway.get(data.rid)
      // 删除旧的索引
      bfglob.gcontrollerGateway.deleteIndexByKey(old_data.refDevId)
      const t = bfproto.bfdx_proto_msg_T('db_controller_gateway_manage')
      const d = t.create(data)
      const new_data = Object.assign(d, old_data, data)
      that.setGlobControllerGatewayData(new_data)
      bfglob.emit('update_global_controller_gateway', data)
    }
    bfglob.on('update_global_db_controller_gateway', update_global_db_controller_gateway)
    const delete_global_db_controller_gateway = data => {
      bfglob.gcontrollerGateway.delete(data.rid)
      bfglob.emit('delete_global_controller_gateway', data)
    }
    bfglob.on('delete_global_db_controller_gateway', delete_global_db_controller_gateway)

    // 全局预定义电话簿管理
    const add_global_db_phone_no_list = data => {
      that.setGlobPhoneNoListData(data)
      bfglob.emit('add_global_phone_no_list', data)
      this.publishTableEvent('vpredefinedPhoneBookTable', 'add', data)
    }
    bfglob.on('add_global_db_phone_no_list', add_global_db_phone_no_list)
    const update_global_db_phone_no_list = data => {
      const old_data = bfglob.gphoneBook.get(data.rid)
      // 删除旧的索引
      bfglob.gphoneBook.deleteIndexByKey(old_data.phoneNo)
      const t = bfproto.bfdx_proto_msg_T('db_phone_no_list')
      const d = t.create(data)
      const new_data = Object.assign(d, old_data, data)
      that.setGlobPhoneNoListData(new_data)
      bfglob.emit('update_global_phone_no_list', data)
      this.publishTableEvent('vpredefinedPhoneBookTable', 'update', new_data)
    }
    bfglob.on('update_global_db_phone_no_list', update_global_db_phone_no_list)
    const delete_global_db_phone_no_list = data => {
      bfglob.gphoneBook.delete(data.rid)
      bfglob.emit('delete_global_phone_no_list', data)
      this.publishTableEvent('vpredefinedPhoneBookTable', 'delete', data)
    }
    bfglob.on('delete_global_db_phone_no_list', delete_global_db_phone_no_list)

    // 全局物联网终端数据管理
    const add_global_db_iot_device = data => {
      const newData = that.setGlobalIotDeviceData(data)
      bfglob.emit('add_global_iot_device', newData)
      this.publishTableEvent('iotDevicesTable', 'add', newData)
    }
    bfglob.on('add_global_db_iot_device', add_global_db_iot_device)
    const update_global_db_iot_device = data => {
      const oldData = bfglob.giotDevices.get(data.rid)
      // 删除旧的索引
      bfglob.giotDevices.deleteIndexByKey(oldData.devId)
      const t = bfproto.bfdx_proto_msg_T('db_iot_device')
      const d = t.create(data)
      let newData = Object.assign(d, oldData, data)
      newData = that.setGlobalIotDeviceData(newData)
      // 更新 marker
      const marker = bfglob.giotDevices.getMarker(data.rid)
      if (marker) {
        const content = get_popup_content_for_iot_device(data)
        marker.updateMapName(data.devName).updatePopup(content).setLngLat([data.lon, data.lat])
      }
      bfglob.emit('update_global_iot_device', newData)
      this.publishTableEvent('iotDevicesTable', 'update', newData)
    }
    bfglob.on('update_global_db_iot_device', update_global_db_iot_device)
    const delete_global_db_iot_device = data => {
      bfglob.giotDevices.delete(data.rid)
      bfglob.emit('delete_global_iot_device', data)
      this.publishTableEvent('iotDevicesTable', 'delete', data)
    }
    bfglob.on('delete_global_db_iot_device', delete_global_db_iot_device)

    // 同步更新其他中心的数据
    const convertDbFeildName2CamelCasedName = name => {
      const strs = name.split('_')
      let result = strs[0]
      for (let i = 1; i < strs.length; i++) {
        const str = strs[i]
        result += str[0].toUpperCase() + str.slice(1)
      }
      return result
    }
    const getPupdateDataFromRpcCmdObj = (data, rpc_cmd_obj) => {
      const updatedFeilds = rpc_cmd_obj.resInfo.split(',')
      return updatedFeilds
        .map(key => {
          const feild = convertDbFeildName2CamelCasedName(key.trim())
          return {
            [feild]: data[feild],
          }
        })
        .reduce((p, c) => {
          return Object.assign(p, c)
        }, {})
    }
    const sync_db_org = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_ORG_INSERT]: 'add_global_orgData',
        [dbCmd.DB_ORG_UPDATE]: 'update_global_orgData',
        [dbCmd.DB_ORG_DELETE]: 'delete_global_orgData',
        [dbCmd.DB_ORG_PUPDATE]: 'pupdate_global_orgData',
      }

      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_ORG_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      } else if (operation === dbCmd.DB_ORG_UPDATE) {
        const oldData = bfglob.gorgData.get(data.rid) || {}
        Object.assign(data, {
          oldVirtual: oldData.orgIsVirtual,
          oldShortName: oldData.orgShortName,
          oldDmrId: oldData.dmrId,
        })
      }

      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.org'))
    }
    const sync_db_user_title = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_USER_TITLE_INSERT]: 'add_global_jobData',
        [dbCmd.DB_USER_TITLE_UPDATE]: 'update_global_jobData',
        [dbCmd.DB_USER_TITLE_DELETE]: 'delete_global_jobData',
        [dbCmd.DB_USER_TITLE_PUPDATE]: 'pupdate_global_jobData',
      }

      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_USER_TITLE_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }

      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.userTitle'))
    }
    const sync_db_user = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_USER_INSERT]: 'add_global_userData',
        [dbCmd.DB_USER_UPDATE]: 'update_global_userData',
        [dbCmd.DB_USER_DELETE]: 'delete_global_userData',
        [dbCmd.DB_USER_PUPDATE]: 'pupdate_global_userData',
      }

      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_USER_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }

      bfglob.emit(subjects[operation], data)
      // 忽略更新坐席配置的同步提示 voipSpeakInfo
      const ignoreSyncVoipSpeakInfo = () => {
        if (subjects[operation] === 'pupdate_global_userData') {
          try {
            if (!data.userSetting) {
              return false
            }
            const settings = JSON.parse(data.userSetting ?? '{}')
            return settings.ispUdateVoipSpeakInfo
          } catch (e) {
            bfglob.console.error('pupdate_global_userData JSON.parse err:', e)
            return true
          }
        }
        return false
      }
      if (!ignoreSyncVoipSpeakInfo()) {
        bfNotify.messageBox(i18n.global.t('syncCenter.user'))
      }
    }
    const sync_db_user_privelege = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_USER_PRIVELEGE_INSERT]: 'add_global_db_user_privelege',
        [dbCmd.DB_USER_PRIVELEGE_UPDATE]: 'update_global_db_user_privelege',
        [dbCmd.DB_USER_PRIVELEGE_DELETE]: 'delete_global_db_user_privelege',
        [dbCmd.DB_USER_PRIVELEGE_PUPDATE]: 'pupdate_global_db_user_privelege',
      }

      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_USER_PRIVELEGE_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      // bfNotify.messageBox(i18n.global.t('syncCenter.userPrivelege'))
    }
    const sync_db_device = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_DEVICE_INSERT]: 'add_global_deviceData',
        [dbCmd.DB_DEVICE_UPDATE]: 'update_global_deviceData',
        [dbCmd.DB_DEVICE_DELETE]: 'delete_global_deviceData',
        [dbCmd.DB_DEVICE_PUPDATE]: 'pupdate_global_deviceData',
      }

      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_DEVICE_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      } else if (operation === dbCmd.DB_DEVICE_UPDATE) {
        const oldData = bfglob.gdevices.get(data.rid) || {}
        Object.assign(data, {
          oldDeviceUser: oldData.deviceUser,
          oldDeviceType: oldData.deviceType,
          oldDmrId: oldData.dmrId,
        })
      }

      // if(operation === dbCmd.DB_DEVICE_DELETE){
      //   // 同步删除对应的终端信道数据
      // }else{
      //   // 只要不是删除终端，就重新查找该终端对应的信道数据
      // }

      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.device'))
    }
    const sync_db_line_point = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_LINE_POINT_INSERT]: 'add_global_linePointData',
        [dbCmd.DB_LINE_POINT_UPDATE]: 'update_global_linePointData',
        [dbCmd.DB_LINE_POINT_DELETE]: 'delete_global_linePointData',
        [dbCmd.DB_LINE_POINT_PUPDATE]: 'pupdate_global_linePointData',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_LINE_POINT_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.linePoint'))
    }
    const sync_db_line_master = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_LINE_MASTER_INSERT]: 'add_global_lineMasterData',
        [dbCmd.DB_LINE_MASTER_UPDATE]: 'update_global_lineMasterData',
        [dbCmd.DB_LINE_MASTER_DELETE]: 'delete_global_lineMasterData',
        [dbCmd.DB_LINE_MASTER_PUPDATE]: 'pupdate_global_lineMasterData',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_LINE_MASTER_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.lineMaster'))
    }
    const sync_db_line_detail = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_LINE_DETAIL_INSERT]: 'add_global_db_line_detail',
        [dbCmd.DB_LINE_DETAIL_UPDATE]: 'update_global_db_line_detail',
        [dbCmd.DB_LINE_DETAIL_DELETE]: 'delete_global_db_line_detail',
        [dbCmd.DB_LINE_DETAIL_PUPDATE]: 'pupdate_global_db_line_detail',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_LINE_DETAIL_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      // bfNotify.messageBox(i18n.global.t('syncCenter.lineDetail'))
    }
    const sync_db_rfid_rule_master = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_RFID_RULE_MASTER_INSERT]: 'add_global_ruleMasterData',
        [dbCmd.DB_RFID_RULE_MASTER_UPDATE]: 'update_global_ruleMasterData',
        [dbCmd.DB_RFID_RULE_MASTER_DELETE]: 'delete_global_ruleMasterData',
        [dbCmd.DB_RFID_RULE_MASTER_PUPDATE]: 'pupdate_global_ruleMasterData',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_RFID_RULE_MASTER_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.ruleMaster'))
    }
    const sync_db_map_point = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_MAP_POINT_INSERT]: 'add_global_mapPointData',
        [dbCmd.DB_MAP_POINT_UPDATE]: 'update_global_mapPointData',
        [dbCmd.DB_MAP_POINT_DELETE]: 'delete_global_mapPointData',
        [dbCmd.DB_MAP_POINT_PUPDATE]: 'pupdate_global_mapPointData',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_MAP_POINT_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.mapPoint'))
    }
    const sync_db_controller = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_CONTROLLER_INSERT]: 'add_global_controllerData',
        [dbCmd.DB_CONTROLLER_UPDATE]: 'update_global_controllerData',
        [dbCmd.DB_CONTROLLER_DELETE]: 'delete_global_controllerData',
        [dbCmd.DB_CONTROLLER_PUPDATE]: 'pupdate_global_controllerData',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_CONTROLLER_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.controller'))
    }
    const sync_db_controller_gateway_manage = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_INSERT]: 'add_global_db_controller_gateway',
        [dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_UPDATE]: 'update_global_db_controller_gateway',
        [dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_DELETE]: 'delete_global_db_controller_gateway',
        [dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_PUPDATE]: 'pupdate_global_db_controller_gateway',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      // bfNotify.messageBox(i18n.global.t('syncCenter.controllerGateway'))
    }
    const sync_db_phone_short_no = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_PHONE_SHORT_NO_INSERT]: 'add_global_db_phone_short_no',
        [dbCmd.DB_PHONE_SHORT_NO_UPDATE]: 'update_global_db_phone_short_no',
        [dbCmd.DB_PHONE_SHORT_NO_DELETE]: 'delete_global_db_phone_short_no',
        [dbCmd.DB_PHONE_SHORT_NO_PUPDATE]: 'pupdate_global_db_phone_short_no',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_PHONE_SHORT_NO_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.phoneShortNo'))
    }
    const sync_db_phone_gateway_filter = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_PHONE_GATEWAY_FILTER_INSERT]: 'add_global_db_phone_gateway_filter',
        [dbCmd.DB_PHONE_GATEWAY_FILTER_UPDATE]: 'update_global_db_phone_gateway_filter',
        [dbCmd.DB_PHONE_GATEWAY_FILTER_DELETE]: 'delete_global_db_phone_gateway_filter',
        [dbCmd.DB_PHONE_GATEWAY_FILTER_PUPDATE]: 'pupdate_global_db_phone_gateway_filter',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_PHONE_GATEWAY_FILTER_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.phoneGatewayFilter'))
    }
    const sync_db_phone_gateway_permission = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_PHONE_GATEWAY_PERMISSION_INSERT]: 'add_global_db_phone_gateway_permission',
        [dbCmd.DB_PHONE_GATEWAY_PERMISSION_UPDATE]: 'update_global_db_phone_gateway_permission',
        [dbCmd.DB_PHONE_GATEWAY_PERMISSION_DELETE]: 'delete_global_db_phone_gateway_permission',
        [dbCmd.DB_PHONE_GATEWAY_PERMISSION_PUPDATE]: 'pupdate_global_db_phone_gateway_permission',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_PHONE_GATEWAY_PERMISSION_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.phoneGatewayPermission'))
    }
    const sync_db_phone_no_list = (operation, data, rpc_cmd_obj) => {
      const subjects = {
        [dbCmd.DB_PHONE_NO_LIST_INSERT]: 'add_global_db_phone_no_list',
        [dbCmd.DB_PHONE_NO_LIST_UPDATE]: 'update_global_db_phone_no_list',
        [dbCmd.DB_PHONE_NO_LIST_DELETE]: 'delete_global_db_phone_no_list',
        [dbCmd.DB_PHONE_NO_LIST_PUPDATE]: 'pupdate_global_db_phone_no_list',
      }
      // 部分更新，需要解析rpc_cmd_obj.resInfo, 将指定的更新字段数据拷贝出来
      if (operation === dbCmd.DB_PHONE_NO_LIST_PUPDATE) {
        data = getPupdateDataFromRpcCmdObj(data, rpc_cmd_obj)
      }
      bfglob.emit(subjects[operation], data)
      bfNotify.messageBox(i18n.global.t('syncCenter.phoneNoList'))
    }

    const SyncDataEvents = {
      db_org: sync_db_org,
      db_user_title: sync_db_user_title,
      db_user: sync_db_user,
      db_user_privelege: sync_db_user_privelege,
      db_device: sync_db_device,
      db_line_point: sync_db_line_point,
      db_line_master: sync_db_line_master,
      db_line_detail: sync_db_line_detail,
      db_rfid_rule_master: sync_db_rfid_rule_master,
      db_map_point: sync_db_map_point,
      db_controller: sync_db_controller,
      db_controller_gateway_manage: sync_db_controller_gateway_manage,
      db_phone_short_no: sync_db_phone_short_no,
      db_phone_gateway_filter: sync_db_phone_gateway_filter,
      db_phone_gateway_permission: sync_db_phone_gateway_permission,
      db_phone_no_list: sync_db_phone_no_list,
    }

    const syncOtherCenterDBData = () => {
      const dbNames = Object.keys(SyncDataMap)
      for (let i = 0; i < dbNames.length; i++) {
        // db_org...
        const dbName = dbNames[i]
        // dbCmd.DB_ORG_INSERT...
        const dbCmds = SyncDataMap[dbName]
        for (let j = 0; j < dbCmds.length; j++) {
          const operation = dbCmds[j]
          bfglob.on(operation + '', function (rpc_cmd_obj) {
            // 过滤自己的操作
            if (bfglob.cmdReqId.has(rpc_cmd_obj.reqId)) {
              bfglob.console.log('[syncOtherCenterDBData] self operation:', dbName, operation)
              bfglob.cmdReqId.delete(rpc_cmd_obj.reqId)
              return
            }

            // 没有orgId的数据的同步，需要判断rpc_cmd_obj.opt存储的上级数据是否有权限
            if (ManualSyncOperations.includes(operation) && !bfglob.gorgData.get(rpc_cmd_obj.opt)) {
              return
            }

            // 尝试解析对应的数据，进行同步方法调用
            try {
              const data = bfproto.decodeMessage(rpc_cmd_obj.body, dbName)
              bfglob.console.log('[syncOtherCenterDBData] decodeMessage:', dbName, operation, data, rpc_cmd_obj)
              SyncDataEvents[dbName]?.(operation, data, rpc_cmd_obj)
            } catch (e) {
              bfglob.console.error('[syncOtherCenterDBData] can not decode message:', e, dbName, operation, rpc_cmd_obj)
            }
          })
        }
      }
    }
    syncOtherCenterDBData()
  },
  setGlobalIotDeviceData(data) {
    // 设置下拉列表
    const selOpt = {
      label: data.devName,
      rid: data.rid,
      devId: data.devId,
    }

    // 保存数据，并设置索引
    bfglob.giotDevices.set(data.rid, data, data.devId).setList(data.rid, selOpt)

    return data
  },
  setGlobUserData(data, image) {
    data.orgShortName = bfglob.guserData.getOrgNameByKey(data.orgId)
    data.userTitleName = bfglob.guserData.getJobNameByKey(data.userTitle)
    var _image = bfglob.gimages.get(data.userImage)
    data.fileContent = image ? image.fileContent : _image ? _image.fileContent : ''
    var selOpts = {
      rid: data.rid,
      label: data.selfId + ' / ' + data.userName,
      rfid: data.userRfid,
      orgRid: data.orgId,
    }
    bfglob.guserData.set(data.rid, data, data.userRfid || false)
    bfglob.guserData.setList(data.rid, selOpts)
  },
  get_login_user_setting(user_rid, _db_cmd) {
    const msgObj = {
      rid: user_rid,
    }
    const msgOpts = {
      rpcCmdFields: {
        origReqId: 'rid',
        resInfo: '*',
      },
      decodeMsgType: 'db_user_list',
    }

    bfproto
      .sendMessage(_db_cmd, msgObj, 'db_user', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_user_list_obj = rpc_cmd_obj.body
        if (typeof db_user_list_obj.rows === 'undefined' || db_user_list_obj.rows.length === 0) {
          return
        }

        bfglob.userInfo.origData = bfproto.copyFieldsFromProto(db_user_list_obj.rows[0], defPackageName)
        const item = db_user_list_obj.rows[0]
        this.get_db_image(item.userImage)
          .then(res => {
            this.setGlobUserData(item, res)
          })
          .catch(err => {
            bfglob.console.error(err)
          })

        if (item.userSetting !== '{}') {
          const setting = JSON.parse(item.userSetting)
          const oldSettings = cloneDeep(bfglob.userInfo.setting)
          bfglob.userInfo.setting = Object.assign({}, bfglob.userInfo.setting, setting)
          bfglob.emit('update_user_settings', bfglob.userInfo.setting, oldSettings)
        }
      })
      .catch(err => {
        bfglob.console.warn('获取账户配置超时', err)
        // bfutil.messageBox(i18n.global.t("msgbox.getDevDataError, 'warning')
      })
  },
  findDbOrgByDmrid(dmrid) {
    return bfglob.gorgData.getDataByIndex(dmrid) || bfglob.noPermOrgData.getDataByIndex(dmrid)
  },
  gotDeviceUpdate(dbDevice) {
    // 判断该终端是否是自己的指挥座席
    if (dbDevice.dmrId !== bfglob.userInfo.setting.voipSpeakInfo?.speaker) {
      return
    }
    this.queryNoPermissionUnitByDmrId()
  },
  queryNoPermissionUnitByDmrId() {
    const lisGroups = bfglob.gdevices.getDataByIndex(bfglob.userInfo.setting.voipSpeakInfo?.speaker)?.channels?.find(item => item.no === 1)?.listenGroup || []
    const noPermOrgDmrids = lisGroups.filter(item => !this.findDbOrgByDmrid(item))
    this.requestNoPermsDmrIdOrgData(noPermOrgDmrids)
  },
  // 重置设备进行有源卡读卡后定位数据
  resetDeviceReadRfidLnglat(devItem, pointItem) {
    let rfidCard
    if (pointItem) {
      rfidCard = pointItem
      devItem.lastRfid = pointItem.pointRfid
      devItem.lastRfidTime = pointItem.lastCheckTime
    }

    // 没有进行过读卡操作
    if (!devItem.lastRfid) {
      return
    }
    // 判断最后定位时间和读卡时间的先后,跳过gps定位时间大于读卡时间
    if (bfTime.comparison_two_time(devItem.lastRfidTime, devItem.lastGpsTime)) {
      return
    }
    // 读取lastRfid卡点经纬度数据,重置设备的最后定位经纬度
    if (!rfidCard) {
      rfidCard = bfglob.glinePoints.getDataByIndex(devItem.lastRfid)
    }
    if (rfidCard) {
      // 为了不与无源点的定位信息冲突，所以进行偏移处理
      const [lon, lat] = randomOffsetLocation([rfidCard.lon, rfidCard.lat])
      devItem.lastLon = lon
      devItem.lastLat = lat
    }
  },
  createDeviceMarker(devItem) {
    // 如果只显示在线终端，则不创建手台Marker
    let cls = bftree.get_device_status_className(devItem)
    // 手台状态classname包含'none','gray'，则为不在线
    if (bfglob.userInfo.setting.showOnlyOnlineTerminals && (cls.includes('none') || cls.includes('gray'))) {
      return
    }
    if (bfutil.checkedDeviceLastLonValid(devItem)) {
      // 7day for hour
      const time = bfglob.sysConfig.maxAgeOfDataTime || 10080
      const oneDayTime = bfTime.get_current_time_before_one_day_time(time)
      const lastDataTime = bfTime.utcTimeToLocalTime(devItem.lastDataTime)

      if (bfTime.comparison_two_time(oneDayTime, lastDataTime)) {
        if (cls === 'device_status_none') {
          cls += ' hide'
        }
        // 添加 devMarker marker
        const createMapMarker = devItem => {
          if (bfglob.map) {
            bfglob.gdevices.setMarker(
              devItem.rid,
              maputil.mapMarker({
                type: MapMarkerTypes.Device,
                id: devItem.rid,
                className: cls,
                markerName: devItem.selfId,
                lngLat: [devItem.lastLon, devItem.lastLat],
                data: devItem,
              })
            )
            this.updateDeviceNodeAndMarker(devItem)
          } else {
            setTimeout(() => {
              createMapMarker(devItem)
            }, 200)
          }
        }
        createMapMarker(devItem)
      }
    }
  },
  queryExOnlineDevices() {
    const msgObj = {
      sid: bfglob.sessionId,
      userRid: bfglob.userInfo.rid,
    }
    const opt = uuid()
    const options = {
      encodePackageName: defPackageName,
      rpcCmdFields: {
        opt,
      },
    }
    const processData = (msg_data, msg_reply, msg_subject, nats_ssid) => {
      const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
      const dbType = bfproto.bfdx_proto_msg_T('ex_oneline_devices', kcpPackageName)
      const data = dbType.decode(rpc_cmd_obj.body)
      // 更新终端最后数据时间
      for (let i = 0; i < data.dmrId.length; i++) {
        const dmrId = toHexDmrId(data.dmrId[i])
        const device = bfglob.gdevices.getDataByIndex(dmrId)
        if (!device) {
          continue
        }

        // 存储的是utc时间
        const time = data.lastDataTime[i]
        device.lastDataTime = getUtcTimeString(time * 1000)
        this.updateDeviceNodeAndMarker(device)
      }

      if (rpc_cmd_obj.opt.endsWith('end')) {
        bfglob.server.unsubscribe(nats_ssid)
      }
    }

    bfglob.server.subscribe(opt, processData)
    bfproto
      .sendMessage(333, msgObj, 'client_privilege_request', dbSubject, options)
      .then(_rpc_cmd_obj => {
        // console.log('queryExOnlineDevices res:', rpc_cmd_obj)
        // if (rpc_cmd_obj.resInfo === '+OK') {
        //   this.cmd83(rpc_cmd_obj)
        // } else {
        // }
      })
      .catch(err => {
        bfglob.console.warn('queryExOnlineDevices', err)
      })
  },
  get_device_last_info(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const processStreamData = rpc_cmd_obj => {
      const db_device_last_info_list_obj = rpc_cmd_obj.body

      const deviceList = []
      for (const i in db_device_last_info_list_obj.rows) {
        const item = db_device_last_info_list_obj.rows[i]
        let devItem = bfglob.gdevices.get(item.rid)
        if (!devItem) {
          continue
        }

        devItem = Object.assign(devItem, item)

        devItem.userName = bfutil.get_device_userName(devItem)
        devItem.deviceUserName = bfglob.gdevices.getUserNameByKey(devItem.deviceUser)
        devItem.msStatusBin = bfutil.ms_status_hex2bin(item.msStatus)
        devItem.av = bfutil.checked_device_gps_is_valid(item)
        const lastPoweroffTime = new Date(item.lastPoweroffTime).getTime()
        const lastDataTime = new Date(item.lastDataTime).getTime()
        devItem.pS = typeof devItem.pS !== 'undefined' ? devItem.pS : lastPoweroffTime - lastDataTime >= 0 ? 0 : 1
        devItem.speed = 0
        devItem.direction = 0
        devItem.altitude = 0

        devItem = assignFixedDeviceLonLat(devItem)

        // 缓存到数组中，以便分片加载到fancytree中
        deviceList.push(devItem)

        // 从有源卡读卡数据中重置手台的定位坐标
        this.resetDeviceReadRfidLnglat(devItem)
        // 创建marker
        this.createDeviceMarker(devItem)
      }

      const loadDeviceNodes = list => {
        if (orgNodesIsLoaded) {
          bftree.sliceLoadingDeviceNodes('bftree', list)
          return
        }
        setTimeout(() => {
          loadDeviceNodes(list)
        }, 100)
      }
      loadDeviceNodes(deviceList)

      if (rpc_cmd_obj.opt.endsWith('end')) {
        // 取消登录后动画
        setTimeout(() => {
          bfglob.emit('cancel-login-loading')
        }, 0)
        // 获取线路巡查点
        this.get_linePoint_data(bfglob.sessionId, bfglob.userInfo.rid)
        this.queryExOnlineDevices()
      }
    }
    const msgOpts = {
      decodeMsgType: 'db_device_last_info_list',
      processStreamData,
    }

    bfproto.sendMessage(23, msgObj, 'client_privilege_request', dbSubject, msgOpts).catch(err => {
      bfglob.console.warn('获取对讲机最后数据超时', err)
      // bfutil.warningBox(i18n.global.t("msgbox.getDevDataError")).then(bfutil.systemReload);
    })
  },
  setDeviceChannelZone(channelZone) {
    if (!channelZone) {
      return
    }
    bfglob.gchannelZone.setList(channelZone.rid, {
      rid: channelZone.rid,
      label: channelZone.zoneNo ? `${channelZone.zoneNo} - ${channelZone.zoneTitle}` : channelZone.zoneTitle,
    })
    bfglob.gchannelZone.set(channelZone.rid, channelZone)
  },
  get_device_channel_zone() {
    const msgObj = {
      sid: bfglob.sessionId,
      userRid: bfglob.userInfo.rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_device_channel_zone_list',
    }

    bfproto
      .sendMessage(28, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const rows = rpc_cmd_obj.body.rows
        if (rpc_cmd_obj.resInfo === '+OK') {
          for (let i = 0; i < rows.length; i++) {
            this.setDeviceChannelZone(rows[i])
          }
        }
      })
      .catch(err => {
        bfglob.console.warn('get_device_channel_zone failed:', err)
        // bfutil.warningBox(i18n.global.t("msgbox.getDevDataError")).then(bfutil.systemReload);
      })
  },
  setGlobDeviceData(data) {
    data.virOrgs = data.virOrgs ?? ''
    data.virOrgsArr = data.virOrgs ? data.virOrgs.split(',') : []
    data.virOrgsName = bfutil.get_device_virOrgsName(data.virOrgsArr)
    data.orgShortName = bfglob.gorgData.getOrgNameByKey(data.orgId)
    data.deviceTypeName = data.deviceType === 0 ? i18n.global.t('dialog.interphone') : i18n.global.t('dialog.DMRDevice')
    data.userName = bfutil.get_device_userName(data)
    data.deviceUserName = bfglob.gdevices.getUserNameByKey(data.deviceUser)
    data.channels = bfutil.sortChannels(JSON.parse(data.channel || '{"channels":[]}').channels ?? [])
    data.gatewayFilterName = bfglob.gatewayFilter.getName(data.gatewayFilterRid)

    bfglob.gdevices.set(data.rid, data, data.dmrId)

    var selOpt = {
      rid: data.rid,
      label: data.selfId + ' / ' + data.dmrId,
      dmrId: data.dmrId,
      icon: deviceIconCls,
      isOrg: false,
      deviceType: data.deviceType,
    }
    bfglob.gdevices.setList(data.rid, selOpt)
  },
  mockDevices(rows) {
    console.time('mockDevices')
    let list = cloneDeep(rows)
    while (list.length < 30000) {
      list = list.concat(cloneDeep(list))
    }
    let dmrId = 474287
    let lastIndex = 0
    for (let i = 0; i < list.length; i++) {
      list[i].rid = uuid()
      list[i].dmrId = (dmrId++).toString(16).padStart(8, '0').toUpperCase()
      list[i].selfId = list[i].dmrId + '--' + i
      this.setGlobDeviceData(list[i])
      if (i > 0 && i % 500 === 0) {
        ;(k => {
          setTimeout(() => {
            lastIndex = k + 500
            bftree.sliceLoadingDeviceNodes('bftree', list.slice(k, lastIndex))
          }, 0)
        })(i)
      }
    }
    setTimeout(() => {
      bftree.sliceLoadingDeviceNodes('bftree', list.slice(lastIndex, list.length))
      console.timeEnd('mockDevices')
    }, 0)
  },
  // 通知服务器，当前登录用户的权限优先级
  noticeServerUserPriorityChange() {
    bfproto.sendMessage(1316, null, '', getGroupSubject()).catch()
  },
  // 查找登录用户的最高优先级的指挥坐席数据
  async setSelfSpeakerInfo() {
    const info = {
      speaker: '',
      priority: 0,
    }
    const allDevices = bfglob.gdevices.getAll()
    for (const k in allDevices) {
      const item = allDevices[k]
      // 跳过非控制台类型数据
      if (item.deviceType !== 2 || item.deviceUser !== bfglob.userInfo.rid) {
        continue
      }

      // 找到最高权限的指挥坐席，设置为调度台调度使用
      if (item.priority >= info.priority) {
        info.priority = item.priority
        info.speaker = item.dmrId
      }
    }

    if (!bfglob.userInfo.setting.voipSpeakInfo) {
      bfglob.userInfo.setting.voipSpeakInfo = {}
    }
    // 如果指挥坐席和优先级没有变化，则不需要更新配置
    const isPriorityChange = bfglob.userInfo.setting.voipSpeakInfo.priority !== info.priority
    const isSpeakerChange = bfglob.userInfo.setting.voipSpeakInfo.speaker !== info.speaker
    if (isPriorityChange || isSpeakerChange) {
      Object.assign(bfglob.userInfo.setting.voipSpeakInfo, info)
      bfglob.userInfo.setting.ispUdateVoipSpeakInfo = true
      await this.updateUserSetting(JSON.stringify(bfglob.userInfo.setting), dbCmd.DB_USER_PUPDATE).catch()
    }

    if (isPriorityChange) {
      this.noticeServerUserPriorityChange()
    }
  },
  get_device_data(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }

    const processStreamData = rpc_cmd_obj => {
      const device_list_obj = rpc_cmd_obj.body

      const dmrIds = []
      for (const i in device_list_obj.rows) {
        const item = device_list_obj.rows[i]
        dmrIds.push(item.dmrId)
        this.setGlobDeviceData(item)
      }
      // 查询在线手台信息
      this.listenOnceCtrlOnlineStats(dmrIds, 11)

      if (rpc_cmd_obj.opt.endsWith('end')) {
        // this.mockDevices(device_list_obj.rows)
        // 获取对讲机最后数据信息
        this.get_device_last_info(bfglob.sessionId, bfglob.userInfo.rid)
        // 获取对讲机信道区域数据
        this.get_device_channel_zone()

        // 请求系统内全部的短号映射数据
        this.get_all_short_no_data()

        // 请求网关授权数据
        this.get_gateway_permission_data()

        // 请求电话网关设备关系数据
        // this.get_controller_gateway_data();

        // 请求预定义电话簿数据
        this.get_phone_no_list_data()

        // 订阅拥有权限的组织消息
        this.bfdx_subscribe_broadcast_cmd()
        this.listenServerCmdSubject()

        // 重置登录用户指挥坐席
        this.setSelfSpeakerInfo().catch()

        setTimeout(() => {
          // 接收动态组订阅广播事件
          bfglob.server.subscribe(`${getGroupSubject()}`, this.OnServerCommand)
          bfglob.on('dynamic-group-subscribe-server-command', orgData => {
            bfglob.server.subscribe(orgData.rid, this.OnServerCommand)
            bfglob.server.subscribe(`${getGroupSubject()}.${orgData.dmrId}`, this.OnServerCommand)
          })
          requestAllDynamicGroup().catch(err => {
            bfglob.console.error('[requestAllDynamicGroup] err:', err)
          })
        }, 100)
      }
    }

    const msgOpts = {
      decodeMsgType: 'db_device_list',
      processStreamData,
    }

    bfproto.sendMessage(15, msgObj, 'client_privilege_request', dbSubject, msgOpts).catch(err => {
      // 消息发送超时提示
      bfglob.console.warn('获取对讲机数据超时', err)
      bfNotify.warningBox(i18n.global.t('msgbox.getDevDataError')).then(bfutil.systemReload)
    })
  },
  setGlobPrivilegeDeviceData(data) {
    bfglob.gapppMapPivilegeDevice.set(data.rid, data)

    var selOpt = {
      rid: data.rid,
      label: data.appDmrid + ' / ' + data.grantDeviceDmrid + '/' + data.expireTime,
    }
    bfglob.gapppMapPivilegeDevice.setList(data.rid, selOpt)
  },
  get_app_map_privilege_device_data() {
    const msgObj = {
      isSetExpire: 1,
    }
    const processStreamData = rpc_cmd_obj => {
      const privilege_device_list_obj = rpc_cmd_obj.body
      for (const i in privilege_device_list_obj.rows) {
        const item = privilege_device_list_obj.rows[i]
        if (timeIsBefore(item.expireTime, new Date())) continue
        this.setGlobPrivilegeDeviceData(item)
      }
      // bfglob.console.log('db_app_map_privilege_device_list', bfglob.gapppMapPivilegeDevice.getAll())
    }
    const msgOpts = {
      rpcCmdFields: {
        origReqId: 'is_set_expire',
        resInfo: '*',
      },
      decodeMsgType: 'db_app_map_privilege_device_list',
      processStreamData,
    }
    bfproto.sendMessage(dbCmd.DB_APP_MAP_PRIVILEGE_DEVICE_GETBY, msgObj, 'db_app_map_privilege_device', dbSubject, msgOpts).catch(err => {
      bfglob.console.warn('get db_app_map_privilege_device_get_by time out', err)
      // reject(err)
    })
  },
  get_user_data(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_user_list',
    }

    bfproto
      .sendMessage(16, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_user_list_obj = rpc_cmd_obj.body
        const rows = db_user_list_obj.rows
        const trySetImageForUserData = data => {
          this.get_db_image(data.userImage)
            .then(res => {
              this.setGlobUserData(data, res)
            })
            .catch(err => {
              bfglob.console.error(err)
            })
        }
        for (let i = 0; i < rows.length; i++) {
          const item = rows[i]
          trySetImageForUserData(item)
        }
        // 获取对讲机数据
        this.get_device_data(bfglob.sessionId, bfglob.userInfo.rid)
        this.get_app_map_privilege_device_data()
      })
      .catch(err => {
        bfglob.console.warn('获取用户数据超时', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getUserDataError')).then(bfutil.systemReload)
      })
  },
  setGlobUsetTitleData(data) {
    var selOpt = {
      label: data.titleName,
      rid: data.rid,
    }
    bfglob.gjobsData.set(data.rid, data, data.titleName)
    bfglob.gjobsData.setList(data.rid, selOpt)
  },
  get_user_title_data(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_user_title_list',
    }

    bfproto
      .sendMessage(18, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_user_title_list_obj = rpc_cmd_obj.body
        const rows = db_user_title_list_obj.rows

        if (rows.length > 0) {
          // 加载用户职位下拉选项
          for (let i = 0; i < rows.length; i++) {
            const item = rows[i]
            this.setGlobUsetTitleData(item)
          }
        }
        // 获取用户数据
        this.get_user_data(bfglob.sessionId, bfglob.userInfo.rid)
      })
      .catch(err => {
        bfglob.console.warn('获取用户职称响应超时', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getUserTitleDataError')).then(bfutil.systemReload)
      })
  },
  setGlobLinePointData(data) {
    data.gpsPointRadius = data.pointType === 3 ? data.gpsPointRadius : ''
    data.fileContent = bfutil.getPointTypeFileContent(data.pointType)
    data.orgShortName = bfglob.glinePoints.getOrgNameByKey(data.orgId)
    data.colorRGB = 'rgb(' + data.colorR + ',' + data.colorG + ',' + data.colorB + ')'
    data.background = 'background:' + data.colorRGB
    data.typeName = bfutil.getPointTypeName(data.pointType)

    bfglob.glinePoints.set(data.rid, data, data.pointRfid)
    var option = {
      rid: data.rid,
      label: data.pointId + ' / ' + data.pointName,
      rfid: data.pointRfid,
    }
    bfglob.glinePoints.setList(data.rid, option)

    // 添加 linePoint marker
    const createMapMarker = data => {
      if (bfglob.map) {
        bfglob.glinePoints.setMarker(
          data.rid,
          maputil.mapMarker({
            type: MapMarkerTypes.LinePoint,
            id: data.rid,
            markerName: data.pointName,
            icon: data.imgOrColorPoint === 1 ? data.fileContent : data.colorRGB,
            lngLat: [data.lon, data.lat],
            data: data,
          })
        )
      } else {
        setTimeout(() => {
          createMapMarker(data)
        }, 200)
      }
    }
    createMapMarker(data)
  },
  updateLinePointBadge(linePoint) {
    bfutil.getRfidDevices(linePoint.pointRfid).forEach(device => {
      maputil.addLinePointActiveDevice(linePoint.pointRfid, device.rid)
    })

    const marker = bfglob.glinePoints.getMarker(linePoint.rid)
    if (marker) {
      marker.updateLinePointBadge(linePoint.activeDevices)
    }
  },
  get_linePoint_data(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const processStreamData = rpc_cmd_obj => {
      const db_line_point_list_obj = rpc_cmd_obj.body
      const rows = db_line_point_list_obj.rows

      for (let i = 0; i < rows.length; i++) {
        const item = rows[i]
        this.setGlobLinePointData(item)
        this.updateLinePointBadge(item)
      }

      if (rpc_cmd_obj.opt.endsWith('end')) {
        // 获取巡查点最后数据信息
        this.get_line_point_latest_info(bfglob.sessionId, bfglob.userInfo.rid)
        // 获取巡查线路数据
        this.get_line_master_data(bfglob.sessionId, bfglob.userInfo.rid)
      }
    }
    const msgOpts = {
      decodeMsgType: 'db_line_point_list',
      processStreamData,
    }

    bfproto.sendMessage(17, msgObj, 'client_privilege_request', dbSubject, msgOpts).catch(err => {
      bfglob.console.warn('获取线路巡查点超时', err)
      bfNotify.warningBox(i18n.global.t('msgbox.getLinePointDataError')).then(bfutil.systemReload)
    })
  },
  get_linePoint_for_lineMast(detail, sel_db_cmd) {
    return new Promise((resolve, reject) => {
      const msgObj = {
        rid: detail.pointId,
      }
      const msgOpts = {
        decodeMsgType: 'db_line_point_list',
        rpcCmdFields: {
          origReqId: 'rid',
          resInfo: '*',
        },
      }

      bfproto
        .sendMessage(sel_db_cmd, msgObj, 'db_line_point', dbSubject, msgOpts)
        .then(rpc_cmd_obj => {
          const db_line_point_list_obj = rpc_cmd_obj.body
          if (rpc_cmd_obj.resInfo === '+OK') {
            const point = db_line_point_list_obj.rows[0]
            bfglob.glineMaster.setNoAccessPoint(point.rid, point)
            bfutil.handle_linePoint_for_lineMast(detail, point)
            resolve(rpc_cmd_obj)
          }
        })
        .catch(err => {
          bfglob.console.warn('获取没有权限的巡查点数据超时', err)
          reject(err)
        })
    })
  },
  setGlobLineMasterData(data) {
    if (data.lineDetailModify === 1) {
      bfNotify.warningBox(data.lineName + i18n.global.t('msgbox.insLineNotHasPoint'))
    }
    data.orgShortName = bfglob.glineMaster.getOrgNameByKey(data.orgId)
    // 添加规则管理线路下拉列表
    var selOpt = {
      rid: data.rid,
      label: data.lineId + ' / ' + data.lineName,
    }
    bfglob.glineMaster.set(data.rid, data)
    bfglob.glineMaster.setList(data.rid, selOpt)
  },
  get_line_detail_for_lineMast(lineMast, sel_db_cmd) {
    return new Promise((resolve, reject) => {
      const msgObj = {
        lineId: lineMast.rid,
      }
      const msgOpts = {
        decodeMsgType: 'db_line_detail_list',
        rpcCmdFields: {
          origReqId: 'line_id',
          resInfo: '*',
          opt: 'point_no',
        },
      }

      bfproto
        .sendMessage(sel_db_cmd, msgObj, 'db_line_detail', dbSubject, msgOpts)
        .then(async rpc_cmd_obj => {
          const db_line_detail_list_obj = rpc_cmd_obj.body
          const rows = db_line_detail_list_obj.rows

          if (rpc_cmd_obj.resInfo === '+OK') {
            const promises = []
            for (let i = 0; i < rows.length; i++) {
              const detail = rows[i]
              // 如果没有巡查点数据，则向服务器获取
              const point = bfglob.glinePoints.get(detail.pointId, true)
              if (point) {
                bfutil.handle_linePoint_for_lineMast(detail, point)
              } else {
                const promise = this.get_linePoint_for_lineMast(detail, dbCmd.DB_LINE_POINT_GETBY)
                promises.push(promise)
              }
            }
            await Promise.all(promises)
            resolve(rpc_cmd_obj)
          }
          reject(rpc_cmd_obj)
        })
        .catch(err => {
          bfglob.console.warn('查询线路详细表超时', err)
          reject(err)
        })
    })
  },
  get_line_master_data(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_line_master_list',
    }

    bfproto
      .sendMessage(20, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_line_master_list_obj = rpc_cmd_obj.body
        const rows = db_line_master_list_obj.rows

        if (rows.length > 0) {
          for (let i = 0; i < rows.length; i++) {
            const item = rows[i]
            this.setGlobLineMasterData(item)
            // 获取巡查线路与巡查点的关系表
            this.get_line_detail_for_lineMast(item, dbCmd.DB_LINE_DETAIL_GETBY)
          }
        }
        // 获取巡查规则数据
        this.get_rule_master_data(bfglob.sessionId, bfglob.userInfo.rid)
      })
      .catch(err => {
        bfglob.console.warn('获取巡查线路超时', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getLineDataError')).then(bfutil.systemReload)
      })
  },
  setGlobRuleMasterData(data, type) {
    data.orgShortName = bfglob.gorgData.getOrgNameByKey(data.orgId)
    var lineMaster = bfglob.glineMaster.get(data.ruleLineRid)
    data.checkStartTimeDate = type === 2 ? new Date('0000-01-01 ' + data.checkStartTime) : new Date(data.checkStartTime)
    data.checkStartTime = bfTime.getLocalTimeString(data.checkStartTimeDate, 'HH:mm')
    data.lineName = lineMaster ? lineMaster.lineName : ''
    data.day1 = data.day_1 ? '√' : ''
    data.day2 = data.day_2 ? '√' : ''
    data.day3 = data.day_3 ? '√' : ''
    data.day4 = data.day_4 ? '√' : ''
    data.day5 = data.day_5 ? '√' : ''
    data.day6 = data.day_6 ? '√' : ''
    data.day7 = data.day_7 ? '√' : ''
    data.checkAllTime = data.checkAllTime === 0 ? '' : data.checkAllTime
    data.checkCount = data.checkCount === 0 ? '' : data.checkCount
    if (data.ruleEffectiveType === 1) {
      // 有效时间段
      data.ruleEffectiveTypeName = i18n.global.t('dialog.timeSegment')
      data.ruleEffectiveStartDate = data.ruleEffectiveStart === '2000-01-01 00:00:00' ? '' : bfTime.utcTimeToLocalTime(data.ruleEffectiveStart)
      data.ruleEffectiveStart = bfTime.getLocalTimeString(data.ruleEffectiveStartDate, 'YYYY-MM-DD')
      data.ruleEffectiveEndDate = data.ruleEffectiveEnd === '2000-01-01 00:00:00' ? '' : bfTime.utcTimeToLocalTime(data.ruleEffectiveEnd)
      data.ruleEffectiveEnd = bfTime.getLocalTimeString(data.ruleEffectiveEndDate, 'YYYY-MM-DD')
    } else {
      // 总是有效
      data.ruleEffectiveTypeName = i18n.global.t('dialog.always')
      data.ruleEffectiveStartDate = ''
      data.ruleEffectiveStart = ''
      data.ruleEffectiveEndDate = ''
      data.ruleEffectiveEnd = ''
    }
    // 添加规则选项下拉列表
    var ruleOpt = {
      rid: data.rid,
      label: data.ruleId + ' / ' + data.ruleName,
    }
    bfglob.gruleMaster.set(data.rid, data).setList(data.rid, ruleOpt)
  },
  get_rule_master_data(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_rfid_rule_master_list',
    }

    bfproto
      .sendMessage(21, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_rfid_rule_master_list_obj = rpc_cmd_obj.body
        const rows = db_rfid_rule_master_list_obj.rows

        if (rows.length > 0) {
          for (let i = 0; i < rows.length; i++) {
            const item = rows[i]
            this.setGlobRuleMasterData(item)
          }
        }
      })
      .catch(err => {
        bfglob.console.warn('获取巡查规则超时', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getRuleDataError')).then(bfutil.systemReload)
      })
  },
  get_controller_last_info(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_controller_last_info_list',
    }

    bfproto
      .sendMessage(24, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_controller_last_info_list_obj = rpc_cmd_obj.body
        const rows = db_controller_last_info_list_obj.rows

        if (rows.length > 0) {
          for (let i = 0; i < rows.length; i++) {
            const item = rows[i]
            const ctrlItem = bfglob.gcontrollers.get(item.rid)
            if (!ctrlItem) {
              continue
            }
            ctrlItem.lastDataTime = item.lastDataTime
            ctrlItem.connected = item.connected
            ctrlItem.orgId = item.orgId
            ctrlItem.ctrlStats = typeof ctrlItem.ctrlStats !== 'undefined' ? ctrlItem.ctrlStats : -1

            // 在地图上创建控制器 marker
            const createMapMarker = ctrlItem => {
              if (bfglob.map) {
                bfglob.gcontrollers.setMarker(
                  ctrlItem.rid,
                  maputil.mapMarker({
                    type: MapMarkerTypes.Controller,
                    id: ctrlItem.rid,
                    markerName: ctrlItem.selfId,
                    className: 'iconfont icon-base-station',
                    lngLat: [ctrlItem.lon, ctrlItem.lat],
                    data: ctrlItem,
                  })
                )
                maputil.show_or_hide_ctrlMarker()
              } else {
                setTimeout(() => {
                  createMapMarker(ctrlItem)
                }, 200)
              }
            }
            createMapMarker(ctrlItem)
          }
        }
      })
      .catch(err => {
        bfglob.console.warn('获取控制器最后数据超时', err)
      })
  },
  setGlobControllerData(data) {
    data.orgShortName = bfglob.gcontrollers.getOrgNameByKey(data.orgId)
    data.currentRoom = '-0'
    data.controllerGateway = {}
    data.writeFrequencySetting = {}

    var selOpt = {
      label: data.selfId + ' / ' + data.dmrId,
      rid: data.rid,
      dmrId: data.dmrId,
      controllerType: data.controllerType,
    }
    bfglob.gcontrollers.set(data.rid, data, data.dmrId).setList(data.rid, selOpt)
  },
  get_controller_data(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_controller_list',
    }

    bfproto
      .sendMessage(22, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_controller_list_obj = rpc_cmd_obj.body
        const rows = db_controller_list_obj.rows

        if (rows.length > 0) {
          const dmrIds = []
          for (let i = 0; i < rows.length; i++) {
            const item = rows[i]
            dmrIds.push(item.dmrId)
            this.setGlobControllerData(item)
          }

          // 查询中继器在线信息
          this.listenOnceCtrlOnlineStats(dmrIds)

          // 获取控制器最后数据信息
          this.get_controller_last_info(bfglob.sessionId, bfglob.userInfo.rid)

          // 订阅同播控制器102命令事件，同播中继注册、注销命令
          bfglob.on('102', rpcCmd => {
            const kcpRpcCmd = bfproto.decodeMessage(rpcCmd.body, 'rpc_cmd', kcpPackageName)
            processInfoReport(kcpRpcCmd)
          })

          // 订阅虚拟集群控制器103命令事件，集群中继注册、注销命令
          bfglob.on('103', rpcCmd => {
            const kcpRpcCmd = bfproto.decodeMessage(rpcCmd.body, 'rpc_cmd', kcpPackageName)
            processSvtRepeaterInfoReport(kcpRpcCmd)
          })
        }
      })
      .catch(err => {
        bfglob.console.warn('获取控制器超时', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getCtrlDataError')).then(bfutil.systemReload)
      })
  },
  /**
   * 通过控制器的dmrId获取设备存在的固定订阅收听表数据
   * @param {string} dmrId 控制器的dmrId
   * @returns {Promise<Array<*>>} 返回查找到的收听表数据，可能为空
   */
  getStaticSubscribesByDmrId(dmrId) {
    return new Promise((resolve, reject) => {
      const opt = uuid()
      const rows = []
      const processData = (msg_data, msg_reply, msg_subject, nats_ssid) => {
        const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
        const dbType = bfproto.bfdx_proto_msg_T('db_static_subscribes_list')
        const data = dbType.decode(rpc_cmd_obj.body)
        rows.push(...data.rows)

        if (rpc_cmd_obj.opt.endsWith('end')) {
          bfglob.server.unsubscribe(nats_ssid)
          resolve(rows)
        }
      }
      bfglob.server.subscribe(opt, processData)

      const msgOpts = {
        // decodeMsgType: 'db_static_subscribes',
        rpcCmdFields: {
          optInt: parseInt(dmrId, 16),
          opt,
        },
      }
      // const CMD_CLIENT_GET_CONTROLLER_STATIC_SUBSCRIBER_LIST = 31
      bfproto
        .sendMessage(31, null, null, getDbSubject(), msgOpts)
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo !== '+OK') {
            reject(rpc_cmd_obj.resInfo)
          }
        })
        .catch(err => {
          bfglob.console.warn('获取指定控制器的静态订阅收听表超时', err)
          reject(err)
        })
    })
  },
  get_line_point_latest_info(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const processStreamData = rpc_cmd_obj => {
      const db_line_point_latest_info_list_obj = rpc_cmd_obj.body
      const rows = db_line_point_latest_info_list_obj.rows

      for (let i = 0; i < rows.length; i++) {
        const item = rows[i]
        const pointItem = bfglob.glinePoints.get(item.rid)
        if (!pointItem) {
          continue
        }
        pointItem.updateAt = item.updateAt
        pointItem.lastCheckUserId = item.lastCheckUserId
        pointItem.lastCheckTime = item.lastCheckTime
        pointItem.lastCheckDeviceId = item.lastCheckDeviceId

        const marker = bfglob.glinePoints.getMarker(pointItem.rid)
        if (marker) {
          const content = maputil.get_popup_content_for_linePoint(pointItem)
          marker.updatePopup(content)
        }
      }
    }
    const msgOpts = {
      decodeMsgType: 'db_line_point_latest_info_list',
      processStreamData,
    }

    bfproto.sendMessage(25, msgObj, 'client_privilege_request', dbSubject, msgOpts).catch(err => {
      bfglob.console.warn('获取巡查点最后数据超时', err)
      bfNotify.warningBox(i18n.global.t('msgbox.getLinePointDataError')).then(bfutil.systemReload)
    })
  },
  setGlobMapPointData(data, image) {
    data.orgShortName = bfglob.gmapPoints.getOrgNameByKey(data.orgId)
    data.colorRGB = data.colorRGB ? data.colorRGB : 'rgb(' + data.colorR + ',' + data.colorG + ',' + data.colorB + ')'
    data.fileContent = data.imgOrColorPoint === 0 ? '' : image ? image.fileContent : (bfglob.gimages.get(data.pointImg)?.fileContent ?? '')
    bfglob.gmapPoints.set(data.rid, data)
    // 添加 mapPoint marker
    const createMapMarker = data => {
      if (bfglob.map) {
        bfglob.gmapPoints.setMarker(
          data.rid,
          maputil.mapMarker({
            type: MapMarkerTypes.MapPoint,
            id: data.rid,
            markerName: data.pointName,
            icon: data.imgOrColorPoint === 1 ? data.fileContent : data.colorRGB,
            lngLat: [data.lon, data.lat],
            data: data,
          })
        )
      } else {
        setTimeout(() => {
          createMapMarker(data)
        }, 200)
      }
    }
    createMapMarker(data)
  },
  get_map_point_data(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_map_point_list',
    }

    bfproto
      .sendMessage(19, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_map_point_list_obj = rpc_cmd_obj.body
        const rows = db_map_point_list_obj.rows
        const tryGetImageForSetMapPoint = data => {
          this.get_db_image(data.pointImg)
            .then(res => {
              this.setGlobMapPointData(data, res)
            })
            .catch(err => {
              bfglob.console.error(err)
            })
        }

        for (let i = 0; i < rows.length; i++) {
          const item = rows[i]
          tryGetImageForSetMapPoint(item)
        }
      })
      .catch(err => {
        bfglob.console.warn('获取地图标记点超时', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getMapPointDataError')).then(bfutil.systemReload)
      })
  },
  // 请求短号中没有权限的组织和设备相关数据
  setGlobalNoPermOrgData(data) {
    bfglob.noPermOrgData.set(data.rid, data, data.dmrId).setList(data.rid, {
      dmrId: data.dmrId,
      icon: 'icon-organize',
      isOrg: true,
      label: `${data.orgShortName} / ${data.dmrId}`,
      rid: data.rid,
    })
  },
  processNoPermOrgShortNoData(item) {
    const global_shortNo = bfglob.gshortNo.getAll()

    for (const k in global_shortNo) {
      const data = global_shortNo[k]
      if (data.refOrgId === item.rid) {
        data.mappingTargetName = item.orgShortName
      }
      if (data.orgId === item.rid) {
        data.orgShortName = item.orgShortName
      }
    }
  },
  requestNoPermsShortNoOrgData(rids = [], opts = { requestCode: 1 }) {
    this.fetchNoPermsOrgs(rids, opts).then(res => {
      for (let i = 0; i < res.length; i++) {
        const item = res[i]
        // 返回的数据中，默认包含上级，但上级可能是有权限，需要过滤
        if (bfglob.gorgData.get(item.rid)) {
          continue
        }

        this.setGlobalNoPermOrgData(item)
        this.processNoPermOrgShortNoData(item)
      }
    })
  },
  fetchNoPermsOrgs(rids, options) {
    return new Promise((resolve, reject) => {
      const opts = {
        requestCode: 1,
        resultCols: [],
        ...options,
      }
      const msgObj = {
        rids: rids,
        requestCode: opts.requestCode,
        resultCols: opts.resultCols,
      }
      const opt = uuid()
      const decodeMsgType = 'db_org_list'
      const msgOpts = {
        rpcCmdFields: {
          opt,
        },
      }
      let result = []

      const onServermessage = (msg_data, msg_reply, msg_subject, nats_ssid) => {
        const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
        const dbListName_t = bfproto.bfdx_proto_msg_T(decodeMsgType)
        result = result.concat(dbListName_t.decode(rpc_cmd_obj.body).rows)

        if (rpc_cmd_obj.opt === '0,end') {
          bfglob.server.unsubscribe(nats_ssid)
          resolve(result)
        }
      }
      bfglob.server.subscribe(opt, onServermessage)

      bfproto
        .sendMessage(93, msgObj, 'rid_filter', dbSubject, msgOpts)
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo !== '+OK') {
            reject()
          }
        })
        .catch(err => {
          bfglob.console.warn('get requestShortNumberPermissionData err:', err)
          reject()
        })
    })
  },

  requestNoPermsDmrIdOrgData(dmrids = [], opts = { requestCode: 1 }) {
    if (dmrids.length === 0) {
      return
    }
    this.fetchNoPermsOrgsByDmrid(dmrids, opts).then(res => {
      for (let i = 0; i < res.length; i++) {
        const item = res[i]
        // 返回的数据中，默认包含上级，但上级可能是有权限，需要过滤
        if (bfglob.gorgData.get(item.rid)) {
          continue
        }

        this.setGlobalNoPermOrgData(item)
        this.processNoPermOrgShortNoData(item)
      }
    })
  },
  fetchNoPermsOrgsByDmrid(dmrids, options) {
    return new Promise((resolve, reject) => {
      const opts = {
        requestCode: 1,
        resultCols: [],
        ...options,
      }
      const msgObj = {
        rids: dmrids,
        requestCode: opts.requestCode,
        resultCols: opts.resultCols,
      }
      const opt = uuid()
      const decodeMsgType = 'db_org_list'
      const msgOpts = {
        rpcCmdFields: {
          opt,
        },
      }
      let result = []

      const onServermessage = (msg_data, msg_reply, msg_subject, nats_ssid) => {
        const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
        const dbListName_t = bfproto.bfdx_proto_msg_T(decodeMsgType)
        result = result.concat(dbListName_t.decode(rpc_cmd_obj.body).rows)

        if (rpc_cmd_obj.opt === '0,end') {
          bfglob.server.unsubscribe(nats_ssid)
          resolve(result)
        }
      }
      bfglob.server.subscribe(opt, onServermessage)
      bfproto
        .sendMessage(96, msgObj, 'rid_filter', dbSubject, msgOpts)
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo !== '+OK') {
            reject()
          }
        })
        .catch(err => {
          bfglob.console.warn('get requestShortNumberPermissionData err:', err)
          reject()
        })
    })
  },

  setGlobalNoPermDevData(data) {
    bfglob.noPermDevData.set(data.rid, data, data.dmrId).setList(data.rid, {
      deviceType: data.deviceType,
      dmrId: data.dmrId,
      icon: 'icon-interphone',
      isOrg: false,
      label: `${data.orgShortName} / ${data.dmrId}`,
      rid: data.rid,
    })
  },
  processNoPermDevShortNoData(item) {
    const global_shortNo = bfglob.gshortNo.getAll()

    for (const k in global_shortNo) {
      const data = global_shortNo[k]
      if (data.refDevId === item.rid) {
        data.mappingTargetName = item.selfId
      }
    }
  },
  requestNoPermsShortNoDeviceData(rids = [], opts) {
    this.fetchNoPermDevices(rids, opts).then(res => {
      for (let i = 0; i < res.length; i++) {
        const item = res[i]
        this.setGlobalNoPermDevData(item)
        this.processNoPermDevShortNoData(item)
      }
    })
  },
  fetchNoPermDevices(rids, options) {
    return new Promise((resolve, reject) => {
      const opts = {
        requestCode: 1,
        resultCols: [],
        ...options,
      }
      const msgObj = {
        rids: rids,
        requestCode: opts.requestCode,
        resultCols: opts.resultCols,
      }

      const opt = uuid()
      const decodeMsgType = 'db_device_list'
      const msgOpts = {
        rpcCmdFields: {
          opt,
        },
      }
      let result = []
      const onServermessage = (msg_data, msg_reply, msg_subject, nats_ssid) => {
        const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
        const dbListName_t = bfproto.bfdx_proto_msg_T(decodeMsgType)
        result = result.concat(dbListName_t.decode(rpc_cmd_obj.body).rows)

        if (rpc_cmd_obj.opt === '0,end') {
          bfglob.server.unsubscribe(nats_ssid)
          resolve(result)
        }
      }
      bfglob.server.subscribe(opt, onServermessage)

      bfproto
        .sendMessage(94, msgObj, 'rid_filter', dbSubject, msgOpts)
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo !== '+OK') {
            reject()
          }
        })
        .catch(err => {
          bfglob.console.warn('get requestShortNumberPermissionData err:', err)
          reject()
        })
    })
  },
  // 请求系统内所有短号数据
  setGlobShortNoData(data) {
    data.refOrgName = bfglob.gorgData.getOrgNameByKey(data.refOrgId)
    data.refDevName = bfglob.gdevices.getSelfIdByKey(data.refDevId)

    // // 根据映射目标的rid设置目标显示名称
    // data.mappingTargetName = "";
    // if (data.refOrgId && data.refOrgId !== bfutil.DefOrgRid) {
    //   data.mappingTargetName = data.refOrgName;
    // }
    // if (data.refDevId && data.refDevId !== bfutil.DefOrgRid) {
    //   data.mappingTargetName = data.refDevName;
    // }

    data.orgShortName = bfglob.gorgData.getShortName(data.orgId)

    // 设置下拉列表
    const selOpt = {
      label: data.shortNo + ' / ' + data.mappingTargetName,
      rid: data.rid,
      shortNo: data.shortNo,
    }

    // 先删除索引，确保一个短号只有一个索引
    bfglob.gshortNo.deleteIndexByVal(data.rid)
    // 保存数据
    bfglob.gshortNo.set(data.rid, data, data.shortNo).setList(data.rid, selOpt)
  },
  get_all_short_no_data() {
    const msgObj = {
      sid: bfglob.sessionId,
      userRid: bfglob.userInfo.rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_phone_short_no_list',
    }

    bfproto
      .sendMessage(92, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        // bfglob.console.log("get_all_short_no_data rpc_cmd_obj:", rpc_cmd_obj);
        const db_map_point_list_obj = rpc_cmd_obj.body
        const rows = db_map_point_list_obj.rows

        // 没有权限的短号rid集合
        const no_permission_org_ids = []
        const no_permission_device_ids = []

        const push_no_permission_rid = (rid, targetList, type = 1) => {
          if (rid && rid !== bfutil.DefOrgRid) {
            let target = null
            if (type === 1) {
              target = bfglob.gorgData.get(rid)
            } else {
              target = bfglob.gdevices.get(rid)
            }
            if (target) {
              return false
            } else {
              // 去重判断
              if (!targetList.includes(rid)) {
                targetList.push(rid)
              }
              return true
            }
          }
        }

        for (let i = 0; i < rows.length; i++) {
          const item = rows[i]

          // 判断是否有权限外的数据，有需要去请求相关数据
          push_no_permission_rid(item.refDevId, no_permission_device_ids, 2)
          push_no_permission_rid(item.refOrgId, no_permission_org_ids, 1)
          item.noPermission = push_no_permission_rid(item.orgId, no_permission_org_ids, 1)

          this.setGlobShortNoData(item)
        }

        if (no_permission_org_ids.length > 0) {
          this.requestNoPermsShortNoOrgData(no_permission_org_ids)
        }
        if (no_permission_device_ids.length > 0) {
          this.requestNoPermsShortNoDeviceData(no_permission_device_ids)
        }
      })
      .catch(err => {
        bfglob.console.warn('get db_phone_short_no err:', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getMapPointDataError')).then(bfutil.systemReload)
      })
  },
  // 请求网关规则数据
  setGlobGatewayFilterData(data) {
    const toArray = source => {
      if (Array.isArray(source)) {
        return source
      }

      try {
        return JSON.parse(source)
      } catch (_e) {
        return []
      }
    }

    data.inBlack = toArray(data.inBlack)
    data.inWhite = toArray(data.inWhite)
    data.outBlack = toArray(data.outBlack)
    data.outWhite = toArray(data.outWhite)
    data.detailData = [
      {
        inBlack: data.inBlack,
        inWhite: data.inWhite,
        outBlack: data.outBlack,
        outWhite: data.outWhite,
      },
    ]

    data.orgShortName = bfglob.gorgData.getOrgNameByKey(data.orgId)

    // 设置下拉列表
    const selOpt = {
      label: data.name,
      rid: data.rid,
      name: data.name,
    }

    // 保存数据
    bfglob.gatewayFilter.set(data.rid, data, data.name).setList(data.rid, selOpt)
  },
  get_gateway_filter_data() {
    const msgObj = {
      sid: bfglob.sessionId,
      userRid: bfglob.userInfo.rid,
    }
    const opt = uuid()
    const decodeMsgType = 'db_phone_gateway_filter_list'
    const msgOpts = {
      rpcCmdFields: {
        opt,
      },
    }

    const onServermessage = (msg_data, msg_reply, msg_subject, nats_ssid) => {
      const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
      if (rpc_cmd_obj.opt === '0,end') {
        bfglob.server.unsubscribe(nats_ssid)
      }
      const dbListName_t = bfproto.bfdx_proto_msg_T(decodeMsgType)
      const dbListName_obj = dbListName_t.decode(rpc_cmd_obj.body)

      for (let i = 0; i < dbListName_obj.rows.length; i++) {
        const item = dbListName_obj.rows[i]
        this.setGlobGatewayFilterData(item)
      }
    }
    bfglob.server.subscribe(opt, onServermessage)

    bfproto
      .sendMessage(90, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          // no-empty
        } else {
          bfglob.console.warn('get db_phone_gateway_filter err:', rpc_cmd_obj.resInfo)
        }
      })
      .catch(err => {
        bfglob.console.warn('get db_phone_gateway_filter err:', err)
        // bfNotify.warningBox(i18n.global.t("msgbox.getMapPointDataError")).then(bfutil.systemReload);
      })
  },
  // 请求网关授权数据
  setGlobGatewayPermissionData(data) {
    data.orgShortName = bfglob.gorgData.getOrgNameByKey(data.orgId)
    data.permOrgName = bfglob.gorgData.getOrgNameByKey(data.permOrgId)
    data.permDevName = bfglob.gdevices.getSelfIdByKey(data.permDevId)
    data.gatewayName = bfglob.gdevices.getSelfIdByKey(data.gatewayRid)
    // // 根据映射目标的rid设置目标显示名称
    // data.mappingTargetName = "";
    // if (data.permOrgId && data.permOrgId !== bfutil.DefOrgRid) {
    //   data.mappingTargetName = data.permOrgName;
    // }
    // if (data.permDevId && data.permDevId !== bfutil.DefOrgRid) {
    //   data.mappingTargetName = data.permDevName;
    // }

    // 设置下拉列表
    const selOpt = {
      label: data.name,
      rid: data.rid,
      name: data.name,
    }

    // 保存数据
    bfglob.gatewayPermission.set(data.rid, data, data.name).setList(data.rid, selOpt)
  },
  get_gateway_permission_data() {
    const msgObj = {
      sid: bfglob.sessionId,
      userRid: bfglob.userInfo.rid,
    }
    const opt = uuid()
    const decodeMsgType = 'db_phone_gateway_permission_list'
    const msgOpts = {
      rpcCmdFields: {
        opt,
      },
    }

    const onServermessage = (msg_data, msg_reply, msg_subject, nats_ssid) => {
      const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
      if (rpc_cmd_obj.opt === '0,end') {
        bfglob.server.unsubscribe(nats_ssid)
      }
      const dbListName_t = bfproto.bfdx_proto_msg_T(decodeMsgType)
      const dbListName_obj = dbListName_t.decode(rpc_cmd_obj.body)

      const permOrgIdList = []
      const permOrgCache = {}
      const permDevIdList = []
      const permDevCache = {}
      for (let i = 0; i < dbListName_obj.rows.length; i++) {
        const item = dbListName_obj.rows[i]
        this.setGlobGatewayPermissionData(item)
        // 检查被授权的单位或终端设备是否有权限
        if (item.permOrgId) {
          const orgData = bfglob.gorgData.get(item.permOrgId)
          if (!orgData) {
            permOrgIdList.push(item.permOrgId)
            permOrgCache[item.permOrgId] = item
          }
        }

        if (item.permDevId) {
          const device = bfglob.gdevices.get(item.permDevId)
          if (!device) {
            permDevIdList.push(item.permDevId)
            permOrgCache[item.permOrgId] = item
          }
        }
      }

      // 请求没有权限的单位或终端设备数据
      if (permOrgIdList.length) {
        this.fetchNoPermsOrgs(permOrgIdList).then(orgs => {
          for (let i = 0; i < orgs.length; i++) {
            const orgItem = orgs[i]
            if (bfglob.gorgData.get(orgItem.rid)) {
              continue
            }
            this.setGlobalNoPermOrgData(orgItem)
            if (permOrgCache[orgItem.rid]) {
              permOrgCache[orgItem.rid].permOrgName = orgItem.orgShortName
            }
          }
        })
      }
      if (permDevIdList.length) {
        this.fetchNoPermDevices(permDevIdList).then(devices => {
          for (let i = 0; i < devices.length; i++) {
            const deviceItem = devices[i]
            this.setGlobalNoPermDevData(deviceItem)
            if (permDevCache[deviceItem.rid]) {
              permDevCache[deviceItem.rid].permDevName = deviceItem.selfId
            }
          }
        })
      }
    }
    bfglob.server.subscribe(opt, onServermessage)

    bfproto
      .sendMessage(91, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          // no-empty
        } else {
          bfglob.console.warn('get db_phone_gateway_permission err:', rpc_cmd_obj.resInfo)
        }
      })
      .catch(err => {
        bfglob.console.warn('get db_phone_gateway_permission err:', err)
        // bfNotify.warningBox(i18n.global.t("msgbox.getMapPointDataError")).then(bfutil.systemReload);
      })
  },

  // 请求电话网关设备关系数据
  setGlobControllerGatewayData(data) {
    data.orgShortName = bfglob.gorgData.getOrgNameByKey(data.orgId)
    data.refControllerName = bfglob.gcontrollers.getSelfId(data.refControllerId)
    data.refDevName = bfglob.gdevices.getSelfIdByKey(data.refDevId)

    // 设置下拉列表
    const selOpt = {
      label: data.phoneNo,
      rid: data.rid,
      refControllerId: data.refControllerId,
      refDevId: data.refDevId,
    }

    // 保存数据
    bfglob.gcontrollerGateway.set(data.rid, data, data.refDevId).setList(data.rid, selOpt)

    return data
  },
  get_controller_gateway_data() {
    const msgObj = {
      sid: bfglob.sessionId,
      userRid: bfglob.userInfo.rid,
    }
    const opt = uuid()
    const decodeMsgType = 'db_controller_gateway_manage_list'
    const msgOpts = {
      rpcCmdFields: {
        opt,
      },
    }

    const onServermessage = (msg_data, msg_reply, msg_subject, nats_ssid) => {
      const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
      if (rpc_cmd_obj.opt === '0,end') {
        bfglob.server.unsubscribe(nats_ssid)
      }
      const dbListName_t = bfproto.bfdx_proto_msg_T(decodeMsgType)
      const dbListName_obj = dbListName_t.decode(rpc_cmd_obj.body)

      for (let i = 0; i < dbListName_obj.rows.length; i++) {
        const item = dbListName_obj.rows[i]
        this.setGlobControllerGatewayData(item)
      }
    }
    bfglob.server.subscribe(opt, onServermessage)

    bfproto
      .sendMessage(-1, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          // no-empty
        } else {
          bfglob.console.error('get db_controller_gateway_manage err:', rpc_cmd_obj.resInfo)
        }
      })
      .catch(err => {
        bfglob.console.warn('get db_controller_gateway_manage err:', err)
        // bfNotify.warningBox(i18n.global.t("msgbox.getMapPointDataError")).then(bfutil.systemReload);
      })
  },

  // 请求预定义电话簿数据
  setGlobPhoneNoListData(data) {
    data.orgShortName = bfglob.gorgData.getOrgNameByKey(data.orgId)

    // 设置下拉列表
    const selOpt = {
      label: data.phoneNo,
      rid: data.rid,
      phoneNo: data.phoneNo,
    }

    // 保存数据
    bfglob.gphoneBook.set(data.rid, data, data.phoneNo).setList(data.rid, selOpt)

    return data
  },
  get_phone_no_list_data() {
    const msgObj = {
      sid: bfglob.sessionId,
      userRid: bfglob.userInfo.rid,
    }
    const opt = uuid()
    const decodeMsgType = 'db_phone_no_list_list'
    const msgOpts = {
      rpcCmdFields: {
        opt,
      },
    }

    const onServermessage = (msg_data, msg_reply, msg_subject, nats_ssid) => {
      const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
      if (rpc_cmd_obj.opt === '0,end') {
        bfglob.server.unsubscribe(nats_ssid)
      }
      const dbListName_t = bfproto.bfdx_proto_msg_T(decodeMsgType)
      const dbListName_obj = dbListName_t.decode(rpc_cmd_obj.body)

      for (let i = 0; i < dbListName_obj.rows.length; i++) {
        const item = dbListName_obj.rows[i]
        this.setGlobPhoneNoListData(item)
      }
    }
    bfglob.server.subscribe(opt, onServermessage)

    bfproto
      .sendMessage(95, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          // no-empty
        } else {
          bfglob.console.error('get db_phone_no_list err:', rpc_cmd_obj.resInfo)
        }
      })
      .catch(err => {
        bfglob.console.warn('get db_phone_no_list err:', err)
        // bfNotify.warningBox(i18n.global.t("msgbox.getMapPointDataError")).then(bfutil.systemReload);
      })
  },

  // 找到最顶级单位,用最顶级单位的 rid 获取滚动标题
  getOneOrgData(rid, db_cmd) {
    const msgObj = {
      rid: rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_org_list',
      rpcCmdFields: {
        origReqId: 'rid',
        resInfo: '*',
      },
    }

    bfproto
      .sendMessage(db_cmd, msgObj, 'db_org', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_org_list_obj = rpc_cmd_obj.body
        this.temp_orgLists.push(db_org_list_obj.rows[0])
      })
      .catch(_err => {})
  },
  getTopOrgRid(targetArr) {
    this.temp_orgLists = []
    for (let i = 0; i < targetArr.length; i++) {
      const item = targetArr[i]
      this.getOneOrgData(item.parentOrgId, dbCmd.DB_ORG_GETBY)
    }

    var getorgList = function () {
      if (this.temp_orgLists.length !== targetArr.length) {
        setTimeout(getorgList, 100)
        return
      }

      var finallyArr = []
      var obj = {}

      for (let k = 0; k < this.temp_orgLists.length; k++) {
        const item = this.temp_orgLists[k]
        if (obj[item.rid]) {
          continue
        }
        finallyArr.push(item)
        obj[item.rid] = 1
      }
      if (finallyArr.length > 1) {
        this.getTopOrgRid(finallyArr)
        return
      }

      this.temp_orgLists = null
      // 获取滚动文字
      bfglob.systemSetting.titleConfKey = 'clientTitle.' + finallyArr[0].rid
      this.getSysLogoAndTitle(bfglob.systemSetting.titleConfKey, dbCmd.DB_SYS_CONFIG_GETBY)
    }.bind(this)

    setTimeout(getorgList, 100)
  },
  getTopOrgMoveTitle() {
    var orgs = bfglob.gorgData.getAll()
    if (Object.keys(orgs).length === 0) {
      this.getSysLogoAndTitle(bfglob.systemSetting.titleConfKey, dbCmd.DB_SYS_CONFIG_GETBY)
      return
    }
    // 找到拥有权限的最顶级单位
    var result = []
    var resultObj = {}
    for (const k in orgs) {
      const orgItem = orgs[k]
      const item = bfglob.gorgData.getParent(orgItem.parentOrgId)
      if (item || resultObj[orgItem.rid]) {
        continue
      }
      result.push(orgItem)
      resultObj[orgItem.rid] = 1
    }
    if (result.length === 0) {
      return
    }

    var titleConfKey = result[0].rid

    // 如果权限内的顶级单位有多个，则找到这些单位共同的父级单位
    if (result.length === 1) {
      // 获取滚动文字
      bfglob.systemSetting.titleConfKey = 'clientTitle.' + titleConfKey
      this.getSysLogoAndTitle(bfglob.systemSetting.titleConfKey, dbCmd.DB_SYS_CONFIG_GETBY)
    } else {
      this.getTopOrgRid(result)
    }
  },
  // 获取账号权限数据
  setGlobOrgData(data) {
    if (data.rid === '00000000-0000-0000-0000-000000000000') {
      return
    }
    if (data.rid === data.parentOrgId) {
      data.parentOrgId = '00000000-0000-0000-0000-000000000000'
    }

    bfglob.gorgData.set(data.rid, data, data.dmrId)
    var selOpt = {
      label: data.orgShortName + ' / ' + data.dmrId,
      rid: data.rid,
      dmrId: data.dmrId,
      icon: orgIconCls,
      isOrg: true,
    }
    bfglob.gorgData.setList(data.rid, selOpt, data.orgIsVirtual)
  },
  // todo 所有数据请求，都应该独立并发，使用 async/await 和 Promise.all 来处理
  get_privilege_request(sid, user_rid) {
    bfglob.orgsIsLoaded = deferred()
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_org_list',
    }

    bfproto
      .sendMessage(13, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const org_list_obj = rpc_cmd_obj.body
        const rows = org_list_obj.rows

        for (const i in rows) {
          const item = rows[i]
          this.setGlobOrgData(item)
        }

        // todo 暂时使用Promise来标记请求完成
        bfglob.orgsIsLoaded?.resolve()

        //todo not tree in data manager platform
        // const initOrgNodes = () => {
        //   if (bfglob.treeLoaded) {
        //     addOrgNodeToTree('bftree', createTreeOrgSource(rows))
        //     orgNodesIsLoaded = true
        //   } else {
        //     setTimeout(initOrgNodes, 100)
        //   }
        // }
        // initOrgNodes()

        // 上下级单位数据中间部分单位没有权限，需要请求回来
        setTimeout(() => {
          const no_permission_org_ids = []
          const allOrgs = bfglob.gorgData.getAll()
          for (const i in allOrgs) {
            const item = allOrgs[i]
            if (item.parentOrgId === bfutil.DefOrgRid) continue

            const parentOrg = bfglob.gorgData.get(item.parentOrgId)
            if (!parentOrg) {
              no_permission_org_ids.push(item.parentOrgId)
            }
          }
          if (no_permission_org_ids.length) {
            this.fetchNoPermsOrgs(no_permission_org_ids, {
              requestCode: 1,
            }).then(result => {
              for (let i = 0; i < result.length; i++) {
                const item = result[i]
                const org = bfglob.gorgData.get(item.rid)
                if (org) continue

                this.setGlobalNoPermOrgData(item)
              }
            })
          }
        }, 0)

        // 获取控制器数据
        this.get_controller_data(bfglob.sessionId, bfglob.userInfo.rid)

        // 获取网关黑白名单数据
        this.get_gateway_filter_data()

        // 获取职称数据
        this.get_user_title_data(bfglob.sessionId, bfglob.userInfo.rid)

        // 获取地图标记点
        this.get_map_point_data(bfglob.sessionId, bfglob.userInfo.rid)

        // 计算调度组呼地址
        bfutil.calc_bc11_target_dmrId_address()

        this.getTopOrgMoveTitle()

        // 请求物联网终端数据
        if (bfglob.sysIniConfig.iotEnable) {
          this.get_iot_device_data()
        }

        this.listenBaseDataChanged()
      })
      .catch(err => {
        bfglob.console.warn('get_privilege', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getOrgDataError')).then(bfutil.systemReload)
      })
  },
  get_iot_device_data() {
    const msgObj = {
      sid: bfglob.sessionId,
      userRid: bfglob.userInfo.rid,
    }
    const msgOpts = {
      decodeMsgType: 'db_iot_device_list',
    }

    bfproto
      .sendMessage(29, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        bfglob.console.log('get_iot_device_data res:', rpc_cmd_obj)
        const rows = rpc_cmd_obj.body.rows
        if (!rows) {
          return
        }

        // 在地图上创建物联网终端marker
        const displayIotDeviceMarker = throttle(() => {
          showIotMarker()
        }, 500)
        const createMapMarker = data => {
          if (bfglob.map) {
            bfglob.giotDevices.setMarker(
              data.rid,
              maputil.mapMarker({
                type: MapMarkerTypes.IotDevice,
                id: data.rid,
                markerName: data.devName,
                className: 'iconfont icon-signal',
                lngLat: [data.lon, data.lat],
                data: data,
              })
            )
            displayIotDeviceMarker()
          } else {
            setTimeout(() => {
              createMapMarker(data)
            }, 200)
          }
        }

        for (let i = 0; i < rows.length; i++) {
          const item = rows[i]
          this.setGlobalIotDeviceData(item)
          createMapMarker(item)
        }

        // query all iot device last info
        this.getIotDeviceLastInfo(bfglob.sessionId, bfglob.userInfo.rid)
      })
      .catch(err => {
        bfglob.console.warn('get_iot_device_data catch:', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getIotDataError')).then(bfutil.systemReload)
      })
  },
  getIotDeviceLastInfo(sid, user_rid) {
    const msgObj = {
      sid: sid,
      userRid: user_rid,
    }

    const parseIotLastInfoRequestData = (msg_data, msg_reply, msg_subject, nats_ssid) => {
      const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data)
      const dbListName_t = bfproto.bfdx_proto_msg_T('db_iot_device_last_info_list')
      const db_iot_device_last_info_list_obj = dbListName_t.decode(rpc_cmd_obj.body)

      const rows = db_iot_device_last_info_list_obj.rows

      for (let i = 0; i < rows.length; i++) {
        const rowItem = rows[i]
        const iotDev = bfglob.giotDevices.get(rowItem.rid)
        if (!iotDev) {
          bfglob.console.warn('获取物联网终端失败', rowItem)
          continue
        }

        Object.assign(iotDev, rowItem)

        try {
          iotDev.optStatusMap = JSON.parse(iotDev.optStatus)
          iotDev.optStatusMap.cmdParam = hex2bin(iotDev.optStatusMap.cmdParam)
        } catch (_e) {
          iotDev.optStatusMap = {
            cmdParam: [],
          }
        }

        // 有些物联终端有参数要重新解析
        if (iotDev.lastCmd === TemperatureCmd.Report) {
          decodeTempHumiToIotDevice(iotDev, iotDev.optStatusMap.cmdParam)
        }

        // 更新地图物联终端图标状态
        updateIotDeviceMarkerInfo(iotDev)
      }

      if (rpc_cmd_obj.opt.endsWith('end')) {
        bfglob.server.unsubscribe(nats_ssid)
      }
    }

    const opt = uuid()
    const msgOpts = {
      rpcCmdFields: {
        opt,
      },
    }

    bfglob.server.subscribe(opt, parseIotLastInfoRequestData)

    bfproto
      .sendMessage(30, msgObj, 'client_privilege_request', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          // bfutil.messageBox(this.$t("msgbox.selSuccess,'success');
        } else {
          bfglob.console.error('获取物联网终端最后数据错误', rpc_cmd_obj.resInfo)
        }
      })
      .catch(err => {
        bfglob.console.warn('获取物联网终端最后数据超时', err)
        // bfutil.warningBox(i18n.global.t("msgbox.getDevDataError")).then(bfutil.systemReload);
      })
  },
  // 获取系统 logo saler_id 滚动文字
  getSysLogoAndTitle(data, sys_cmd) {
    const msgObj = {
      confKey: data,
    }
    const msgOpts = {
      decodeMsgType: 'db_sys_config_list',
      rpcCmdFields: {
        origReqId: 'conf_key',
        resInfo: 'rid,conf_key,conf_value',
      },
    }

    bfproto
      .sendMessage(sys_cmd, msgObj, 'db_sys_config', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const db_sys_config_list_obj = rpc_cmd_obj.body
        const rows = db_sys_config_list_obj.rows

        const len = rows.length
        const confKey = msgObj.confKey
        switch (confKey) {
          case 'salerId':
            if (len) {
              bfglob.salerId = rows[0].confValue
            } else {
              // bfutil.warningBox(i18n.global.t("msgbox.salerId,'error')
            }
            break
          case 'clientLogo':
            if (len) {
              const config = rows[0]
              bfglob.emit('logoImage', config.confValue)

              bfglob.systemSetting.clientLogo = config.confValue
              bfglob.systemSetting.logoRid = config.rid
              bfglob.systemSetting.logoConfKey = confKey
            }
            break
          case 'mapLevel':
            if (len) {
              const sys_obj = rows[0]
              const config = JSON.parse(sys_obj.confValue)

              bfglob.mapSetting.maxZoom = config.maxZoom
              bfglob.mapSetting.minZoom = config.minZoom
              bfglob.mapSetting.rid = sys_obj.rid
              bfglob.mapSetting.signale = true

              // bfglob.emit("resetMapZoom", config)
            }
            break
          default:
            if (confKey.indexOf('clientTitle') >= 0 && len) {
              const config = rows[0]
              bfglob.emit('moveTitle', config.confValue)
              bfglob.systemSetting.clientTitle = config.confValue
              bfglob.systemSetting.titleRid = config.rid
              bfglob.systemSetting.titleConfKey = confKey
            }
            break
        }
      })
      .catch(err => {
        bfglob.console.warn('获取系统logo和滚动文字超时', err)
        bfNotify.warningBox(i18n.global.t('msgbox.getOrgDataError')).then(bfutil.systemReload)
      })
  },
  saveSysLogoAndTitle(data, sys_cmd, confKey, rid) {
    const msgObj = {
      rid: rid || uuid(),
      confKey: confKey,
      confValue: data,
    }

    bfproto
      .sendMessage(sys_cmd, msgObj, 'db_sys_config', dbSubject)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          // bfNotify.messageBox(i18n.global.t('msgbox.sysSetSuccess'), 'success')

          let content = ''
          const confKey = msgObj.confKey
          const confValue = msgObj.confValue
          switch (confKey) {
            case 'salerId':
              break
            case 'clientLogo':
              bfglob.systemSetting.logoConfKey = confKey
              bfglob.systemSetting.clientLogo = confValue
              bfglob.systemSetting.logoRid = msgObj.rid
              bfglob.emit('logoImage', confValue)
              content = i18n.global.t('msgbox.updataClientLogo')
              break
            case 'mapLevel':
              const config = JSON.parse(confValue)
              bfglob.mapSetting.maxZoom = config.maxZoom
              bfglob.mapSetting.minZoom = config.minZoom
              bfglob.mapSetting.rid = msgObj.rid
              bfglob.mapSetting.signale = true
              // bfglob.emit("resetMapZoom", config);
              break
            default:
              if (confKey.indexOf('clientTitle') >= 0) {
                bfglob.systemSetting.titleConfKey = confKey
                bfglob.systemSetting.clientTitle = confValue
                bfglob.systemSetting.titleRid = msgObj.rid
                bfglob.emit('moveTitle', confValue)
                content = i18n.global.t('msgbox.updataClientTitle')
              }
              break
          }

          // 添加运行日志
          bfglob.emit('addnote', bfglob.userInfo.name + ' ' + content)
        } else {
          bfNotify.messageBox(i18n.global.t('msgbox.sysSetError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('系统设置超时', err)
        bfNotify.warningBox(i18n.global.t('msgbox.sysSetError')).then(bfutil.systemReload)
      })
  },
  updateUserSetting(userSetting, db_cmd, rid = bfglob.userInfo.rid, orgId = bfglob.userInfo.orgId) {
    const msgObj = {
      rid: rid,
      userSetting: userSetting,
      orgId,
    }
    const msgOpts = {
      rpcCmdFields: {
        origReqId: 'rid',
        resInfo: 'org_id,rid,user_setting',
      },
    }

    return bfproto
      .sendMessage(db_cmd, msgObj, 'db_user', dbSubject, msgOpts)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          return Promise.resolve(rpc_cmd_obj.resInfo)
        }

        bfglob.console.error('update user setting error', rpc_cmd_obj)
        bfNotify.messageBox(i18n.global.t('msgbox.upError'), 'error')
        return Promise.reject(rpc_cmd_obj.resInfo)
      })
      .catch(err => {
        bfglob.console.warn('update user settings error:', err)
        bfNotify.messageBox(i18n.global.t('msgbox.upError'), 'error')
        return Promise.reject()
      })
  },
  get_db_image(img_rid) {
    return new Promise((resolve, reject) => {
      const msgObj = {
        rid: img_rid,
      }
      const msgOpts = {
        decodeMsgType: 'db_image_list',
        rpcCmdFields: {
          origReqId: 'rid',
          resInfo: '*',
        },
      }

      bfproto
        .sendMessage(dbCmd.DB_IMAGE_GETBY, msgObj, 'db_image', dbSubject, msgOpts)
        .then(rpc_cmd_obj => {
          const db_image_obj = rpc_cmd_obj.body
          if (rpc_cmd_obj.resInfo === '+OK') {
            // 将图片保存给全局变量 bfglob.gimages
            const item = db_image_obj.rows[0]
            item && bfglob.gimages.set(item.rid, item)
            resolve(item)
          } else {
            reject(rpc_cmd_obj.resInfo)
          }
        })
        .catch(err => {
          bfglob.console.warn('获取图片超时', err)
          reject(err)
        })
    })
  },
  set_db_image(orgId, fileName = '', fileContent = '', add_db_cmd) {
    return new Promise((resolve, reject) => {
      const msgObj = {
        rid: uuid(),
        orgId: orgId,
        fileName: fileName,
        fileContent: fileContent,
        hash: bfCrypto.sha256(fileContent),
      }

      bfproto
        .sendMessage(add_db_cmd, msgObj, 'db_image', dbSubject)
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo === '+OK') {
            bfglob.gimages.set(msgObj.rid, msgObj)
            resolve(msgObj)
          } else {
            reject(rpc_cmd_obj.resInfo)
          }
        })
        .catch(err => {
          bfglob.console.warn('创建用户图像超时', err)
          reject(0)
        })
    })
  },
  listenServerCmdSubject() {
    bfglob.on('2000', rpc_cmd_obj => {
      this.bc00(rpc_cmd_obj)
    })
    bfglob.on('2042', rpc_cmd_obj => {
      this.bc42(rpc_cmd_obj)
    })
    bfglob.on('2001', rpc_cmd_obj => {
      this.bc01(rpc_cmd_obj)
    })
    bfglob.on('2002', rpc_cmd_obj => {
      this.bc02(rpc_cmd_obj)
    })
    bfglob.on('2003', rpc_cmd_obj => {
      this.bc03(rpc_cmd_obj)
    })
    bfglob.on('2004', rpc_cmd_obj => {
      this.bc04(rpc_cmd_obj)
    })
    bfglob.on('2005', rpc_cmd_obj => {
      this.bc05(rpc_cmd_obj)
    })
    bfglob.on('2006', rpc_cmd_obj => {
      this.bc06(rpc_cmd_obj)
    })
    bfglob.on('2007', rpc_cmd_obj => {
      this.bc07(rpc_cmd_obj)
    })
    bfglob.on('2009', rpc_cmd_obj => {
      this.bc09(rpc_cmd_obj)
    })
    bfglob.on('2011', rpc_cmd_obj => {
      this.bc11(rpc_cmd_obj)
    })
    bfglob.on('2012', rpc_cmd_obj => {
      this.bc12(rpc_cmd_obj)
    })
    bfglob.on('2013', rpc_cmd_obj => {
      this.bc13(rpc_cmd_obj)
    })
    bfglob.on('2014', rpc_cmd_obj => {
      this.bc14(rpc_cmd_obj)
    })
    // bfglob.on("2015", (rpc_cmd_obj) => {
    //   this.bc15(rpc_cmd_obj);
    // });
    bfglob.on('2115', rpc_cmd_obj => {
      this.bc15New(rpc_cmd_obj)
    })
    bfglob.on('2016', rpc_cmd_obj => {
      this.bc16(rpc_cmd_obj)
    })
    bfglob.on('2018', rpc_cmd_obj => {
      this.bc18(rpc_cmd_obj)
    })
    bfglob.on('2025', rpc_cmd_obj => {
      this.bc25(rpc_cmd_obj)
    })
    bfglob.on('2032', rpc_cmd_obj => {
      this.bc32(rpc_cmd_obj)
    })
    bfglob.on('1100', rpc_cmd_obj => {
      this.bc1100(rpc_cmd_obj)
    })
    bfglob.on('1101', rpc_cmd_obj => {
      this.bc1101(rpc_cmd_obj)
    })
    bfglob.on('1102', rpc_cmd_obj => {
      this.bc1102(rpc_cmd_obj)
    })
    bfglob.on('2201', rpc_cmd_obj => {
      this.dc01(rpc_cmd_obj)
    })
    // 动态组及成员状态变更通知
    bfglob.on('1317', rpc_cmd_obj => {
      this.dynamicGroupChangeNotice(rpc_cmd_obj)
    })
    // 新加入的动态组
    bfglob.on('1318', rpc_cmd_obj => {
      this.createDynamicGroup(rpc_cmd_obj)
    })
    // 更新动态组所有成员信息，包含更新、删除、添加
    bfglob.on('1331', rpc_cmd_obj => {
      this.modifyDynamicGroupMemberStatus(rpc_cmd_obj)
    })
    if (bfglob.sysIniConfig.iotEnable) {
      // iot_data 事件
      bfglob.on('3', rpc_cmd_obj => {
        processIotDataCmd(rpc_cmd_obj)
      })
      // 新的iot_device
      bfglob.on('1110', rpc_cmd_obj => {
        newIotDeviceCmd(rpc_cmd_obj)
      })

      // 网络对讲终端请求终端位置信息
      bfglob.on('8181', this.cmd8181)
      // 已同意网络对讲终端对终端位置信息的请求
      bfglob.on('8182', this.cmd8182)
    }
  },
  addDynamicGroupMember(members) {
    processDetailList(members).catch(() => {})
  },
  cmd8182(rpc_cmd_obj) {
    const data = bfproto.decodeMessage(rpc_cmd_obj.body, 'db_app_map_privilege_device')
    if (rpc_cmd_obj.sid !== bfglob.sessionId) {
      bfglob.emit('deleteItemPrivilege', data)
    }
  },
  cmd8181(rpc_cmd_obj) {
    const data = bfproto.decodeMessage(rpc_cmd_obj.body, 'cc81')
    if (!bfglob.gdevices.getDataByIndex(toHexDmrId(data.applyDmrId)) || bfutil.notEditDataPermission()) {
      return
    }
    const defaultData = {
      rid: uuid(),
      appDmrid: toHexDmrId(data.applyDmrId),
      grantDeviceDmrid: toHexDmrId(data.targetDmrId),
      grantUserRid: bfglob.userInfo.rid,
      expireTime: addTime(new Date(), 3, 'day').format(DateMask) + ' 23:59:59',
      isSetExpire: 1,
      applyTime: nowUtcTime(),
      grantTime: '',
      grantUserName: bfglob.userInfo.name,
    }
    bfglob.emit('openMenuItem', 'command/authAppMapPrivilegeDevice', vm => {
      const p = vm.privilegeDeviceFromAppReqData.find(v => v.appDmrid === defaultData.appDmrid && v.grantDeviceDmrid === defaultData.grantDeviceDmrid)
      if (p) {
        p.applyTime = defaultData.applyTime
      } else {
        vm.privilegeDeviceFromAppReqData.push(defaultData)
      }
    })
  },
  updateDynamicGroupMember(members) {
    // 找出状态为11的成员，进行删除
    const deleteMembers = []
    const updateMembers = []
    const deleteStatus = [MemberState.Delete, MemberState.AnsweredExit]
    for (let i = 0; i < members.length; i++) {
      const data = members[i]
      if (deleteStatus.includes(data.memberState)) {
        deleteMembers.push(data)
      } else {
        updateMembers.push(data)
      }
    }

    // update member
    updateMembers.length && bfglob.emit('update_global_dynamic_group_detail', updateMembers)
    updateMembers.forEach(v => {
      checkPcDeviceJoinDynamicGroup(v).catch()
      checkPcDeviceExitDynamicGroup(v).catch()
    })

    // delete member
    this.deleteDynamicGroupMember(deleteMembers, true)
  },
  deleteDynamicGroupMember(members, force = false) {
    members.length && bfglob.emit('remove_global_dynamic_group_detail', members, force)
    members.forEach(v => {
      checkPcDeviceExitDynamicGroup(v).catch(() => {})
    })
  },
  filterDynamicGroupMemberWithStatus(tempGroupList) {
    const dynamicGroup = tempGroupList.dynamicGroup[0]
    const members = tempGroupList.members
    // 待返回的结果集
    const result = {
      add: [],
      remove: [],
      keep: [],
    }

    // 查找动态组下的所有成员数据
    const groupDetails = bfglob.gdynamicGroupDetail.getDataByGroupRid(dynamicGroup.rid)
    // 未被处理的所有详情
    const unProcessDetails = []
    const processRemoveDetail = detail => {
      if (members.find(item => item.rid === detail.rid)) {
        unProcessDetails.push(detail)
      } else {
        result.remove.push(detail)
      }
    }
    const processAddOrKeepDetail = detail => {
      const index = unProcessDetails.findIndex(item => item.rid === detail.rid)
      // 在原数据中查找到数据，则保持不变
      if (index >= 0) {
        result.keep.push(detail)
        unProcessDetails.splice(index, 1)
      } else {
        result.add.push(detail)
      }
    }

    // 遍历一次原数据，筛选出被删除的详情
    for (let i = 0; i < groupDetails.length; i++) {
      processRemoveDetail(groupDetails[i])
    }

    // 遍历一次当前选择的数据，筛选出需要添加的详情
    for (let j = 0; j < members.length; j++) {
      processAddOrKeepDetail(members[j])
    }

    return result
  },
  modifyDynamicGroupMemberStatus(rpcCmd) {
    const temp_group_list_type = bfproto.bfdx_proto_msg_T('temp_group_list')
    const body = (rpcCmd.body = temp_group_list_type.decode(rpcCmd.body))
    const { add, keep, remove } = this.filterDynamicGroupMemberWithStatus(body)
    bfglob.console.log('[1331] modifyDynamicGroupMemberStatus', body, add, keep, remove)
    const dynamicGroup = body.dynamicGroup[0]
    this.updateDynamicGroup(dynamicGroup)

    if (add.length) {
      this.addDynamicGroupMember(add)
    }

    if (keep.length) {
      this.updateDynamicGroupMember(keep)
    }

    if (remove.length) {
      this.deleteDynamicGroupMember(remove, true)
    }
  },
  deleteDynamicGroup(rpcCmd) {
    // 删除本地动态组数据
    const dynamicGroup = rpcCmd.body.dynamicGroup[0]
    if (!dynamicGroup) {
      return
    }

    // 发事件，删除对应的动态组数据及节点
    bfglob.emit('delete_global_dynamic_group', dynamicGroup)
    this.publishTableEvent('dynamicGroupTable', 'delete', dynamicGroup)
  },
  async createDynamicGroup(rpcCmd) {
    const temp_group_list_coder = bfproto.bfdx_proto_msg_T('temp_group_list')
    rpcCmd.body = temp_group_list_coder.decode(rpcCmd.body)
    bfglob.console.log('[1318] createDynamicGroup', rpcCmd)

    const dynamicGroup = rpcCmd.body.dynamicGroup[0]
    if (!dynamicGroup) {
      return
    }

    // 处理动态组本身数据
    processDynamicGroup(dynamicGroup)
    // 处理动态组成员
    await processDetailList(rpcCmd.body.members).catch(() => {})

    // 主动查询一次动态组成员状态,因名个客户端的网络状态不一样,可能导致部分客户端接收不到完整的状态通知
    const requestRpcCmd = await requestDynamicGroupStatus(dynamicGroup).catch(() => {})
    requestRpcCmd.body.dynamicGroup.length > 0 && this.updateDynamicGroup(requestRpcCmd.body.dynamicGroup[0])
    this.updateDynamicGroupMember(requestRpcCmd.body.members)
  },
  updateDynamicGroup(dynamicGroup) {
    bfglob.emit('update_global_dynamic_group', dynamicGroup)
  },
  dynamicGroupChangeNotice(rpc_cmd_obj) {
    const temp_group_list_coder = bfproto.bfdx_proto_msg_T('temp_group_list')
    rpc_cmd_obj.body = temp_group_list_coder.decode(rpc_cmd_obj.body)
    bfglob.console.log('[1317] dynamic group notice:', rpc_cmd_obj)

    // 一个动态组一次通知，只取第一个元素
    const dynamicGroup = rpc_cmd_obj.body.dynamicGroup[0]
    if (!dynamicGroup) {
      return
    }

    const members = rpc_cmd_obj.body.members
    // 主要接收动态组下成员的添加、删除、状态变更等通知
    switch (rpc_cmd_obj.opt) {
      case 'Insert':
        this.updateDynamicGroup(dynamicGroup)
        this.addDynamicGroupMember(members)
        break
      case 'Delete':
        this.updateDynamicGroup(dynamicGroup)
        this.deleteDynamicGroupMember(members, true)
        break
      case 'Update':
        this.updateDynamicGroup(dynamicGroup)
        this.updateDynamicGroupMember(members)
        break
      case 'DeleteGroup':
        this.deleteDynamicGroup(rpc_cmd_obj)
        break
    }
  },
  listenOnceCtrlOnlineStats(dmrIds, actionCode = 1) {
    // listen 83 cmd response
    if (!isListenCmd83) {
      isListenCmd83 = true
      bfglob.on('83', rpc_cmd_obj => {
        this.cmd83(rpc_cmd_obj)
      })
    }

    const msgObj = {
      actionCode: actionCode,
      hexDmrids: dmrIds,
    }

    bfproto
      .sendMessage(83, msgObj, 'cc83', radioSubject)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          this.cmd83(rpc_cmd_obj)
        }
      })
      .catch(err => {
        bfglob.console.warn('查询控制器参数超时', err)
      })
  },
  updateCtrlItemPosition(ctrlItem) {
    // 找到控制器marker
    var marker = bfglob.gcontrollers.getMarker(ctrlItem.rid)
    if (!marker) {
      return
    }
    // 重新设置marker的定位位置
    marker.setLngLat([ctrlItem.lon, ctrlItem.lat])
  },
  listenCtrlRoomChanged(ccxxStr, ctrlItem) {
    if (!ccxxStr) {
      return
    }
    var ccxxStrArr = ccxxStr.split(',')
    var room_stats_str = ccxxStrArr[ccxxStrArr.length - 1].replace('#', '')
    if (ctrlItem.currentRoom !== room_stats_str) {
      ctrlItem.currentRoom = room_stats_str
      // bfglob.emit("roomsListchanged", ctrlItem);
    }

    // 处理控制器定位信息
    if (ccxxStrArr[2] !== 'CC01') {
      return
    }
    var lon = ccxxStrArr[16]
    var lat = ccxxStrArr[14]
    const invalid = ccxxStrArr[13] === 'V'
    // 判断控制器是否有定位,A：有效，V：无效
    if (invalid) {
      return
    }
    // 判断经度、纬度是否有效
    const lon_dd = parseInt(lon / 100)
    const lon_mm = lon % 100
    lon = lon_dd + lon_mm / 60
    const lat_dd = parseInt(lat / 100)
    const lat_mm = lat % 100
    lat = lat_dd + lat_mm / 60

    if (ctrlItem.lon !== lon || ctrlItem.lat !== lat) {
      // 控制器坐标有变化，更新地图上控制器的坐标位置
      ctrlItem.lon = lon
      ctrlItem.lat = lat
      this.updateCtrlItemPosition(ctrlItem)
    }
  },
  update_controller_marker_popup(ctrlItem) {
    var marker = bfglob.gcontrollers.getMarker(ctrlItem.rid)
    if (!marker) {
      return
    }
    marker.updatePopup(maputil.get_popup_content_for_controller(ctrlItem))
  },
  setControllerStats(ctrlItem, stats) {
    // stats: 0断开，1连接，3报警

    // 更新控制器 marker 的popup内容
    ctrlItem.lastDataTime = bfTime.nowLocalTime()
    this.update_controller_marker_popup(ctrlItem)

    ctrlItem.ctrlStats = stats
    bfglob.emit('update_controller_stats', ctrlItem)
  },
  cc01(rpc_cmd_obj) {
    var that = this
    var cc01_t = bfproto.bfdx_proto_msg_T('cc01')
    var cc01_obj = cc01_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('cc01_obj 80 cmd', cc01_obj)

    // 通过 dmr_id 找到控制器数据
    var ctrlItem = bfglob.gcontrollers.getDataByIndex(cc01_obj.dmrId)
    if (!ctrlItem) {
      bfglob.console.error('没有此控制器', cc01_obj)
      return
    }
    var result = {
      title: '',
      content: '',
      btn: false,
    }
    switch (cc01_obj.actionCode) {
      case 1:
        //= 1 控制器连接注册成功
        bfglob.emit('bc11_destroyDialog', ctrlItem.rid)
        that.setControllerStats(ctrlItem, 1)
        break
      case 2:
        //= 2 控制器连接已断开
        if (bfglob.userInfo.setting.showCtrlOnlineInfo) {
          result.title += ctrlItem.orgShortName + '/' + ctrlItem.selfId + '/' + ctrlItem.dmrId
          result.content += '<p>' + i18n.global.t('msgbox.ctrlDisConnect') + '</p>'
          result.content += '<p>' + bfTime.nowLocalTime() + '</p>'
          that.show_bcxx_result(ctrlItem, result, 'cc01')
        }
        that.setControllerStats(ctrlItem, 0)
        break
      case 3:
        //= 3 控制器报警
        result.title += ctrlItem.orgShortName + '/' + ctrlItem.selfId + '/' + ctrlItem.dmrId
        result.content += '<p>' + i18n.global.t('msgbox.ctrollerFailureAlarm') + '</p>'
        result.content += '<p>' + bfTime.nowLocalTime() + '</p>'
        that.show_bcxx_result(ctrlItem, result, 'cc01')
        that.setControllerStats(ctrlItem, 3)
        break
      case 4:
        //= 4 设置控制器参数
        break
      case 5:
        //= 5 查询控制器参数
        break
      case 6:
        //= 6,控制器在线监测
        bfglob.emit('bc11_destroyDialog', ctrlItem.rid)
        that.setControllerStats(ctrlItem, 1)
        break
      case 103:
        // 控制器连接会议室状态
        // that.listenCtrlRoomChanged(cc01_obj.ccxxStr,ctrlItem);
        break
    }

    that.listenCtrlRoomChanged(cc01_obj.ccxxStr, ctrlItem)

    // 发布重新过滤在线设备事件
    bfglob.emit('refilterOnlineCtrol')
  },
  setOnlineDeviceState(device, online = 0) {
    // online=0,默认为关机
    if (!device) {
      return
    }

    const nowTime = bfTime.nowUtcTime()
    if (online === 1) {
      device.lastPoweronTime = nowTime
    } else {
      device.lastPoweroffTime = nowTime
    }
    device.pS = online
    device.lastDataTime = nowTime

    device = assignFixedDeviceLonLat(device)
    this.updateDeviceNodeAndMarker(device)
  },
  cmd83(rpc_cmd_obj) {
    const rpc_cmd_body = bfproto.decodeMessage(rpc_cmd_obj.body, 'cc83')
    bfglob.console.log('cc83 cmd', rpc_cmd_body)

    for (let i = 0; i < rpc_cmd_body.hexDmrids.length; i++) {
      const dmrId = rpc_cmd_body.hexDmrids[i]

      // 通过 dmrId 找到目标中继器/手台设备数据
      let target
      switch (rpc_cmd_body.actionCode) {
        case 1:
        case 2:
        case 3:
        case 4:
          // 目标是中继
          target = bfglob.gcontrollers.getDataByIndex(dmrId)
          break
        default:
          // 目标是手台
          target = bfglob.gdevices.getDataByIndex(dmrId)
      }
      if (!target) {
        bfglob.console.error('没有找到对应设备', dmrId)
        continue
      }

      const result = {
        title: '',
        content: '',
        btn: false,
      }
      switch (rpc_cmd_body.actionCode) {
        // 1:查询中继在线状态,
        // 2:回应查询 1,返回在线的中继
        // 3:中继上线通知
        // 4:中继下线通知
        // 11:查询手台在线状态
        // 12:回应查询11,返回在线的手台
        // 13:手台上线通知
        // 14:手台下线通知
        case 1:
        case 2:
        case 3:
          // 中继在线
          // 缓存中继的型号信息
          target.model = rpc_cmd_body.models[i]
          bfglob.emit('bc11_destroyDialog', target.rid)
          target.functions = (rpc_cmd_body.functions[i] ?? '').split(',')
          this.setControllerStats(target, 1)
          break
        case 4:
          // 中继下线
          if (bfglob.userInfo.setting.showCtrlOnlineInfo) {
            result.title += target.orgShortName + '/' + target.selfId + '/' + target.dmrId
            result.content += '<p>' + i18n.global.t('msgbox.ctrlDisConnect') + '</p>'
            result.content += '<p>' + bfTime.nowLocalTime() + '</p>'
            this.show_bcxx_result(target, result, 'cc01')
          }
          this.setControllerStats(target, 0)
          break
        case 12:
        case 13:
          // 手台在线
          this.setOnlineDeviceState(target, 1)
          break
        case 14:
          // 手台下线
          this.setOnlineDeviceState(target, 0)
          break
      }
    }
  },
  // 解析bc00应答指令
  delete_bcxx_obj_resNo(bcxx_obj, cbxx) {
    if (
      typeof this.g_cbxx_cmd_resp_obj[cbxx] === 'undefined' ||
      typeof this.g_cbxx_cmd_resp_obj[cbxx].targetDevice === 'undefined' ||
      typeof this.g_cbxx_cmd_resp_obj[cbxx].targetDevice[bcxx_obj.head.mIdS] === 'undefined'
    ) {
      return
    }
    if (cbxx === 'CB01') {
      if (bcxx_obj.gps.av !== 0) {
        bfglob.map.setCenter([bcxx_obj.gps.lon, bcxx_obj.gps.lat])
        delete this.g_cbxx_cmd_resp_obj[cbxx].targetDevice[bcxx_obj.head.mIdS]
        delete this.g_cbxx_cmd_resp_obj[cbxx].targetDevice
        if (Object.keys(this.g_cbxx_cmd_resp_obj[cbxx]).length === 0) {
          delete this.g_cbxx_cmd_resp_obj[cbxx]
        }
        return
      }
    }
    delete this.g_cbxx_cmd_resp_obj[cbxx].targetDevice[bcxx_obj.head.mIdS]
    if (Object.keys(this.g_cbxx_cmd_resp_obj[cbxx].targetDevice).length === 0) {
      delete this.g_cbxx_cmd_resp_obj[cbxx].targetDevice
    }
    if (Object.keys(this.g_cbxx_cmd_resp_obj[cbxx]).length === 0) {
      delete this.g_cbxx_cmd_resp_obj[cbxx]
    }
    if (cbxx === 'CB09') {
      delete this.g_cbxx_cmd_resp_obj[cbxx]
    }
  },

  updateDeviceNodeAndMarker(device) {
    bfglob.emit('updateDeviceNodeTitle', device)
    bfglob.emit('updateDeviceMarker', device)
  },
  add_runNote_for_bcxx(device, content) {
    var user = device.selfId
    if (device.userName) {
      user += '/' + device.userName
    }
    bfglob.emit('addnote', user + ' ' + content)
  },
  update_device_state_data(device) {
    var title = device.selfId
    if (device.userName) {
      title += '/' + device.userName
    }
    var tableObj = {
      title: title,
      name: device.rid,
      device: device,
    }
    bfglob.emit('show_device_state', tableObj)
  },
  update_device_last_datatime(device, head) {
    device.lastDataTime = head.cmdTime
    device.lastController = head.conCh
    if (head.msStatus.length > 0) {
      device.msStatus = head.msStatus
      device.msStatusBin = bfutil.ms_status_hex2bin(head.msStatus)
      // bfglob.emit('addnote', device.selfId + '-' + device.dmrId + '-' + device.msStatus + '-' + device.msStatusBin)
    }
  },
  update_device_gps(device, gps) {
    if (!gps) {
      return
    }

    // 服务器模拟定位指令，以让特定设备(如带插话功能中继)保活，忽略不处理
    if (gps.av === 4) {
      return
    }

    if (gps.av === 0) {
      device.av = 2
      return
    }

    // 如果当前的gps数据时间比最后定位时间还要早，则停止更新数据
    if (!isSameOrAfter(gps.gpsTime, device.lastGpsTime)) {
      return
    }

    device.av = gps.av
    device.lastGpsTime = gps.gpsTime
    device.lastLon = gps.lon
    device.lastLat = gps.lat
    device.speed = gps.speed
    device.direction = gps.direction
    device.altitude = gps.altitude

    // 检测定位时间是否过期
    device.av = bfutil.checked_device_gps_is_valid(device)

    this.resetDeviceReadRfidLnglat(device)
  },
  bc00_respone_for_cbxx(cDic, bc00_obj, device, promptContent) {
    var cbxx
    bc00_obj.cbxx += ''
    if (bc00_obj.cbxx.length === 1) {
      cbxx = 'CB0' + bc00_obj.cbxx
    } else if (bc00_obj.cbxx.length === 2) {
      cbxx = 'CB' + bc00_obj.cbxx
    }
    // 切换信道，信道值大于99的服务器自动转换为CB41指令下发终端
    if (cbxx === 'CB41') {
      cbxx = 'CB21'
    }
    var res_obj = this.g_cbxx_cmd_resp_obj[cbxx]
    if (typeof res_obj === 'undefined' || typeof res_obj.targetDevice === 'undefined' || typeof res_obj.targetDevice[bc00_obj.head.mIdS] === 'undefined') {
      bfglob.console.error('非本中心下发的命令', cbxx, cDic, bc00_obj.head.mIdS)
      return
    }
    var resNo = res_obj.targetDevice[bc00_obj.head.mIdS]
    bfglob.console.log('bc00 回应指令序号', cbxx, cDic, resNo)

    var spac = i18n.locale === 'en' ? ' ' : ''
    if (cDic === resNo) {
      device.promptContent = promptContent
      device.userImageFile = bfutil.get_device_userImageFile(device)
      if (cbxx === 'CB09') {
        var cb09Cmd = this.g_cbxx_cmd_resp_obj[cbxx].cb09Cmd
        var cb09CmdLabel
        if (cb09Cmd === 0) {
          cb09CmdLabel = i18n.global.t('msgbox.unLocked')
        } else {
          cb09CmdLabel = i18n.global.t('msgbox.locked')
        }
        device.promptContent = device.userName + spac + cb09CmdLabel + i18n.global.t('msgbox.success')
      }
      bfutil.createNotify(device)

      if (cbxx === 'CB01') {
        if (bc00_obj.gps.av !== 0) {
          bfglob.map.setCenter([bc00_obj.gps.lon, bc00_obj.gps.lat])
          delete this.g_cbxx_cmd_resp_obj[cbxx].targetDevice[bc00_obj.head.mIdS]
          delete this.g_cbxx_cmd_resp_obj[cbxx].targetDevice
          if (Object.keys(res_obj).length === 0) {
            delete this.g_cbxx_cmd_resp_obj[cbxx]
          }
          return
        }
      }
      delete this.g_cbxx_cmd_resp_obj[cbxx].targetDevice[bc00_obj.head.mIdS]
      if (Object.keys(res_obj.targetDevice).length === 0) {
        delete this.g_cbxx_cmd_resp_obj[cbxx].targetDevice
      }
      if (Object.keys(res_obj).length === 0) {
        delete this.g_cbxx_cmd_resp_obj[cbxx]
      }
    }
  },
  bc00(rpc_cmd_obj) {
    var bc00_t = bfproto.bfdx_proto_msg_T('bc00')
    var bc00_obj = bc00_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc00_obj', bc00_obj, bfutil.uintToString(bc00_obj.head.origData))

    var device = bfglob.gdevices.getDataByIndex(bc00_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc00_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc00_obj.head)
    this.update_device_gps(device, bc00_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    var cDic = bfutil.toHex(bc00_obj.cDic)
    var prompt = ''
    var content = ''
    // var spac = i18n.locale === 'en' ? ' ' : ''
    // 定位监控
    if (bc00_obj.cbxx === 1) {
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, i18n.global.t('msgbox.locateCmdSendOk'))
      // 添加运行日志
      content = i18n.global.t('msgbox.resLocateCmd')
      this.add_runNote_for_bcxx(device, content)
    }
    // 跟踪监控
    if (bc00_obj.cbxx === 2) {
      if (typeof bfglob.vsendcmd !== 'undefined') {
        if (bfglob.vsendcmd.cb02Cmd.track === 0) {
          prompt = i18n.global.t('msgbox.cancelTrailCmd') + i18n.global.t('msgbox.success')
          content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.cancelTrailCmd')
        } else if (bfglob.vsendcmd.cb02Cmd.track === 1) {
          prompt = i18n.global.t('msgbox.enableTrailCmd') + i18n.global.t('msgbox.success')
          content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableTrailCmd')
        }
      } else {
        prompt = i18n.global.t('msgbox.trailCtrlCmdOk')
        content = i18n.global.t('msgbox.resTrailCtrlCmd')
      }
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }
    // 电子围栏
    if (bc00_obj.cbxx === 4) {
      if (typeof bfglob.vsendcmd !== 'undefined') {
        switch (bfglob.vsendcmd.cb04Cmd.setCmd) {
          case 0:
            prompt = i18n.global.t('dialog.removeFenceAllCtrl') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('dialog.removeFenceAllCtrl')
            break
          case 1:
            prompt = i18n.global.t('msgbox.enableInCtrlCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableInCtrlCmd')
            break
          case 2:
            prompt = i18n.global.t('msgbox.enableOutCtrlCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableOutCtrlCmd')
            break
          case 3:
            prompt = i18n.global.t('msgbox.enableInAndOut') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableInAndOut')
            break
        }
      } else {
        prompt = i18n.global.t('msgbox.fenceCmdSendOk')
        content = i18n.global.t('msgbox.resFenceCtrlCmd')
      }
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }
    // 岗哨围栏
    if (bc00_obj.cbxx === 5) {
      if (typeof bfglob.vsendcmd !== 'undefined') {
        switch (bfglob.vsendcmd.cb05Cmd.setCmd) {
          case 0:
            prompt = i18n.global.t('msgbox.cancelSentinelCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.cancelSentinelCmd')
            break
          case 1:
            prompt = i18n.global.t('msgbox.enableSentinelCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableSentinelCmd')
            break
        }
      } else {
        prompt = i18n.global.t('msgbox.sentinelCtrlSendOk')
        content = i18n.global.t('msgbox.resSentinelCtrlCmd')
      }
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }
    // 移动监控
    if (bc00_obj.cbxx === 6) {
      if (typeof bfglob.vsendcmd !== 'undefined') {
        switch (bfglob.vsendcmd.cb06Cmd.setCmd) {
          case 0:
            prompt = i18n.global.t('msgbox.cancelMoveCtrlCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.cancelMoveCtrlCmd')
            break
          case 1:
            prompt = i18n.global.t('msgbox.enableMoveCtrlCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableMoveCtrlCmd')
            break
        }
      } else {
        prompt = i18n.global.t('msgbox.moveCtrlSendOk')
        content = i18n.global.t('msgbox.resMoveCtrlCmd')
      }
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }
    // 报警设置
    if (bc00_obj.cbxx === 7) {
      if (typeof bfglob.vsendcmd !== 'undefined') {
        switch (bfglob.vsendcmd.cb07Cmd.setCmd) {
          case 0:
            prompt = i18n.global.t('msgbox.cancelAlarmCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.cancelAlarmCmd')
            break
          case 1:
            prompt = i18n.global.t('msgbox.enableAlarmCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableAlarmCmd')
            break
        }
      } else {
        prompt = i18n.global.t('msgbox.alarmSetSendOk')
        content = i18n.global.t('msgbox.resAlarmSetCmd')
      }
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }
    // 语音监控
    if (bc00_obj.cbxx === 8) {
      if (typeof bfglob.vsendcmd !== 'undefined') {
        switch (bfglob.vsendcmd.cb08Cmd.setCmd) {
          case 0:
            prompt = i18n.global.t('msgbox.cancelVioCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.cancelVioCmd')
            break
          case 1:
            prompt = i18n.global.t('msgbox.enableVioCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableVioCmd')
            break
        }
      } else {
        prompt = i18n.global.t('msgbox.vioCtrlSendOk')
        content = i18n.global.t('msgbox.resVioCtrlCmd')
      }
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }
    // 遥开遥闭
    if (bc00_obj.cbxx === 9) {
      if (typeof bfglob.vsendcmd !== 'undefined') {
        switch (bfglob.vsendcmd.cb09Cmd.status) {
          case 0:
            content = i18n.global.t('msgbox.resClearLockDev')
            break
          case 1:
            content = i18n.global.t('msgbox.resDisListen')
            break
          case 2:
            content = i18n.global.t('msgbox.resDisSend')
            break
          case 3:
            content = i18n.global.t('msgbox.resDisSendListen')
            break
        }
      } else {
        content = i18n.global.t('msgbox.resCb09Cmd')
      }
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, '')
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }
    // 解除报警
    if (bc00_obj.cbxx === 10) {
      // 发布解除紧急报警消息，删除对应设备报警弹框
      bfglob.emit('destroy_alarm_dialog_of_bc00', device.rid)

      prompt = i18n.global.t('msgbox.clearAlarmOk')
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      content = i18n.global.t('msgbox.clearEGAlarm')
      this.add_runNote_for_bcxx(device, content)
    }
    // 切换信道
    if (bc00_obj.cbxx === 21 || bc00_obj.cbxx === 41) {
      // if (typeof bfglob.vsendcmd !== 'undefined') {
      //   // switch (bfglob.vsendcmd.cb09Cmd.setCmd) {
      //   //   case 0:
      //   //     prompt = i18n.global.t("dialog.cancelCenterCHCtrl") +
      //   //       i18n.global.t("msgbox.success");
      //   //     content = i18n.global.t("msgbox.respond") +
      //   //       i18n.global.t("dialog.cancelCenterCHCtrl");
      //   //     break;
      //   //   case 1:
      //   //     prompt = i18n.global.t("dialog.openCenterCHCtrl") +
      //   //       i18n.global.t("msgbox.success");
      //   //     content = i18n.global.t("msgbox.respond") +
      //   //       i18n.global.t("dialog.openCenterCHCtrl");
      //   //     break;
      //   // }
      // } else {
      //   prompt = i18n.global.t("msgbox.centerChannelCtrl");
      //   content = i18n.global.t("msgbox.resCenterChannelCtrl");
      // }
      prompt = i18n.global.t('msgbox.centerChannelCtrl')
      content = i18n.global.t('msgbox.resCenterChannelCtrl')
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }
    // 文本短信
    if (bc00_obj.cbxx === 24) {
      prompt = i18n.global.t('msgbox.sendTextMsgCmd') + i18n.global.t('msgbox.success')
      content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.sendTextMsgCmd')
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }
    // GPS虚拟点
    if (bc00_obj.cbxx === 25) {
      if (typeof bfglob.vsendcmd !== 'undefined') {
        switch (bfglob.vsendcmd.cb25Cmd.setCmd) {
          case 0:
            prompt = i18n.global.t('msgbox.cancelGPSVirCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.cancelGPSVirCmd')
            break
          case 1:
            prompt = i18n.global.t('msgbox.enableGPSVirCmd') + i18n.global.t('msgbox.success')
            content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableGPSVirCmd')
            break
        }
      } else {
        prompt = i18n.global.t('msgbox.gpsVirSendOk')
        content = i18n.global.t('msgbox.resGpsVirCmd')
      }
      this.bc00_respone_for_cbxx(cDic, bc00_obj, device, prompt)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, content)
    }

    // 更新对讲机所有状态表
    this.update_device_state_data(device)
  },

  show_bcxx_result(device, result, bcxx) {
    let title = device.selfId
    if (device.userName) {
      title += ' / ' + device.userName
    }
    const _result = {
      content: result,
      btn: false,
    }
    if (bcxx === 'dc01') {
      _result.title = device.orgShortName + '/' + device.pointId + '/' + device.rfidId
    }
    if (bcxx !== 'bc03') {
      _result.title = title
    }
    if (bcxx === 'bc25') {
      _result.btn = device.pointCard === '0000000000' ? false : 'gps'
      _result.pointCard = device.pointCard
    }
    let contentObj = {
      name: device.rid,
      result: _result,
    }
    if (bcxx === 'bc1100') {
      contentObj.name = device.title
      _result.title = device.title
      _result.btn = 'dev'
      _result.dmrId = device.dmrId
    }
    if (bcxx === 'bc1101') {
      contentObj.name = device.title
      _result.title = device.title
    }
    if (bcxx === 'bc1102') {
      contentObj.name = device.title
      _result.title = device.title
      _result.btn = 'ctrl'
      _result.dmrId = device.dmrId
    }
    if (bcxx === 'cc01') {
      contentObj = {
        name: device.rid,
        result: result,
      }
    }

    bfglob.emit('show_bcxx_result', contentObj)
  },
  checked_bcxx_bDic_is_current_center(bcxx_obj, cbxx) {
    if (
      typeof this.g_cbxx_cmd_resp_obj[cbxx] === 'undefined' ||
      typeof this.g_cbxx_cmd_resp_obj[cbxx].targetDevice === 'undefined' ||
      typeof this.g_cbxx_cmd_resp_obj[cbxx].targetDevice[bcxx_obj.head.mIdS] === 'undefined'
    ) {
      bfglob.console.error('非本中心下发的命令：', cbxx, bcxx_obj)
      return false
    }

    var bDic = bfutil.toHex(bcxx_obj.head.bDic)
    if (bDic !== this.g_cbxx_cmd_resp_obj[cbxx].targetDevice[bcxx_obj.head.mIdS]) {
      bfglob.console.error('命令序号不正确：', bDic, cbxx, bcxx_obj)
      return false
    }
    return true
  },

  bc42(rpc_cmd_obj) {
    const bc42_t = bfproto.bfdx_proto_msg_T('bc42')
    const bc42_obj = bc42_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc42 cmd', bc42_obj, bfutil.uintToString(bc42_obj.head.origData))

    const device = bfglob.gdevices.getDataByIndex(bc42_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc42_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc42_obj.head)
    // 更新对讲机所有状态表
    this.update_device_state_data(device)
    // GPS状态消息
    const messages = {
      10: i18n.global.t('msgbox.satellitePositioningTurnedOff'),
      11: i18n.global.t('msgbox.satellitePositioningTurnedOn'),
      14: i18n.global.t('msgbox.notSupportSatellitePositioning'),
    }
    // 本中心客户端下发的CB42命令
    if (this.checked_bcxx_bDic_is_current_center(bc42_obj, 'CB42')) {
      // 删除指令顺序号
      this.delete_bcxx_obj_resNo(bc42_obj, 'CB42')
      // 展示结果
      const msg = messages[bc42_obj.code] ?? bc42_obj.code + ''
      const label = `${device.selfId}: ${msg}`
      bfNotify.messageBox(label, bc42_obj.code === 11 ? Types.success : Types.warning)
      // 添加运行日志
      this.add_runNote_for_bcxx(device, label)
      return
    }

    // 不是本中心下发的CB42命令，可能是其他与GPS相关的命令的应答，提示GPS已关闭的消息即可
    const bDic = bfutil.toHex(bc42_obj.head.bDic)
    const mIdS = bc42_obj.head.mIdS
    // 所有与GPS相关的命令
    const CBxxOfGps = new Set(['CB01', 'CB02', 'CB03', 'CB04', 'CB05', 'CB06', 'CB25'])
    const cbxxRespObj = Object.keys(this.g_cbxx_cmd_resp_obj)
      .filter(key => CBxxOfGps.has(key))
      .map(key => this.g_cbxx_cmd_resp_obj[key])
      .find(respObj => respObj.targetDevice[mIdS] === bDic)
    //  其他中心下发的GPS相关的命令，忽略
    if (!cbxxRespObj) {
      return
    }

    // 删除对应GPS指令顺序号
    delete cbxxRespObj.targetDevice[mIdS]
    if (cbxxRespObj === this.g_cbxx_cmd_resp_obj.CB03) {
      delete this.g_cbxx_cmd_resp_obj.CB03.res
    }

    const msg = messages[bc42_obj.code] ?? bc42_obj.code + ''
    const label = `${device.selfId}: ${msg}`
    bfNotify.messageBox(label, bc42_obj.code === 11 ? Types.success : Types.warning)
  },
  bc01(rpc_cmd_obj) {
    var bc01_t = bfproto.bfdx_proto_msg_T('bc01')
    var bc01_obj = bc01_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc01 cmd', bc01_obj, bfutil.uintToString(bc01_obj.head.origData))

    var device = bfglob.gdevices.getDataByIndex(bc01_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc01_obj.head.mIdS)
      return
    }

    var lastDataTime = device.lastDataTime
    if (bfTime.comparison_two_time(bc01_obj.head.cmdTime, lastDataTime)) {
      bfglob.console.error('本次定位信息已过时', bc01_obj.head.cmdTime, lastDataTime)
      return
    }

    this.update_device_last_datatime(device, bc01_obj.head)
    this.update_device_gps(device, bc01_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    if (bc01_obj.gps.av !== 0) {
      maputil.updateDeviceMarkerStatus(device)
    }

    // 添加运行日志
    var content = i18n.global.t('msgbox.upLoadLocateInfo')
    this.add_runNote_for_bcxx(device, content)

    // 删除指令顺序号
    this.delete_bcxx_obj_resNo(bc01_obj, 'CB01')

    // 更新对讲机所有状态表
    this.update_device_state_data(device)
  },
  bc02(rpc_cmd_obj) {
    var bc02_t = bfproto.bfdx_proto_msg_T('bc02')
    var bc02_obj = bc02_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc02 cmd', bc02_obj, bfutil.uintToString(bc02_obj.head.origData))

    if (!this.checked_bcxx_bDic_is_current_center(bc02_obj, 'CB02')) {
      return
    }

    var device = bfglob.gdevices.getDataByIndex(bc02_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc02_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc02_obj.head)
    this.update_device_gps(device, bc02_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    var result = ''
    if (bc02_obj.yN === 1) {
      result += '<p>' + i18n.global.t('dialog.trailSpacing') + ':' + parseInt(bc02_obj.time) * 5 + 's</p>'
      result += '<p>' + i18n.global.t('dialog.size') + ':' + bc02_obj.size * 5 + 'M</p>'
    } else if (bc02_obj.yN === 0) {
      result += '<p>' + i18n.global.t('msgbox.cancelTrailCmd') + '</p>'
    }
    // 显示查询结果
    this.show_bcxx_result(device, result)

    // 删除指令顺序号
    this.delete_bcxx_obj_resNo(bc02_obj, 'CB02')

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 添加运行日志
    var content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.queryTrailCmd')
    this.add_runNote_for_bcxx(device, content)
  },
  bc03(rpc_cmd_obj) {
    var bc03_t = bfproto.bfdx_proto_msg_T('bc03')
    var bc03_obj = bc03_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc03 cmd', bc03_obj, bfutil.uintToString(bc03_obj.head.origData))

    var device = bfglob.gdevices.getDataByIndex(bc03_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc03_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc03_obj.head)
    this.update_device_gps(device, bc03_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    const cb03Cache = this.g_cbxx_cmd_resp_obj.CB03
    if (!cb03Cache) {
      return
    }
    if (typeof cb03Cache.res !== 'undefined') {
      delete cb03Cache.res
    }

    var lngLats = cb03Cache.lngLat
    var dev_user = device.userName ? device.selfId + '/' + device.userName : device.selfId
    var result = bfTime.nowLocalTime('HH:mm:ss') + ' ' + dev_user
    var stats = false
    var check_bc03_isGroup_cmd = bc03_obj => {
      var bDic = bfutil.toHex(bc03_obj.head.bDic)
      const targetGroup = cb03Cache.targetGroup || []
      for (var i in targetGroup) {
        var number = targetGroup[i]
        if (bDic === number) {
          // 组呼
          return true
        }
      }
      return false
    }
    var _verifyLonLat = maputil.verifyLonLat([bc03_obj.gps.lon, bc03_obj.gps.lat], lngLats)
    if (check_bc03_isGroup_cmd(bc03_obj)) {
      // 是组呼命令
      if (bc03_obj.gps.av === 1 && _verifyLonLat) {
        result += i18n.global.t('msgbox.inTheRange')
        stats = true
      }
    } else if (this.checked_bcxx_bDic_is_current_center(bc03_obj, 'CB03')) {
      // 是选呼命令
      if (bc03_obj.gps.av === 1 && _verifyLonLat) {
        result += i18n.global.t('msgbox.inTheRange')
      } else {
        result += i18n.global.t('msgbox.unInTheRange')
      }
      stats = true
    }
    // stats==false 则因组呼命令中有无效的定位数据，即av!=1
    if (stats) {
      this.show_bcxx_result(device, result, 'bc03')
      maputil.create_layers_of_bcxx_result(lngLats)
    }

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 删除指令顺序号
    this.delete_bcxx_obj_resNo(bc03_obj, 'CB03')

    // 添加运行日志
    var content = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.queryTrailCmd')
    this.add_runNote_for_bcxx(device, content)
  },
  bc04(rpc_cmd_obj) {
    var bc04_t = bfproto.bfdx_proto_msg_T('bc04')
    var bc04_obj = bc04_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc04 cmd', bc04_obj, bfutil.uintToString(bc04_obj.head.origData))

    // if (!this.checked_bcxx_bDic_is_current_center(bc04_obj, 'CB04')) {
    //   return;
    // }

    var device = bfglob.gdevices.getDataByIndex(bc04_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc04_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc04_obj.head)
    this.update_device_gps(device, bc04_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    var result = ''
    var note = ''
    switch (bc04_obj.tp) {
      case 0:
        // 查询参数
        switch (bc04_obj.yN) {
          case 0:
            result += '<p>' + i18n.global.t('dialog.removeFenceAllCtrl') + '</p>'
            break
          case 1:
            result += '<p>' + i18n.global.t('dialog.enableInCacnelOut') + '</p>'
            break
          case 2:
            result += '<p>' + i18n.global.t('dialog.enableOutCacnelIn') + '</p>'
            break
          case 3:
            result += '<p>' + i18n.global.t('dialog.enableInAndOut') + '</p>'
            break
        }
        if (bc04_obj.yN !== 0) {
          result += '<p>' + i18n.global.t('dialog.crossTOT') + ':' + bc04_obj.time + '</p>'

          // 在地图上显示围栏结果
          maputil.create_layers_of_bcxx_result(bc04_obj)
        }

        note = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.queryFenceCmd')
        break
      case 1:
        // 入界报警
        result += '<p>' + (bc04_obj.penN + 1) + i18n.global.t('msgbox.fence') + i18n.global.t('msgbox.intoAlarm') + '</p>'
        note = `${bc04_obj.penN + 1} ${i18n.global.t('msgbox.fence')} ${i18n.global.t('msgbox.intoAlarm')}`
        break
      case 2:
        // 出界报警
        result += '<p>' + (bc04_obj.penN + 1) + i18n.global.t('msgbox.fence') + i18n.global.t('msgbox.outAlarm') + '</p>'
        note = `${bc04_obj.penN + 1} ${i18n.global.t('msgbox.fence')} ${i18n.global.t('msgbox.outAlarm')}`
        break
    }

    if (!bc04_obj.tp || bfglob.userInfo.setting.showCrossBorderAlarm) {
      this.show_bcxx_result(device, result, 'bc04')
    }

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 删除指令顺序号
    this.delete_bcxx_obj_resNo(bc04_obj, 'CB04')

    // 添加运行日志
    this.add_runNote_for_bcxx(device, note)
  },
  bc05(rpc_cmd_obj) {
    var bc05_t = bfproto.bfdx_proto_msg_T('bc05')
    var bc05_obj = bc05_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc05 cmd', bc05_obj, bfutil.uintToString(bc05_obj.head.origData))

    // if (!this.checked_bcxx_bDic_is_current_center(bc05_obj, 'CB05')) {
    //   return;
    // }

    var device = bfglob.gdevices.getDataByIndex(bc05_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc05_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc05_obj.head)
    this.update_device_gps(device, bc05_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    var result = ''
    var note = ''
    switch (bc05_obj.tp) {
      case 0:
        // 查询岗哨围栏参数
        if (bc05_obj.yN === 0) {
          result = '<p>' + i18n.global.t('msgbox.cancelSentinelCmd') + '</p>'
        } else if (bc05_obj.yN === 1) {
          result += '<p>' + i18n.global.t('dialog.leaveTOT') + ':' + bc05_obj.time + '</p>'
          result += '<p>' + i18n.global.t('dialog.sentinelRadius') + ':' + bfutil.LonDiff2Radius(bc05_obj.latDif) + '</p>'
          bfglob.map.setCenter([bc05_obj.lon, bc05_obj.lat])
        }
        note = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.selSentinelCmd')
        break
      case 1:
        // 入界回岗报警提示
        result += i18n.global.t('msgbox.intoGang')
        note = result
        break
      case 2:
        // 出界离岗报警提示
        result += i18n.global.t('msgbox.outGang')
        note = result
        break
    }

    if (!bc05_obj.tp || bfglob.userInfo.setting.showCrossBorderAlarm) {
      this.show_bcxx_result(device, result, 'bc05')
    }

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 删除指令顺序号
    this.delete_bcxx_obj_resNo(bc05_obj, 'CB05')

    // 添加运行日志
    this.add_runNote_for_bcxx(device, note)
  },
  bc06(rpc_cmd_obj) {
    var bc06_t = bfproto.bfdx_proto_msg_T('bc06')
    var bc06_obj = bc06_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc06 cmd', bc06_obj, bfutil.uintToString(bc06_obj.head.origData))

    // if (!this.checked_bcxx_bDic_is_current_center(bc06_obj, 'CB06')) {
    //   return;
    // }

    var device = bfglob.gdevices.getDataByIndex(bc06_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc06_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc06_obj.head)
    this.update_device_gps(device, bc06_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    var result = ''
    var note = ''
    switch (bc06_obj.tp) {
      case 0:
        switch (bc06_obj.yN) {
          case 0:
            result = '<p>' + i18n.global.t('msgbox.cancelMoveCtrlCmd') + '</p>'
            break
          case 1:
            result += '<p>' + i18n.global.t('msgbox.enableMoveCtrlCmd') + '</p>'
            result += '<p>' + i18n.global.t('dialog.stopTOT') + ':' + bc06_obj.time + '</p>'
            break
        }
        note = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.selMoveCtrlCmd')
        break
      case 1:
        // 开始走动提示
        result += '<p>' + i18n.global.t('msgbox.startMove') + '</p>'
        note = i18n.global.t('msgbox.startMove')
        break
      case 2:
        // 停留报警提示
        result += '<p>' + i18n.global.t('msgbox.stopMove') + '</p>'
        note = i18n.global.t('msgbox.stopMove')
        break
    }

    if (!bc06_obj.tp || bfglob.userInfo.setting.showCrossBorderAlarm) {
      this.show_bcxx_result(device, result, 'bc06')
    }

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 删除指令顺序号
    this.delete_bcxx_obj_resNo(bc06_obj, 'CB06')

    // 添加运行日志
    this.add_runNote_for_bcxx(device, note)
  },
  bc07(rpc_cmd_obj) {
    var bc07_t = bfproto.bfdx_proto_msg_T('bc07')
    var bc07_obj = bc07_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc07 cmd', bc07_obj, bfutil.uintToString(bc07_obj.head.origData))

    if (!this.checked_bcxx_bDic_is_current_center(bc07_obj, 'CB07')) {
      return
    }

    var device = bfglob.gdevices.getDataByIndex(bc07_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc07_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc07_obj.head)
    this.update_device_gps(device, bc07_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    var result = ''
    var note = ''
    switch (bc07_obj.yN) {
      case 0:
        result += '<p>' + i18n.global.t('msgbox.cancelAlarmCmd') + '</p>'
        note = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.selAlarmCmd')
        break
      case 1:
        result += '<p>' + i18n.global.t('msgbox.enableAlarmCmd') + '</p>'
        result += '<p>' + i18n.global.t('dialog.listenTime') + ':' + bc07_obj.jtTime + '</p>'
        result += '<p>' + i18n.global.t('dialog.locateTime') + ':' + bc07_obj.dwTime + '</p>'
        break
    }
    this.show_bcxx_result(device, result, 'bc07')

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 删除指令顺序号
    this.delete_bcxx_obj_resNo(bc07_obj, 'CB07')

    // 添加运行日志
    this.add_runNote_for_bcxx(device, note)
  },
  bc09(rpc_cmd_obj) {
    var bc09_t = bfproto.bfdx_proto_msg_T('bc09')
    var bc09_obj = bc09_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc09 cmd', bc09_obj, bfutil.uintToString(bc09_obj.head.origData))

    if (!this.checked_bcxx_bDic_is_current_center(bc09_obj, 'CB09')) {
      return
    }

    var device = bfglob.gdevices.getDataByIndex(bc09_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc09_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc09_obj.head)
    this.update_device_gps(device, bc09_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    var result = ''
    var note = ''
    switch (bc09_obj.yN) {
      case 0:
        result += i18n.global.t('msgbox.unLocked')
        note = i18n.global.t('msgbox.resDevLockSt')
        break
      case 1:
        result +=
          bc09_obj.st === 0
            ? i18n.global.t('msgbox.unLocked')
            : bc09_obj.st === 1
              ? i18n.global.t('dialog.disListen')
              : bc09_obj.st === 2
                ? i18n.global.t('dialog.disSend')
                : bc09_obj.st === 3
                  ? i18n.global.t('dialog.disSL')
                  : i18n.global.t('msgbox.unknownCmd')
        break
    }
    this.show_bcxx_result(device, result, 'bc09')

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 删除指令顺序号
    this.delete_bcxx_obj_resNo(bc09_obj, 'CB09')

    // 添加运行日志
    this.add_runNote_for_bcxx(device, note)
  },
  bc11(rpc_cmd_obj) {
    var bc11_t = bfproto.bfdx_proto_msg_T('bc11')
    var bc11_obj = bc11_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc11 cmd', bc11_obj, bfutil.uintToString(bc11_obj.head.origData))

    var device = bfglob.gdevices.getDataByIndex(bc11_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc11_obj.head.mIdS)
      return
    }

    var ctp_code = String.fromCharCode(bc11_obj.cTp[1])
    var schedule_model = bfutil.get_schedule_model(ctp_code)
    // 计算被调度的对象
    var targetAddr = bfglob.dmrAddr.get(bc11_obj.head.mIdT)
    var dispatchTarget = ''
    if (targetAddr) {
      if (targetAddr.type === 1) {
        const key = targetAddr.title
        const text = 'dataTable.' + key
        dispatchTarget = i18n.global.t(text)
      } else {
        dispatchTarget = targetAddr.baseNo + i18n.global.t('dialog.baseStation') + i18n.global.t('dataTable.allChannel')
      }
    }

    var result = ''
    var note = ''
    switch (bc11_obj.yN) {
      case 0:
        // 取消调度
        result = '<p>' + i18n.global.t('dialog.cancel') + ': ' + schedule_model + '</p>'
        note = i18n.global.t('dialog.cancel') + schedule_model
        //  删除调度提示框
        bfglob.emit('bc11_destroyDialog', device.rid)
        break
      case 1:
        // 开启紧急信道调度
        result = '<p>' + i18n.global.t('dialog.starting') + ': ' + schedule_model + '</p>'
        result += '<p>' + i18n.global.t('dataTable.dispatchTarget') + ': ' + dispatchTarget + ' </p>'
        result += '<p>' + i18n.global.t('msgbox.ScheduleTargetChannel') + ': ' + bc11_obj.ddCh + ' </p>'
        result +=
          '<p>' + i18n.global.t('msgbox.ScheduleStartTime') + ': ' + bfTime.getLocalTimeString(bfTime.utcTimeToLocalTime(bc11_obj.head.cmdTime)) + ' </p>'
        note = i18n.global.t('dialog.starting') + schedule_model
        break
      case 2:
        // 开启紧急基站联网调度
        result = '<p>' + i18n.global.t('dialog.starting') + ': ' + schedule_model + '</p>'
        result += '<p>' + i18n.global.t('dataTable.dispatchTarget') + ': ' + dispatchTarget + ' </p>'
        result +=
          '<p>' + i18n.global.t('msgbox.ScheduleStartTime') + ': ' + bfTime.getLocalTimeString(bfTime.utcTimeToLocalTime(bc11_obj.head.cmdTime)) + ' </p>'

        note = i18n.global.t('dialog.starting') + schedule_model
        break
      case 3:
        // 繁忙
        result = '<p>' + i18n.global.t('msgbox.channelBusy') + '</p>'
        note = i18n.global.t('msgbox.channelBusy')
        break
      case 4:
        // 设备故障
        result = '<p>' + i18n.global.t('msgbox.deviceFault') + '</p>'
        note = i18n.global.t('msgbox.deviceFault')
        break
    }
    if (bc11_obj.yN !== 0) {
      if (bfglob.userInfo.setting.showDispatchInfo) {
        this.show_bcxx_result(device, result, 'bc11')
      }
    }

    // 添加运行日志
    if (schedule_model) {
      this.add_runNote_for_bcxx(device, note)
    }
  },
  cleanDeviceCallStatus(device) {
    if (typeof device.call_uuid !== 'undefined') {
      delete device.call_uuid
    }
  },
  // bc12, bc13注册时，检测信道接收组，如果没有则通知提示
  checkDeviceChannelListenGroup(device) {
    if (!device) {
      return
    }

    // 没有计算出当前信道，则结束
    const currentChannel = getDeviceChannel(device)
    if (!currentChannel) {
      return
    }

    // 信道配置如果不存在，则尝试从channel中解析json
    if (!device.channels) {
      try {
        device.channels = JSON.parse(device.channel).channels ?? []
      } catch (e) {
        bfglob.console.error('checkDeviceChannelListenGroup JSON.parse err:', e)
        return
      }
    }

    // 找到当前信道对应的配置
    const channelConfig = device.channels.find(channel => {
      return channel.no === currentChannel
    })
    if (!channelConfig) {
      return
    }

    // 检测信道的接收组是否有配置，没有则通知提示
    if (!channelConfig.listenGroup || !channelConfig.listenGroup.length) {
      bfNotify.notifyBox({
        title: `${device.selfId} - ${device.dmrId}`,
        message: i18n.global.t('msgbox.channelNotSetListenGroup', {
          channel: channelConfig.no,
        }),
        type: Types.error,
        // 30分钟后关闭
        duration: 30 * 60 * 1000,
        offset: 36,
        position: 'top-left',
        customClass: `checkChannelListenGroup:${device.rid}`,
      })
    }
  },
  bc12(rpc_cmd_obj) {
    var bc12_t = bfproto.bfdx_proto_msg_T('bc12')
    var bc12_obj = bc12_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc12 cmd', bc12_obj, bfutil.uintToString(bc12_obj.head.origData))

    var device = bfglob.gdevices.getDataByIndex(bc12_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc12_obj.head.mIdS)
      return
    }

    var update_device_lastInfo_of_onOrOff = () => {
      device.licence = bc12_obj.licence
      device.pS = bc12_obj.pS
      device.lpS = bc12_obj.lpS
      device.lsTime = bc12_obj.lsTime
    }
    this.update_device_last_datatime(device, bc12_obj.head)
    this.update_device_gps(device, bc12_obj.gps)
    update_device_lastInfo_of_onOrOff()

    var spac = i18n.locale === 'en' ? ' ' : ''
    var note = ''
    switch (bc12_obj.pS) {
      case 0:
        // 关机
        device.lastPoweroffTime = bc12_obj.head.cmdTime
        device.promptContent = device.userName + spac + i18n.global.t('msgbox.poweredOff')
        note = i18n.global.t('msgbox.poweredOff')
        // 收到关机指令时先判断是否处理通话状态中，如果是则先清除通话状态
        this.cleanDeviceCallStatus(device)
        break
      case 1:
        // 开机
        device.promptContent = device.userName + spac + i18n.global.t('msgbox.poweredOn')
        note = i18n.global.t('msgbox.poweredOn')
        device.lastPoweronTime = bc12_obj.head.cmdTime
        // 类似虚拟集群终端，从数字系统信道切换到模拟信道时，定位有效需要设置为2
        if (!bc12_obj.gps || (bc12_obj.gps.av !== 1 && bc12_obj.gps.av !== 4)) {
          device.av = 2
        }
        if (bfglob.userInfo.setting.checkChannelListenGroup) {
          this.checkDeviceChannelListenGroup(device)
        }
        break
    }
    device.userImageFile = bfutil.get_device_userImageFile(device)
    if (bfglob.userInfo.setting.deviceOnoff) {
      bfutil.createNotify(device)
    }
    this.updateDeviceNodeAndMarker(device)

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 添加运行日志
    this.add_runNote_for_bcxx(device, note)

    // 发布重新过滤在线设备事件
    bfglob.emit('refilterOnlineDev')
  },
  bc13(rpc_cmd_obj) {
    var bc13_t = bfproto.bfdx_proto_msg_T('bc13')
    var bc13_obj = bc13_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc13 cmd', bc13_obj, bfutil.uintToString(bc13_obj.head.origData))

    var device = bfglob.gdevices.getDataByIndex(bc13_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc13_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc13_obj.head)
    this.update_device_gps(device, bc13_obj.gps)
    // 切换信道时先判断是否处理通话状态中，如果是则先清除通话状态
    this.cleanDeviceCallStatus(device)
    this.updateDeviceNodeAndMarker(device)

    // 更新对讲机所有状态表
    this.update_device_state_data(device)
    if (bfglob.userInfo.setting.checkChannelListenGroup) {
      this.checkDeviceChannelListenGroup(device)
    }

    // 添加运行日志
    // this.add_runNote_for_bcxx(device, note);
    // 发布重新过滤在线设备事件
    bfglob.emit('refilterOnlineDev')
  },
  bc14(rpc_cmd_obj) {
    var bc14_t = bfproto.bfdx_proto_msg_T('bc14')
    var bc14_obj = bc14_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc14 cmd', bc14_obj, bfutil.uintToString(bc14_obj.head.origData))

    var device = bfglob.gdevices.getDataByIndex(bc14_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc14_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc14_obj.head)
    this.update_device_gps(device, bc14_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 添加运行日志
    // this.add_runNote_for_bcxx(device, note);
  },
  // bc15(rpc_cmd_obj) {
  //   var bc15_t = bfproto.bfdx_proto_msg_T('bc15');
  //   var bc15_obj = bc15_t.decode(rpc_cmd_obj.body);
  //   bfglob.console.log('bc15 cmd', bc15_obj, bfutil.uintToString(bc15_obj.head.origData));
  //
  //   var device = bfglob.gdevices.getDataByIndex(bc15_obj.head.mIdS);
  //   if (!device) {
  //     bfglob.console.error("can not found device of target dmrId:", bc15_obj.head.mIdS);
  //     return
  //   }
  //
  //   this.update_device_last_datatime(device, bc15_obj.head);
  //
  //   device.userImageFile = bfutil.get_device_userImageFile(device);
  //   var bbccmm = bfutil.dmrid2ssbbccmm(bc15_obj.head.conCh);
  //   var channel = bbccmm.cc;
  //   var spac = i18n.locale === "en" ? " " : "";
  //   switch (bc15_obj.callStatus) {
  //     case 0:
  //       // 结束讲话
  //       device.promptContent = device.userName + spac +
  //         i18n.global.t("msgbox.endCall");
  //       device['call_uuid'] = '';
  //       break;
  //     case 1:
  //       // 开始讲话
  //       var _channelStr = channel ? spac + channel + spac +
  //         i18n.global.t("dialog.channel") + spac
  //         : spac;
  //       device.promptContent = device.userName + spac +
  //         i18n.global.t("msgbox.being") + _channelStr +
  //         i18n.global.t("msgbox.onCalling");
  //
  //       // 如果3分钟后还没有收到结束讲话命令，视为已经结束讲话。
  //       var call_uuid = uuid();
  //       device['call_uuid'] = call_uuid;
  //       setTimeout(() => {
  //         if (device['call_uuid'] === call_uuid) {
  //           device['call_uuid'] = '';
  //           this.updateDeviceNodeAndMarker(device);
  //         }
  //       }, 3 * 60 * 1000);
  //       break;
  //   }
  //   if (bfglob.userInfo.setting.calling) {
  //     bfutil.createNotify(device);
  //   }
  //   var set_device_channel = () => {
  //     if (device.msStatusBin.length < 6) {
  //       device.msStatus = "000000000000";
  //       device.msStatusBin = [0, 0, 0, 0, 0, 0];
  //     }
  //     device.msStatusBin[4] = (device.msStatusBin[4] & 0xe0) | (channel & 0x1f);
  //   };
  //   // set_device_channel();
  //   this.updateDeviceNodeAndMarker(device);
  //
  //   // 更新对讲机所有状态表
  //   this.update_device_state_data(device);
  //
  //   // 添加运行日志
  //   // this.add_runNote_for_bcxx(device, device.promptContent);
  //
  // },
  bc15NewUpdateStatus(device, bc15_obj) {
    device.lastDataTime = bfTime.nowUtcTime()
    device.lastController = bfutil.uint32DmrId2Hex(bc15_obj.repeaterDmrid)
  },
  bc15New(rpc_cmd_obj) {
    const bc15_t = bfproto.bfdx_proto_msg_T('bc15', 'bfkcp')
    const bc15_obj = bc15_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('new bc15 cmd', bc15_obj)

    const sourceDmrid = bfutil.uint32DmrId2Hex(bc15_obj.sourceDmrid)
    const device = bfglob.gdevices.getDataByIndex(sourceDmrid)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc15_obj.sourceDmrid, sourceDmrid)
      return
    }

    this.bc15NewUpdateStatus(device, bc15_obj)
    device.userImageFile = bfutil.get_device_userImageFile(device)

    const channel = bftree.getDeviceChannel(device)
    const spac = i18n.locale === 'en' ? ' ' : ''

    switch (bc15_obj.callStatus) {
      case 0:
        // 结束讲话
        device.promptContent = device.userName + spac + i18n.global.t('msgbox.endCall')
        device.call_uuid = ''
        break
      case 1:
        // 开始讲话
        const _channelStr = channel ? spac + channel + spac + i18n.global.t('dialog.channel') + spac : spac
        device.promptContent = device.userName + spac + i18n.global.t('msgbox.being') + _channelStr + i18n.global.t('msgbox.onCalling')

        // 如果3分钟后还没有收到结束讲话命令，视为已经结束讲话。
        // 如果是坐席呼叫，则以坐席的超时为准，其他默认为3分钟
        let timeout = 3 * 60 * 1000
        const voipSpeakInfo = bfglob.userInfo.setting.voipSpeakInfo
        if (voipSpeakInfo?.speaker === toHexDmrId(bc15_obj.sourceDmrid)) {
          timeout = voipSpeakInfo.maxSpeakTime * 1000
        }
        const call_uuid = uuid()
        device.call_uuid = call_uuid
        setTimeout(() => {
          if (device.call_uuid === call_uuid) {
            device.call_uuid = ''
            this.updateDeviceNodeAndMarker(device)
            bfglob.emit('bc15_call_timeout', device)
          }
        }, timeout)
        break
    }
    if (bfglob.userInfo.setting.calling) {
      bfutil.createNotify(device)
    }
    this.updateDeviceNodeAndMarker(device)

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 添加运行日志
    // this.add_runNote_for_bcxx(device, device.promptContent);

    // 判断呼叫的目标是否为登录用户所在组或设备，向vspeaking组件发送消息
    bfglob.emit('bc15_speaker_pending', bc15_obj, device)
  },
  bc16(rpc_cmd_obj) {
    var bc16_t = bfproto.bfdx_proto_msg_T('bc16')
    var bc16_obj = bc16_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc16 cmd', bc16_obj, bfutil.uintToString(bc16_obj.head.origData))

    var device = bfglob.gdevices.getDataByIndex(bc16_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc16_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc16_obj.head)
    this.update_device_gps(device, bc16_obj.gps)

    device.userImageFile = bfutil.get_device_userImageFile(device)
    if (bc16_obj.gps.av !== 0) {
      var spac = i18n.locale === 'en' ? ' ' : ''
      device.promptContent = device.userName + spac + i18n.global.t('msgbox.receiveLocateInfo')
      bfutil.createNotify(device)
    }
    this.updateDeviceNodeAndMarker(device)

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 添加运行日志
    this.add_runNote_for_bcxx(device, device.promptContent)
  },
  bc18(rpc_cmd_obj) {
    var bc18_t = bfproto.bfdx_proto_msg_T('bc18')
    var bc18_obj = bc18_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc18 cmd', bc18_obj, bfutil.uintToString(bc18_obj.head.origData))

    var device = bfglob.gdevices.getDataByIndex(bc18_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc18_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc18_obj.head)
    this.update_device_gps(device, bc18_obj.gps)
    this.updateDeviceNodeAndMarker(device)

    device.userImageFile = bfutil.get_device_userImageFile(device)

    var spac = i18n.locale === 'en' ? ' ' : ''
    switch (bc18_obj.alarm) {
      case 1:
        device.promptContent = device.userName + spac + i18n.global.t('dataTable.emergency')
        maputil.updateDeviceMarkerStatus(device)
        if (bfglob.userInfo.setting.showAlarmOnMapCenter) {
          // 设置在地图中心显示
          let lonlat = [bc18_obj.gps.lon, bc18_obj.gps.lat]
          if (bc18_obj.gps.av === 0) {
            lonlat = [device.lastLon, device.lastLat]
          }
          mapFlyTo(lonlat)
        }
        var node = bftree.getTreeNodeByRid('bftree', device.rid)
        if (node) {
          node.setSelected(true)
        }
        // 紧急报警弹框处理
        if (bfglob.userInfo.setting.alarmPrompt) {
          bfglob.emit('show_alarm_dialog', device, bc18_obj.head.cmdTime, bc18_obj.dbRid)
        }
        if (bfglob.userInfo.setting.alarmVoice) {
          bfglob.emit('play_alarm_voice')
        }
        break
      case 2:
        device.promptContent = device.userName + spac + i18n.global.t('dataTable.offNetworkAlarm')
        break
      case 3:
        // device.promptContent = device.userName + spac +
        // i18n.global.t("dataTable.lowVoltageAlarm;
        break
      case 4:
        device.promptContent = device.userName + spac + i18n.global.t('dataTable.gpsAlarm')
        break
    }
    if (bfglob.userInfo.setting.alarmPrompt && bc18_obj.alarm !== 3) {
      bfutil.createNotify(device)
    }

    // 更新对讲机所有状态表
    this.update_device_state_data(device)
  },
  bc25(rpc_cmd_obj) {
    var bc25_t = bfproto.bfdx_proto_msg_T('bc25')
    var bc25_obj = bc25_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc25 cmd', bc25_obj, bfutil.uintToString(bc25_obj.head.origData))

    if (!this.checked_bcxx_bDic_is_current_center(bc25_obj, 'CB25')) {
      return
    }

    var device = bfglob.gdevices.getDataByIndex(bc25_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', bc25_obj.head.mIdS)
      return
    }

    this.update_device_last_datatime(device, bc25_obj.head)
    // this.update_device_gps(device, bc25_obj.gps);
    this.updateDeviceNodeAndMarker(device)

    var result = ''
    var note = ''
    switch (bc25_obj.yN) {
      case 0:
        result += '<p>' + i18n.global.t('dialog.savePenNo') + ':' + bc25_obj.pointN + '</p>'
        result += '<p>' + i18n.global.t('msgbox.cancelGpsInsSet') + '</p>'
        break
      case 1:
        result += '<p>' + i18n.global.t('msgbox.confirmGpsInsSet') + '</p>'
        result += '<p>' + i18n.global.t('dialog.savePenNo') + ':' + bc25_obj.pointN + '</p>'
        result += '<p>' + i18n.global.t('dialog.gpsCardNo') + ':' + bc25_obj.pointCard + '</p>'
        note = i18n.global.t('msgbox.respond') + i18n.global.t('msgbox.enableGPSVirCmd')
        // 判断查询到的虚拟点数据是否已经被更新类型，是则弹框警告
        ;(rfid => {
          var linePointItem = bfglob.glinePoints.getDataByIndex(rfid)
          if (!linePointItem) {
            // GPS虚拟点不存在或没有权限
            return
          }
          if (linePointItem.pointType === 3) {
            return
          }
          bfNotify.warningBox(rfid + ' ' + i18n.global.t('msgbox.updatePointType'))
        })(bc25_obj.pointCard)
        break
    }
    device.pointCard = bc25_obj.pointCard
    this.show_bcxx_result(device, result, 'bc25')

    // 更新对讲机所有状态表
    this.update_device_state_data(device)

    // 删除指令顺序号
    this.delete_bcxx_obj_resNo(bc25_obj, 'CB25')

    // 添加运行日志
    this.add_runNote_for_bcxx(device, note)
  },
  // 收到还没登录的对讲机上来的数据
  bc1100(rpc_cmd_obj) {
    var not_register_device_cmd_t = bfproto.bfdx_proto_msg_T('not_register_device_cmd')
    var not_register_device_cmd_obj = not_register_device_cmd_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('收到重复的命令 bc1100 cmd', not_register_device_cmd_obj)

    if (!bfglob.userInfo.setting.showNewDeviceData) {
      return
    }
    var bbccmm = bfutil.dmrid2ssmmm(not_register_device_cmd_obj.dmrId)
    if (bfutil.ss2SysId(bbccmm.ss) !== bfglob.sysId) {
      return
    }

    let msg = `<p>
                <span>DMRID ${i18n.global.t('dialog.number')}: </span>
                <span>${bbccmm.mmm}</span>
               </p>`
    msg += `<p>
            <span>${i18n.global.t('dialog.deviceDMRID')}: </span>
            <span>${not_register_device_cmd_obj.dmrId}</span>
          </p>`
    msg += `<p>
            <span>${i18n.global.t('msgbox.receiveData')} ${i18n.global.t('dialog.ctrlDMRID')}: </span>
            <span>${not_register_device_cmd_obj.receivedControllerDmrId}/${parseInt(not_register_device_cmd_obj.receivedControllerDmrId, 16)}</span>
          </p>`
    var obj = {
      title: i18n.global.t('msgbox.gotNoRegisterDevData'),
      dmrId: not_register_device_cmd_obj.dmrId,
    }
    this.show_bcxx_result(obj, msg, 'bc1100')
    // 添加运行日志
    var content = i18n.global.t('msgbox.gotNoRegisterDevData') + msg
    bfglob.emit('addnote', bfglob.userInfo.name + ' ' + content)
  },
  // 收到重复的命令
  bc1101(rpc_cmd_obj) {
    var cmd_repeate_received_t = bfproto.bfdx_proto_msg_T('cmd_repeate_received')
    var cmd_repeate_received_obj = cmd_repeate_received_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('收到重复的命令 bc1101 cmd', cmd_repeate_received_obj)

    if (!bfglob.userInfo.setting.showNewDeviceData) {
      return
    }
    var bbccmm = bfutil.dmrid2ssmmm(cmd_repeate_received_obj.dmrId)
    if (bfutil.ss2SysId(bbccmm.ss) !== bfglob.sysId) {
      return
    }

    let msg = ''
    msg += `
      <p>${i18n.global.t('dialog.number')}:${bbccmm.mmm}</p>
      <p>DMRID: ${cmd_repeate_received_obj.dmrId}/${parseInt(cmd_repeate_received_obj.dmrId, 16)}</p>
    `
    var obj = {
      title: i18n.global.t('msgbox.gotRepeatCmd') + ':' + cmd_repeate_received_obj.cmd,
    }
    this.show_bcxx_result(obj, msg, 'bc1101')
    // 添加运行日志
    var content = i18n.global.t('msgbox.gotRepeatCmd') + ':' + cmd_repeate_received_obj.cmd + msg
    bfglob.emit('addnote', bfglob.userInfo.name + ' ' + content)
  },
  // 收到还没登记到数据库的控制器信息
  bc1102(rpc_cmd_obj) {
    var new_controller_t = bfproto.bfdx_proto_msg_T('new_controller')
    var new_controller_obj = new_controller_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('没登记的控制器数据 bc1102 cmd', new_controller_obj)

    if (!bfglob.userInfo.setting.showNewDeviceData) {
      return
    }
    var bbccmm = bfutil.dmrid2ssmmm(new_controller_obj.dmrId)
    if (bfutil.ss2SysId(bbccmm.ss) !== bfglob.sysId) {
      return
    }
    let msg = `<p>
                <span>DMRID ${i18n.global.t('dialog.number')}: </span>
                <span>${bbccmm.mmm}</span>
               </p>`
    msg += `<p>
            <span>DMRID: </span>
            <span>${new_controller_obj.dmrId}/${parseInt(new_controller_obj.dmrId, 16)}</span>
          </p>`
    msg += `<p>
            <span>${i18n.global.t('msgbox.ipAddress')}: </span>
            <span>${new_controller_obj.ipInfo}</span>
          </p>`
    msg += `<p>
            <span>${i18n.global.t('dialog.model')}:</span>
            <span>${new_controller_obj.model}</span>
          </p>`

    var obj = {
      title: i18n.global.t('msgbox.gotNoRegisterCtrlData'),
      dmrId: new_controller_obj.dmrId,
    }
    this.show_bcxx_result(obj, msg, 'bc1102')
    // 添加运行日志
    var content = i18n.global.t('msgbox.gotNoRegisterCtrlData') + msg
    bfglob.emit('addnote', bfglob.userInfo.name + ' ' + content)
  },

  // 巡查打卡命令
  set_popup_for_dc01(pointItem, html_content) {
    var marker = bfglob.glinePoints.getMarker(pointItem.rid)
    if (!marker) {
      return
    }

    var oldPopup = marker.getPopup()
    var popup = maputil.createPopup({
      closeButton: false,
      closeOnClick: true,
      lngLat: oldPopup._lngLat,
      content: html_content,
    })
    marker.setPopup(popup)

    deferCloseMarkerPopup(marker, oldPopup)
  },
  checked_device_popup_isOpen_for_updata(device) {
    var marker = bfglob.gdevices.getMarker(device.rid)
    if (!marker) {
      return
    }

    var popup = marker.getPopup()
    if (typeof popup === 'undefined') {
      return
    }
    if (popup.isOpen()) {
      popup.setHTML(maputil.get_popup_content_for_device(device))
    }
  },
  /**
   * 修正dc01指令的终端的错误的最后读卡时间
   * @param {object} data db_device_last_info表中的字段属性
   * @param {object} reqOpts 部分字段更新操作需要的配置
   * @returns {void}
   */
  fixDbDeviceLastInfoSomeFields(data, reqOpts) {
    const msgOpts = {
      rpcCmdFields: {
        origReqId: 'rid',
        ...reqOpts,
      },
    }
    bfproto
      .sendMessage(dbCmd.DB_DEVICE_LAST_INFO_PUPDATE, data, 'db_device_last_info', getDbSubject(), msgOpts)
      .then(rpc_cmd_obj => {
        bfglob.console.log('fixDbDeviceLastInfoSomeFields res:', rpc_cmd_obj)
      })
      .catch(err => {
        bfglob.console.warn('fixDbDeviceLastInfoSomeFields catch:', err)
      })
  },
  /**
   * 提示用户是否修正上次读卡时间
   * @param {string} lastRfidTime 上次读卡时间
   * @param {string} readTime 本次读卡时间
   * @returns {boolean}
   */
  async askFixLastRfidTime(lastRfidTime, readTime) {
    return new Promise(resolve => {
      warningBoxWithOption(
        `<div class="fix-last-rfid-time-warning">
        <div class="warning-content-item">${i18n.global.t('msgbox.lastRfidTime')}: ${lastRfidTime}</div>
        <div class="warning-content-item">${i18n.global.t('msgbox.currentRfidTime')}: ${readTime}</div>
      </div>`,
        {
          title: i18n.global.t('msgbox.fixLastRfidTimeWarning'),
          type: Types.warning,
          dangerouslyUseHTMLString: true,
        }
      )
        .then(res => resolve(res === 'confirm'))
        .catch(_err => resolve(false))
    })
  },
  async dc01(rpc_cmd_obj) {
    const dc01_t = bfproto.bfdx_proto_msg_T('dc01')
    const dc01_obj = dc01_t.decode(rpc_cmd_obj.body)
    bfglob.console.log('dc01 cmd', dc01_obj, bfutil.uintToString(dc01_obj.head.origData))

    const device = bfglob.gdevices.getDataByIndex(dc01_obj.head.mIdS)
    if (!device) {
      bfglob.console.error('can not found device of target dmrId:', dc01_obj.head.mIdS)
      return
    }

    // update device lastdatatime
    this.update_device_last_datatime(device, dc01_obj.head)

    const that = this
    const spac = i18n.locale === 'en' ? ' ' : ''
    let lastRfidTime = device.lastRfidTime
    const nowUtc = nowUtcTime()
    const after3day = addTime(nowUtc, 3, 'day')

    for (let i = 0; i < dc01_obj.rfids.length; i++) {
      const rfidsObj = dc01_obj.rfids[i]
      const readTime = rfidsObj.readTime

      // this.update_device_gps(device, bc25_obj.gps);
      let cardTypeName = ''
      switch (rfidsObj.dbType) {
        // 人员身份卡
        case 100:
          cardTypeName = i18n.global.t('msgbox.userIDCard')
          var userItem = bfglob.guserData.getDataByIndex(rfidsObj.rfidId)
          if (!userItem) {
            bfglob.console.error('没有找到此卡号对应的用户数据', rfidsObj.rfidId)
            const format_data = { rfid: rfidsObj.rfidId }
            bfglob.emit('addnote', i18n.global.t('msgbox.dc01UserDataNotFound', format_data))
            continue
          }
          dc01_obj.userRid = userItem.rid
          var lastRfidPersonTime = device.lastRfidPersonTime
          // 检查并修正读取人员卡时间
          let invalidUserRfidReadTime = timeIsBefore(readTime, lastRfidPersonTime)
          if (invalidUserRfidReadTime && timeIsBefore(after3day, lastRfidPersonTime)) {
            const needFixed = await this.askFixLastRfidTime(lastRfidPersonTime, readTime)
            if (needFixed) {
              invalidUserRfidReadTime = false
              lastRfidPersonTime = device.lastRfidPersonTime = readTime
              const msgObj = {
                rid: device.rid,
                lastRfidPersonTime,
                lastDataTime: nowUtcTime(),
              }
              const pupdateOptions = {
                origReqId: 'rid',
                resInfo: 'last_data_time, last_rfid_person_time',
              }
              this.fixDbDeviceLastInfoSomeFields(msgObj, pupdateOptions)
            }
          }
          if (invalidUserRfidReadTime) {
            bfglob.console.error(`本次信息已过时: readTime=${rfidsObj.readTime}, lastRfidPersonTime=${lastRfidPersonTime}`)
            const format_data = {
              lastTime: lastRfidPersonTime,
              thisTime: rfidsObj.readTime,
            }
            bfglob.emit('addnote', i18n.global.t('msgbox.theCardInfoOutDate', format_data))
            continue
          }

          var old_devices = bfutil.get_devices_by_user_rid(userItem.rid)
          for (var k = 0; k < old_devices.length; ++k) {
            if (old_devices[k].rid === device.rid) {
              continue
            }
            old_devices[k].lastRfidPerson = old_devices[k].deviceUser
            old_devices[k].userName = bfutil.get_device_userName(old_devices[k])
            this.updateDeviceNodeAndMarker(old_devices[k])
          }

          device.lastRfidPerson = userItem.rid
          device.lastRfidPersonTime = rfidsObj.readTime
          device.userName = bfutil.get_device_userName(device)
          this.updateDeviceNodeAndMarker(device)

          if (device.lastRfidPerson === '' || device.lastRfidPerson === '00000000-0000-0000-0000-000000000000') {
            device.promptContent = device.selfId + '/' + userItem.userName + spac + i18n.global.t('msgbox.isClockIn')
          } else {
            device.promptContent = device.selfId + '/' + userItem.userName + spac + i18n.global.t('msgbox.isPunchShift')
          }
          var img_rid = userItem.userImage
          device.userImageFile = bfglob.gimages.getFileContentBykey(img_rid)
          if (bfglob.userInfo.setting.changeShifts) {
            bfutil.createNotify(device)
          }
          break
        // 线路巡查点
        case 1:
        case 2:
        case 3:
          cardTypeName = i18n.global.t('msgbox.insPointsCar')
          const pointItem = bfglob.glinePoints.getDataByIndex(rfidsObj.rfidId)
          if (!pointItem) {
            bfglob.console.error('can not found linePoint of rfidId', rfidsObj.rfidId)
            const format_data = { rfid: rfidsObj.rfidId }
            bfglob.emit('addnote', i18n.global.t('msgbox.dc01NotFoundLinePoint', format_data))
            continue
          }

          // 检查终端的最后读卡时间是否异常，提示用户修正
          // 例如：当前读卡时间为2023-05-18 17:11:00，但lastRfidTime时间为2026-01-01 00:00:00
          // 则以当前读卡时间来修正数据库的最后时间(数据库在更新时如果当前读卡时间比数据库的要小，则没有更新)
          let invalidReadTime = timeIsBefore(readTime, lastRfidTime)
          if (invalidReadTime && timeIsBefore(after3day, lastRfidTime)) {
            const needFixed = await this.askFixLastRfidTime(lastRfidTime, readTime)
            if (needFixed) {
              invalidReadTime = false
              lastRfidTime = device.lastRfidTime = readTime
              const msgObj = {
                rid: device.rid,
                lastRfidTime,
                lastDataTime: nowUtcTime(),
              }
              const pupdateOptions = {
                origReqId: 'rid',
                resInfo: 'last_data_time, last_rfid_time',
              }
              this.fixDbDeviceLastInfoSomeFields(msgObj, pupdateOptions)
            }
          }

          if (invalidReadTime) {
            bfglob.console.warn(`本次读卡信息已过时:lastRfidTime=${lastRfidTime},readTime=${readTime}`)
            const format_data = {
              lastTime: lastRfidTime,
              thisTime: readTime,
            }
            bfglob.emit('addnote', i18n.global.t('msgbox.theCardInfoOutDate', format_data))

            continue
          }

          // 更新巡查点最后打卡终端信息
          pointItem.lastCheckDeviceId = device.rid
          pointItem.lastCheckUserId = device.deviceUser || bfutil.DefOrgRid
          pointItem.lastCheckTime = readTime
          // 处理巡查点活动终端的显示
          maputil.setupLinePointActiveDevice(rfidsObj, device)

          if (rfidsObj.rfidType === 1) {
            // 无源
          }
          if (rfidsObj.rfidType === 2) {
            // 有源点，电压正常
            // 清除低压报警表数据
            pointItem.lastLpalarmState = false
            bfglob.emit('rfid-battery-normal', rfidsObj)
          }
          if (rfidsObj.rfidType === 3) {
            // GPS
            // 检测系统是否存在该虚拟点，如果不存在，则不显示提示，并下发命令取消该虚拟点设置
          }
          if (rfidsObj.rfidType === 4) {
            // 有源点，电量低
            // 判断是否提示有源点电量低
            if (bfglob.userInfo.setting.showActivePointBattery) {
              // let result = ''
              // result += `<h5>${pointItem.orgShortName} /
              //                             ${pointItem.pointId} /
              //                             ${rfidsObj.rfidId}</h5>`
              // result += '<h5>' + i18n.global.t('msgbox.showActivePointAlert') + '</h5>'
              // result += '<h5>' + bfTime.nowLocalTime() + '</h5>'
              // that.show_bcxx_result(pointItem, result, 'dc01')

              // 处理低压报警表格数据
              if (!pointItem.lastLpalarmTime || new dayJs(readTime).isAfter(new dayJs(pointItem.lastLpalarmTime))) {
                // 低压报警
                pointItem.lastLpalarmTime = readTime
                pointItem.lastLpalarmState = true
                pointItem.lastAlarmTime = utcToLocalTimeFormat(readTime)
                bfglob.emit('rfid-battery-alarm', rfidsObj)
              }
            }
          }

          // 张三正在巡查0001/双阳镇 有源/无源/GPS虚拟巡查点
          device.userImageFile = bfutil.get_device_userImageFile(device)
          device.promptContent = i18n.global.t('msgbox.areInspections') + ' ' + pointItem.pointId + ' / ' + pointItem.pointName

          if (bfglob.userInfo.setting.showInsNotifi) {
            bfutil.createNotify(device)
          }
          if (bfglob.userInfo.setting.InsOnMapToolips) {
            // 地图上提示
            var insUserTitle = device.selfId
            if (device.userName) {
              insUserTitle += '/' + device.userName
            }
            var html_content = '<p>' + insUserTitle + device.promptContent + '</p>'
            this.set_popup_for_dc01(pointItem, html_content)
          }

          // 重置设备最后的定位信息
          that.resetDeviceReadRfidLnglat(device, pointItem)
          break
        case -1:
          // -1:未知卡点，数据库没有此rfid卡
          cardTypeName = i18n.global.t('msgbox.newCard')
          break
        case -2:
          // -2:数据库未连接，搜索本地巡查rfid点，是否有
          cardTypeName = i18n.global.t('msgbox.other')
          break
      }
      this.checked_device_popup_isOpen_for_updata(device)

      // 将读卡信息传递给巡查点数据管理组件中做下拉列表
      var rfid_obj = {
        value: rfidsObj.rfidId,
        label: rfidsObj.rfidId,
        dmrId: device.dmrId,
        readTime: bfTime.getLocalTimeString(bfTime.utcTimeToLocalTime(readTime), 'HH:mm:ss'),
      }
      bfglob.emit('linePoint_readRfidList', rfid_obj)

      // 运行日志
      var content = device.selfId
      if (device.userName) {
        content += '/' + device.userName
      }
      content += i18n.global.t('msgbox.reading') + cardTypeName + i18n.global.t('dataTable.type') + i18n.global.t('msgbox.cardNo') + ':' + rfidsObj.rfidId
      content += '; ' + i18n.global.t('dataTable.readTime') + ':' + bfTime.getLocalTimeString(bfTime.utcTimeToLocalTime(rfidsObj.readTime))
      bfglob.emit('addnote', content)
    }

    this.updateDeviceNodeAndMarker(device)
  },

  cbxx_target(target) {
    var cbxx_target_t = bfproto.bfdx_proto_msg_T('cbxx_target')

    var cbxx_target_obj = {}
    var msgObj = {
      targetGroud: target.groud,
      targetDevice: target.device,
    }
    // 通过 verify 验证消息字段类型
    var errMsg = cbxx_target_t.verify(msgObj)
    if (errMsg) {
      bfglob.console.error(errMsg, msgObj)
      return {
        targetGroud: [],
        targetDevice: [],
      }
    }
    cbxx_target_obj = cbxx_target_t.create(msgObj)

    return cbxx_target_obj
  },
  add_runNote_for_cbxx(target, content) {
    // 1: 选呼  2：组呼  3：都有 -1:error
    var checked_cbxx_target_type = target => {
      if (target.targetGroud.length === 0) {
        return 1
      }
      if (target.targetDevice.length === 0) {
        return 2
      }
      if (target.targetGroud.length > 0 && target.targetDevice.length > 0) {
        return 3
      }
      return -1
    }
    var cmdType = checked_cbxx_target_type(target)
    if (cmdType === -1) {
      return
    }
    var dmrId = target.targetDevice[0]
    var add_runNote_for_cbxx_device = () => {
      var __add_runNote_for_cbxx_device = (dmrId, content) => {
        var device = bfglob.gdevices.getDataByIndex(dmrId)
        if (!device) {
          return
        }
        var user = device.selfId
        if (device.userName) {
          user += '/' + device.userName
        }
        var note = bfglob.userInfo.name + ' ' + i18n.global.t('msgbox.dui') + ' ' + user + ' ' + content
        bfglob.emit('addnote', note)
      }
      for (var i in target.targetDevice) {
        dmrId = target.targetDevice[i]
        __add_runNote_for_cbxx_device(dmrId, content)
      }
    }
    var add_runNote_for_cbxx_groud = () => {
      var __add_runNote_for_cbxx_groud = (dmrId, content) => {
        var item = bfglob.gorgData.getDataByIndex(dmrId)
        if (item === undefined) {
          return
        }
        var note = bfglob.userInfo.name + ' ' + i18n.global.t('msgbox.dui') + ' ' + item.orgShortName + ' ' + content
        bfglob.emit('addnote', note)
      }
      for (var j in target.targetGroud) {
        dmrId = target.targetGroud[j]
        __add_runNote_for_cbxx_groud(dmrId, content)
      }
    }
    if (cmdType === 1) {
      // 选呼
      add_runNote_for_cbxx_device()
    }
    if (cmdType === 2) {
      // 组呼
      add_runNote_for_cbxx_groud()
    }
    if (cmdType === 3) {
      add_runNote_for_cbxx_groud()
      add_runNote_for_cbxx_device()
    }
  },
  g_cbxx_cmd_resp_obj: {},
  save_res_no_for_cbxx(cbxx_send_stub_obj, cb03_obj) {
    if (typeof this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx] === 'undefined') {
      this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx] = {}
    }
    if (cbxx_send_stub_obj.cbxx === 'CB03') {
      this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx].lngLat = cb03_obj
      this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx].res = true
    }
    if (cbxx_send_stub_obj.targetDevice.length > 0) {
      // 有对讲机的命令
      if (typeof this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx].targetDevice === 'undefined') {
        this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx].targetDevice = {}
      }
      for (var k in cbxx_send_stub_obj.targetDevice) {
        var dev_dmrId = cbxx_send_stub_obj.targetDevice[k]
        this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx].targetDevice[dev_dmrId] = cbxx_send_stub_obj.targetDeviceSeqNo[k]
      }
    }
    if (cbxx_send_stub_obj.targetGroup.length > 0) {
      // 有针对群组的命令
      if (typeof this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx].targetGroup === 'undefined') {
        this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx].targetGroup = {}
      }
      for (var j in cbxx_send_stub_obj.targetGroup) {
        var org_dmrId = cbxx_send_stub_obj.targetGroup[j]
        this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx].targetGroup[org_dmrId] = cbxx_send_stub_obj.targetGroupSeqNo[j]
      }
    }
    return this.g_cbxx_cmd_resp_obj[cbxx_send_stub_obj.cbxx]
  },
  checked_cbxx_is_respone(cbxxResNo, respon_obj, prompt_content, devLength) {
    if (typeof this.g_cbxx_cmd_resp_obj[respon_obj.cbxx] === 'undefined' || typeof this.g_cbxx_cmd_resp_obj[respon_obj.cbxx].targetDevice === 'undefined') {
      return
    }
    var times = 30 * 1000
    if (this.g_cbxx_cmd_resp_obj[respon_obj.cbxx].targetDevice.length > 30) {
      times = 60 * 1000
    } else {
      times = 30 * 1000
    }

    if (respon_obj.cbxx === 'CB01') {
      times = 10 * 1000
    } else if (respon_obj.cbxx === 'CB03') {
      setTimeout(() => {
        if (typeof this.g_cbxx_cmd_resp_obj.CB03.res === 'undefined') {
          return
        }
        bfutil.createNotify('bc03')
      }, times)
      return
    }
    const originTargetDevice = cloneDeep(this.g_cbxx_cmd_resp_obj[respon_obj.cbxx].targetDevice)
    var timeoutFunc = () => {
      var dmrIds = respon_obj.targetDevice
      for (var i in dmrIds) {
        var dmrId = dmrIds[i]
        if (
          !this.g_cbxx_cmd_resp_obj[respon_obj.cbxx] ||
          !this.g_cbxx_cmd_resp_obj[respon_obj.cbxx].targetDevice ||
          !this.g_cbxx_cmd_resp_obj[respon_obj.cbxx].targetDevice[dmrId] ||
          originTargetDevice[dmrId] !== this.g_cbxx_cmd_resp_obj[respon_obj.cbxx].targetDevice[dmrId]
        ) {
          continue
        }
        var device = bfglob.gdevices.getDataByIndex(dmrId)
        if (!device) {
          bfglob.console.error('没有找到此 dmrId 的对讲机数据', dmrId)
          continue
        }
        device.userImageFile = bfutil.get_device_userImageFile(device)
        device.promptContent = prompt_content
        if ((respon_obj.cbxx === 'CB21' || respon_obj.cbxx === 'CB24') && devLength > 1) {
          // 如果是切换信道和发送短信,在选呼类型下，不成功则不冒泡通知
        } else {
          bfutil.createNotify(device)
        }
        // 添加运行日志
        var user = device.selfId
        if (device.userName) {
          user += '/' + device.userName
        }
        bfglob.emit('addnote', user + ' ' + device.promptContent)

        // 删除指令序号
        delete this.g_cbxx_cmd_resp_obj[respon_obj.cbxx].targetDevice[dmrId]
        if (Object.keys(this.g_cbxx_cmd_resp_obj[respon_obj.cbxx].targetDevice).length === 0) {
          delete this.g_cbxx_cmd_resp_obj[respon_obj.cbxx].targetDevice
        }
        if (Object.keys(this.g_cbxx_cmd_resp_obj[respon_obj.cbxx]).length === 0) {
          delete this.g_cbxx_cmd_resp_obj[respon_obj.cbxx]
        }
        bfglob.console.warn('指令响应超时', respon_obj.cbxx, cbxxResNo, respon_obj)
      }
    }
    setTimeout(timeoutFunc, times)
  },
  return_two_bit_lngLatDif(lngLatDif) {
    if (lngLatDif >= 100) {
      lngLatDif = '99'
    } else if (lngLatDif >= 10) {
      lngLatDif = '' + lngLatDif
    } else {
      lngLatDif = '0' + lngLatDif
    }
    return lngLatDif
  },
  cb42(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      code: cmd_obj.code,
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3042, msgObj, 'cb42', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb42 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.noResponseSatellitePositioningSwitchCommand'))

          // 添加运行日志
          const note =
            msgObj.code === 10
              ? i18n.global.t('msgbox.turnOffSatellitePositioning')
              : msgObj.code === 11
                ? i18n.global.t('msgbox.turnOnSatellitePositioning')
                : msgObj.code === 12
                  ? i18n.global.t('msgbox.querySatellitePositioningStatus')
                  : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb42 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb01(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      time: cmd_obj.spaceTime / 5,
      size: cmd_obj.size / 5,
      count: cmd_obj.count,
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3001, msgObj, 'cb01', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxx_send_stub_obj = rpc_cmd_obj.body
          bfglob.console.log('server res cb01 cmd obj:', cbxx_send_stub_obj)

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResPositionCmd'))

          // 添加运行日志
          const note = i18n.global.t('msgbox.sendPositionCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('发送定位超时', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb02(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      time: cmd_obj.track === 0 ? 0 : parseInt(cmd_obj.spaceTime) / 5,
      size: cmd_obj.size / 5,
      yN: cmd_obj.track,
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3002, msgObj, 'cb02', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb02 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResTrailCmd'))

          // 添加运行日志
          const note =
            msgObj.yN === 0
              ? i18n.global.t('msgbox.cancelTrailCmd')
              : msgObj.yN === 1
                ? i18n.global.t('msgbox.enableTrailCmd')
                : msgObj.yN === 2
                  ? i18n.global.t('msgbox.queryTrailCmd')
                  : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb02 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb03(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      minLon: cmd_obj.minLon,
      minLat: cmd_obj.minLat,
      maxLon: cmd_obj.maxLon,
      maxLat: cmd_obj.maxLat,
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3003, msgObj, 'cb03', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb03 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj, msgObj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResTrailCmd'))

          // 添加运行日志
          const note = i18n.global.t('msgbox.sendAreaCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb03 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb04(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      yN: cmd_obj.setCmd,
      penN: cmd_obj.penN === '' ? 0 : parseInt(cmd_obj.penN) - 1,
      time: cmd_obj.spaceTime,
      minLon: cmd_obj.minLon || 0,
      minLat: cmd_obj.minLat || 0,
      maxLon: cmd_obj.maxLon || 0,
      maxLat: cmd_obj.maxLat || 0,
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3004, msgObj, 'cb04', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb04 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResFenceCmd'))

          // 添加运行日志
          const note =
            msgObj.yN === 0
              ? i18n.global.t('dialog.removeFenceAllCtrl')
              : msgObj.yN === 1
                ? i18n.global.t('dialog.enableInCacnelOut')
                : msgObj.yN === 2
                  ? i18n.global.t('dialog.enableOutCacnelIn')
                  : msgObj.yN === 3
                    ? i18n.global.t('dialog.enableInAndOut')
                    : msgObj.yN === 9
                      ? i18n.global.t('msgbox.queryFenceCmd')
                      : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb04 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb05(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      yN: cmd_obj.setCmd,
      time: cmd_obj.setCmd === 0 ? 0 : cmd_obj.spaceTime,
      lon: cmd_obj.lon || 0,
      lat: cmd_obj.lat || 0,
      lonDif: this.return_two_bit_lngLatDif(cmd_obj.lonDif),
      latDif: this.return_two_bit_lngLatDif(cmd_obj.latDif),
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3005, msgObj, 'cb05', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb05 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResSentinelCmd'))

          // 添加运行日志
          const note =
            msgObj.yN === 0
              ? i18n.global.t('msgbox.cancelSentinelCmd')
              : msgObj.yN === 1
                ? i18n.global.t('msgbox.enableSentinelCmd')
                : msgObj.yN === 2
                  ? i18n.global.t('msgbox.selSentinelCmd')
                  : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb05 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb06(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      yN: cmd_obj.setCmd,
      time: cmd_obj.setCmd === 0 ? 0 : cmd_obj.spaceTime,
      lonDif: this.return_two_bit_lngLatDif(cmd_obj.lonDif),
      latDif: this.return_two_bit_lngLatDif(cmd_obj.latDif),
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3006, msgObj, 'cb06', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb06 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResMoveCtrlCmd'))

          // 添加运行日志
          const note =
            msgObj.yN === 0
              ? i18n.global.t('msgbox.cancelMoveCtrlCmd')
              : msgObj.yN === 1
                ? i18n.global.t('msgbox.enableMoveCtrlCmd')
                : msgObj.yN === 2
                  ? i18n.global.t('msgbox.selMoveCtrlCmd')
                  : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb06 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb07(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      yN: cmd_obj.setCmd,
      jtTime: cmd_obj.setCmd === 0 ? 0 : cmd_obj.jtTime,
      dwTime: cmd_obj.setCmd === 0 ? 0 : cmd_obj.dwTime,
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3007, msgObj, 'cb07', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb07 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResAlarmCmd'))

          // 添加运行日志
          const note =
            msgObj.yN === 0
              ? i18n.global.t('msgbox.cancelAlarmCmd')
              : msgObj.yN === 1
                ? i18n.global.t('msgbox.enableAlarmCmd')
                : msgObj.yN === 2
                  ? i18n.global.t('msgbox.selAlarmCmd')
                  : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb07 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb08(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      yN: cmd_obj.setCmd,
      time: cmd_obj.setCmd === 0 ? 0 : cmd_obj.jtTime,
      jtCh: cmd_obj.jtCh,
      src: bfglob.userInfo.setting.voipSpeakInfo.speaker,
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3008, msgObj, 'cb08', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb08 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResVioCmd'))

          // 添加运行日志
          const note =
            msgObj.yN === 0 ? i18n.global.t('msgbox.cancelVioCmd') : msgObj.yN === 1 ? i18n.global.t('msgbox.enableVioCmd') : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb08 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb09(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      yN: cmd_obj.setCmd,
      st: cmd_obj.status,
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3009, msgObj, 'cb09', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb09 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          cbxxResNo.cb09Cmd = msgObj.st
          const res_info =
            msgObj.yN === 0
              ? i18n.global.t('msgbox.notResPowerOnCmd')
              : msgObj.yN === 1
                ? i18n.global.t('msgbox.notResLockDevCmd')
                : msgObj.yN === 2
                  ? i18n.global.t('msgbox.notResSelectCmd')
                  : i18n.global.t('msgbox.unknownCmd')
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, res_info)

          // 添加运行日志
          const status =
            msgObj.st === 1
              ? i18n.global.t('msgbox.setDisListenCmd')
              : msgObj.st === 2
                ? i18n.global.t('msgbox.setDisSendCmd')
                : msgObj.st === 3
                  ? i18n.global.t('msgbox.setDisSLCmd')
                  : ''
          const note =
            msgObj.yN === 0
              ? i18n.global.t('msgbox.clearSendListenSt')
              : msgObj.yN === 1
                ? status
                : msgObj.yN === 2
                  ? i18n.global.t('msgbox.selLockDevSt')
                  : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb09 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb10(target, isSilent = false) {
    const msgObj = {
      target: this.cbxx_target(target),
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3010, msgObj, 'cb10', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb10 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          if (!isSilent) {
            bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')
          }

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResClearAlarmCmd'))

          // 添加运行日志
          const note = i18n.global.t('msgbox.sendClearAlarmCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (isSilent) return
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb10 cmd timeout', err)
        if (isSilent) return
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb21(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      yN: cmd_obj.setCmd || 0,
      ddCh: parseInt(cmd_obj.channel) || 20,
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3021, msgObj, 'cb21', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb21 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(
            cbxxResNo,
            cbxx_send_stub_obj,
            i18n.global.t('msgbox.notResCenterChCtrl'),
            Object.keys(msgObj.target.targetDevice).length
          )

          // 添加运行日志
          const note =
            msgObj.yN === 0
              ? i18n.global.t('dialog.cancelCenterCHCtrl')
              : msgObj.yN === 1
                ? i18n.global.t('dialog.openCenterCHCtrl')
                : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb21 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  cb24(_target, _cmd_obj) {
    // let msgObj = {
    //   target: this.cbxx_target(target),
    //   yN: cmd_obj.setCmd,
    //   data: cmd_obj.message,
    //   codeTp: cmd_obj.codeTP,
    //   scheduleSendTime: (!cmd_obj.sendTime) ? "" : bfTime.getUtcTimeString(cmd_obj.sendTime)
    // };
    // let msgOpts = {
    //   decodeMsgType: "cbxx_send_stub"
    // };
    //
    // bfproto.sendMessage(3024, msgObj, "cb24", radioSubject, msgOpts).then(rpc_cmd_obj => {
    //   let cbxx_send_stub_obj = rpc_cmd_obj.body;
    //   bfglob.console.log('server res cb24 cmd obj:', cbxx_send_stub_obj);
    //
    //   if (rpc_cmd_obj.resInfo === "+OK") {
    //     bfNotify.messageBox(i18n.global.t("msgbox.sendSuccess"), 'success');
    //
    //     let cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj);
    //     this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t("msgbox.notResTextMsgCmd"), Object.keys(msgObj.target.targetDevice).length);
    //
    //     // 添加运行日志
    //     let note = i18n.global.t("msgbox.sendTextMsgCmd");
    //     this.add_runNote_for_cbxx(msgObj.target, note);
    //   } else {
    //     if (rpc_cmd_obj.resInfo.includes('session')) {
    //       bfNotify.warningBox(i18n.global.t("msgbox.serverReconnect"));
    //       return
    //     }
    //     bfNotify.messageBox(i18n.global.t("msgbox.sendError"), 'error');
    //   }
    // }).catch(err => {
    //   bfglob.console.warn('send cb24 cmd timeout', err);
    //   bfNotify.messageBox(i18n.global.t("msgbox.sendError"), 'error');
    // });
  },
  bc31(target, cmd_obj) {
    target = this.cbxx_target(target)

    const db_not_confirm_sms_list = {
      rows: [],
    }
    const createSmsObj = targetDmrId => {
      const db_not_confirm_sms_obj = bfproto.bfdx_proto_msg_obj('db_not_confirm_sms')
      db_not_confirm_sms_obj.rid = uuid()
      db_not_confirm_sms_obj.orgId = bfglob.userInfo.orgId
      db_not_confirm_sms_obj.senderUserRid = bfglob.userInfo.rid
      db_not_confirm_sms_obj.startTime = bfTime.nowUtcTime()
      db_not_confirm_sms_obj.senderDmrid = '00000000' // 系统中心默认设备
      db_not_confirm_sms_obj.targetDmrid = targetDmrId
      db_not_confirm_sms_obj.smsContent = cmd_obj.message
      db_not_confirm_sms_obj.smsType = cmd_obj.smsType

      db_not_confirm_sms_list.rows.push(db_not_confirm_sms_obj)
    }

    for (let i = 0; i < target.targetGroud.length; i++) {
      const groupTarget = target.targetGroud[i]
      createSmsObj(groupTarget)
    }
    for (let i = 0; i < target.targetDevice.length; i++) {
      const deviceTarget = target.targetDevice[i]
      createSmsObj(deviceTarget)
    }

    bfproto
      .sendMessage(3031, db_not_confirm_sms_list, 'db_not_confirm_sms_list', radioSubject)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res bc31 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          // 添加运行日志
          const note = i18n.global.t('msgbox.sendTextMsgCmd')
          this.add_runNote_for_cbxx(target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb24 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },
  bc32(rpc_cmd_obj) {
    const db_not_confirm_sms_type = bfproto.bfdx_proto_msg_T('db_not_confirm_sms')
    const db_not_confirm_sms_obj = db_not_confirm_sms_type.decode(rpc_cmd_obj.body)
    bfglob.console.log('bc32 cmd', rpc_cmd_obj, db_not_confirm_sms_obj)

    // 如果不是当前用户发送的短信，则不作处理
    if (db_not_confirm_sms_obj.senderUserRid !== bfglob.userInfo.rid) {
      return
    }
    const device = bfglob.gdevices.getDataByIndex(db_not_confirm_sms_obj.targetDmrid)
    if (!device) {
      bfglob.console.error('can not found device of dmrId:', db_not_confirm_sms_obj.targetDmrid)
      return
    }

    // 通知格式： 收到 xxx 在 yyyy-mm-dd HH:MM:ss 发送的短信, xxx为发件人名称
    const sender = bfglob.userInfo.name
    const sendTime = bfTime.getLocalTimeString(bfTime.utcTimeToLocalTime(db_not_confirm_sms_obj.startTime))
    const note = i18n.global.t('msgbox.receivedSMS', {
      sender,
      sendTime,
    })

    // 创建通知实例，通知用户
    if (bfglob.userInfo.setting.showSmsResNotify) {
      device.userImageFile = bfutil.get_device_userImageFile(device)
      device.promptContent = note
      bfutil.createNotify(device)
    }

    // 添加运行日志
    this.add_runNote_for_bcxx(device, note)
  },
  cb25(target, cmd_obj) {
    const msgObj = {
      target: this.cbxx_target(target),
      yN: cmd_obj.setCmd,
      pointN: cmd_obj.pointN,
      pointCard: cmd_obj.setCmd === 2 ? '0000000000' : cmd_obj.pointCard,
      lon: cmd_obj.lon || 0,
      lat: cmd_obj.lat || 0,
      lonDif: this.return_two_bit_lngLatDif(cmd_obj.lonDif),
      latDif: this.return_two_bit_lngLatDif(cmd_obj.latDif),
    }
    const msgOpts = {
      decodeMsgType: 'cbxx_send_stub',
    }

    bfproto
      .sendMessage(3025, msgObj, 'cb25', radioSubject, msgOpts)
      .then(rpc_cmd_obj => {
        const cbxx_send_stub_obj = rpc_cmd_obj.body
        bfglob.console.log('server res cb25 cmd obj:', cbxx_send_stub_obj)

        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(i18n.global.t('msgbox.sendSuccess'), 'success')

          const cbxxResNo = this.save_res_no_for_cbxx(cbxx_send_stub_obj)
          this.checked_cbxx_is_respone(cbxxResNo, cbxx_send_stub_obj, i18n.global.t('msgbox.notResGPSVirSet'), Object.keys(msgObj.target.targetDevice).length)

          // 添加运行日志
          const note =
            msgObj.yN === 0
              ? i18n.global.t('msgbox.cancelGPSVirCmd')
              : msgObj.yN === 1
                ? i18n.global.t('msgbox.enableGPSVirCmd')
                : msgObj.yN === 2
                  ? i18n.global.t('msgbox.selGPSVirCmd')
                  : i18n.global.t('msgbox.unknownCmd')
          this.add_runNote_for_cbxx(msgObj.target, note)
        } else {
          if (rpc_cmd_obj.resInfo.includes('session')) {
            bfNotify.warningBox(i18n.global.t('msgbox.serverReconnect'))
            return
          }
          bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('send cb25 cmd timeout', err)
        bfNotify.messageBox(i18n.global.t('msgbox.sendError'), 'error')
      })
  },

  add_alarm_history(dbRid, result, alarmTime) {
    const msgObj = {
      rid: dbRid,
      deallerResult: result,
      deallerRid: bfglob.userInfo.rid,
      deallerTime: bfTime.nowUtcTime(),
    }

    const dbNameSuffixTime = (alarmTime || msgObj.deallerTime).split(' ').shift().split('-').slice(0, 2).join('')
    const msgOpts = {
      rpcCmdFields: {
        origReqId: 'rid',
        resInfo: 'dealler_rid,dealler_time,dealler_result',
        opt: `db_alarm_history${dbNameSuffixTime}`,
      },
    }

    bfproto
      .sendMessage(dbCmd.DB_ALARM_HISTORY_PUPDATE, msgObj, 'db_alarm_history', dbSubject, msgOpts)
      .then(_rpc_cmd_obj => {})
      .catch(err => {
        bfglob.console.warn('add_alarm_history timeout', err)
      })
  },
  add_user_privelege(data, up_db_cmd, orgId = '') {
    return new Promise((resolve, reject) => {
      const options = {
        orgId,
      }

      bfproto
        .sendMessage(up_db_cmd, data, 'db_user_privelege', dbSubject, options)
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo === '+OK') {
            resolve(1)
          } else {
            reject(0)
          }
        })
        .catch(err => {
          bfglob.console.warn('添加用户权限超时', err)
          reject(0)
        })
    })
  },
}

// 通过dmrId向服务器请求一个终端数据，终端可能不存在
export async function requestDbDataByDmrId(dmrId, encodeMsgType, decodeMsgType, command) {
  const msgObj = {
    dmrId: dmrId,
  }
  const msgOpts = {
    rpcCmdFields: {
      origReqId: 'dmr_id',
      resInfo: '*',
    },
    decodeMsgType: decodeMsgType,
  }
  const rpc_cmd_obj = await bfproto.sendMessage(command, msgObj, encodeMsgType, getDbSubject(), msgOpts).catch(err => {
    bfglob.console.warn('requestDeviceByDmrId error:', err)
    return undefined
  })

  if (!rpc_cmd_obj) {
    return undefined
  }

  if (rpc_cmd_obj.resInfo === '+OK') {
    // 请求回来的终端数据是一个数组
    const rows = rpc_cmd_obj.body.rows ?? []
    // 一定有rid属性，可以作为条件判断
    // eslint-disable-next-line no-prototype-builtins
    if (rows[0]?.hasOwnProperty('rid')) {
      return rows[0]
    }

    return undefined
  }

  //   // 需要判断dmrId和selfId是否重复
  //   if (rpc_cmd_obj.resInfo.includes('db_device_self_id_key')) {
  //     bfNotify.messageBox(this.$t('msgbox.repeaterVirtualDeviceRepeatSelfId'), 'warning')
  //     res.code = 1
  //   } else if (rpc_cmd_obj.resInfo.includes('db_device_dmr_id_key')) {
  //     res.code = 2
  //     bfNotify.messageBox(this.$t('msgbox.repeaterVirtualDeviceRepeatDmrId'), 'warning')
  //   }
  //
  //   reject(res)
  bfglob.console.warn('requestDeviceByDmrId failed:', rpc_cmd_obj, msgObj)
  // bfNotify.messageBox(rpc_cmd_obj.resInfo, 'warning')

  return undefined
}
