import { StrPubSub } from 'ypubsub'

/**
 * StrPubSub.subscribe
 * @param {string} subject
 * @param {Function} handler
 */
export function on(subject, handler) {
  StrPubSub.subscribe(subject, handler)
}

/**
 * StrPubSub.subscribeOnce
 * @param {string} subject
 * @param {Function} handler
 */
export function once(subject, handler) {
  StrPubSub.subscribeOnce(subject, handler)
}

/**
 * StrPubSub.unsubscribe
 * @param {string|string[]} subject
 * @param {Function?} handler
 */
export function off(subject, handler) {
  StrPubSub.unsubscribe(subject, handler)
}

/**
 * StrPubSub.publish
 * @param {string} subject
 * @param {any[]} args
 */
export function emit(subject, ...args) {
  StrPubSub.publish(subject, ...args)
}

export default {
  on,
  once,
  off,
  emit,
}
