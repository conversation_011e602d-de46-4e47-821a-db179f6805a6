<template>
  <el-transfer
    v-model="chIdList"
    class="channel-transfer"
    :titles="titles"
    :data="availableChannelList"
    :props="{ key: 'value' }"
    @change="onChange"
  >
    <template #default="{ option }">
      <span>{{ option.label }}</span>
    </template>
  </el-transfer>
</template>

<script>
import { messageBox, Types } from '@/utils/notify'
import i18n from '@/modules/i18n'

export default {
  name: 'ChannelTransfer',
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: Array,
      required: true,
    },
    channelList: {
      type: Array,
      required: true,
    },
    maxlength: {
      type: Number,
      default: 16,
    },
    specialSelectOptions: {
      type: Array,
      default() {
        return [
          // {
          //   label: this.$t('dialog.nothing'),
          //   disabled: true,
          //   value: 0xFFFF,
          // },
          {
            label: i18n.global.t('writeFreq.theSelected'),
            disabled: true,
            value: 0xfffe,
          },
          // {
          //   label: this.$t('writeFreq.lastActiveChannel'),
          //   disabled: true,
          //   value: 0xFFFD,
          // },
        ]
      },
    },
  },
  data() {
    return {
      chIdList: [],
    }
  },
  methods: {
    onChange(list) {
      // 设置选中的信道时，最大为16个
      if (list.length > this.maxlength) {
        this.chIdList = this.chIdList.slice(0, this.maxlength)
        messageBox(this.$t('writeFreq.fullList'), Types.warning)
      }
    },
  },
  computed: {
    titles() {
      return [
        this.$t('writeFreq.availableChannel'),
        this.$t('writeFreq.containedChannel'),
      ]
    },
    availableChannelList() {
      return [...this.specialSelectOptions, ...this.channelList]
    },
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler(v) {
        this.chIdList = v
      },
    },
    chIdList: {
      deep: true,
      handler(v) {
        this.$emit('update:modelValue', v)
      },
    },
  },
}
</script>

<style lang="scss">
.el-transfer.channel-transfer {
  width: 100%;
  display: flex;
  min-height: 300px;
  max-height: 560px;

  .el-transfer-panel {
    flex: auto;
    width: auto;

    .el-transfer-panel__item {
      $height: 24px;

      display: block;
      height: $height;
      line-height: $height;
      padding: 0 10px;
      margin-right: 0;

      .el-checkbox__label {
        line-height: $height;
      }
    }

    .el-transfer-panel__body {
      height: calc(100% - 40px);

      .el-transfer-panel__list {
        height: 100%;
      }
    }
  }

  .el-transfer__buttons {
    display: flex;
    flex-direction: column;
    gap: calc(var(--spacing) * 3);
    align-self: center;

    .el-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin: 0;
    }
  }
}
</style>
