<template>
  <el-form
    ref="patrolSystemEmergency"
    class="patrol-system-emergency-form"
    :model="emergencyAlarm"
    label-width="95px"
    label-position="top"
  >
    <el-form-item>
      <el-checkbox v-model="emergencyAlarm.alarmConfig.alarmEnable">
        <span v-text="$t('writeFreq.enable')" />
      </el-checkbox>
    </el-form-item>
    <el-form-item :label="$t('dataTable.alarmType')">
      <el-select
        v-model="emergencyAlarm.alarmConfig.alarmType"
        :placeholder="$t('dialog.select')"
        filterable
        :disabled="disAlarmSettings"
        :no-match-text="$t('dialog.noMatchText')"
      >
        <el-option
          v-for="(item, i) in alarmTypeList"
          :key="i"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('dialog.sendCount')">
      <bf-input-number
        v-model="sendCount"
        :disabled="disAlarmSettings"
        :formatter="sendCountFormat"
        step-strictly
        :min="0"
        :max="14"
      />
    </el-form-item>
    <el-form-item :label="$t('dialog.callingContact')">
      <el-select
        v-model="emergencyAlarm.callingContact"
        :placeholder="$t('dialog.select')"
        filterable
        :disabled="disAlarmSettings"
        :no-match-text="$t('dialog.noMatchText')"
      >
        <el-option
          v-for="(item, i) in callingContactList"
          :key="i"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('dialog.autoListeningTime')">
      <el-input-number
        v-model="emergencyAlarm.autoListenTime"
        :disabled="disAlarmSettings"
        step-strictly
        :min="0"
        :max="99"
        :step="1"
      />
    </el-form-item>
    <el-form-item
      v-if="showAutoTrackTime"
      :label="$t('dialog.autoPositioningTime')"
    >
      <el-input-number
        v-model="emergencyAlarm.autoTrackTime"
        :disabled="disAlarmSettings"
        step-strictly
        :min="0"
        :max="99"
        :step="1"
      />
    </el-form-item>
  </el-form>
</template>

<script>
import { cloneDeep } from 'lodash'
import { defineAsyncComponent } from 'vue'

const EmergencyAlarm = {
  alarmConfig: {
    alarmEnable: true,
    alarmType: 1,
  },
  sendCount: 3,
  callingContact: 0xffff,
  autoListenTime: 20,
  autoTrackTime: 30,
}

export default {
  name: 'EmergencyAlarmConfig',
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: Object,
      required: true,
    },
    addressBooks: {
      type: Array,
      default() {
        return []
      },
    },
    showAutoTrackTime: {
      type: Boolean,
      default: true,
    },
    hasSelectedItem: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      emergencyAlarm: cloneDeep(EmergencyAlarm),
      sendCount: 3,
    }
  },
  methods: {
    // 紧急报警发送次数格式化方法
    sendCountFormat(val) {
      if (val === 0) {
        return this.$t('dialog.keepSending')
      }

      return val
    },
    initData() {
      this.emergencyAlarm = cloneDeep(EmergencyAlarm)
    },
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler(val) {
        this.emergencyAlarm = Object.assign(this.emergencyAlarm, val)
        let count = this.emergencyAlarm.sendCount
        if (count === 0xff) {
          count = 0
        }
        this.sendCount = count
      },
    },
    emergencyAlarm: {
      deep: true,
      immediate: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
    sendCount(val) {
      let count = val
      if (count === 0) {
        count = 0xff
      }
      this.emergencyAlarm.sendCount = count
    },
  },
  computed: {
    disAlarmSettings() {
      return !(
        this.emergencyAlarm.alarmConfig &&
        this.emergencyAlarm.alarmConfig.alarmEnable
      )
    },
    callingContactList() {
      const list = this.hasSelectedItem
        ? [{ label: this.$t('writeFreq.theSelected'), value: 0xffff }]
        : [{ label: this.$t('dialog.default'), value: 0xffff }]
      for (let i = 0; i < this.addressBooks.length; i++) {
        const item = this.addressBooks[i]
        list.push({
          label: item.name,
          value: item.id,
        })
      }
      return list
    },
    alarmTypeList() {
      return [
        { label: this.$t('dialog.common'), value: 0 },
        { label: this.$t('dialog.silent'), value: 1 },
      ]
    },
  },
  components: {
    bfInputNumber: defineAsyncComponent(
      () => import('@/components/common/bfInputNumber'),
    ),
  },
}
</script>

<style scoped></style>
