<template>
  <el-form
    ref="virtualCluster"
    class="grid grid-cols-2 gap-2 virtual-cluster-config-form"
    :model="virtualCluster"
    :label-position="labelPosition"
    :rules="rules"
  >
    <el-divider class="col-span-2">
      <el-icon>
        <CaretBottom />
      </el-icon>
      <span v-text="$t('writeFreq.baseSettings')" />
    </el-divider>
    <el-form-item :label="$t('writeFreq.ownGroup')">
      <el-input v-model="vcGroupIdLabel" disabled />
    </el-form-item>
    <!-- 测机信号间隔 -->
    <el-form-item
      v-if="hasTestMachineInterval"
      :label="$t('dialog.testMachineSignalInterval', { unit: 'ms' })"
    >
      <el-input-number
        v-model.number="virtualCluster.testMachineInterval"
        :min="960"
        :max="18000"
        :step="120"
        step-strictly
      />
    </el-form-item>
    <el-form-item :label="$t('dialog.rssiThreshold')">
      <el-input-number
        v-model.number="virtualClusterRssiValue"
        :min="-120"
        :max="-80"
        :step="1"
        step-strictly
      />
    </el-form-item>
    <el-form-item
      :label="$t('writeFreq.authenticationSecretKey')"
      prop="authKey"
    >
      <el-input
        v-model="virtualCluster.authKey"
        :maxlength="32"
        @blur="authKeyOnchange"
      />
    </el-form-item>

    <el-divider class="col-span-2">
      <el-icon>
        <CaretBottom />
      </el-icon>
      <span v-text="$t('writeFreq.scanList')" />
    </el-divider>
    <channel-transfer
      v-model="siteList"
      :channelList="virtualClusterChIdList"
      class="col-span-2"
    />
  </el-form>
</template>

<script>
import { cloneDeep } from 'lodash'
import { VirtualClusterMixin } from '@/writingFrequency/interphone/virtualClusterMixin'
import { defineAsyncComponent } from 'vue'

// 虚拟集群默认配置
const VirtualCluster = {
  // 所属归属组，从组呼联系人选择
  vcGroupId: 0xffff,
  // 测机信号间隔，单位MS，范围960~18000，步进120
  // v1版本才有测机信号间隔参数
  testMachineInterval: 3600,
  // RSSI阈值，范围-120~-80，步进1，绝对值传输
  rssiValue: 0,
  // 站点成员数量
  siteListMemCount: 1,
  // 密钥值,0~9,A~F,没填满默认补F
  authKey: ''.padEnd(32, 'F'),
  // 站点列表(信道)，固定包含"选定的"站点 默认：0xFFFE
  // "0xFFFF:无
  // 0xFFFE:选定的信道
  // 0~1023：指定已配置信道"
  siteList: [0xfffe],
}

export default {
  name: 'VirtualCluster',
  mixins: [VirtualClusterMixin],
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: Object,
      required: true,
    },
    labelPosition: {
      type: String,
      default: () => 'left',
    },
    selectDeviceData: {
      type: Object,
    },
    selectedAddressBook: {
      type: Array,
      default: () => {
        return []
      },
    },
    channelDataList: {
      type: Array,
      default: () => {
        return []
      },
    },
    version: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      virtualCluster: cloneDeep(VirtualCluster),
    }
  },
  methods: {
    reset() {
      this.virtualCluster = cloneDeep(VirtualCluster)
    },
    authKeyOnchange() {
      // blur event
      const val = this.virtualCluster.authKey
      this.$refs.virtualCluster?.validateField('authKey', valid => {
        if (!valid) {
          return
        }
        // 表单检验通过后，自动补齐32字节
        if (val.length < 32) {
          this.virtualCluster.authKey = val.padEnd(32, 'F')
        }
      })
    },
    // 提供给父组件调用内容的el-form表单校验规则
    validate(callback) {
      if (typeof callback === 'function') {
        this.$refs.virtualCluster?.validate(valid => {
          callback(valid)
        })
        return
      }

      return Promise.resolve(this.$refs.virtualCluster?.validate() ?? true)
    },
  },
  computed: {
    hasTestMachineInterval() {
      return this.version === 1
    },
    rules() {
      return {
        authKey: [
          {
            validator: function (rule, value, callback) {
              // 只能是大写的16进制字符
              const reg = /^[0-9A-F]+$/
              // 可以为空
              if (!value) {
                callback()
              } else if (!reg.test(value)) {
                const errorMsg = new Error('0-9, A-F')
                callback(errorMsg)
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ],
      }
    },
    virtualClusterRssiValue: {
      get() {
        return this.virtualCluster.rssiValue * -1
      },
      set(v) {
        this.virtualCluster.rssiValue = v * -1
      },
    },
    virtualClusterChIdList() {
      // 虚拟集群扫描列表的可用信道只允许包含勾选了”虚拟集群”的数字信道
      return this.channelDataList
        .filter(ch => {
          const isVirtualCluster =
            ch.subChannelData.funcSettings?.svtEnable ?? false
          return ch.chType === 0 && isVirtualCluster
        })
        .map(ch => ({
          value: ch.chId,
          label: ch.chName,
        }))
    },
    siteList: {
      get() {
        const list = this.virtualCluster.siteList
        const siteListMemCount = this.virtualCluster.siteListMemCount
        if (list.length > siteListMemCount) {
          return list.slice(0, siteListMemCount)
        }
        return list
      },
      set(list) {
        this.virtualCluster.siteListMemCount = list.length
        this.virtualCluster.siteList = list
      },
    },
  },
  watch: {
    modelValue: {
      deep: true,
      handler(data) {
        Object.assign(this.virtualCluster, data)
      },
    },
    virtualCluster: {
      deep: true,
      handler(data) {
        this.$emit('update:modelValue', data)
      },
    },
  },
  components: {
    channelTransfer: defineAsyncComponent(() => import('../channelTransfer')),
  },
}
</script>

<style scoped>
.el-form.virtual-cluster-config-form {
  height: auto;
}
</style>
