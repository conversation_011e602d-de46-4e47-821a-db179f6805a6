<template>
  <el-form
    ref="virtualCluster"
    class="virtual-cluster-config-form"
    :model="virtualCluster"
    :label-position="labelPosition"
    :rules="rules"
  >
    <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
      <el-divider>
        <el-icon>
          <CaretBottom />
        </el-icon>
        <span v-text="$t('writeFreq.baseSettings')" />
      </el-divider>
      <el-col :xs="24">
        <el-form-item :label="$t('writeFreq.ownGroup')">
          <el-input v-model="vcGroupIdLabel" disabled />
        </el-form-item>
      </el-col>
      <!-- 测机信号间隔 -->
      <el-col :xs="24">
        <el-form-item
          :label="$t('dialog.testMachineSignalInterval', { unit: 'ms' })"
        >
          <el-input-number
            v-model.number="virtualCluster.testMachineInterval"
            :min="960"
            :max="18000"
            :step="120"
            step-strictly
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item :label="$t('dialog.rssiThreshold')">
          <el-input-number
            v-model.number="virtualClusterRssiValue"
            :min="-120"
            :max="-80"
            :step="1"
            step-strictly
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item
          :label="$t('writeFreq.authenticationSecretKey')"
          prop="authenticationSecretKey"
        >
          <el-input
            v-model="virtualCluster.authenticationSecretKey"
            :maxlength="32"
            @blur="authenticationSecretKeyOnchange"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
      <el-divider>
        <el-icon>
          <CaretBottom />
        </el-icon>
        <span v-text="$t('writeFreq.svtChannelList')" />
      </el-divider>
      <el-col :xs="24" class="transfer-wrapper">
        <channel-transfer
          v-model="channelList"
          :channelList="virtualClusterChIdList"
        />
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { cloneDeep } from 'lodash'
import {
  createDefaultArgs,
  TableIndex,
} from '@/writingFrequency/interphone/930SVT00'
import { VirtualClusterMixin } from '@/writingFrequency/interphone/virtualClusterMixin'
import { defineAsyncComponent } from 'vue'

const VirtualCluster = createDefaultArgs({ type: TableIndex.VirtualCluster })

export default {
  name: 'VirtualCluster',
  mixins: [VirtualClusterMixin],
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: Object,
      required: true,
    },
    labelPosition: {
      type: String,
      default: () => 'left',
    },
    selectDeviceData: {
      type: Object,
    },
    selectedAddressBook: {
      type: Array,
      default: () => {
        return []
      },
    },
    channelDataList: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      virtualCluster: cloneDeep(VirtualCluster),
    }
  },
  methods: {
    // 提供给父组件调用内容的el-form表单校验规则
    validate(callback) {
      if (typeof callback === 'function') {
        this.$refs.virtualCluster?.validate(valid => {
          callback(valid)
        })
        return
      }

      return Promise.resolve(this.$refs.virtualCluster?.validate() ?? true)
    },
    authenticationSecretKeyOnchange() {
      // blur event
      const val = this.virtualCluster.authenticationSecretKey
      this.$refs.virtualCluster?.validateField(
        'authenticationSecretKey',
        valid => {
          if (!valid) {
            return
          }
          // 表单检验通过后，自动补齐32字节
          if (val.length < 32) {
            this.virtualCluster.authenticationSecretKey = val.padEnd(32, 'F')
          }
        },
      )
    },
  },
  computed: {
    rules() {
      return {
        authenticationSecretKey: [
          {
            validator: function (rule, value, callback) {
              // 只能是大写的16进制字符
              const reg = /^[0-9A-F]+$/
              // 可以为空
              if (!value) {
                callback()
              } else if (!reg.test(value)) {
                const errorMsg = new Error('0-9, A-F')
                callback(errorMsg)
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ],
      }
    },
    virtualClusterRssiValue: {
      get() {
        return this.virtualCluster.rssiValue - 120
      },
      set(v) {
        this.virtualCluster.rssiValue = v + 120
      },
    },
    virtualClusterChIdList() {
      // 虚拟集群扫描列表的可用信道只允许包含勾选了”虚拟集群”的数字信道
      return this.channelDataList
        .filter(ch => {
          const isVirtualCluster = !!ch.encryptionSettings.virtualCluster
          return ch.chType === 0 && isVirtualCluster
        })
        .map(ch => ({
          value: ch.chId,
          label: ch.chName,
        }))
    },
    channelList: {
      get() {
        const list = this.virtualCluster.channelList
        if (list.includes(0xffff)) {
          return list.filter(v => v !== 0xffff)
        }
        return list
      },
      set(list) {
        this.virtualCluster.channelList = list
      },
    },
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler(data) {
        Object.assign(this.virtualCluster, data)
      },
    },
    virtualCluster: {
      deep: true,
      handler(data) {
        this.$emit('update:modelValue', data)
      },
    },
  },
  components: {
    channelTransfer: defineAsyncComponent(() => import('../channelTransfer')),
  },
}
</script>

<style scoped></style>
