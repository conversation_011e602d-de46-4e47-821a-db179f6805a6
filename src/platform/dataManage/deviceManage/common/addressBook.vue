<template>
  <section class="p-3 address-book-section">
    <div class="address-book-selected-info mb-1 right">
      <span v-text="selectedNodes.length || 0" />
      <span>/</span>
      <span v-text="maxSize" />
    </div>
    <TableTree
      :ref="treeId"
      :treeId="treeId"
      :option="treeOpts"
      :filterOption="filterOption"
      @loaded="treeLoaded"
    />
  </section>
</template>

<script>
import TableTree from '@/components/common/tableTree'
import { cloneDeep } from 'lodash'
import bftree from '@/utils/bftree'
import bfutil from '@/utils/bfutil'
import bfStorage from '@/utils/storage'

let selectedTimer = 0
const addressIdCache = {}

export default {
  name: 'AddressBook',
  emits: ['select'],
  props: {
    treeId: {
      type: String,
      required: true,
    },
    callTypes: {
      type: Object,
      required: true,
    },
    maxSize: {
      type: Number,
      default: 256,
    },
    noSysSelect: {
      type: <PERSON>olean,
      default: false,
    },
    redrawTree: {
      type: Boolean,
      default: false,
    },
    encode: {
      type: Function,
    },
    decode: {
      type: Function,
    },
    addressBook: {
      type: Array,
    },
  },
  data() {
    return {
      selectedNodes: [],
      notInSystemNode: [],
    }
  },
  methods: {
    clickNode(event, data) {
      const excludeList = ['expander', 'prefix', 'checkbox']
      if (excludeList.includes(data.targetType)) {
        return true
      }
      const node = data.node
      if (!node) {
        return false
      }
      node.setActive()
      node.setSelected(!node.isSelected())
    },
    // 深度选中节点，如果当前节点已经选中，则取消当前节点及所有子级节点的选中状态，反之选中节点
    deepSelectedNodes(node, isSelected = false, selectedCount = 0) {
      const children = [node]
      while (children.length > 0 && selectedCount < this.maxSize) {
        const child = children.shift()
        if (!child) {
          continue
        }

        child.data.dblClickSelected = isSelected
        child.setSelected(isSelected)
        if (isSelected) {
          selectedCount++
        } else {
          selectedCount--
        }
        if (child.hasChildren()) {
          children.push(...child.getChildren())
        }
      }
    },
    dblclickNode(event, data) {
      const node = data.node
      if (!node) {
        return false
      }
      node.setActive()
      const dblClickSelected = node.data.dblClickSelected
      this.deepSelectedNodes(
        node,
        node.unselectable ? !dblClickSelected : !node.selected,
        this.selectedNodes.length - 1,
      )
      return false
    },
    getUsedIdFromCache() {
      return Object.keys(addressIdCache).map(key => addressIdCache[key])
    },
    getNextId(dmrId, limit = this.maxSize) {
      // 使用dmrId从缓存的通讯录中查找数据，如果存在则使用之前的ID
      let id = addressIdCache[dmrId]
      if (typeof id !== 'undefined') {
        return id
      }

      // 缓存中没有该dmrId的数据，则重新生成一个
      id = 0
      const usedId = this.getUsedIdFromCache()
      while (id < limit) {
        if (!usedId.includes(id)) {
          addressIdCache[dmrId] = id
          return id
        }
        id++
      }

      addressIdCache[dmrId] = id
      return id
    },
    selectNodes(event, data) {
      clearTimeout(selectedTimer)
      selectedTimer = setTimeout(() => {
        const nodes = data.tree.getSelectedNodes()
        const selectedNodes = []
        const booksCache = {}
        for (let i = 0; i < nodes.length; i++) {
          if (selectedNodes.length >= this.maxSize) {
            break
          }

          const node = nodes[i]
          let dmrId = node.data.dmrId ?? node.data.origin.dmrId
          const isGroup = parseInt(dmrId, 16) >>> 24 === 0x80
          let callType = isGroup ? this.callTypes.GROUP : this.callTypes.SINGLE
          let name = this.$t('dialog.fullCall')
          let target = null
          // 判断是否为全呼节点
          if (node.key === bfglob.fullCallDmrId) {
            target = {
              orgShortName: name,
            }
            callType = this.callTypes.BROADCAST
          } else if (isGroup) {
            target = bfglob.gorgData.getDataByIndex(dmrId)
            name = target ? target.orgShortName : ''
          } else {
            target = bfglob.gdevices.getDataByIndex(dmrId)
            name = target ? target.selfId : ''
          }

          // 非本系统权限内的数据，判断是否可选，不可选则跳过不处理
          if (!target) {
            if (this.noSysSelect) {
              name = node.data.origin.name
              dmrId = node.data.origin.dmrId
            } else {
              continue
            }
          }

          if (booksCache[dmrId]) {
            continue
          }

          const id = this.getNextId(dmrId)
          let configGroupId = null
          if (this.addressBook?.length > 0) {
            configGroupId =
              this.addressBook.find(item => item.id === id)?.groupId ?? null
          }
          let config = {
            id,
            name: name,
            number: parseInt(dmrId.slice(2), 16),
            callType,
            setting: {
              callType,
              ishide: 0,
              islock: 1,
            },
            ringType: 0,
            dmrId,
            groupId: configGroupId ?? 0xffff, // TD910 标记属于哪个联系人群组
            nodeKey: node.key,
          }
          config =
            typeof this.encode === 'function' ? this.encode(config) : config
          selectedNodes.push(config)
          booksCache[dmrId] = 1
        }
        this.selectedNodes = selectedNodes
        this.$emit('select', this.selectedNodes)
      }, 0)
    },
    beforeSelect(event, data) {
      const node = data.node
      if (!node) {
        return true
      }
      if (node.isSelected()) {
        return true
      }
      return !(this.selectedNodes.length >= this.maxSize)
    },
    toDictDefaultTreeNodes() {
      this.tableTree.toDictTree('bftree', dict => {
        dict.partsel = false
        dict.selected = false
        dict.expanded = true
      })
    },
    treeLoaded() {
      if (!this.tableTree) {
        setTimeout(() => {
          this.treeLoaded()
        }, 50)
        return
      }

      this.toDictDefaultTreeNodes()
      // 添加一个全呼通讯录节点
      bftree.addFullCallNode(this.treeId)
    },
    // restoreNodeExpanded() {
    //   let key = `fancytree:${bfglob.userInfo.rid}:${this.treeId}`
    //   let state = bfStorage.getItem(key) || ''
    //   if (state) {
    //     state.split('~').forEach((key) => {
    //       let node = bftree.getTreeNodeByRid(this.treeEl, key)
    //       if (node) {
    //         node.setExpanded(false)
    //       }
    //     })
    //   }
    // },
    restoreNodeSelectedStatus() {
      // 恢复节点展开状态
      // this.restoreNodeExpanded()
      // 重置选中的节点数据
      const selectedNodesCache = cloneDeep(this.selectedNodes)
      for (let i = 0; i < selectedNodesCache.length; i++) {
        const book = selectedNodesCache[i]
        let target, key
        if (book.callType === this.callTypes.GROUP) {
          // 组呼节点
          target = bfglob.gorgData.getDataByIndex(book.dmrId)
        } else if (book.callType === this.callTypes.SINGLE) {
          // 单呼节点
          target = bfglob.gdevices.getDataByIndex(book.dmrId)
        } else {
          // 全呼节点
          target = {
            rid: bfglob.fullCallDmrId,
          }
        }
        if (target) {
          key = target.rid
        }
        this.tableTree.setNodeSelected(key, true)
      }
    },
    treeReload(clearCache = false) {
      this.tableTree.clearTree()
      this.treeLoaded()
      this.$nextTick(() => {
        if (clearCache) {
          this.selectedNodes = []
          this.$emit('select', this.selectedNodes)
        } else {
          this.restoreNodeSelectedStatus()
        }
      })
    },
    // 同步通讯录节点，通讯录ID要保持不变，以变正确处理其他功能使用
    asyncNodeSelectStatus(books) {
      if (!Array.isArray(books)) {
        return
      }
      for (let i = 0; i < books.length; i++) {
        let item = books[i]
        if (typeof this.decode === 'function') {
          item = this.decode(item)
        }

        let callType = item.callType
        if (typeof callType === 'undefined') {
          callType = item.setting.callType
        }
        const isGroup = callType === this.callTypes.GROUP
        let target = null
        // 生成dmrId，过滤非本系统数据
        if (!item.dmrId) {
          item.dmrId = bfutil.toHexDmrId(item.number, isGroup)
        }

        if (callType === this.callTypes.BROADCAST) {
          // 全呼节点
          item.dmrId = bfglob.fullCallDmrId
          target = {
            rid: bfglob.fullCallDmrId,
          }
        } else if (isGroup) {
          target = bfglob.gorgData.getDataByIndex(item.dmrId)
        } else {
          target = bfglob.gdevices.getDataByIndex(item.dmrId)
        }

        if (target) {
          bftree.setSelected(this.treeId, target.rid)
        } else {
          // 非本系统权限内的数据，只显示相关数据信息
          // 添加联系人数据节点，并禁用选择功能
          this.notInSystemNode.push(item)
          const key = `${item.name} - ${item.dmrId}`
          const node = bftree.getTreeNodeByRid(this.treeId, key)
          if (!node) {
            const rootNode = this.tableTree.getRootNode()
            rootNode.addChildren({
              title: key,
              key: key,
              icon: 'mdi mdi-alert-circle-outline',
              selected: this.noSysSelect,
              folder: false,
              otherOwner: true,
              origin: item,
              unselectable: true,
              // unselectableStatus default undefined
              // unselectableStatus: undefined
            })
          }
        }

        addressIdCache[item.dmrId] = item.id
      }
      this.redrawViewport()
    },
    unselectableSelectedRxGroup(dmrIdList, cb) {
      for (let i = 0; i < dmrIdList.length; i++) {
        const dmrId = dmrIdList[i]
        let data
        if (dmrId === bfglob.fullCallDmrId) {
          data = {
            rid: bfglob.fullCallDmrId,
          }
        } else {
          data = bfglob.gorgData.getDataByIndex(dmrId)
        }

        if (!data) {
          continue
        }
        bftree.resetNodeUnselectable(this.treeId, data.rid, {
          reset(node) {
            node.setSelected(true)
            node.unselectable = true
            node.unselectableStatus = true
          },
        })
      }
      this.tableTree.getTree().redrawViewport()
      this.$nextTick(() => {
        typeof cb === 'function' && cb()
      })
    },
    // saveNodeCollapsedStatus(event, data) {
    //   let key = `fancytree:${bfglob.userInfo.rid}:${this.treeId}`
    //   let val = data.node.key
    //   let state = bfStorage.getItem(key) || ''
    //   if (state.includes(val)) {
    //     return
    //   }
    //   state += `${state ? '~' : ''}${val}`
    //   bfStorage.setItem(key, state)
    // },
    // removeNodeCollapsedStatus(event, data) {
    //   let key = `fancytree:${bfglob.userInfo.rid}:${this.treeId}`
    //   let val = data.node.key
    //   let state = bfStorage.getItem(key) || ''
    //   if (state.includes(val)) {
    //     state = state.split('~')
    //       .filter((k) => {
    //         return k !== val
    //       })
    //       .join('~')
    //   }
    //   bfStorage.setItem(key, state)
    // },
    // 删除非系统内节点数据
    removeNotInSystemNodes() {
      for (let i = 0; i < this.notInSystemNode.length; i++) {
        const item = this.notInSystemNode[i]
        const key = `${item.name} - ${item.dmrId}`
        const node = this.tableTree.getNodeByKey(key)
        if (!node) {
          continue
        }
        node.remove()
      }
      this.notInSystemNode = []
      this.tableTree.updateViewport()
    },

    updateAddrBookTree(data) {
      this.treeReload()
      this.redrawViewport()
    },
    redrawViewport(retry = 0) {
      if (!this.tableTree) {
        setTimeout(() => {
          this.redrawViewport(retry)
        }, 50)
        return
      }
      if (retry > 10) {
        return
      }
      this.tableTree.updateViewport()
      setTimeout(() => {
        const viewport = this.tableTree.getTree().viewport
        const count = this.tableTree.getViewportCount()
        if (
          isNaN(viewport.count) ||
          isNaN(viewport.start) ||
          count !== viewport.count
        ) {
          this.redrawTree && this.redrawViewport(++retry)
        }
      }, 0)
    },

    addOneDeviceNode(device) {
      bftree.addOneDeviceNode(this.treeId, device, { selected: false })
      bftree.renderOrgCounter(this.treeId)
    },
    delOneDeviceNode(device) {
      bftree.delOneDeviceNode(this.treeId, device)
      bftree.renderOrgCounter(this.treeId)
    },
    updateOneDeviceNode(device) {
      bftree.updateOneDeviceNode(this.treeId, device)
      bftree.renderOrgCounter(this.treeId)
    },
    addOneOrgNode(orgData) {
      bftree.addOneOrgNode(this.treeId, orgData, { selected: false })
      bftree.renderOrgCounter(this.treeId)
    },
    delOneOrgNode(orgData) {
      bftree.delOneOrgNode(this.treeId, orgData)
      bftree.renderOrgCounter(this.treeId)
    },
    updateOneOrgNode(orgData) {
      bftree.updateOneOrgNode(this.treeId, orgData)
      bftree.renderOrgCounter(this.treeId)
    },
    treeSortChildren() {
      bftree.sortChildren(this.treeId)
    },
    updateTreeNode(device) {
      bftree.updateDeviceNodeTitle(this.treeId, device)
    },
  },
  computed: {
    treeOpts() {
      return {
        selectMode: 2,
        // collapse: this.saveNodeCollapsedStatus,
        // expand: this.removeNodeCollapsedStatus,
        select: this.selectNodes,
        beforeSelect: this.beforeSelect,
        dblclick: this.dblclickNode,
        click: this.clickNode,
      }
    },
    filterOption() {
      return {
        leavesOnly: false,
      }
    },
    tableTree() {
      return this.$refs[this.treeId]
    },
  },
  watch: {
    redrawTree(val) {
      if (val && this.tableTree) {
        this.redrawViewport()
      }
    },
  },
  components: {
    TableTree,
  },
  beforeMount() {
    // 监听数据变化，以同步通讯录节点
    bfglob.on('vorgs_table_add_data', this.updateAddrBookTree)
    bfglob.on('vorgs_table_update_data', this.updateAddrBookTree)
    bfglob.on('vorgs_table_delete_data', this.updateAddrBookTree)
    bfglob.on('vdevices_table_add_data', this.updateAddrBookTree)

    bfglob.on(
      `${this.treeId}:unselectableNode`,
      this.unselectableSelectedRxGroup,
    )
    bfglob.on('wf:redrawTree', this.redrawViewport)

    // 同步树节点的增、删、改操作
    bfglob.on('addOneDeviceNode', this.addOneDeviceNode)
    bfglob.on('delOneDeviceNode', this.delOneDeviceNode)
    bfglob.on('updateOneDeviceNode', this.updateOneDeviceNode)
    bfglob.on('addOneOrgNode', this.addOneOrgNode)
    bfglob.on('delOneOrgNode', this.delOneOrgNode)
    bfglob.on('updateOneOrgNode', this.updateOneOrgNode)

    bfglob.on('treeSortChildren', this.treeSortChildren)
    bfglob.on('updateDeviceNodeTitle', this.updateTreeNode)
  },
  beforeUnmount() {
    // Vue实例销毁前，取消订阅的一些方法主题
    bfglob.off('vorgs_table_add_data', this.updateAddrBookTree)
    bfglob.off('vorgs_table_update_data', this.updateAddrBookTree)
    bfglob.off('vorgs_table_delete_data', this.updateAddrBookTree)
    bfglob.off('vdevices_table_add_data', this.updateAddrBookTree)

    bfglob.off(
      `${this.treeId}:unselectableNode`,
      this.unselectableSelectedRxGroup,
    )
    bfglob.off('wf:redrawTree', this.redrawViewport)

    bfglob.off('addOneDeviceNode', this.addOneDeviceNode)
    bfglob.off('delOneDeviceNode', this.delOneDeviceNode)
    bfglob.off('updateOneDeviceNode', this.updateOneDeviceNode)
    bfglob.off('addOneOrgNode', this.addOneOrgNode)
    bfglob.off('delOneOrgNode', this.delOneOrgNode)
    bfglob.off('updateOneOrgNode', this.updateOneOrgNode)

    bfglob.off('treeSortChildren', this.treeSortChildren)
    bfglob.off('updateDeviceNodeTitle', this.updateTreeNode)

    // 清除旧的展开节点缓存
    bfStorage.removeItem(`fancytree:${bfglob.userInfo.rid}:${this.treeId}`)
  },
}
</script>

<style lang="scss">
.address-book-section {
  height: 100%;
  display: flex;
  flex-direction: column;

  .address-book-selected-info {
    flex: none;
    line-height: 22px;
  }
}
</style>
