<template>
  <el-table
    ref="zoneTable"
    :data="getViewData()"
    :default-sort="{ prop: zoneName, order: 'ascending' }"
    border
    stripe
    highlight-current-row
    style="width: 100%"
    @row-dblclick="rowDblclick"
  >
    <el-table-column type="index" />
    <el-table-column :label="$t('dialog.areaName')" :prop="zoneName" sortable />
  </el-table>
</template>

<script>
import { cloneDeep } from 'lodash'

const ZoneRoot = {
  zoneId: 0,
  zoneName: '',
}
const ZoneParent = {
  zoneId: 0,
  rootId: 0,
  zoneName: '',
}
const ZoneLeaf = {
  zoneId: 0,
  rootId: 0,
  parentId: 0,
  zoneEffective: 1,
  zoneName: '',
  usedFlag: 0,
  usedList: [],
}

export default {
  name: 'MultistageZone',
  emits: ['update:modelValue', 'rowDblclick'],
  props: {
    modelValue: {
      type: Array,
      required: true,
    },
    limit: {
      type: Number,
      required: true,
    },
    level: {
      type: Number,
      default: 1,
    },
    parentZone: {
      type: Object,
      default() {
        return { origin: {} }
      },
    },
    dataId: {
      type: Number,
      default: 0xfff,
    },
    zoneName: {
      type: String,
      default: 'zoneName',
    },
  },
  data() {
    return {
      dataList: [],
    }
  },
  methods: {
    rowDblclick(row, column, event) {
      this.$emit('rowDblclick', row, column, event)
    },
    nextId(limit = this.limit) {
      const usedId = (this.dataList || []).map(item => item.zoneId)
      let id = 0
      while (id < limit) {
        if (!usedId.includes(id)) {
          return id
        }
        id++
      }
      return id
    },
    newOneZoneRoot() {
      const data = cloneDeep(ZoneRoot)
      const id = this.nextId()
      data.zoneId = id
      data.zoneName = `${this.$t('writeFreq.zones.root')} ${id + 1}`
      return data
    },
    newOneZoneParent() {
      const data = cloneDeep(ZoneParent)
      const id = this.nextId()
      data.zoneId = id
      data.zoneName = `${this.$t('writeFreq.zones.parent')} ${id + 1}`
      return data
    },
    newOneZoneLeaf() {
      const data = cloneDeep(ZoneLeaf)
      const id = this.nextId()
      data.zoneId = id
      data.zoneName = `${this.$t('writeFreq.zones.leaf')} ${id + 1}`
      return data
    },
    addOneZoneData() {
      if (this.dataList.length >= this.limit) {
        return
      }

      let data
      if (this.level === 1) {
        data = this.newOneZoneRoot()
      } else if (this.level === 2) {
        data = this.newOneZoneParent()
      } else {
        data = this.newOneZoneLeaf()
      }
      this.dataList.push(data)

      return data
    },
    initZoneData() {
      this.dataList = []
    },
    getViewData() {
      // 一级区域时，全部显示
      if (this.level === 1) {
        return this.dataList
      }

      const parentKey = this.level === 2 ? 'rootId' : 'parentId'
      const list = []
      for (let i = 0; i < this.dataList.length; i++) {
        const data = this.dataList[i]
        if (data[parentKey] === this.parentZone.zoneId) {
          list.push(data)
        }
      }

      return list
    },
  },
  watch: {
    modelValue: {
      deep: true,
      handler(val) {
        this.dataList = val
      },
    },
    dataList: {
      deep: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
  },
}
</script>

<style scoped></style>
