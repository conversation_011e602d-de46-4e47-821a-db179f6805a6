<template>
  <div class="!w-2xl flex-none select-device-form">
    <el-form-item
      :label="$t('dialog.deviceToWriteFrequency')"
      :label-width="labelWidth"
    >
      <el-select
        v-model="selectDmrId"
        :placeholder="$t('dialog.select')"
        filterable
        clearable
        :disabled="disabled"
        :no-match-text="$t('dialog.noMatchText')"
      >
        <el-option
          v-for="item in deviceList"
          :key="item.dmrId"
          :label="item.label"
          :disabled="item.disabled"
          :value="item.dmrId"
        />
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
// 能写频的设备类型列表
// 设备类型 0:对讲机手台 1：车台 2:指挥座席 3:电话网关设备 4:中继虚拟终端 5:互联网关终端
// 6:模拟网关终端 7:数字网关终端 8:2.4G物联巡查终端 9:传统常规dmr手台 10:sip网关终端
// 11:虚拟集群对讲手台 21:基地台  22:android模拟终端
import { SupportedLang } from '@/modules/i18n'
import { DeviceTypes } from '@/utils/bfutil'

// 支持写频的终端类型
const WfDeviceTypes = [
  DeviceTypes.Device,
  DeviceTypes.Mobile,
  DeviceTypes.VirtualClusterDevice,
]

export default {
  name: 'SelectDevice',
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: String,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      gdeviceList: bfglob.gdevices.getList(),
    }
  },
  computed: {
    selectDmrId: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue', val || '')
      },
    },
    isEN() {
      return this.$i18n.locale === SupportedLang.enUS
    },
    isFR() {
      return this.$i18n.locale === SupportedLang.fr
    },
    labelWidth() {
      return this.isFR || this.isEN ? '180px' : '120px'
    },
    deviceList() {
      // 中心虚拟的设备不能写频，因设备没有属性标记是否为虚拟设备，故暂不处理
      const deviceList = []
      for (const k in this.gdeviceList) {
        const item = this.gdeviceList[k]
        const device = bfglob.gdevices.get(item.rid)
        // 设备不存在或不是对讲机
        if (!device || !WfDeviceTypes.includes(device.deviceType)) {
          continue
        }
        item.disabled = false

        deviceList.push(item)
      }
      return deviceList
    },
  },
}
</script>

<style lang="scss"></style>
