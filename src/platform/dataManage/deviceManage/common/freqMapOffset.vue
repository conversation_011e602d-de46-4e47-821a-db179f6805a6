<template>
  <el-form-item
    :label="$t('writeFreq.freqOffset')"
    class="freq-offset-container"
  >
    <el-input-number
      v-model="freqOffset"
      controls-position="right"
      :disabled="disabled"
    />
    <el-button
      icon="search"
      :disabled="disabled"
      @click="mappingFreqOffset"
      v-text="$t('writeFreq.mapping')"
    />
  </el-form-item>
</template>

<script>
import { frequencyHz2Mhz, frequencyMhz2Hz } from '@/utils/bfutil'
import { calcFreqFromOffset } from './channel/channel'

const defaultMinFreq = 400
const defaultMaxFreq = 480
const oneFreqRange = {
  min: defaultMinFreq,
  max: defaultMaxFreq,
}

export default {
  name: 'FreqMapOffset',
  emits: ['update:modelValue', 'mapping', 'update:dstFreq'],
  props: {
    modelValue: {
      type: Number,
      required: true,
    },
    freqRange: {
      type: Array,
      default: () => {
        return [{ ...oneFreqRange }]
      },
    },
    srcFreq: {
      type: Number,
      default: defaultMinFreq,
    },
    dstFreq: {
      type: Number,
      default: defaultMaxFreq,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // freqOffset: 0,
    }
  },
  methods: {
    mappingFreqOffset() {
      let freqOffset = this.freqOffset
      // 输入的不是数字，则采用默认值
      if (isNaN(freqOffset)) {
        freqOffset = 10
      }

      const rxFreq = Number(frequencyHz2Mhz(this.srcFreq))
      const freqRange = this.freqRange.find(rang => {
        return rxFreq >= rang.min && rxFreq <= rang.max
      }) ?? { min: 400, max: 480 }

      const { offset, dstFreq } = calcFreqFromOffset(
        rxFreq,
        freqOffset,
        freqRange.min,
        freqRange.max,
      )
      this.freqOffset = offset
      this.$emit('update:dstFreq', frequencyMhz2Hz(dstFreq))
      this.$emit('mapping', offset)
    },
  },
  computed: {
    freqOffset: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      },
    },
  },
}
</script>

<style lang="scss">
.freq-offset-container .el-form-item__content {
  display: flex;
  flex-wrap: nowrap;

  .el-input-number input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .el-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
