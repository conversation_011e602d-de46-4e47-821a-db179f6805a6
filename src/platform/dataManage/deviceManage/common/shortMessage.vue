<template>
  <section class="smsContentForm">
    <el-table
      :data="shortMessage"
      :empty-text="$t('msgbox.emptyText')"
      class="fix-table-width short-message-table"
      style="width: 100%"
    >
      <el-table-column
        label=""
        type="index"
        width="60"
        class-name="index-column"
      />
      <el-table-column
        :label="$t('writeFreq.sms')"
        min-width="120"
        class-name="sms-column"
      >
        <template #default="scope">
          <el-input
            v-model="scope.row.msgContent"
            class="sms-textarea"
            resize="none"
            type="textarea"
            :rows="3"
            :maxlength="maxLen"
            show-word-limit
            :placeholder="$t('dialog.msgcontentplaceholder')"
            @change="
              () => {
                msgContentChanged(scope.row)
              }
            "
          />
          <el-icon class="sms-del-icon" @click="deleteSms(scope.row.msgId)">
            <Delete />
          </el-icon>
        </template>
      </el-table-column>
    </el-table>
  </section>
</template>

<script>
import { cloneDeep } from 'lodash'

const DefaultSms = {
  msgId: 0,
  msgContent: '',
}
const hasLenModels = ['TD081100', '825SDC00', '930SDC00']

export default {
  name: 'ShortMessage',
  emits: ['update:modelValue', 'change'],
  props: {
    maxSize: {
      type: Number,
      default: 10,
    },
    maxLen: {
      type: Number,
      default: 140,
    },
    model: {
      type: String,
      default: '',
    },
    modelValue: {
      type: Array,
      default: () => {
        return []
      },
    },
  },
  data() {
    return {
      shortMessage: [],
    }
  },
  methods: {
    initMessages() {
      const shortMessage = []
      while (shortMessage.length < this.maxSize) {
        const sms = cloneDeep(DefaultSms)
        sms.msgId = shortMessage.length
        shortMessage.push(sms)
      }

      this.shortMessage = shortMessage
    },
    emitValue() {
      this.$nextTick(() => {
        const data = this.shortMessage.filter(item => !!item.msgContent)
        this.$emit('update:modelValue', data)
      })
    },
    deleteSms(msgId) {
      if (!this.shortMessage[msgId].msgContent) {
        return
      }

      this.shortMessage[msgId].msgContent = ''
      this.emitValue()
    },
    // 短信内容有变化时向父级组件发送所有非空短信集合
    msgContentChanged(sms) {
      if (hasLenModels.includes(this.model)) {
        sms.length = sms.msgContent.length
      }
      this.$emit('change', sms)
      this.emitValue()
    },
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler(data) {
        if (data.length === 0) {
          this.initMessages()
          return
        }
        this.$nextTick(() => {
          // 过滤掉非法短信，短信的id号不能大于短信条数限制值
          for (let i = 0; i < data.length; i++) {
            const sms = data[i]
            if (sms.msgId > this.maxSize) {
              continue
            }
            this.shortMessage[sms.msgId] = sms
          }
        })
      },
    },
  },
  beforeMount() {
    this.initMessages()
  },
}
</script>

<style lang="scss">
.smsContentForm {
  width: 100%;
  height: 100%;
  overflow: auto;

  .el-dialog.is-fullscreen & {
    max-height: unset;
  }

  .short-message-table {
    .el-table .el-form-item {
      margin-bottom: 0;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: transparent;
    }

    .index-column {
      padding: 0;
    }

    .sms-column {
      padding: 4px 0;

      .cell {
        position: relative;

        &:hover .sms-del-icon,
        .sms-del-icon:hover {
          display: block;
        }
      }

      .sms-del-icon {
        color: #f56c6c;
        display: none;
        position: absolute;
        font-size: 16px;
        right: 10px;
        top: 16px;
        transform: translate(-50%, -50%);
        cursor: pointer;
      }

      .sms-textarea {
        .el-textarea__inner {
          padding: 5px 50px 5px 10px;
        }
      }
    }
  }
}
</style>
