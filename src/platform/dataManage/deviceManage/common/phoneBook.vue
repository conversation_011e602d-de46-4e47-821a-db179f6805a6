<template>
  <section class="p-3 phone-book-section">
    <div class="phone-book-selected-info mb-1 right">
      <span v-text="selectedNodes.length || 0" />
      <span>/</span>
      <span v-text="maxSize" />
    </div>
    <TableTree
      :ref="treeId"
      :treeId="treeId"
      :option="treeOpts"
      @loaded="treeLoaded"
    />
  </section>
</template>

<script>
import { cloneDeep } from 'lodash'
import bftree from '@/utils/bftree'
import bfutil from '@/utils/bfutil'
import TableTree from '@/components/common/tableTree'

export default {
  name: 'PhoneBook',
  emits: ['select'],
  props: {
    treeId: {
      type: String,
      required: true,
    },
    maxSize: {
      type: Number,
      default: 100,
    },
    redrawTree: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      globalPhoneBooks: bfglob.gphoneBook.getAll(),
      selectedNodes: [],
      notInSystemNode: [],
    }
  },
  methods: {
    clickNode(event, data) {
      const excludeList = ['expander', 'prefix', 'checkbox']
      if (excludeList.includes(data.targetType)) {
        return true
      }
      const node = data.node
      if (!node) {
        return false
      }
      node.setActive()
      node.setSelected(!node.isSelected())
    },
    deepSelectedNodes(node, isSelected = false) {
      if (!node) {
        return
      }

      node.data.dblClickSelected = isSelected
      node.setSelected(isSelected)

      const children = node.getChildren() || []
      children.forEach(n => {
        this.deepSelectedNodes(n, isSelected)
      })
    },
    selectNodes(event, data) {
      let selectedNodes = data.tree.getSelectedNodes()
      const phoneBookRids = Object.keys(this.globalPhoneBooks).map(key => {
        return key.slice(1)
      })
      selectedNodes = selectedNodes.filter(node => {
        return phoneBookRids.includes(node.key)
      })

      this.selectedNodes = []
      for (let i = 0; i < selectedNodes.length; i++) {
        const phoneBookData = bfglob.gphoneBook.get(selectedNodes[i].key)
        this.selectedNodes.push({
          phoneId: this.selectedNodes.length,
          phoneName: phoneBookData.phoneName,
          phoneNo: phoneBookData.phoneNo,
        })
      }

      const cache = this.selectedNodes
      this.$nextTick(() => {
        if (cache !== this.selectedNodes) {
          return
        }
        this.$emit('select', this.selectedNodes)
      })
    },
    phoneBookTreeBeforeSelect(event, data) {
      if (this.selectedNodes.length >= this.maxSize) {
        return false
      }
    },
    restoreNodeSelectedStatus() {
      // 重置选中的节点数据
      const selectedNodesCache = cloneDeep(this.selectedNodes)
      for (let i = 0; i < selectedNodesCache.length; i++) {
        const book = selectedNodesCache[i]
        // 判断本地是否有该电话号码的数据
        const localPhoneBook = bfglob.gphoneBook.getDataByIndex(book.phoneNo)
        if (!localPhoneBook) {
          continue
        }

        const phoneBookNode = this.tableTree.getNodeByKey(localPhoneBook.rid)
        if (!phoneBookNode) {
          continue
        }
        // 设置为选中状态
        phoneBookNode.setSelected(true)
      }
    },
    createPhoneBookParentNodeData(parentData) {
      return {
        title: parentData.orgShortName,
        folder: true,
        expanded: true,
        key: parentData.rid,
        icon: false,
        selected: false,
        children: [],
        sortString: parentData.orgShortName,
        origin: parentData,
      }
    },
    createPhoneBookNodeTitle(phoneBook) {
      return phoneBook.phoneName + ' - ' + phoneBook.phoneNo
    },
    createPhoneBookNodeData(phoneBook) {
      return {
        title: this.createPhoneBookNodeTitle(phoneBook),
        folder: false,
        expanded: false,
        key: phoneBook.rid,
        icon: phoneBook.icon || 'iconfont icon-phone-book',
        selected: false,
        sortString: phoneBook.phoneNo,
        origin: phoneBook,
      }
    },
    loadPhoneBookTreeNode() {
      // 遍历所有的电话本，将每个电话记录和上级父节点加载到树上
      // 处理电话本节点关系数据
      const booksMap = {}
      for (const k in this.globalPhoneBooks) {
        const phoneBook = this.globalPhoneBooks[k]
        const orgId = phoneBook.orgId
        // 如果还没有生成父节点数据，则先生成父节点
        if (!booksMap[orgId]) {
          const orgData = bfglob.gorgData.get(orgId)
          if (!orgData) {
            continue
          }
          booksMap[orgId] = this.createPhoneBookParentNodeData(orgData)
        }
        booksMap[orgId].children.push(this.createPhoneBookNodeData(phoneBook))
      }

      // 处理父节点关系数据，直到父级节点为rootNode
      const children = []
      const booksMapKeys = Object.keys(booksMap)
      while (booksMapKeys.length > 0) {
        const key = booksMapKeys.shift()
        const data = booksMap[key]
        const parentOrgId = data.origin.parentOrgId

        // rootNode的直接子节点，放到children中
        if (parentOrgId === bfutil.DefOrgRid) {
          children.push(data)
          continue
        }

        // 如果当前节点的父节点已经生成，则直接设置关系
        if (booksMap[parentOrgId]) {
          booksMap[parentOrgId].children.push(data)
          continue
        }

        // booksMap中没有父节点，生成父级节点数据
        const orgData = bfglob.gorgData.get(parentOrgId)
        if (!orgData) {
          continue
        }
        const orgNodeData = this.createPhoneBookParentNodeData(orgData)
        orgNodeData.children.push(data)
        booksMap[parentOrgId] = orgNodeData

        // 向队列中添加父节点的key，以便继续处理所有父级节点
        booksMapKeys.push(orgData.rid)
      }

      if (children.length) {
        const rootNode = this.tableTree.getRootNode()
        this.tableTree.addNodeChildren(rootNode, children)
      }
      this.tableTree.sortChildren()
    },
    treeLoaded() {
      if (!this.tableTree) {
        setTimeout(() => {
          this.treeLoaded()
        }, 50)
        return
      }
      this.loadPhoneBookTreeNode()
    },
    treeReload(clearCache = false) {
      this.tableTree.clearTree()
      this.treeLoaded()
      this.$nextTick(() => {
        if (clearCache) {
          this.selectedNodes = []
          this.$emit('select', this.selectedNodes)
        }
        this.restoreNodeSelectedStatus()
      })
    },
    asyncNodeSelectStatus(books) {
      if (!Array.isArray(books)) {
        return
      }

      const notInSystemNode = []
      for (let i = 0; i < books.length; i++) {
        const book = books[i]
        // 判断本地是否有该电话号码的数据
        let localPhoneBook = bfglob.gphoneBook.getDataByIndex(book.phoneNo)
        if (!localPhoneBook) {
          // 将非本系统的电话本转换成节点数据
          // 非本系统数据，只显示且禁止选择
          this.notInSystemNode.push(book)
          const _data = {
            ...book,
            orgId: bfutil.DefOrgRid,
            rid: `${book.phoneId}-${book.phoneNo}`,
            icon: 'mdi mdi-alert-circle-outline',
          }
          localPhoneBook = {
            ..._data,
            ...this.createPhoneBookNodeData(_data),
            unselectable: true,
            unselectableStatus: undefined,
            selected: false,
            folder: false,
            otherOwner: true,
          }
          notInSystemNode.push(localPhoneBook)
          continue
        }

        const phoneBookNode = this.tableTree.getNodeByKey(localPhoneBook.rid)
        if (!phoneBookNode) {
          continue
        }
        // 设置为选中状态
        phoneBookNode.setSelected(true)
      }

      // 添加非本系统电话本的节点
      if (notInSystemNode.length > 0) {
        this.tableTree.addNodeChildren(
          this.tableTree.getRootNode(),
          notInSystemNode,
        )
      }
      this.redrawViewport()
    },
    // 删除非系统内节点数据
    removeNotInSystemNodes() {
      bftree.clearNodeUnselectable(this.treeId, null, { selected: false })

      for (let i = 0; i < this.notInSystemNode.length; i++) {
        const item = this.notInSystemNode[i]
        const key = `${item.phoneId}-${item.phoneNo}`
        const node = this.tableTree.getNodeByKey(key)
        if (!node) {
          continue
        }
        node.remove()
      }
      this.notInSystemNode = []
      this.tableTree.updateViewport()
    },

    updatePhoneBookTree() {
      this.treeReload()
      this.redrawViewport()
    },
    redrawViewport(retry = 0) {
      if (!this.tableTree) {
        setTimeout(() => {
          this.redrawViewport(retry)
        }, 50)
        return
      }

      if (retry > 10) {
        return
      }

      this.tableTree.updateViewport()
      setTimeout(() => {
        const viewport = this.tableTree.getTree().viewport
        const count = this.tableTree.getViewportCount()
        if (
          isNaN(viewport.count) ||
          isNaN(viewport.start) ||
          count !== viewport.count
        ) {
          this.redrawTree && this.redrawViewport(++retry)
        }
      }, 0)
    },
  },
  computed: {
    treeOpts() {
      return {
        selectMode: 3,
        select: this.selectNodes,
        beforeSelect: this.phoneBookTreeBeforeSelect,
        click: this.clickNode,
      }
    },
    tableTree() {
      return this.$refs[this.treeId]
    },
  },
  watch: {
    redrawTree(val) {
      if (val && this.tableTree) {
        this.redrawViewport()
      }
    },
  },
  components: {
    TableTree,
  },
  beforeMount() {
    // 监听电话簿数据变化，以同步电话簿节点
    bfglob.on('add_global_phone_no_list', this.updatePhoneBookTree)
    bfglob.on('update_global_phone_no_list', this.updatePhoneBookTree)
    bfglob.on('delete_global_phone_no_list', this.updatePhoneBookTree)
    bfglob.on('wf:redrawTree', this.redrawViewport)
  },
  beforeUnmount() {
    // Vue实例销毁前，取消订阅的一些方法主题
    bfglob.off('add_global_phone_no_list', this.updatePhoneBookTree)
    bfglob.off('update_global_phone_no_list', this.updatePhoneBookTree)
    bfglob.off('delete_global_phone_no_list', this.updatePhoneBookTree)
    bfglob.off('wf:redrawTree', this.redrawViewport)
  },
}
</script>

<style lang="scss">
.phone-book-section {
  height: 100%;
  display: flex;
  flex-direction: column;

  .phone-book-selected-info {
    flex: none;
    line-height: 22px;
  }
}
</style>
