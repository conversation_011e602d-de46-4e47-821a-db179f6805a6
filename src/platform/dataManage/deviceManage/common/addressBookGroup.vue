<template>
  <el-form
    class="address-group-form"
    :model="oneBookGroup"
    :rules="oneBookGroupRules"
    :label-width="formLabelWidth"
  >
    <!--    <el-row :gutter='20' class='no-margin-x'-->
    <!--            type='flex' align='middle'>-->
    <el-form-item :label="$t('dialog.name')" prop="name">
      <el-input
        v-model="oneBookGroup.name"
        :maxlength="16"
        @change="nameChange"
      />
    </el-form-item>
    <el-transfer
      v-model="oneBookGroup.memberList"
      class="address-group-transfer"
      :titles="titles"
      :data="availableContactList"
      :props="{
        key: 'value',
      }"
      @change="memberListChange"
    >
      <template #default="{ option }">
        <span>{{ option.label }}</span>
      </template>
    </el-transfer>
  </el-form>
</template>

<script>
import { cloneDeep } from 'lodash'
import validateRules from '@/utils/validateRules'

const AddressGroup = {
  id: 0,
  name: '',
  // 缓存所辖信道
  dmrIdList: [],
  memberList: [],
}

export default {
  name: 'AddressBookGroup',
  emits: [
    'update:modelValue',
    'initData',
    'update:alertId',
    'nameChange',
    'update:dataId',
    'update:addressBook',
  ],
  props: {
    modelValue: {
      type: Array,
      required: true,
    },
    addressBook: {
      type: Array,
      required: true,
    },
    limit: {
      type: Number,
      default: 10,
    },
    dataId: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      oneBookGroup: cloneDeep(AddressGroup),
      dataList: [],
    }
  },
  methods: {
    nameChange(val) {
      if (!val) {
        this.oneBookGroup.name = `${this.$t('writeFreq.addressBookGroup')} ${this.oneBookGroup.id + 1}`
      }
      this.$emit('nameChange', this.oneBookGroup)
    },
    nextId(usedId, limit = this.limit) {
      let id = 0
      while (id < limit) {
        if (!usedId.includes(id)) {
          return id
        }
        id++
      }
      return id
    },
    newOneBookGroup() {
      const data = cloneDeep(AddressGroup)
      const usedId = (this.dataList || []).map(item => item.id)
      const id = this.nextId(usedId)
      data.id = id
      data.name = `${this.$t('writeFreq.addressBookGroup')} ${id + 1}`
      return data
    },
    addOneBookGroup() {
      if (this.dataList.length >= this.limit) {
        return
      }
      const data = this.newOneBookGroup()
      this.dataList.push(data)
      this.oneBookGroup = data
      this.$emit('update:dataId', data.id)

      return data
    },
    initDataList() {
      this.dataList = []
      this.addOneBookGroup()
      this.$nextTick(() => {
        this.$emit('initData')
      })
    },
    updateAddressGroupId(list, oldList = []) {
      this.oneBookGroup.dmrIdList = []
      for (let i = 0; i < this.addressBook.length; i++) {
        const book = this.addressBook[i]
        if (oldList.includes(book.id)) {
          book.groupId = 0xffff
        }
        if (list.includes(book.id)) {
          book.groupId = this.oneBookGroup.id
          this.oneBookGroup.dmrIdList.push(book.dmrId)
        }
      }
      this.$emit('update:addressBook', this.addressBook)
    },
    memberListChange(list) {
      this.updateAddressGroupId(list, this.getMembersList())
    },
    getMembersList() {
      const list = []
      for (let i = 0; i < this.oneBookGroup.dmrIdList.length; i++) {
        const dmrId = this.oneBookGroup.dmrIdList[i]
        const id = this.contactIdIndex[dmrId]
        if (typeof id !== 'undefined') {
          list.push(id)
        }
      }
      return list
    },
  },
  watch: {
    modelValue: {
      deep: true,
      handler(val) {
        this.dataList = val
      },
    },
    dataList: {
      deep: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
    dataId(val) {
      const data = this.dataList.filter(item => item.id === val)[0]
      if (data) {
        this.oneBookGroup = data
        this.oneBookGroup.memberList = this.getMembersList()
      }
    },
    addressBook: {
      deep: true,
      handler() {
        // 通讯录变化时，同步所有群组中已经选择的通讯录
        // 将群组中已经不存在于通讯录的数据从群组成员列表中删除
        for (let i = 0; i < this.dataList.length; i++) {
          const data = this.dataList[i]
          data.dmrIdList = data.dmrIdList.filter(dmrId => {
            return typeof this.contactIdIndex[dmrId] !== 'undefined'
          })
          data.memberList = data.dmrIdList.map(
            dmrId => this.contactIdIndex[dmrId],
          )
        }
      },
    },
  },
  computed: {
    oneBookGroupRules() {
      return {
        name: [validateRules.required(['blur'])],
      }
    },
    availableContactList() {
      // 通讯录候选列表不能有其他群组已选中的数据
      return this.addressBook
        .filter(item => {
          return (
            item.groupId === 0xffff || item.groupId === this.oneBookGroup.id
          )
        })
        .map(item => {
          return { label: item.name, value: item.id }
        })
    },
    contactIdIndex() {
      return this.addressBook
        .map(item => {
          return { [item.dmrId]: item.id }
        })
        .reduce((p, c) => {
          return Object.assign(p, c)
        }, {})
    },
    titles() {
      return [
        this.$t('writeFreq.addressGroup.ungroupedContact'),
        this.$t('writeFreq.addressGroup.selectedContact'),
      ]
    },
    isMobile() {
      return this.$root.layoutLevel === 0
    },
    formLabelWidth() {
      return '70px'
    },
  },
  beforeMount() {
    this.initDataList()
  },
}
</script>

<style lang="scss">
.address-group-form {
  .el-row.el-row--flex {
    height: 100%;
    align-content: flex-start;

    .el-col.transfer-wrapper {
      height: calc(100% - 42px);
      overflow: auto;
    }
  }

  .address-group-transfer {
    width: 100%;
    height: calc(100% - 62px);
    display: flex;

    .el-transfer-panel {
      flex: auto;
      width: auto;

      .el-transfer-panel__item {
        $height: 24px;

        display: block;
        height: $height;
        line-height: $height;
        padding: 0 10px;
        margin-right: 0;

        .el-checkbox__label {
          line-height: $height;
        }
      }

      .el-transfer-panel__body {
        height: calc(100% - 40px);

        .el-transfer-panel__list {
          height: 100%;
        }
      }
    }

    .el-transfer__buttons {
      display: flex;
      flex-direction: column;
      gap: calc(var(--spacing) * 3);
      align-self: center;

      .el-button {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 0;
      }
    }
  }
}
</style>
