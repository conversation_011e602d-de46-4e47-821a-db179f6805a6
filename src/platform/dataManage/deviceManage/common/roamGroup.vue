<template>
  <el-form
    class="roam-form"
    :model="oneRoamGroup"
    :rules="oneRoamGroupRules"
    label-position="top"
  >
    <div :class="['transfer-wrapper', locale]">
      <el-transfer
        v-model="oneRoamGroup.roamChList"
        :titles="titles"
        :data="availableChannelList"
        :props="{
          key: 'value',
        }"
        @change="memberListChange"
      >
        <template #default="{ option }">
          <span>{{ option.label }}</span>
        </template>
      </el-transfer>
    </div>
    <div class="w-full flex justify-center">
      <div class="roma-form-item grid grid-cols-1">
        <el-form-item v-if="!isTD811">
          <el-checkbox v-model="oneRoamGroup.config.mainSiteRoaming">
            <span v-text="$t('writeFreq.activeSiteRoamingEnable')" />
          </el-checkbox>
        </el-form-item>
        <el-form-item :label="$t('dialog.name')" prop="name">
          <el-input
            v-model="oneRoamGroup.name"
            :maxlength="16"
            @change="nameChange"
          />
        </el-form-item>
        <el-form-item v-if="!isTD811" :label="$t('writeFreq.siteSearchTimer')">
          <el-input-number
            v-model="oneRoamGroup.siteSearchTimer"
            step-strictly
            :min="0"
            :max="255"
          />
        </el-form-item>
        <el-form-item
          v-if="autoSiteSearchTimer"
          :label="$t('writeFreq.autoSiteSearchTimer')"
        >
          <el-input-number
            v-model="oneRoamGroup.autoSearchTimer"
            step-strictly
            :min="1"
            :max="300"
          />
        </el-form-item>
        <el-form-item v-if="!isTD811" :label="$t('dialog.rssiThreshold')">
          <el-input-number
            v-model="oneRoamGroup.rssi"
            step-strictly
            :min="-120"
            :max="-80"
          />
        </el-form-item>
        <el-form-item v-if="isTD811" :label="$t('dialog.rssiThreshold')">
          <bf-input-number
            v-model="oneRoamGroup.rssi"
            step-strictly
            :min="0"
            :max="40"
            :formatter="rssiFormat"
            @cus-change="rssiChange"
          />
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script>
import { cloneDeep } from 'lodash'
import validateRules from '@/utils/validateRules'
import { defineAsyncComponent } from 'vue'

const BaseConfig = {
  roamId: 0,
  name: '',
  rssi: -120,
  roamChList: [0xfffe],
  roamChCount: 1,
}
const OneRoamGroup = {
  ...BaseConfig,
  config: {
    mainSiteRoaming: false,
    followMainSite: false,
    channelAsScanList: false,
  },
  siteSearchTimer: 60,
  autoSearchTimer: 60,
}
const TD811RoamGroup = {
  ...BaseConfig,
}
const ModelList = ['TD081100', '825SDC00', '930SDC00']

export default {
  name: 'RoamGroup',
  emits: ['update:modelValue', 'initData', 'update:dataId', 'nameChange'],
  props: {
    modelValue: {
      type: Array,
      required: true,
    },
    channels: {
      type: Array,
      required: true,
    },
    limit: {
      type: Number,
      default: 32,
    },
    dataId: {
      type: Number,
      default: -1,
    },
    directMode: {
      type: Boolean,
      default: false,
    },
    currChannel: {
      type: Object,
      default() {
        return {
          scanConfig: {
            ipSiteConnect: false,
          },
        }
      },
    },
    model: {
      type: String,
      default: 'TD081000',
    },
    isFullscreen: {
      type: Boolean,
      default: false,
    },
    autoSiteSearchTimer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      oneRoamGroup: this.getOneRoamGroup(),
      dataList: [],
    }
  },
  methods: {
    // TD081100 RSSI阀值(dBm) 0~40 信号强度阈值是用户设置接收信号强度的门限。对应显示范围 -120~-80
    rssiFormat(val) {
      return val - 120
    },
    rssiChange(val) {
      this.oneRoamGroup.rssi = parseInt(val) + 120
    },
    getOneRoamGroup() {
      if (ModelList.includes(this.model)) {
        return cloneDeep(TD811RoamGroup)
      }
      if (this.model === 'TD920') {
        OneRoamGroup.siteSearchTimer = 0
      }
      return cloneDeep(OneRoamGroup)
    },
    nameChange(val) {
      if (!val) {
        this.oneRoamGroup.name = `${this.roamGroupName} ${this.oneRoamGroup.roamId + 1}`
      }
      this.$emit('nameChange', this.oneRoamGroup)
    },
    nextId(usedId, limit = this.limit) {
      let id = 0
      while (id < limit) {
        if (!usedId.includes(id)) {
          return id
        }
        id++
      }
      return id
    },
    newOneRoamGroup() {
      const data = this.getOneRoamGroup()
      const usedId = (this.dataList || []).map(item => item.roamId)
      const id = this.nextId(usedId)
      data.roamId = id
      data.name = `${this.roamGroupName} ${id + 1}`
      return data
    },
    addOneRoamGroup() {
      if (this.dataList.length >= this.limit) {
        return
      }
      const data = this.newOneRoamGroup()
      this.dataList.push(data)
      this.oneRoamGroup = data
      this.$emit('update:dataId', data.roamId)

      return data
    },
    initDataList() {
      this.dataList = []
      this.addOneRoamGroup()
      this.$nextTick(() => {
        this.$emit('initData')
      })
    },
    memberListChange(list) {},
  },
  watch: {
    modelValue: {
      deep: true,
      handler(val) {
        this.dataList = val
      },
    },
    dataList: {
      deep: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
    dataId(val) {
      const data = this.dataList.find(item => item.roamId === val)
      if (data) {
        this.oneRoamGroup = data
      }
    },
    // directMode(val) {
    //   // 常规设置开启了直通模式，不能配置漫游组，取消所有漫游组已配置的信道数据
    //   if (val) {
    //     for (let i = 0; i < this.dataList.length; i++) {
    //       const data = this.dataList[i]
    //       data.roamChList = [0xFFFE]
    //       data.roamChCount = 1
    //     }
    //   }
    // },
    ipSiteConnect(val) {
      if (val) {
        return
      }
      // IP站点连接取消时，需要重置漫游组已选信道列表，没有开启IP站点连接的信道，不能被漫游组选中
      for (let i = 0; i < this.dataList.length; i++) {
        const data = this.dataList[i]
        data.roamChList = data.roamChList.filter(chId => {
          return this.currChannel.chId !== chId
        })
        data.roamChCount = data.roamChList.length
      }
    },
    'oneRoamGroup.roamChList': {
      deep: true,
      handler(val) {
        this.oneRoamGroup.roamChCount = val.length
      },
    },
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    roamGroupName() {
      return ModelList.includes(this.model)
        ? this.$t('writeFreq.roamList')
        : this.$t('writeFreq.roamingGroup')
    },
    oneRoamGroupRules() {
      return {
        name: [validateRules.required(['blur'])],
      }
    },
    titles() {
      return [
        this.$t('writeFreq.availableChannel'),
        this.$t('writeFreq.containedChannel'),
      ]
    },
    responseColumn() {
      return this.isFullscreen ? 12 : 24
    },
    nothingList() {
      return [{ label: this.$t('dialog.nothing'), value: 0xffff }]
    },
    theSelectedList() {
      return [{ label: this.$t('writeFreq.theSelected'), value: 0xfffe }]
    },
    lastActiveChannelList() {
      return [{ label: this.$t('writeFreq.lastActiveChannel'), value: 0xfffd }]
    },
    ipSiteConnect() {
      return ModelList.includes(this.model)
        ? this.currChannel.callingSettings.ipSiteConnect
        : this.currChannel.scanConfig.ipSiteConnect
    },
    channelList() {
      const filter = ModelList.includes(this.model)
        ? channel => channel.callingSettings.ipSiteConnect
        : channel => channel.scanConfig.ipSiteConnect
      return this.channels.filter(filter).map(channel => {
        return { label: channel.chName, value: channel.chId }
      })
    },
    channelsIndex() {
      if (!Array.isArray(this.channels)) {
        return {}
      }

      return this.channels
        .map(data => {
          const prop = data.chId
          return { [prop]: data }
        })
        .reduce((p, c) => {
          return Object.assign(p, c)
        }, {})
    },
    availableChannelList() {
      return [
        ...this.theSelectedList.map(item => {
          item.disabled = true
          return item
        }),
        ...this.channelList,
      ]
    },
    isTD811() {
      return ModelList.includes(this.model)
    },
  },
  beforeMount() {
    this.initDataList()
  },
  components: {
    bfInputNumber: defineAsyncComponent(
      () => import('@/components/common/bfInputNumber'),
    ),
  },
}
</script>

<style lang="scss">
@use '@/css/interphoneWf/scan-roam-group.scss' as *;
</style>
