<template>
  <el-table
    :data="preSetChannel"
    :empty-text="$t('msgbox.emptyText')"
    class="table-no-bg"
  >
    <el-table-column label="">
      <template #default="scope">
        <span v-text="$t(`writeFreq.defaultChannel.ch${scope.$index + 1}`)" />
      </template>
    </el-table-column>
    <el-table-column :label="$t('writeFreq.zone')">
      <template #default="scope">
        <el-form-item label-width="0">
          <el-select
            v-model="scope.row.zone"
            :placeholder="$t('dialog.select')"
            filterable
            :no-match-text="$t('dialog.noMatchText')"
            @change="preSetChannelZoneChanged(scope.row)"
          >
            <el-option
              v-for="item in zoneList"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </template>
    </el-table-column>
    <el-table-column :label="$t('dialog.channel')">
      <template #default="scope">
        <el-form-item label-width="0">
          <el-select
            v-model="scope.row.channel"
            :placeholder="$t('dialog.select')"
            filterable
            :disabled="scope.row.zone === 0xff"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="item in digitalChannelList(scope.row)"
              :key="item.label"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'PreSetChannel',
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: Array,
      default() {
        return []
      },
    },
    channels: {
      type: Array,
      default() {
        return []
      },
    },
    zones: {
      type: Array,
      default() {
        return []
      },
    },
    zoneIndex: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      preSetChannel: [],
    }
  },
  methods: {
    preSetChannelZoneChanged(row) {
      if (row.zone === 0xff) {
        row.channel = 0xffff
        return
      }

      // 选择区域后，自动选择信道候选列表第一个信道
      const channelList = this.digitalChannelList(row)
      if (!channelList || !channelList.length) {
        row.zone = 0xff
        return
      }
      row.channel = channelList[0].value
    },
    digitalChannelList(row) {
      if (row.zone === 0xff) {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
      }

      const zoneData = this.zoneIndex[row.zone] || {}
      const channelIds = zoneData.list || []

      return this.channels
        .filter(channel => {
          return channelIds.includes(channel.chId)
        })
        .map(data => {
          return {
            label: data.chName,
            value: data.chId,
          }
        })
    },
  },
  computed: {
    zoneList() {
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0xff,
        },
      ].concat(
        this.zones.map(data => {
          return {
            label: data.name,
            value: data.zoneId,
          }
        }),
      )
    },
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler(val) {
        this.preSetChannel = val
      },
    },
    preSetChannel: {
      deep: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
  },
}
</script>
