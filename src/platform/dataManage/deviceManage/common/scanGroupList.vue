<template>
  <el-form
    class="scan-form"
    :model="oneScanGroup"
    :rules="oneScanGroupRules"
    label-position="top"
  >
    <el-row
      :gutter="20"
      class="no-margin-x flex justify-center"
      type="flex"
      align="middle"
    >
      <el-col :xs="24" :class="['transfer-wrapper', locale]">
        <el-transfer
          v-model="oneScanGroup.membersList"
          :titles="titles"
          :data="availableChannelList"
          :props="{
            key: 'value',
          }"
          @change="membersListChange"
        >
          <template #default="{ option }">
            <span>{{ option.label }}</span>
          </template>
        </el-transfer>
      </el-col>
      <div class="scan-group-form-item">
        <el-col v-if="hasReply" :xs="24" :sm="responseColumn">
          <el-form-item>
            <el-checkbox v-model="oneScanGroup.reply">
              <span v-text="$t('dialog.answer')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col v-if="hasScanHangTime" :xs="24" :sm="responseColumn">
          <el-form-item :label="$t('dialog.hangTime')">
            <el-input-number
              v-model="oneScanGroup.scanHangTime"
              :min="500"
              :max="10000"
              :step="500"
              step-strictly
            />
          </el-form-item>
        </el-col>
        <el-col v-if="!isTD811 && hasReply" :xs="24" :sm="responseColumn">
          <el-form-item>
            <el-checkbox v-model="oneScanGroup.config.reply">
              <span v-text="$t('dialog.answer')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="responseColumn">
          <el-form-item :label="$t('dialog.name')" prop="name">
            <el-input
              v-model="oneScanGroup.name"
              :maxlength="16"
              @change="nameChange"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="responseColumn">
          <el-form-item :label="$t('writeFreq.firstPriorityChannel')">
            <el-select
              v-model="oneScanGroup.priority1Ch"
              :placeholder="$t('dialog.select')"
              filterable
              :no-match-text="$t('dialog.noMatchText')"
            >
              <el-option
                v-for="(item, i) in priority1ChList"
                :key="i"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="responseColumn">
          <el-form-item :label="$t('writeFreq.secondPriorityChannel')">
            <el-select
              v-model="oneScanGroup.priority2Ch"
              :placeholder="$t('dialog.select')"
              filterable
              :disabled="noPriority1Ch"
              :no-match-text="$t('dialog.noMatchText')"
            >
              <el-option
                v-for="(item, i) in priority2ChList"
                :key="i"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="responseColumn">
          <el-form-item :label="$t('writeFreq.specifyTransmitChannel')">
            <el-select
              v-model="oneScanGroup.appointTxCh"
              :placeholder="$t('dialog.select')"
              filterable
              :no-match-text="$t('dialog.noMatchText')"
            >
              <el-option
                v-for="(item, i) in appointTxChList"
                :key="i"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="hasStayTime" :xs="24" :sm="responseColumn">
          <el-form-item :label="$t('writeFreq.residenceTime')">
            <el-input-number
              v-model="oneScanGroup.stayTime"
              step-strictly
              :min="0.5"
              :max="10"
              :step="0.5"
            />
          </el-form-item>
        </el-col>
        <el-col v-if="hasStayTime" :xs="24" :sm="responseColumn">
          <el-form-item :label="$t('writeFreq.scanningEmissionMode')">
            <el-select
              v-model="oneScanGroup.config.scanTxMode"
              :placeholder="$t('dialog.select')"
              filterable
              :no-match-text="$t('dialog.noMatchText')"
            >
              <el-option
                v-for="(item, i) in scanTxModeList"
                :key="i"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </div>
    </el-row>
  </el-form>
</template>

<script>
import { messageBox, Types } from '@/utils/notify'
import validateRules from '@/utils/validateRules'
import {
  TD930SDCR7F,
  TD930SVTR7F,
  TM8250SDCR7F,
  TM825FRModel,
  TM8250SVTR7F,
} from '@/writingFrequency/interphone/models'

// const BaseConfig = {
//   scanId: 0,
//   name: '',
//   membersList: [0xFFFE],
//   memberCount: 1,
//   priority1Ch: 0xFFFF,
//   priority2Ch: 0xFFFF,
//   appointTxCh: 0xFFFE,
// }
// const OneScanGroup = {
//   ...BaseConfig,
//   priSampleTime: 0,
//   stayTime: 3,
//   config: {
//     subScanMode: 3,
//     reply: true,
//     scanTxMode: 0,
//   },
// }
// const TD811ScanGroup = {
//   ...BaseConfig,
//   scanHangTime: 4000,
//   reply: true,
// }
const ModelList = [
  'TD081100',
  '825SDC00',
  TM8250SDCR7F,
  TM825FRModel,
  '930SDC00',
  '930SVT00',
  TD930SVTR7F,
  TD930SDCR7F,
  'R935SDC0',
  'R935SVT0',
  'D860SD00',
  'D860SV00',
  TM8250SVTR7F,
]
const SvtChannelsNotInScanListModel = ['D860SV00']

export default {
  name: 'ScanGroupList',
  emits: ['update:modelValue', 'nameChange'],
  props: {
    modelValue: {
      type: Object,
      required: true,
      validator(v) {
        return Object.prototype.toString.call(v).slice(8, -1) === 'Object'
      },
    },
    channels: {
      type: Array,
      required: true,
    },
    model: {
      type: String,
      default: 'TD081000',
    },
    maxCount: {
      type: Number,
      default: 16,
    },
    isFullscreen: {
      type: Boolean,
      default: false,
    },

    // 控制器各机型不同的数据表单加载
    hasReply: {
      type: Boolean,
      default: true,
    },
    hasScanHangTime: {
      type: Boolean,
      default: true,
    },
    hasStayTime: {
      type: Boolean,
      default: false,
    },
    hasScanTxMode: {
      type: Boolean,
      default: false,
    },
    // 过滤svt信道，部分机型svt信道不可被扫描，将该机型机型码添加到SvtChannelsNotInScanListModel，并在此传递过滤svt信道的方法
    filterSvtChannels: {
      type: Function,
    },
  },
  data() {
    return {
      oneScanGroup: {},
    }
  },
  methods: {
    // 3个参数，当前选中列表，穿梭方向，穿梭数据的key的集合
    membersListChange(list) {
      // 设置选中的信道时，最大为16个
      if (list.length > this.maxCount) {
        this.oneScanGroup.membersList = this.oneScanGroup.membersList.slice(
          0,
          this.maxCount,
        )
        messageBox(this.$t('writeFreq.fullList'), Types.warning)
      }
    },
    nameChange(val) {
      if (!val) {
        this.oneScanGroup.name = `${this.scanGroupName} ${this.oneScanGroup.scanId + 1}`
      }
      this.$emit('nameChange', this.oneScanGroup)
    },
    priSampleTimeFormat(val) {
      if (val === 0) {
        return this.$t('dialog.off')
      }
      return val
    },
  },
  watch: {
    modelValue: {
      deep: true,
      immediate: true,
      handler(val) {
        this.oneScanGroup = val
      },
    },
    oneScanGroup: {
      deep: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
    'oneScanGroup.priority1Ch'(val) {
      if (val === 0xffff || val === this.oneScanGroup.priority2Ch) {
        this.oneScanGroup.priority2Ch = 0xffff
      }
    },
    'oneScanGroup.priSampleTime'(val, oldVal) {
      if (val < oldVal && val < 500) {
        this.oneScanGroup.priSampleTime = 0
      }
      if (val > oldVal && oldVal === 0) {
        this.oneScanGroup.priSampleTime = 500
      }
    },
    'oneScanGroup.membersList': {
      deep: true,
      handler(val) {
        this.oneScanGroup.memberCount = val?.length ?? 1
      },
    },
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    scanGroupName() {
      return this.model === 'TD081100'
        ? this.$t('writeFreq.scanList')
        : this.$t('writeFreq.scanningGroup')
    },
    oneScanGroupRules() {
      return {
        name: [validateRules.required(['blur'])],
      }
    },
    titles() {
      return [
        this.$t('writeFreq.availableChannel'),
        this.$t('writeFreq.containedChannel'),
      ]
    },
    responseColumn() {
      return this.isFullscreen ? 12 : 24
    },
    nothingList() {
      return [
        {
          label: this.$t('dialog.nothing'),
          value: 0xffff,
        },
      ]
    },
    theSelectedList() {
      return [
        {
          label: this.$t('writeFreq.theSelected'),
          value: 0xfffe,
        },
      ]
    },
    lastActiveChannelList() {
      return [
        {
          label: this.$t('writeFreq.lastActiveChannel'),
          value: 0xfffd,
        },
      ]
    },
    channelList() {
      let channels = this.channels
      if (SvtChannelsNotInScanListModel.includes(this.model)) {
        channels = this.filterSvtChannels(this.channels)
      }
      return channels.map(channel => {
        return {
          label: channel.chName,
          value: channel.chId,
        }
      })
    },
    availableChannelList() {
      return [
        ...this.theSelectedList.map(item => {
          item.disabled = true
          return item
        }),
        ...this.channelList,
      ]
    },
    selectedChannelList() {
      return this.channelList.filter(item =>
        this.oneScanGroup.membersList?.includes?.(item.value),
      )
    },
    priority1ChList() {
      // 如果第二优先信道已经选中的信道，则不可选
      return this.nothingList.concat(
        [...this.theSelectedList, ...this.selectedChannelList].filter(item => {
          return item.value !== this.oneScanGroup.priority2Ch
        }),
      )
    },
    noPriority1Ch() {
      return this.oneScanGroup.priority1Ch === 0xffff
    },
    priority2ChList() {
      // 如果第一优先信道已经选中的信道，则不可选
      return this.nothingList.concat(
        [...this.theSelectedList, ...this.selectedChannelList].filter(item => {
          return item.value !== this.oneScanGroup.priority1Ch
        }),
      )
    },
    appointTxChList() {
      return [
        ...this.lastActiveChannelList,
        ...this.theSelectedList,
        ...this.channelList,
      ]
    },
    subScanModeList() {
      // 0 不检测亚音 1 非优先信道检测 2 优先信道检测 3 所有信道检测(默认值)
      return [
        {
          label: this.$t('writeFreq.subtoneNotDetected'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.nonPriorityChannelDetection'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.priorityChannelDetection'),
          value: 2,
        },
        {
          label: this.$t('writeFreq.allChannelDetection'),
          value: 3,
        },
      ]
    },
    scanTxModeList() {
      // 扫描发射模式	0 当前信道(默认值) 1 最后活动信道 2 指定信道
      return [
        {
          label: this.$t('dialog.currentChannel'),
          value: 0,
        },
        {
          label: this.$t('writeFreq.lastActivityChannel'),
          value: 1,
        },
        {
          label: this.$t('writeFreq.specifiedChannel'),
          value: 2,
        },
      ]
    },
    isTD811() {
      return ModelList.includes(this.model)
    },
  },
}
</script>

<style lang="scss">
@use '@/css/interphoneWf/scan-roam-group.scss' as *;
</style>
