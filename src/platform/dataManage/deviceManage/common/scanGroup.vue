<template>
  <el-form
    class="scan-form"
    :model="oneScanGroup"
    :rules="oneScanGroupRules"
    label-position="top"
  >
    <div :class="['transfer-wrapper', locale]">
      <el-transfer
        v-model="oneScanGroup.membersList"
        :titles="titles"
        :data="availableChannelList"
        :props="{
          key: 'value',
        }"
        @change="memberListChange"
      >
        <template #default="{ option }">
          <span>{{ option.label }}</span>
        </template>
      </el-transfer>
    </div>
    <div class="w-full flex justify-center">
      <div class="scan-group-form-item grid grid-cols-1">
        <el-form-item v-if="isTD811">
          <el-checkbox v-model="oneScanGroup.reply">
            <span v-text="$t('dialog.answer')" />
          </el-checkbox>
        </el-form-item>
        <el-form-item v-if="isTD811" :label="$t('dialog.hangTime')">
          <el-input-number
            v-model="oneScanGroup.scanHangTime"
            step-strictly
            :min="500"
            :max="10000"
            :step="500"
          />
        </el-form-item>
        <el-form-item v-if="!isTD811">
          <el-checkbox v-model="oneScanGroup.config.reply">
            <span v-text="$t('dialog.answer')" />
          </el-checkbox>
        </el-form-item>
        <el-form-item :label="$t('dialog.name')" prop="name">
          <el-input
            v-model="oneScanGroup.name"
            :maxlength="16"
            @change="nameChange"
          />
        </el-form-item>
        <el-form-item :label="$t('writeFreq.firstPriorityChannel')">
          <el-select
            v-model="oneScanGroup.priority1Ch"
            :placeholder="$t('dialog.select')"
            filterable
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in priority1ChList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('writeFreq.secondPriorityChannel')">
          <el-select
            v-model="oneScanGroup.priority2Ch"
            :placeholder="$t('dialog.select')"
            filterable
            :disabled="noPriority1Ch"
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in priority2ChList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('writeFreq.specifyTransmitChannel')">
          <el-select
            v-model="oneScanGroup.appointTxCh"
            :disabled="oneScanGroup.config.scanTxMode !== 2 && isTD920"
            :placeholder="$t('dialog.select')"
            filterable
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in appointTxChList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!isTD811" :label="$t('writeFreq.residenceTime')">
          <el-input-number
            v-model="oneScanGroup.stayTime"
            step-strictly
            :min="0.5"
            :max="10"
            :step="0.5"
          />
        </el-form-item>
        <el-form-item
          v-if="!isTD811"
          :label="$t('writeFreq.scanningEmissionMode')"
        >
          <el-select
            v-model="oneScanGroup.config.scanTxMode"
            :placeholder="$t('dialog.select')"
            filterable
            :no-match-text="$t('dialog.noMatchText')"
          >
            <el-option
              v-for="(item, i) in scanTxModeList"
              :key="i"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script>
import { cloneDeep } from 'lodash'
import { messageBox, Types } from '@/utils/notify'
import validateRules from '@/utils/validateRules'

const BaseConfig = {
  scanId: 0,
  name: '',
  membersList: [0xfffe],
  memberCount: 1,
  priority1Ch: 0xffff,
  priority2Ch: 0xffff,
  appointTxCh: 0xfffe,
}
const OneScanGroup = {
  ...BaseConfig,
  priSampleTime: 0,
  stayTime: 3,
  config: {
    subScanMode: 3,
    reply: true,
    scanTxMode: 0,
  },
}
const TD811ScanGroup = {
  ...BaseConfig,
  scanHangTime: 4000,
  reply: true,
}
const ModelList = ['TD081100', '825SDC00', '930SDC00']

export default {
  name: 'ScanGroup',
  emits: ['update:modelValue', 'initData', 'update:dataId', 'nameChange'],
  props: {
    modelValue: {
      type: Array,
      required: true,
    },
    channels: {
      type: Array,
      required: true,
    },
    limit: {
      type: Number,
      default: 32,
    },
    maxCount: {
      type: Number,
      default: 16,
    },
    dataId: {
      type: Number,
      default: -1,
    },
    model: {
      type: String,
      default: 'TD081000',
    },
    isFullscreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      oneScanGroup: this.getOneScanGroup(),
      dataList: [],
    }
  },
  methods: {
    getOneScanGroup() {
      if (ModelList.includes(this.model)) {
        return cloneDeep(TD811ScanGroup)
      }

      return cloneDeep(OneScanGroup)
    },
    nameChange(val) {
      if (!val) {
        this.oneScanGroup.name = `${this.scanGroupName} ${this.oneScanGroup.scanId + 1}`
      }
      this.$emit('nameChange', this.oneScanGroup)
    },
    nextId(usedId, limit = this.limit) {
      let id = 0
      while (id < limit) {
        if (!usedId.includes(id)) {
          return id
        }
        id++
      }
      return id
    },
    newOneScanGroup() {
      const data = this.getOneScanGroup()
      const usedId = (this.dataList || []).map(item => item.scanId)
      const id = this.nextId(usedId)
      data.scanId = id
      data.name = `${this.scanGroupName} ${id + 1}`
      return data
    },
    addOneScanGroup() {
      if (this.dataList.length >= this.limit) {
        return
      }
      const data = this.newOneScanGroup()
      this.dataList.push(data)
      this.oneScanGroup = data
      this.$emit('update:dataId', data.scanId)

      return data
    },
    initDataList() {
      this.dataList = []
      this.addOneScanGroup()
      this.$nextTick(() => {
        this.$emit('initData')
      })
    },
    priSampleTimeFormat(val) {
      if (val === 0) {
        return this.$t('dialog.off')
      }
      return val
    },
    memberListChange(list) {
      if (list.length > this.maxCount) {
        this.oneScanGroup.membersList = this.oneScanGroup.membersList.slice(
          0,
          this.maxCount,
        )
        messageBox(this.$t('writeFreq.fullList'), Types.warning)
      }
    },
  },
  watch: {
    modelValue: {
      deep: true,
      handler(val) {
        this.dataList = val
      },
    },
    dataList: {
      deep: true,
      handler(val) {
        this.$emit('update:modelValue', val)
      },
    },
    dataId(val) {
      const data = this.dataList.find(item => item.scanId === val)
      if (data) {
        this.oneScanGroup = data
      }
    },
    'oneScanGroup.priority1Ch'(val) {
      if (val === 0xffff || val === this.oneScanGroup.priority2Ch) {
        this.oneScanGroup.priority2Ch = 0xffff
      }
    },
    'oneScanGroup.priSampleTime'(val, oldVal) {
      if (val < oldVal && val < 500) {
        this.oneScanGroup.priSampleTime = 0
      }
      if (val > oldVal && oldVal === 0) {
        this.oneScanGroup.priSampleTime = 500
      }
    },
    'oneScanGroup.membersList': {
      deep: true,
      handler(val) {
        this.oneScanGroup.memberCount = val.length
      },
    },
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    scanGroupName() {
      return this.model === 'TD081100'
        ? this.$t('writeFreq.scanList')
        : this.$t('writeFreq.scanningGroup')
    },
    oneScanGroupRules() {
      return {
        name: [validateRules.required(['blur'])],
      }
    },
    titles() {
      return [
        this.$t('writeFreq.availableChannel'),
        this.$t('writeFreq.containedChannel'),
      ]
    },
    responseColumn() {
      return this.isFullscreen ? 12 : 24
    },
    nothingList() {
      return [{ label: this.$t('dialog.nothing'), value: 0xffff }]
    },
    theSelectedList() {
      return [{ label: this.$t('writeFreq.theSelected'), value: 0xfffe }]
    },
    lastActiveChannelList() {
      return [{ label: this.$t('writeFreq.lastActiveChannel'), value: 0xfffd }]
    },
    channelList() {
      return this.channels.map(channel => {
        return { label: channel.chName, value: channel.chId }
      })
    },
    availableChannelList() {
      return [
        ...this.theSelectedList.map(item => {
          item.disabled = true
          return item
        }),
        ...this.channelList,
      ]
    },
    selectedChannelList() {
      this.oneScanGroup.memberCount
      return this.channelList.filter(item =>
        this.oneScanGroup.membersList.includes(item.value),
      )
    },
    priority1ChList() {
      // 如果第二优先信道已经选中的信道，则不可选
      return this.nothingList.concat(
        [...this.theSelectedList, ...this.selectedChannelList].filter(item => {
          return item.value !== this.oneScanGroup.priority2Ch
        }),
      )
    },
    noPriority1Ch() {
      return this.oneScanGroup.priority1Ch === 0xffff
    },
    priority2ChList() {
      // 如果第一优先信道已经选中的信道，则不可选
      return this.nothingList.concat(
        [...this.theSelectedList, ...this.selectedChannelList].filter(item => {
          return item.value !== this.oneScanGroup.priority1Ch
        }),
      )
    },
    appointTxChList() {
      return [
        ...this.lastActiveChannelList,
        ...this.theSelectedList,
        ...this.channelList,
      ]
    },
    subScanModeList() {
      // 0 不检测亚音 1 非优先信道检测 2 优先信道检测 3 所有信道检测(默认值)
      return [
        { label: this.$t('writeFreq.subtoneNotDetected'), value: 0 },
        { label: this.$t('writeFreq.nonPriorityChannelDetection'), value: 1 },
        { label: this.$t('writeFreq.priorityChannelDetection'), value: 2 },
        { label: this.$t('writeFreq.allChannelDetection'), value: 3 },
      ]
    },
    scanTxModeList() {
      // 扫描发射模式	0 当前信道(默认值) 1 最后活动信道 2 指定信道
      return [
        { label: this.$t('dialog.currentChannel'), value: 0 },
        { label: this.$t('writeFreq.lastActivityChannel'), value: 1 },
        { label: this.$t('writeFreq.specifiedChannel'), value: 2 },
      ]
    },
    isTD811() {
      return ModelList.includes(this.model)
    },
    isTD920() {
      return this.model === 'TD920'
    },
  },
  beforeMount() {
    this.initDataList()
  },
}
</script>

<style lang="scss">
@use '@/css/interphoneWf/scan-roam-group.scss' as *;
</style>
