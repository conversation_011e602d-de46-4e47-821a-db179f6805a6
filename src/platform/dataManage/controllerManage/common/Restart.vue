<template>
  <el-form ref="restartRepeater" label-width="0">
    <el-form-item v-if="processRestart" class="center actions">
      <el-button
        type="primary"
        :disabled="disabled"
        @click="repeaterProcessRestart"
        v-text="$t('dialog.restartRepeater')"
      />
    </el-form-item>
    <el-form-item class="center actions">
      <el-button
        type="warning"
        :disabled="disabled"
        @click="repeaterRepowerOnAndRestart"
        v-text="$t('dialog.repowerOnAndRestart')"
      />
    </el-form-item>
  </el-form>
</template>

<script>
import repeaterWfMod, { DefModel } from '@/writingFrequency/repeater'
import bfnotify from '@/utils/notify'
import bfutil from '@/utils/bfutil'
import { merge } from 'lodash'
import { kcpPackageName } from '@/modules/protocol'

export default {
  name: 'BfRestart',
  props: {
    repeaterData: {
      type: [Object, undefined],
    },
    repeater: {
      type: String,
      default: '',
    },
    getRepeaterId: {
      type: Function,
      default: bfutil.noop,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    packageName: {
      type: String,
      default: kcpPackageName,
    },
    deviceModel: {
      type: String,
      default: DefModel,
    },
    saveMethod: {
      type: Function,
      default: bfutil.noop,
    },
    operation: {
      type: Number,
      default: 8,
    },
    processRestart: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    repeaterRestart(options) {
      repeaterWfMod
        .writeInData(null, options)
        .then(res => {
          // 重启中继，需要标记中继下线
          // eslint-disable-next-line
          this.repeaterData.ctrlStats = 0
          // 提示发送指令成功消息
          bfnotify.messageBox(this.$t('msgbox.sendSuccess'), 'success')
        })
        .catch(err => {
          bfglob.console.error('repeaterRestart', err)
        })
    },
    repeaterProcessRestart() {
      const options = merge(this.defQueryOption, {
        paraBin: {
          operation: 8,
        },
      })

      this.repeaterRestart(options)
    },
    repeaterRepowerOnAndRestart() {
      const options = merge(this.defQueryOption, {
        paraBin: {
          operation: 9,
        },
      })

      this.repeaterRestart(options)
    },
  },
  computed: {
    defQueryOption() {
      return {
        paraBin: {
          operation: this.operation,
        },
        sid: this.repeater,
        paraInt: this.getRepeaterId(),
      }
    },
  },
}
</script>

<style></style>
