<template>
  <el-form
    ref="menuSetting"
    :model="menuSetting"
    label-position="top"
    :rules="menuSettingRules"
    class="menu-setting"
  >
    <el-row :gutter="20">
      <el-col :xs="24">
        <el-form-item :label="$t('dialog.menuHangTime')" prop="hangTime">
          <el-input-number
            v-model="menuSetting.hangTime"
            :min="0"
            :max="30"
            :step="1"
          />
        </el-form-item>
      </el-col>
      <el-col :xs="24">
        <el-form-item>
          <el-checkbox v-model="setEnable">
            <span v-text="$t('header.setting')" />
          </el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.deviceSettings')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="deviceSettings" :disabled="disSettingsProp">
              <span v-text="$t('dialog.deviceSettings')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="languageSettings" :disabled="disDevSetProp">
              <span v-text="$t('dialog.languageSettings')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="ledIndication" :disabled="disDevSetProp">
              <span v-text="$t('dialog.ledIndication')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="backlight" :disabled="disDevSetProp">
              <span v-text="$t('dialog.backlight')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="muteTone" :disabled="disDevSetProp">
              <span v-text="$t('dialog.muteTone')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="keyboardLock" :disabled="disDevSetProp">
              <span v-text="$t('dialog.keyboardLock')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="powerOnPwd" :disabled="disPowerPwd">
              <span v-text="$t('dialog.powerOnPwd')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bootInterface" :disabled="disDevSetProp">
              <span v-text="$t('dialog.bootInterface')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="RTC" :disabled="disDevSetProp">
              <span>RTC</span>
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.channelSetting')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="chSetting" :disabled="disSettingsProp">
              <span v-text="$t('dialog.channelSetting')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="chName" :disabled="disChSetProp">
              <span v-text="$t('dialog.chName')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="transTimeLimit" :disabled="disChSetProp">
              <span v-text="$t('dialog.transTimeLimit')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="powerLevel" :disabled="disChSetProp">
              <span v-text="$t('dialog.powerLevel')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="transFrequency" :disabled="disChSetProp">
              <span v-text="$t('dialog.transFrequency')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="receiveFrequency" :disabled="disChSetProp">
              <span v-text="$t('dialog.receiveFrequency')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="squelchLevel" :disabled="disChSetProp">
              <span v-text="$t('dialog.squelchLevel')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="subAudio" :disabled="disChSetProp">
              <span v-text="$t('dialog.subAudio')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="colorCodes" :disabled="disChSetProp">
              <span v-text="$t('dialog.colorCodes')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="repeaterTimeSlot" :disabled="disChSetProp">
              <span v-text="$t('dialog.repeaterTimeSlot')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="launchContact" :disabled="disChSetProp">
              <span v-text="$t('dialog.launchContact')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="suspendTime" :disabled="disChSetProp">
              <span v-text="$t('dialog.suspendTime')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.deviceInfo')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="devInfoEnable" :disabled="disSettingsProp">
              <span v-text="$t('dialog.deviceInfo')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="localName" :disabled="disDevInfoProp">
              <span v-text="$t('dialog.localName')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="localNumber" :disabled="disDevInfoProp">
              <span v-text="$t('dialog.localNumber')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="repeaterNumber" :disabled="disDevInfoProp">
              <span v-text="$t('dialog.repeaterNumber')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="beidouNumber" :disabled="disDevInfoProp">
              <span v-text="$t('dialog.beidouNumber')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="firmwareVersion" :disabled="disDevInfoProp">
              <span v-text="$t('dialog.firmwareVersion')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="cpVersion" :disabled="disDevInfoProp">
              <span v-text="$t('dialog.cpVersion')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.area')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="zoneEnable" :disabled="disSettingsProp">
              <span v-text="$t('dialog.area')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.addressBook')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="contactEnable" :disabled="disSettingsProp">
              <span v-text="$t('dialog.addressBook')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.contactGrouping')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="contactGrouping" :disabled="disContactEnable">
              <span v-text="$t('dialog.contactGrouping')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="newContact" :disabled="disContactGroupProp">
              <span v-text="$t('dialog.newContact')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox
              v-model="deleteContact"
              :disabled="disContactGroupProp"
            >
              <span v-text="$t('dialog.deleteContact')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.dmrAddressBook')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="dmrAddressBook" :disabled="disContactEnable">
              <span v-text="$t('dialog.dmrAddressBook')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="allContacts" :disabled="disContactDmrProp">
              <span v-text="$t('dialog.allContacts')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox
              v-model="frequentContacts"
              :disabled="disContactDmrProp"
            >
              <span v-text="$t('dialog.frequentContacts')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="viewContacts" :disabled="disContactDmrProp">
              <span v-text="$t('dialog.viewContacts')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="editContacts" :disabled="disContactDmrProp">
              <span v-text="$t('dialog.editContacts2')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="dmrNewContact" :disabled="disContactDmrProp">
              <span v-text="$t('dialog.newContact')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox
              v-model="dmrDeleteContact"
              :disabled="disContactDmrProp"
            >
              <span v-text="$t('dialog.deleteContact')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.manualDialing')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="manualDialing" :disabled="disContactEnable">
              <span v-text="$t('dialog.manualDialing')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox
              v-model="singleCallDialing"
              :disabled="disDialVisibleProp"
            >
              <span v-text="$t('dialog.singleCallDialing')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox
              v-model="groupCallDialing"
              :disabled="disDialVisibleProp"
            >
              <span v-text="$t('dialog.groupCallDialing')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.beidouAddressBook')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox
              v-model="beidouAddressBook"
              :disabled="disContactEnable"
            >
              <span v-text="$t('dialog.beidouAddressBook')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdAllContacts" :disabled="disContactBdProp">
              <span v-text="$t('dialog.allContacts')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdViewContacts" :disabled="disContactBdProp">
              <span v-text="$t('dialog.viewContacts')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdEditContacts" :disabled="disContactBdProp">
              <span v-text="$t('dialog.editContacts2')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdNewContact" :disabled="disContactBdProp">
              <span v-text="$t('dialog.newContact')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdDeleteContact" :disabled="disContactBdProp">
              <span v-text="$t('dialog.deleteContact')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdSendMessages" :disabled="disContactBdProp">
              <span v-text="$t('dialog.sendMessages')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.sms')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="smsEnable" :disabled="disContactEnable">
              <span v-text="$t('dialog.sms')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.dmrSms')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="dmrSms" :disabled="disDmrSms">
              <span v-text="$t('dialog.dmrSms')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="newSmsMessage" :disabled="disSmsDmrProp">
              <span v-text="$t('dialog.newSmsMessage')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="preMadeSms" :disabled="disSmsDmrProp">
              <span v-text="$t('dialog.preMadeSms')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="inbox" :disabled="disSmsDmrProp">
              <span v-text="$t('dialog.inbox')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="outbox" :disabled="disSmsDmrProp">
              <span v-text="$t('dialog.outbox')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.beidouSms')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="beidouSms" :disabled="disBeidouSms">
              <span v-text="$t('dialog.beidouSms')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdNewSmsMessage" :disabled="disSmsBdProp">
              <span v-text="$t('dialog.newSmsMessage')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdPreMadeSms" :disabled="disSmsBdProp">
              <span v-text="$t('dialog.preMadeSms')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdInbox" :disabled="disSmsBdProp">
              <span v-text="$t('dialog.inbox')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="bdOutbox" :disabled="disSmsBdProp">
              <span v-text="$t('dialog.outbox')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox
              v-model="remoteDeviceDisabled"
              :disabled="disSmsBdProp"
            >
              <span v-text="$t('dialog.remoteDeviceDisabled')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="remoteDeviceEnable" :disabled="disSmsBdProp">
              <span v-text="$t('dialog.remoteDeviceEnable')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.annex')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="annexEnbale" :disabled="disSettingsProp">
              <span v-text="$t('dialog.annex')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="clearfix">
          <span v-text="$t('dialog.beidouNav')" />
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="beidouNav" :disabled="disBeidouNav">
              <span v-text="$t('dialog.beidouNav')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox v-model="beidouSwitch" :disabled="disAdditionBdProp">
              <span v-text="$t('dialog.beidouSwitch')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12">
          <el-form-item>
            <el-checkbox
              v-model="beidouPositionInfo"
              :disabled="disAdditionBdProp"
            >
              <span v-text="$t('dialog.beidouPositionInfo')" />
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>
    <el-row :gutter="20">
      <!--<el-col :span="24">-->
      <!--<el-form-item label-width="0" class="center actions">-->
      <!--<el-button type="primary" -->
      <!--@click="queryCommonSetting"-->
      <!--:disabled="disabled"-->
      <!--v-text="$t('dialog.querySetting')"></el-button>-->
      <!--<el-button type="warning" -->
      <!--@click="writeInCommonSetting"-->
      <!--:disabled="disabled"-->
      <!--v-text="$t('dialog.writeIn')"></el-button>-->
      <!--</el-form-item>-->
      <!--</el-col>-->
    </el-row>
  </el-form>
</template>

<script>
import repeaterWfMod, { DefModel } from '@/writingFrequency/repeater'
import bfutil from '@/utils/bfutil'
import { tr925PackageName } from '@/modules/protocol'
import { merge } from 'lodash'

const MenuSetting = {
  hangTime: 10,
  setEnable: 1,
  devSet: 0x1bf,
  chSet: 0x1fff,
  devInfoEnable: 1,
  devInfo: 0x3f,
  zoneEnable: 1,
  contactEnable: 1,
  contactGroup: 0x07,
  contactDmr: 0x7f,
  dialVisible: 0x07,
  contactBd: 0x7f,
  smsEnable: 1,
  smsDmr: 0x1f,
  smsBd: 0x7f,
  annexEnbale: 1,
  additionBd: 0x07,
}

export default {
  name: 'TR925MenuSetting',
  props: {
    repeaterData: {
      type: [Object, undefined],
    },
    repeater: {
      type: String,
      default: '',
    },
    getRepeaterId: {
      type: Function,
      default: bfutil.noop,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    packageName: {
      type: String,
      default: '',
    },
    deviceModel: {
      type: String,
      default: DefModel,
    },
    saveMethod: {
      type: Function,
      default: bfutil.noop,
    },
  },
  data() {
    return {
      menuSetting: {
        ...MenuSetting,
      },
      // 一级区域数据
      mainZoneIdData: [],
      // 二级区域数据
      subZoneIdData: [],
      // 三级区域数据
      userZoneIdData: [],
      // 信道数据
      channelData: [],
    }
  },
  methods: {
    coverEnableValue(val, key) {
      const value = val ? 1 : 0
      if (this.menuSetting[key] !== value) {
        this.menuSetting[key] = value
      }

      return value
    },
    decodeInt32ToBool(prop, bit = 0) {
      return ((this.menuSetting[prop] >> bit) & 0x01) === 1
    },
    encodeInt32Value(boolVal, prop, bit = 0) {
      if (boolVal) {
        this.menuSetting[prop] |= 1 << bit
      } else {
        this.menuSetting[prop] -= 1 << bit
      }
    },

    getCommonSettings() {
      if (
        !this.repeaterData ||
        !this.repeaterData.writeFrequencySetting ||
        !this.repeaterData.writeFrequencySetting.commonSetting
      ) {
        return undefined
      }

      return this.repeaterData.writeFrequencySetting.commonSetting
    },

    saveConfig(data) {
      this.commonSetting = merge(this.commonSetting, data)
      this.saveMethod('commonSetting', data)
    },
    // 常规设置
    queryCommonSetting() {
      repeaterWfMod
        .queryConfig(this.defQueryOption)
        .then(res => {
          return this.saveConfig(res)
        })
        .catch(err => {
          bfglob.console.error('queryCommonSetting', err)
        })
    },
    writeInCommonSetting() {
      this.$refs.commonSetting.validate(valid => {
        if (!valid) {
          return false
        }

        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 6,
          },
        })

        repeaterWfMod
          .writeInData(this.commonSetting, options)
          .then(res => {
            return this.saveConfig(this.commonSetting)
          })
          .catch(err => {
            bfglob.console.error('writeInCommonSetting', err)
          })
      })
    },
  },
  computed: {
    defQueryOption() {
      return {
        sid: this.repeater,
        paraInt: this.getRepeaterId(),
        paraBin: {
          operation: 5,
          tableId: 4,
        },
        decodeMsgType: 'RepeaterMenuSetting',
        packageName: tr925PackageName,
      }
    },
    menuSettingRules() {
      return {
        devName: [
          {
            required: true,
            message: this.$t('dialog.requiredRule'),
            trigger: 'blur',
          },
          //             {
          //               validator: (rule, value, callback) => {
          //                 // 检测输入的字符串是否有中文
          //                 bfutil.cannotIncludeChineseRule(rule, value, callback)
          //               },
          // trigger: ['blur']
          //             }
        ],
      }
    },
    languageTypeList() {
      return [
        {
          label: this.$t('header.CN'),
          value: 0,
        },
        {
          label: this.$t('header.EN'),
          value: 1,
        },
      ]
    },
    diPoweronPwdThreshold() {
      return this.commonSetting.poweronPwd.length !== 6
    },
    disDefaultZoneid() {
      return !this.selectZoneCh
    },

    // 按位计算对应开关属性
    setEnable: {
      get() {
        return this.menuSetting.setEnable === 1
      },
      set(val) {
        this.coverEnableValue(val, 'setEnable')
      },
    },
    // 菜单-设备设置 devSet 计算bit3值转换boolean公式：(0x1bf>>3)&0x01===1
    // bit0=设备设置 bit1=语言设置 bit2=LED指示 bit3=背光灯
    // bit4=提示音静音 bit5=键盘锁 bit6=开机密码 bit7=开机界面
    // bit8=RTC bit9~bit15=保留
    deviceSettings: {
      get() {
        return this.decodeInt32ToBool('devSet', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'devSet', 0)
      },
    },
    languageSettings: {
      get() {
        return this.decodeInt32ToBool('devSet', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'devSet', 1)
      },
    },
    ledIndication: {
      get() {
        return this.decodeInt32ToBool('devSet', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'devSet', 2)
      },
    },
    backlight: {
      get() {
        return this.decodeInt32ToBool('devSet', 3)
      },
      set(val) {
        this.encodeInt32Value(val, 'devSet', 3)
      },
    },
    muteTone: {
      get() {
        return this.decodeInt32ToBool('devSet', 4)
      },
      set(val) {
        this.encodeInt32Value(val, 'devSet', 4)
      },
    },
    keyboardLock: {
      get() {
        return this.decodeInt32ToBool('devSet', 5)
      },
      set(val) {
        this.encodeInt32Value(val, 'devSet', 5)
      },
    },
    powerOnPwd: {
      get() {
        return this.decodeInt32ToBool('devSet', 6)
      },
      set(val) {
        this.encodeInt32Value(val, 'devSet', 6)
      },
    },
    bootInterface: {
      get() {
        return this.decodeInt32ToBool('devSet', 7)
      },
      set(val) {
        this.encodeInt32Value(val, 'devSet', 7)
      },
    },
    RTC: {
      get() {
        return this.decodeInt32ToBool('devSet', 8)
      },
      set(val) {
        this.encodeInt32Value(val, 'devSet', 8)
      },
    },

    // 菜单-信道设置
    // bit0=信道设置 bit1=信道名称 bit2=发射限时 bit3=功率等级
    // bit4=发射频率 bit5=接收频率 bit6=静噪等级 bit7=保留
    // bit8=保留 bit9=亚音频 bit10=彩色码 bit11=中继时隙
    // bit12=发射联系人 bit13=挂起时间 bit14~15=保留
    chSetting: {
      get() {
        return this.decodeInt32ToBool('chSet', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 0)
      },
    },
    chName: {
      get() {
        return this.decodeInt32ToBool('chSet', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 1)
      },
    },
    transTimeLimit: {
      get() {
        return this.decodeInt32ToBool('chSet', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 2)
      },
    },
    powerLevel: {
      get() {
        return this.decodeInt32ToBool('chSet', 3)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 3)
      },
    },
    transFrequency: {
      get() {
        return this.decodeInt32ToBool('chSet', 4)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 4)
      },
    },
    receiveFrequency: {
      get() {
        return this.decodeInt32ToBool('chSet', 5)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 5)
      },
    },
    squelchLevel: {
      get() {
        return this.decodeInt32ToBool('chSet', 6)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 6)
      },
    },
    subAudio: {
      get() {
        return this.decodeInt32ToBool('chSet', 9)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 9)
      },
    },
    colorCodes: {
      get() {
        return this.decodeInt32ToBool('chSet', 10)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 10)
      },
    },
    repeaterTimeSlot: {
      get() {
        return this.decodeInt32ToBool('chSet', 11)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 11)
      },
    },
    launchContact: {
      get() {
        return this.decodeInt32ToBool('chSet', 12)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 12)
      },
    },
    suspendTime: {
      get() {
        return this.decodeInt32ToBool('chSet', 13)
      },
      set(val) {
        this.encodeInt32Value(val, 'chSet', 13)
      },
    },

    // 菜单-设备信息
    // bit0=本机名称 bit1=本机号码 bit2=中继号码 bit3=北斗号码
    // bit4=固件版本 bit5=CP版本 bit6~7=保留
    devInfoEnable: {
      get() {
        return this.menuSetting.devInfoEnable === 1
      },
      set(val) {
        this.coverEnableValue(val, 'devInfoEnable')
      },
    },
    localName: {
      get() {
        return this.decodeInt32ToBool('devInfo', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'devInfo', 0)
      },
    },
    localNumber: {
      get() {
        return this.decodeInt32ToBool('devInfo', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'devInfo', 1)
      },
    },
    repeaterNumber: {
      get() {
        return this.decodeInt32ToBool('devInfo', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'devInfo', 2)
      },
    },
    beidouNumber: {
      get() {
        return this.decodeInt32ToBool('devInfo', 3)
      },
      set(val) {
        this.encodeInt32Value(val, 'devInfo', 3)
      },
    },
    firmwareVersion: {
      get() {
        return this.decodeInt32ToBool('devInfo', 4)
      },
      set(val) {
        this.encodeInt32Value(val, 'devInfo', 4)
      },
    },
    cpVersion: {
      get() {
        return this.decodeInt32ToBool('devInfo', 5)
      },
      set(val) {
        this.encodeInt32Value(val, 'devInfo', 5)
      },
    },

    // 菜单区域开关
    zoneEnable: {
      get() {
        return this.menuSetting.zoneEnable === 1
      },
      set(val) {
        this.coverEnableValue(val, 'zoneEnable')
      },
    },
    // 菜单通讯录开关
    contactEnable: {
      get() {
        return this.menuSetting.contactEnable === 1
      },
      set(val) {
        this.coverEnableValue(val, 'contactEnable')
      },
    },

    // 菜单-通讯录-联系人分组
    // bit0=联系人分组 bit1=新建联系人 bit2=删除联系人 bit3~7=保留
    contactGrouping: {
      get() {
        return this.decodeInt32ToBool('contactGroup', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactGroup', 0)
      },
    },
    newContact: {
      get() {
        return this.decodeInt32ToBool('contactGroup', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactGroup', 1)
      },
    },
    deleteContact: {
      get() {
        return this.decodeInt32ToBool('contactGroup', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactGroup', 2)
      },
    },

    // 菜单-通讯录-DMR通讯录
    // bit0=DMR通讯录 bit1=所有联系人 bit2=常用联系人 bit3=查看联系人
    // bit4=编辑联系人 bit5=新建联系人 bit6=删除联系人 bit7=保留
    dmrAddressBook: {
      get() {
        return this.decodeInt32ToBool('contactDmr', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactDmr', 0)
      },
    },
    allContacts: {
      get() {
        return this.decodeInt32ToBool('contactDmr', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactDmr', 1)
      },
    },
    frequentContacts: {
      get() {
        return this.decodeInt32ToBool('contactDmr', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactDmr', 2)
      },
    },
    viewContacts: {
      get() {
        return this.decodeInt32ToBool('contactDmr', 3)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactDmr', 3)
      },
    },
    editContacts: {
      get() {
        return this.decodeInt32ToBool('contactDmr', 4)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactDmr', 4)
      },
    },
    dmrNewContact: {
      get() {
        return this.decodeInt32ToBool('contactDmr', 5)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactDmr', 5)
      },
    },
    dmrDeleteContact: {
      get() {
        return this.decodeInt32ToBool('contactDmr', 6)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactDmr', 6)
      },
    },

    // 菜单-通讯录-手动拨号
    // bit0=手动拨号 bit1=单呼拨号 bit2=组呼拨号 bit3~7=保留
    manualDialing: {
      get() {
        return this.decodeInt32ToBool('dialVisible', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'dialVisible', 0)
      },
    },
    singleCallDialing: {
      get() {
        return this.decodeInt32ToBool('dialVisible', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'dialVisible', 1)
      },
    },
    groupCallDialing: {
      get() {
        return this.decodeInt32ToBool('dialVisible', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'dialVisible', 2)
      },
    },

    // 菜单-通讯录-北斗通讯录
    // bit0=北斗通讯录 bit1=所有联系人 bit2=查看联系人 bit3=编辑联系人
    // bit4=新建联系人 bit5=删除联系人 bit6=发送短信 bit7=保留
    beidouAddressBook: {
      get() {
        return this.decodeInt32ToBool('contactBd', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactBd', 0)
      },
    },
    bdAllContacts: {
      get() {
        return this.decodeInt32ToBool('contactBd', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactBd', 1)
      },
    },
    bdViewContacts: {
      get() {
        return this.decodeInt32ToBool('contactBd', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactBd', 2)
      },
    },
    bdEditContacts: {
      get() {
        return this.decodeInt32ToBool('contactBd', 3)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactBd', 3)
      },
    },
    bdNewContact: {
      get() {
        return this.decodeInt32ToBool('contactBd', 4)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactBd', 4)
      },
    },
    bdDeleteContact: {
      get() {
        return this.decodeInt32ToBool('contactBd', 5)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactBd', 5)
      },
    },
    bdSendMessages: {
      get() {
        return this.decodeInt32ToBool('contactBd', 6)
      },
      set(val) {
        this.encodeInt32Value(val, 'contactBd', 6)
      },
    },

    // 菜单短信开关
    smsEnable: {
      get() {
        return this.menuSetting.smsEnable === 1
      },
      set(val) {
        this.coverEnableValue(val, 'smsEnable')
      },
    },

    // 菜单-短信-DMR短信
    // bit0=DMR短信 bit1=新建短信 bit2=预制短信 bit3=收件箱
    // bit4=发件箱 bit5~7=保留
    dmrSms: {
      get() {
        return this.decodeInt32ToBool('smsDmr', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsDmr', 0)
      },
    },
    newSmsMessage: {
      get() {
        return this.decodeInt32ToBool('smsDmr', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsDmr', 1)
      },
    },
    preMadeSms: {
      get() {
        return this.decodeInt32ToBool('smsDmr', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsDmr', 2)
      },
    },
    inbox: {
      get() {
        return this.decodeInt32ToBool('smsDmr', 3)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsDmr', 3)
      },
    },
    outbox: {
      get() {
        return this.decodeInt32ToBool('smsDmr', 4)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsDmr', 4)
      },
    },

    // 菜单-短信-北斗短信
    // bit0=北斗短信 bit1=新建短信 bit2=预制短信 bit3=收件箱
    // bit4=发件箱 bit5=远程设备禁用 bit6=远程设备启用 bit7=保留
    beidouSms: {
      get() {
        return this.decodeInt32ToBool('smsBd', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsBd', 0)
      },
    },
    bdNewSmsMessage: {
      get() {
        return this.decodeInt32ToBool('smsBd', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsBd', 1)
      },
    },
    bdPreMadeSms: {
      get() {
        return this.decodeInt32ToBool('smsBd', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsBd', 2)
      },
    },
    bdInbox: {
      get() {
        return this.decodeInt32ToBool('smsBd', 3)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsBd', 3)
      },
    },
    bdOutbox: {
      get() {
        return this.decodeInt32ToBool('smsBd', 4)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsBd', 4)
      },
    },
    remoteDeviceDisabled: {
      get() {
        return this.decodeInt32ToBool('smsBd', 5)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsBd', 5)
      },
    },
    remoteDeviceEnable: {
      get() {
        return this.decodeInt32ToBool('smsBd', 6)
      },
      set(val) {
        this.encodeInt32Value(val, 'smsBd', 6)
      },
    },

    // 菜单附件开关
    annexEnbale: {
      get() {
        return this.menuSetting.annexEnbale === 1
      },
      set(val) {
        this.coverEnableValue(val, 'annexEnbale')
      },
    },

    // 菜单-附件-北斗导航
    // bit0=北斗导航 bit1=北斗开关 bit2=北斗定位信息 bit3~7=保留
    beidouNav: {
      get() {
        return this.decodeInt32ToBool('additionBd', 0)
      },
      set(val) {
        this.encodeInt32Value(val, 'additionBd', 0)
      },
    },
    beidouSwitch: {
      get() {
        return this.decodeInt32ToBool('additionBd', 1)
      },
      set(val) {
        this.encodeInt32Value(val, 'additionBd', 1)
      },
    },
    beidouPositionInfo: {
      get() {
        return this.decodeInt32ToBool('additionBd', 2)
      },
      set(val) {
        this.encodeInt32Value(val, 'additionBd', 2)
      },
    },

    // 按伴计算属性禁用互斥
    disSettingsProp() {
      return !this.setEnable
    },
    disDevSetProp() {
      return this.disSettingsProp || !this.deviceSettings
    },
    disPowerPwd() {
      const commonSettings = this.getCommonSettings()
      return (
        this.disDevSetProp ||
        (commonSettings && commonSettings.poweronPwd.length !== 6)
      )
    },

    disChSetProp() {
      return this.disSettingsProp || !this.chSetting
    },
    disDevInfoProp() {
      return this.disSettingsProp || !this.devInfoEnable
    },

    // 通讯录开启，才能设置联系人、通讯录、短信等
    disContactEnable() {
      return this.disSettingsProp || !this.contactEnable
    },
    disContactGroupProp() {
      return this.disContactEnable || !this.contactGrouping
    },
    disContactDmrProp() {
      return this.disContactEnable || !this.dmrAddressBook
    },
    disDialVisibleProp() {
      return this.disContactEnable || !this.manualDialing
    },
    disContactBdProp() {
      return this.disContactEnable || !this.beidouAddressBook
    },

    // 短信开启，才能设置DMR和北斗短信
    disDmrSms() {
      return this.disContactEnable || !this.smsEnable
    },
    disBeidouSms() {
      return this.disContactEnable || !this.smsEnable
    },
    disSmsDmrProp() {
      return this.disSettingsProp || this.disDmrSms || !this.dmrSms
    },
    disSmsBdProp() {
      return this.disSettingsProp || this.disBeidouSms || !this.beidouSms
    },

    // 附件开启，才能设置北斗导航
    disBeidouNav() {
      return !this.annexEnbale
    },
    disAdditionBdProp() {
      return this.disSettingsProp || this.disBeidouNav || !this.beidouNav
    },
  },
  watch: {
    // 禁用设置时，清空所有配置
    setEnable(val) {
      this.deviceSettings = val
      this.chSetting = val
      this.devInfoEnable = val
      this.zoneEnable = val
      this.contactEnable = val
      this.annexEnbale = val
    },
    // 设备设置被禁用时，清空设备的配置
    deviceSettings(val) {
      if (val) {
        this.menuSetting.devSet = MenuSetting.devSet
      } else {
        this.menuSetting.devSet = 0
      }
    },
    // 信道设置被禁用时，清空信道的配置
    chSetting(val) {
      if (val) {
        this.menuSetting.chSet = MenuSetting.chSet
      } else {
        this.menuSetting.chSet = 0
      }
    },
    devInfoEnable(val) {
      if (val) {
        this.menuSetting.devInfo = MenuSetting.devInfo
      } else {
        this.menuSetting.devInfo = 0
      }
    },

    // 通讯录开启/关闭，重置联系人分组、通讯录、短信功能
    contactEnable(val) {
      this.contactGrouping = val
      this.dmrAddressBook = val
      this.manualDialing = val
      this.beidouAddressBook = val
      this.smsEnable = val
    },
    contactGrouping(val) {
      if (val) {
        this.menuSetting.contactGroup = MenuSetting.contactGroup
      } else {
        this.menuSetting.contactGroup = 0
      }
    },
    dmrAddressBook(val) {
      if (val) {
        this.menuSetting.contactDmr = MenuSetting.contactDmr
      } else {
        this.menuSetting.contactDmr = 0
      }
    },
    manualDialing(val) {
      if (val) {
        this.menuSetting.dialVisible = MenuSetting.dialVisible
      } else {
        this.menuSetting.dialVisible = 0
      }
    },
    beidouAddressBook(val) {
      if (val) {
        this.menuSetting.contactBd = MenuSetting.contactBd
      } else {
        this.menuSetting.contactBd = 0
      }
    },

    // 短信开启/关闭，重置DMR短信和北斗短信
    smsEnable(val) {
      this.dmrSms = val
      this.beidouSms = val
    },
    dmrSms(val) {
      if (val) {
        this.menuSetting.smsDmr = MenuSetting.smsDmr
      } else {
        this.menuSetting.smsDmr = 0
      }
    },
    beidouSms(val) {
      if (val) {
        this.menuSetting.smsBd = MenuSetting.smsBd
      } else {
        this.menuSetting.smsBd = 0
      }
    },

    // 附件开启/关闭，重置北斗导航
    annexEnbale(val) {
      this.beidouNav = val
    },
    beidouNav(val) {
      if (val) {
        this.menuSetting.additionBd = MenuSetting.additionBd
      } else {
        this.menuSetting.additionBd = 0
      }
    },
  },
}
</script>

<style>
.menu-setting .el-card .el-card__header {
  padding: 10px 16px;
}

.menu-setting .el-card .el-form-item {
  margin-bottom: 0;
}

.menu-setting .el-card:not(:first-child) {
  margin-top: 10px;
}
</style>
