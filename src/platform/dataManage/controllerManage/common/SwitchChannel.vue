<template>
  <el-form ref="serverSetting" :model="repeaterCurChSet" label-width="auto">
    <el-form-item v-if="showZoneId" :label="$t('dialog.primaryAreaID')">
      <el-input-number
        v-model="repeaterCurChSet.zoneId.mainZoneId"
        :min="0"
        :max="999"
      />
    </el-form-item>
    <el-form-item v-if="showZoneId" :label="$t('dialog.secondaryAreaID')">
      <el-input-number
        v-model="repeaterCurChSet.zoneId.subZoneId"
        :min="0"
        :max="999"
      />
    </el-form-item>
    <el-form-item v-if="showZoneId" :label="$t('dialog.tertiaryAreaID')">
      <el-input-number
        v-model="repeaterCurChSet.zoneId.userZoneId"
        :min="0"
        :max="999"
      />
    </el-form-item>
    <el-form-item :label="$t('dialog.chId')">
      <el-input-number
        v-model="repeaterCurChSet.chId"
        :min="1"
        :max="maxChannel"
        :step="1"
      />
    </el-form-item>
    <el-form-item label=" " class="center actions">
      <el-button
        type="warning"
        :disabled="disabled"
        @click="queryRepeaterCurChSet"
        v-text="$t('dialog.writeIn')"
      />
    </el-form-item>
  </el-form>
</template>

<script>
import repeaterWfMod, { DefModel } from '@/writingFrequency/repeater'
import bfutil from '@/utils/bfutil'
import { kcpPackageName } from '@/modules/protocol'
import { cloneDeep, merge } from 'lodash'

const RepeaterCurChSet = {
  zoneId: {
    // 一级区域ID
    mainZoneId: 0,
    // 二级区域ID
    subZoneId: 0,
    // 三级区域ID
    userZoneId: 0,
  },
  chId: 1,
}

export default {
  name: 'SwitchChannel',
  props: {
    repeaterData: {
      type: [Object, undefined],
    },
    repeater: {
      type: String,
      default: '',
    },
    getRepeaterId: {
      type: Function,
      default: bfutil.noop,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    packageName: {
      type: String,
      default: kcpPackageName,
    },
    deviceModel: {
      type: String,
      default: DefModel,
    },
    saveMethod: {
      type: Function,
      default: bfutil.noop,
    },
    operation: {
      type: Number,
      default: 11,
    },
    maxChannel: {
      type: Number,
      default: 99,
    },
  },
  data() {
    return {
      repeaterCurChSet: cloneDeep(RepeaterCurChSet),
    }
  },
  methods: {
    // 11:切换中继当前信道
    queryRepeaterCurChSet() {
      repeaterWfMod
        .writeInData(this.repeaterCurChSet, this.defQueryOption)
        .then(res => {
          this.saveMethod('repeaterCurChSet', this.repeaterCurChSet)
        })
        .catch(err => {
          bfglob.console.error('queryRepeaterCurChSet', err)
        })
    },
  },
  computed: {
    defQueryOption() {
      return {
        paraBin: {
          operation: this.operation,
        },
        sid: this.repeater,
        paraInt: this.getRepeaterId(),
        decodeMsgType: 'RepeaterCurChSet',
        packageName: this.packageName,
      }
    },
    showZoneId() {
      // let deviceModels = ['TR900M', 'TR925M']
      // return deviceModels.includes(this.deviceModel)
      return false
    },
    switchChLayout() {
      return this.showZoneId ? 12 : 24
    },
  },
  watch: {
    'repeaterData.writeFrequencySetting.repeaterCurChSet': {
      immediate: true,
      deep: true,
      handler(val) {
        this.repeaterCurChSet = merge({}, val || RepeaterCurChSet)
      },
    },
  },
}
</script>

<style></style>
