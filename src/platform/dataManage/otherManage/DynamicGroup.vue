<template>
  <data-form-editor
    ref="formEditor"
    class="page-dynamic-group"
    editor-class="dynamic-group-editor"
    :title="$t('dynamicGroup.title')"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :detail-head="detailHead"
    :detail-render="detailRender"
    :getDetailData="getDetailData"
    :getNewData="getNewData"
    :parseDataForEdit="parseDataForEdit"
    :beforeAction="beforeAction"
    :confirmDeleteAgain="confirmDeleteAgain"
    :getFormRef="() => $refs.dynamicGroupDataEditorForm"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
    @close="editorOnClose"
  >
    <template #form="{ formData, isNewStatus }">
      <div class="flex flex-col form-editor-wrapper">
        <el-form
          ref="dynamicGroupDataEditorForm"
          :model="formData"
          label-width="80px"
          :rules="rules"
          :validate-on-rule-change="false"
          class="flex-none grid grid-cols-1 h-auto dynamic-group-form"
        >
          <el-form-item :label="$t('dialog.type')" prop="orgIsVirtual">
            <el-radio-group v-model="formData.orgIsVirtual" :disabled="!isNewStatus" @change="orgIsVirtualChange">
              <el-radio :value="DbOrgIsVirtual.TaskGroup">
                {{ $t('dynamicGroup.taskGroup') }}
              </el-radio>
              <el-radio :value="DbOrgIsVirtual.TempGroup">
                {{ $t('dynamicGroup.tempGroup') }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('dialog.name')" prop="orgShortName">
            <el-input v-model="formData.orgShortName" :maxlength="16" :disabled="!isNewStatus" />
          </el-form-item>
          <el-form-item v-if="isNewStatus">
            <el-checkbox v-model="isFastDynamicGroup" :disabled="formData.orgIsVirtual !== DbOrgIsVirtual.TempGroup">
              {{ $t('dynamicGroup.tempGroupInvalidAndDelete') }}
            </el-checkbox>
          </el-form-item>
        </el-form>

        <member-info v-if="!isMobile" :members="memberNodes" @click-member="clickMemberInfo" @remove-member="removeMember" />
      </div>

      <dynamicGroupTree
        ref="dynamicGroupTreeRef"
        treeId="dynamic-group-tree"
        :dynamicGroupType="formData.orgIsVirtual === DbOrgIsVirtual.TempGroup ? 0 : 1"
        :dynamicGroup="formData"
        :max-select-size="getDynamicGroupMemberLimit(formData.orgIsVirtual)"
        @select-member="selectMember"
        @loaded="dynamicGroupTreeLoaded.resolve(true)"
      />
    </template>

    <template #form-footer="{ onClose, onConfirm, isNewStatus }">
      <div class="flex justify-center gap-3 dialog-footer">
        <el-popover v-if="isMobile" placement="top" width="100%" trigger="click" popper-class="dynamic-group-members-popper">
          <member-info :members="memberNodes" @click-member="clickMemberInfo" @remove-member="removeMember" />
          <template #reference>
            <el-button>{{ $t('dynamicGroup.member') }}</el-button>
          </template>
        </el-popover>

        <el-button class="w-32" @click="onClose">
          {{ $t('dialog.cancel') }}
        </el-button>
        <el-button class="w-32" type="primary" @click="onConfirm(isNewStatus)" v-text="$t('dialog.confirm')" />
      </div>
    </template>

    <!--  删除动态组的消息提示  -->
    <el-dialog
      v-model="showDeleteAlert"
      :title="$t('dialog.alertTitle')"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="header-border delete-dynamic-group-dialog"
      width="360px"
      @close="onCloseDeleteAlertDialog"
    >
      <div class="flex justify-center items-center gap-2">
        <el-icon class="text-2xl text-yellow-500">
          <Warning />
        </el-icon>
        <span>{{ $t('dynamicGroup.deleteDynamicGroupTips') }}</span>
      </div>
      <div v-if="showIsForceDelete" class="flex justify-center mt-2">
        <el-checkbox v-model="isForceDelete" :disabled="disableIsForceDelete">
          {{ $t('dynamicGroup.isForceDelete') }}
        </el-checkbox>
      </div>

      <template #footer>
        <div class="flex justify-center">
          <el-button type="primary" class="w-32" @click="confirmDeleteDynamicGroup">
            {{ $t('dialog.confirm') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </data-form-editor>
</template>

<script>
  import { defineAsyncComponent } from 'vue'
  import bfutil, { formatDmrIdLabel, notDynamicGroupPermission, deferred } from '@/utils/bfutil'
  import {
    addDynamicGroup,
    checkPcDeviceExitDynamicGroup,
    checkPcDeviceJoinDynamicGroup,
    DbOrgIsVirtual,
    DynamicGroupState,
    DynamicGroupType,
    MemberState,
    MemberType,
    modifyDynamicGroupMember,
    removeDynamicGroup,
  } from '@/utils/dynamicGroup/api'
  import bfNotify, { messageBox, warningBox } from '@/utils/notify'
  import { v1 as uuid } from 'uuid'
  import vueMixin from '@/utils/vueMixin'
  import DataFormEditor from '@/components/common/DataFormEditor.vue'
  import validateRules from '@/utils/validateRules'
  import eventBus from '@/utils/eventBus'
  import { useRouteParams } from '@/router'
  import bfprocess from '@/utils/bfprocess'

  const DefaultData = {
    rid: '',
    parentOrgId: bfglob.userInfo.orgId,
    orgShortName: '',
    dmrId: '',
    orgSortValue: 100,
    note: '',
    // 动态组类型 0:临时组->100 1：任务组->101
    orgIsVirtual: DbOrgIsVirtual.TempGroup,
  }
  const DynamicGroupMemberLimit = {
    100: 16,
    101: 128,
    0: 16,
    1: 128,
  }

  const getMemberOrgShortName = function (detail) {
    // 组成员，memberOrgId===rid，需要先查找到自己的数据，再读取上级名称
    let memberOrgId = detail.memberOrgId
    if (detail.isDeviceGroup === MemberType.Group) {
      memberOrgId = bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)?.parentOrgId ?? ''
    }
    return bfglob.gorgData.getDataMaybeNoPerm(memberOrgId)?.orgShortName ?? '-'
  }

  // 创建快捷临时组成功事件,当前组件订阅使用
  const addFastTempGroupSuccess = 'add-fast-temp-group-success'
  const { getRouteParams } = useRouteParams()

  export default {
    name: 'DynamicGroup',
    mixins: [vueMixin],
    data() {
      return {
        dataTable: {
          name: 'dynamicGroupTable',
          body: bfutil.objToArray(bfglob.gorgData.getDynamicGroup()),
        },

        // 删除动态组对话框参数
        isForceDelete: false,
        isConfirmDelete: false,
        showDeleteAlert: false,
        deleteRow: null,

        // 动态组成员详情
        members: {
          devices: [],
          groups: [],
        },
        isFastDynamicGroup: false,
        dynamicGroupTreeLoaded: deferred(),
      }
    },
    methods: {
      async onDelete(row) {
        try {
          const options = {
            beforeSend: rpcCmd => {
              if (row.dynamicGroupState === DynamicGroupState.Expired || this.isForceDelete) {
                rpcCmd.opt = 'force'
              }
            },
          }
          const rpcCmd = await removeDynamicGroup(row, options)
          bfglob.console.log('[deleteDynamicGroup] res:', rpcCmd)

          if (rpcCmd.resInfo === '+OK') {
            bfNotify.messageBox(this.$t('msgbox.sendSuccess'), 'success')
            // 由服务器通知删除
            // 更新删除中的动态组状态
            // msgData.dynamicGroupState = 10
            // bfglob.emit('update_global_dynamic_group', msgData)
          } else {
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          }
        } catch (err) {
          bfglob.console.warn('[deleteDynamicGroup] err:', err)
          bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
        }
      },
      async onUpdate(row, done) {
        this.updateDynamicGroup(row)
          .then(done)
          .catch(err => {
            bfglob.console.error('[updateDynamicGroup] err', err)
          })
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        this.addDynamicGroup(row)
          .then(() => {
            if (addNewCb) {
              row.orgShortName = bfutil.customNumberIncrement(row.orgShortName, 3)
              this.$refs.dynamicGroupTreeRef.$selectAll(false)
              this.members.devices = []
              this.members.groups = []
              addNewCb(row)
              return
            }
            done()
          })
          .catch(err => {
            bfglob.console.error('[addDynamicGroup] err:', err)
          })
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return { ...DefaultData }
      },
      parseDataForEdit(formData) {
        // 同步树形节点数据
        this.dynamicGroupTreeLoaded.then(() => {
          this.$refs.dynamicGroupTreeRef?.$syncSelectNode()
          return true
        })
      },
      /**
       * 编辑数据前置执行方法，允许拒绝编辑或添加新数据
       * @param {number} status Add: 1, Edit: 2, Delete: 3
       * @param {Record<string, any>?} row
       * @returns {Promise<boolan>}
       */
      beforeAction(status, row) {
        // 判断是否有动态组权限
        if (notDynamicGroupPermission() && bfutil.notEditDataPermission()) {
          return Promise.reject('No dynamic group permission')
        }

        if (status === 2) {
          // 如果是快捷临时组，则不能编辑
          if (row.orgIsVirtual === DbOrgIsVirtual.FastTempGroup) {
            messageBox(this.$t('dynamicGroup.fastDynamicGroupNoModify'), 'warning')
            return Promise.reject('Fast temp group')
          }

          // 任务组已经失效或待删除中，不能编辑
          const invalidStatus = [DynamicGroupState.Expired]
          if (row.orgIsVirtual === DbOrgIsVirtual.TaskGroup && invalidStatus.includes(row.dynamicGroupState)) {
            messageBox(this.$t('dynamicGroup.invalidTaskGroup'), 'warning')
            return Promise.reject('Invalid task group')
          }
        }

        if (status === 3) {
          // judge is have dyGroup permission.and alert that permission died
          if (row.creator !== bfglob.userInfo.rid && !bfglob.gorgData.get(row.parentOrgId)) {
            // alert that permission died
            bfNotify.messageBox(this.$t('dynamicGroup.permDied'), 'error')
            return Promise.reject('permission died')
          }
        }

        return Promise.resolve(true)
      },
      confirmDeleteAgain(row) {
        return new Promise(resolve => {
          eventBus.once('delete-alert-dialog-result', () => {
            resolve(this.isConfirmDelete)
            this.deleteRow = null
          })
          this.deleteRow = row
          // 打开动态组删除提示对话框
          this.showDeleteAlert = true
          this.isForceDelete = row.dynamicGroupState === DynamicGroupState.Expired
          this.isConfirmDelete = false
        })
      },
      editorOnClose() {
        this.$refs.dynamicGroupTreeRef?.$selectAll(false)
        this.isFastDynamicGroup = false
        // 解绑从联网通话快速创建动态组订阅的事件
        eventBus.off(addFastTempGroupSuccess)
      },

      orgIsVirtualChange(val) {
        if (val === DbOrgIsVirtual.TaskGroup) {
          this.isFastDynamicGroup = false
        }
      },
      generateMemberInfo(groups, devices) {
        const list = []
        devices.forEach(data => {
          const source = bfglob.gdevices.getDataMaybeNoPerm(data.deviceRid)
          source &&
            list.push({
              name: source.selfId,
              dmrId: source.dmrId,
              dmrIdLabel: `${source.dmrId} / ${`0x${source.dmrId}` & 0x7fffffff}`,
              rid: source.rid,
              detailRid: data.rid,
              isOrg: false,
            })
        })
        groups.forEach(data => {
          const source = bfglob.gorgData.get(data.groupRid) || bfglob.noPermOrgData.get(data.groupRid)
          source &&
            list.push({
              name: source.orgShortName,
              dmrId: source.dmrId,
              dmrIdLabel: `${source.dmrId} / ${`0x${source.dmrId}` & 0x7fffffff}`,
              rid: source.rid,
              detailRid: data.rid,
              isOrg: true,
            })
        })
        return list
      },
      clickMemberInfo(memberInfo) {
        this.$refs.dynamicGroupTreeRef?.$setActive(memberInfo.rid, true)
      },
      removeMember(memberInfo) {
        this.$refs.dynamicGroupTreeRef?.$setSelected(memberInfo.rid, false)
      },
      selectMember(val = {}) {
        this.members = {
          devices: val.devices,
          groups: val.groups,
        }
      },
      updateDatatableBody() {
        this.$nextTick(() => {
          this.dataTable.body = bfutil.objToArray(bfglob.gorgData.getDynamicGroup())
        })
      },
      getDynamicGroupMemberLimit(type) {
        return DynamicGroupMemberLimit[type] || DynamicGroupMemberLimit[1]
      },
      getDetailData(rid) {
        return bfglob.gdynamicGroupDetail.getSortDataByGroupRid(rid)
      },
      detailRender(row) {
        const detailList = this.getDetailData(row.rid)
        if (!detailList.length) {
          return ''
        }
        let html = `<table cellpadding='0' cellspacing='0' border='0'
          class='display table table-striped table-bordered row-details dynamic-group-detail-table'
           width='100%' style="width:100%"><thead><tr>`
        // 表头
        const header = this.detailHead
        html += header
          .map(col => {
            return `<th class="${col.class || ''}" style="width:${col.width || 'auto'}">${col.title}</th>`
          })
          .join('')
        html += '</tr></thead><tbody>'

        html += detailList
          .map((item, index) => {
            const tdList = header.map((head, colIndex) => {
              if (typeof head.render === 'function') {
                const val = head.render(item[head.data] ?? undefined, 'string', item, {
                  row: index,
                  col: colIndex,
                })
                return `<td>${val}</td>`
              }
              return `<td>${item[head.data] ?? ''}</td>`
            })
            return `<tr>${tdList.join('')}</tr>`
          })
          .join('')

        html += '</tbody></table>'

        return html
      },

      checkGroupDetailIsExits(targets) {
        return targets && (targets.groups.length || targets.devices.length)
      },
      mergeGroupDetail(dataList, dynamicGroup) {
        const dynamicGroupType = dynamicGroup.orgIsVirtual === 100 ? 0 : 1
        return (dataList || []).map(data => {
          return Object.assign(data, {
            rid: data.rid || uuid(),
            orgId: dynamicGroup.rid,
            dynamicGroupType,
          })
        })
      },
      commonOperationErr(rpc_cmd_obj) {
        if (rpc_cmd_obj.resInfo.includes('db_org_org_self_id_key')) {
          bfNotify.warningBox(this.$t('msgbox.repeatNo'))
          return true
        }
        if (rpc_cmd_obj.resInfo.includes('db_org_dmr_id_key')) {
          // 服务器生成DMRID异常
          bfNotify.warningBox(this.$t('msgbox.repeatDMRID'))
          return true
        }
        if (rpc_cmd_obj.resInfo.includes('db_org_org_short_name_key')) {
          bfNotify.warningBox(this.$t('dynamicGroup.repeatGroupName'))
          return true
        }
        if (rpc_cmd_obj.resInfo.includes('no login info for this session')) {
          bfNotify.warningBox(this.$t('msgbox.loginSessionExpired'))
          return true
        }
        if (rpc_cmd_obj.resInfo.includes('DatabaseGetSystemDbString')) {
          bfNotify.warningBox(this.$t('dynamicGroup.notFoundDbHandler'))
          return true
        }
        if (rpc_cmd_obj.resInfo.includes('can not get new dmrid for group')) {
          bfNotify.warningBox(this.$t('dynamicGroup.noDynamicGroupDmrId'))
          return true
        }
        if (
          rpc_cmd_obj.resInfo.includes('no such dynamic group') ||
          rpc_cmd_obj.resInfo.includes('no such dynamic group manager') ||
          rpc_cmd_obj.resInfo.includes('Can not find this dynamic group in manager')
        ) {
          bfNotify.warningBox(this.$t('dynamicGroup.notFoundDynamicGroup'))
          return true
        }
        if (rpc_cmd_obj.resInfo.includes('Can not find this temp group in manager')) {
          bfNotify.warningBox(this.$t('dynamicGroup.notFoundDynamicGroup'))
          return true
        }

        return false
      },
      memberOperationErr(rpc_cmd_obj) {
        if (rpc_cmd_obj.resInfo.includes('Can not find this device in dynamic group') || rpc_cmd_obj.resInfo.includes('Can not find dynamicGroup')) {
          bfNotify.warningBox(this.$t('dynamicGroup.notFoundMemberInDynamicGroup'))
          return true
        }
        return false
      },
      // async gotoSpeakerPage(orgData) {
      //   if (!bfglob.userInfo.setting.gotoSpeakPage) { return }
      //   await warningBox(this.$t('dynamicGroup.jumpToNetworkCall'), 'info')
      //   bfglob.emit('openMenuItem', 'command/bfSpeaking', (vm) => {
      //     vm.speakFast(orgData.dmrId)
      //   })
      // },
      async addDynamicGroup(data) {
        const targets = this.$refs.dynamicGroupTreeRef?.$getSelectTarget()
        if (!this.checkGroupDetailIsExits(targets)) {
          messageBox(this.$t('dynamicGroup.noGroupMembers'), 'error')
          return
        }

        // 检测组成员是否超出限制
        if (this.$refs.dynamicGroupTreeRef?.selectLength > this.getDynamicGroupMemberLimit(data.orgIsVirtual)) {
          messageBox(this.$t('dynamicGroup.groupMembersLimit'), 'error')
          return
        }

        const rid = uuid()
        const orgData = {
          ...data,
          orgIsVirtual: this.isFastDynamicGroup ? DbOrgIsVirtual.FastTempGroup : data.orgIsVirtual,
          rid,
          orgSortValue: 100,
          dmrId: '',

          // 补齐db_org表字段
          orgSelfId: rid,
          orgFullName: data.orgShortName,
          orgImg: '11111111-1111-1111-1111-111111111111',
          setting: '{}',
        }
        const modifyDeviceList = {
          dynamicGroup: orgData,
          userPriority: this.voipSpeakInfo.priority ?? 2,
          devices: this.mergeGroupDetail(targets.devices, orgData),
          groups: this.mergeGroupDetail(targets.groups, orgData),
        }

        const resRpcCmd = await addDynamicGroup(modifyDeviceList)
        bfglob.console.log('[addDynamicGroup] res:', resRpcCmd)

        if (resRpcCmd.resInfo === '+OK') {
          messageBox(this.$t('msgbox.addSuccess'), 'success')
          // 添加动态组成功，返回动态组的dmrId
          Object.assign(orgData, resRpcCmd.body.dynamicGroup)
          // 如果是快捷临时组，发布对应的事件
          if (this.isFastDynamicGroup) {
            eventBus.emit(addFastTempGroupSuccess, orgData.dmrId)
            this.isFastDynamicGroup = false
          }

          DefaultData.orgIsVirtual = DbOrgIsVirtual.FastTempGroup === orgData.orgIsVirtual ? DbOrgIsVirtual.TempGroup : DbOrgIsVirtual.TaskGroup
          // DefaultData.orgShortName = bfutil.customNumberIncrement(orgData.orgShortName, 1)

          // 每个组或终端只能加入一个动态组，所以需要清楚当前的选中节点
          // this.$refs.dynamicGroupTreeRef?.$selectAll(false)
          bfglob.emit('add_global_dynamic_group', orgData)
          bfglob.emit('dynamic-group-subscribe-server-command', orgData)

          // 发布表格数据变更事件
          bfprocess.publishTableEvent(this.dataTable.name, 'add', orgData)

          // 处理动态组下成员
          const resDevices = resRpcCmd.body.devices
          const resGroups = resRpcCmd.body.groups
          bfglob.emit('add_global_dynamic_group_detail', [...resDevices, ...resGroups])
          resDevices.forEach(v => {
            checkPcDeviceJoinDynamicGroup(v).catch()
            checkPcDeviceExitDynamicGroup(v).catch()
          })

          // 添加查询日志
          const content = this.$t('dialog.add') + orgData.orgShortName + this.$t('dynamicGroup.title')
          bfglob.emit('addnote', content)

          // // 访问用户是否跳转联网通话
          // this.$nextTick(() => {
          //   this.gotoSpeakerPage(orgData)
          // })

          return orgData
        } else {
          if (this.commonOperationErr(resRpcCmd)) {
            return
          }
          messageBox(this.$t('msgbox.addError'), 'error')
        }
      },
      // 对比各类操作的结果集
      filterDynamicGroupActionTarget(orgData, targets) {
        // 待返回的结果集
        const result = {
          add: {
            groups: [],
            devices: [],
          },
          remove: {
            groups: [],
            devices: [],
          },
          keep: {
            groups: [],
            devices: [],
          },
        }

        // 查找动态组下的所有成员数据
        const groupDetails = bfglob.gdynamicGroupDetail.getDataByGroupRid(orgData.rid)
        // 未被处理的所有详情
        const unProcessDetails = []
        const processRemoveDetail = (detail, isGroup = false) => {
          const targetName = isGroup ? 'groups' : 'devices'
          const keyProp = isGroup ? 'groupRid' : 'deviceRid'

          // 还在
          if (
            targets[targetName].find(item => {
              return item.isDeviceGroup === detail.isDeviceGroup && item[keyProp] === detail[keyProp]
            })
          ) {
            unProcessDetails.push(detail)
          } else {
            result.remove[targetName].push(detail)
          }
        }
        const processAddOrKeepDetail = (detail, isGroup = false) => {
          const targetName = isGroup ? 'groups' : 'devices'
          const keyProp = isGroup ? 'groupRid' : 'deviceRid'
          const index = unProcessDetails.findIndex(item => {
            return item.isDeviceGroup === detail.isDeviceGroup && item[keyProp] === detail[keyProp]
          })
          // 在原数据中查找到数据，则保持不变
          if (index >= 0) {
            // 如果当前选择的节点，是要删除的组成员下终端，则放到待添加数据中
            if (!detail.rid) {
              result.add[targetName].push(detail)
            } else {
              result.keep[targetName].push(detail)
              unProcessDetails.splice(index, 1)
            }
          } else {
            result.add[targetName].push(detail)
          }
        }

        // 遍历一次原数据，筛选出被删除的详情
        for (let i = 0; i < groupDetails.length; i++) {
          const detail = groupDetails[i]
          // 2为组呼
          processRemoveDetail(detail, detail.isDeviceGroup === 2)
        }

        // 遍历一次当前选择的数据，筛选出需要添加的详情
        for (let j = 0; j < targets.groups.length; j++) {
          const detail = targets.groups[j]
          processAddOrKeepDetail(detail, true)
        }
        for (let k = 0; k < targets.devices.length; k++) {
          const detail = targets.devices[k]
          processAddOrKeepDetail(detail, false)
        }

        return result
      },
      async updateDynamicGroup(data) {
        const targets = this.$refs.dynamicGroupTreeRef?.$getSelectTarget()
        if (!this.checkGroupDetailIsExits(targets)) {
          // 没有选择任何节点，应该提示警告信息
          await warningBox(this.$t('dynamicGroup.noGroupMembers'))
          return
        }

        const orgData = {
          ...data,
        }

        // 查找出动态组需要添加、删除成员的数据
        const { add, remove } = this.filterDynamicGroupActionTarget(orgData, targets)

        // 如果没有成员变化，则不需要更新
        if (!(add.devices.length || add.groups.length || remove.devices.length || remove.groups.length)) {
          messageBox(this.$t('dynamicGroup.noUpdates'), 'warning')
          return
        }

        bfglob.console.log('updateDynamicGroup targets:', add, remove)

        const resRpcCmd = await modifyDynamicGroupMember(orgData, add, remove).catch(this.memberOperationErr)
        bfglob.console.log('updateDynamicGroup res:', resRpcCmd)
        if (resRpcCmd.resInfo.includes('Not allow change member in invalid dyGroup')) {
          messageBox(this.$t('dynamicGroup.taskGroupExpiredWithUpdate'), 'warning')
          orgData.dynamicGroupState = DynamicGroupState.Expired
          bfglob.emit('update_global_dynamic_group', orgData)
          return
        }
        if (resRpcCmd.resInfo.includes('Can not modify auto delete tempGroup')) {
          messageBox(this.$t('dynamicGroup.cannotModifyQuickTempGroup'), 'warning')
          return
        }
        //"Err:no such dynamic group for:xxxxxx"
        if (resRpcCmd.resInfo.includes('Err:no such dynamic group for:')) {
          messageBox(this.$t('dynamicGroup.noSuchDynamicGroup'), 'warning')
          return
        }
        if (resRpcCmd.resInfo !== '+OK') {
          messageBox(resRpcCmd.resInfo, 'warning')
          return
        }

        messageBox(this.$t('msgbox.upSuccess'), 'success')
        const modifyMemberList = resRpcCmd.body
        // 同步新添加组成员
        const addMemberNote = []
        const addDevices = modifyMemberList.addDevices.rows
        addDevices.forEach(v => {
          const device = bfglob.gdevices.getDataMaybeNoPerm(v.deviceRid)
          device && addMemberNote.push(device.selfId)
          checkPcDeviceJoinDynamicGroup(v).catch()
        })
        const addGroups = modifyMemberList.addGroups.rows
        addGroups.forEach(v => {
          const org = bfglob.gorgData.getDataMaybeNoPerm(v.groupRid)
          org && addMemberNote.push(org.orgShortName)
        })
        bfglob.emit('add_global_dynamic_group_detail', [...addDevices, ...addGroups])

        // 同步被删除组成员
        const removeMemberNote = []
        const delDevices = modifyMemberList.delDevices.rows
        delDevices.forEach(v => {
          const device = bfglob.gdynamicGroupDetail.getDetailSource(v.rid)
          device && removeMemberNote.push(device.selfId)
          checkPcDeviceExitDynamicGroup(v).catch()
        })
        const delGroups = modifyMemberList.delGroups.rows
        delGroups.forEach(v => {
          const org = bfglob.gdynamicGroupDetail.getDetailSource(v.rid)
          org && removeMemberNote.push(org.orgShortName)
        })
        bfglob.emit('remove_global_dynamic_group_detail', [...delDevices, ...delGroups])

        bfglob.emit('update_global_dynamic_group', orgData)

        // 添加查询日志
        let content = `${this.$t('dialog.update')} [${orgData.orgShortName}] ${this.$t('dynamicGroup.title')}`
        if (addMemberNote.length) {
          content += `; ${this.$t('dynamicGroup.addMember')}: [${addMemberNote.join(',')}]`
        }
        if (removeMemberNote.length) {
          content += `; ${this.$t('dynamicGroup.removeMember')}: [${removeMemberNote.join(',')}]`
        }
        bfglob.emit('addnote', content)
      },
      confirmDeleteDynamicGroup() {
        this.showDeleteAlert = false
        this.isConfirmDelete = true
      },
      onCloseDeleteAlertDialog() {
        eventBus.emit('delete-alert-dialog-result')
      },
      onCreateFastTempGroup(params) {
        const { routeName, reply } = params ?? {}
        if (!routeName || !reply) return

        this.isFastDynamicGroup = true

        // 订阅添加成功的信号
        eventBus.once(addFastTempGroupSuccess, target => {
          // 回复快捷临时组dmrId
          bfglob.emit(reply, target)

          // 关闭动态组管理界面，返回之前的路由页面
          if (routeName !== this.$route.name) {
            this.$router.replace({
              name: routeName,
            })
          }
        })

        this.$nextTick(() => {
          // 打开添加数据窗口
          this.$refs.formEditor?.addNewData()
        })
      },
    },
    mounted() {
      // 监听请求动态组完成，更新表格数据
      bfglob.once('request-dynamic-group-finish', this.updateDatatableBody)

      bfglob.on('add_global_dynamic_group', this.updateDatatableBody)
      bfglob.on('update_global_dynamic_group', this.updateDatatableBody)
      bfglob.on('delete_global_dynamic_group', this.updateDatatableBody)

      bfglob.on('add_global_dynamic_group_detail', this.updateDatatableBody)
      bfglob.on('update_global_dynamic_group_detail', this.updateDatatableBody)
      bfglob.on('remove_global_dynamic_group_detail', this.updateDatatableBody)
      bfglob.on('create-fast-temp-group', this.onCreateFastTempGroup)
    },
    beforeUnmount() {
      bfglob.off('add_global_dynamic_group', this.updateDatatableBody)
      bfglob.off('update_global_dynamic_group', this.updateDatatableBody)
      bfglob.off('delete_global_dynamic_group', this.updateDatatableBody)

      bfglob.off('add_global_dynamic_group_detail', this.updateDatatableBody)
      bfglob.off('update_global_dynamic_group_detail', this.updateDatatableBody)
      bfglob.off('remove_global_dynamic_group_detail', this.updateDatatableBody)
      bfglob.off('create-fast-temp-group', this.onCreateFastTempGroup)
    },
    components: {
      DataFormEditor,
      dynamicGroupTree: defineAsyncComponent(() => import('@/components/common/dynamicGroupTree')),
      memberInfo: defineAsyncComponent(() => import('@/components/common/DynamicGroupMemberInfo.vue')),
    },
    watch: {
      fullscreen: {
        handler(newVal) {
          bfglob.emit(this.dataTable.name, newVal)
        },
      },
    },
    computed: {
      memberNodes() {
        const devices = this.members.devices || []
        const groups = this.members.groups || []
        return this.generateMemberInfo(groups, devices)
      },
      showIsForceDelete() {
        return this.deleteRow?.orgIsVirtual === DbOrgIsVirtual.TaskGroup
      },
      disableIsForceDelete() {
        return this.showIsForceDelete && this.deleteRow?.dynamicGroupState === DynamicGroupState.Expired
      },
      voipSpeakInfo() {
        return bfglob.userInfo.setting.voipSpeakInfo
      },
      tempGroupDetailStatus() {
        //临时组：1：正常 2：被优先级高的抢占 10:已失效
        return {
          [MemberState.Normal]: this.$t('dynamicGroup.normal'),
          [MemberState.Preempted]: this.$t('dynamicGroup.preempted'),
          [MemberState.Expired]: this.$t('dynamicGroup.expired'),
        }
      },
      taskGroupDetailStatus() {
        //任务组：1:正常/已应答加入，2：被优先级高的抢占， 4:未应答加入 5:已应答退出 6:未应答退出
        return {
          [MemberState.Normal]: this.$t('dynamicGroup.normal'),
          [MemberState.Preempted]: this.$t('dynamicGroup.preempted'),
          [MemberState.JoinWithoutAnswer]: this.$t('dynamicGroup.joinWithoutAnswer'),
          [MemberState.AnsweredExit]: this.$t('dynamicGroup.answeredExit'),
          [MemberState.ExitWithoutAnswer]: this.$t('dynamicGroup.exitWithoutAnswer'),
        }
      },
      groupDetailStatus() {
        return {
          [DynamicGroupType.TempGroup]: this.tempGroupDetailStatus,
          [DynamicGroupType.TaskGroup]: this.taskGroupDetailStatus,
        }
      },
      dynamicGroupStatus() {
        // 1: 正常 10: 失效/删除中
        return {
          [DynamicGroupState.Normal]: this.$t('dynamicGroup.normal'),
          [DynamicGroupState.Expired]: this.$t('dynamicGroup.expired'),
        }
      },
      dynamicGroupTypes() {
        return {
          [DbOrgIsVirtual.TaskGroup]: this.$t('dynamicGroup.taskGroup'),
          [DbOrgIsVirtual.TempGroup]: this.$t('dynamicGroup.tempGroup'),
          [DbOrgIsVirtual.FastTempGroup]: `(${this.$t('dynamicGroup.fast')})${this.$t('dynamicGroup.tempGroup')}`,
        }
      },
      dynamicGroupDetailTypes() {
        // 1: 终端 2: 单位 3: 组呼加入
        return {
          [MemberType.Device]: this.$t('dynamicGroup.device'),
          [MemberType.ListenGroupDevice]: this.$t('dynamicGroup.device'),
          // [MemberType.ListenGroupDevice]: this.$t('dynamicGroup.groupCallDevice'),
          [MemberType.Group]: this.$t('dynamicGroup.groupCall'),
        }
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'parentOrgId',
            width: this.isFR ? '120px' : '100px',
            render: data => {
              const parent = bfglob.gorgData.get(data)
              return parent?.orgShortName ?? ''
            },
          },
          {
            title: this.$t('dynamicGroup.groupName'),
            data: 'orgShortName',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.type'),
            data: 'orgIsVirtual',
            width: this.isFR ? '120px' : '100px',
            render: data => {
              return this.dynamicGroupTypes[data] ?? ''
            },
          },
          {
            title: this.$t('dialog.status'),
            data: 'dynamicGroupState',
            width: '100px',
            render: data => {
              return this.dynamicGroupStatus[data] ?? 'Unknown'
            },
          },
          {
            title: 'DMRID',
            data: 'dmrId',
            width: '135px',
            render: data => {
              return formatDmrIdLabel(data)
            },
          },
        ]
      },
      detailHead() {
        return [
          {
            title: this.$t('dialog.index'),
            data: null,
            defaultContent: '',
            width: '60px',
            render: (data, type, row, meta) => {
              return meta.row + 1
            },
          },
          {
            title: this.$t('dynamicGroup.memberOrg'),
            data: 'memberOrgId',
            width: '100px',
            render: (data, _, row) => {
              return getMemberOrgShortName(row)
            },
          },
          {
            title: this.$t('dynamicGroup.memberName'),
            data: null,
            width: '100px',
            render: (data, _, row) => {
              // 根据数据类型，查找成员名称
              if (row.isDeviceGroup === 2) {
                return bfglob.gorgData.getDataMaybeNoPerm(row.groupRid)?.orgShortName ?? ''
              }
              return bfglob.gdevices.getDataMaybeNoPerm(row.deviceRid)?.selfId ?? ''
            },
          },
          {
            title: this.$t('dialog.type'),
            data: 'isDeviceGroup',
            width: '80px',
            render: (data, _, row) => {
              // 因接收组加入的终端，显示出对应的接收组名称，格式：终端(接收组名称)
              if (data === MemberType.ListenGroupDevice) {
                const listenGroupName = bfglob.gorgData.get(row.groupRid)?.orgShortName ?? '-'
                return `${this.dynamicGroupDetailTypes[data]}(${listenGroupName})`
              }
              return this.dynamicGroupDetailTypes[data] ?? 'Unknown'
            },
          },
          {
            title: this.$t('dialog.status'),
            data: 'memberState',
            width: '80px',
            render: (data, __, row) => {
              const status = this.groupDetailStatus[row.dynamicGroupType]
              return status?.[data] ?? 'Unknown'
            },
          },
          {
            title: 'DMRID',
            data: null,
            width: '135px',
            render: (_, __, row) => {
              return formatDmrIdLabel(row.isDeviceGroup === 2 ? row.groupDmrid : row.deviceDmrid) || ''
            },
          },
        ]
      },
      rules() {
        const orgShortNameRule = []
        if (!this.isFastDynamicGroup) {
          orgShortNameRule.unshift(validateRules.required())
        }

        return {
          orgShortName: orgShortNameRule,
        }
      },
      DbOrgIsVirtual() {
        return DbOrgIsVirtual
      },
    },
    activated() {
      // 处理联网通话打开的创建快捷动态组逻辑
      this.$route.params = getRouteParams(this.$route.name)
      if (this.$route.params.fastCreateDynamicGroup) {
        this.onCreateFastTempGroup(this.$route.params)
      }
    },
    deactivated() {
      // 窗口关闭时，清除已订阅的快捷临时组事件
      eventBus.off(addFastTempGroupSuccess)
    },
  }
</script>

<style lang="scss">
  .el-dialog.data-form-dialog.dynamic-group-editor {
    width: 100%;

    &:not(.is-fullscreen) {
      max-width: 1000px;

      .el-dialog__body {
        margin: 0 auto;
        max-width: 800px;
      }
    }

    .el-dialog__body {
      display: flex;
      gap: 10px;

      & > * {
        flex: auto;
      }
    }

    .form-editor-wrapper,
    .dynamic-group-tree--wrapper {
      height: 480px;
    }

    .dynamic-group-tree--wrapper {
      flex: none;
      max-width: 360px;
      width: 100%;
      border-left: 1px solid #eee;
      margin-left: 10px;
      padding-left: 10px;
      margin: 0 auto;

      .fancytree-container.fancytree-ext-grid {
        overflow-y: hidden;
      }
    }

    &.is-fullscreen {
      display: flex;
      flex-direction: column;

      .el-dialog__body {
        flex: auto;
        flex-direction: column;
      }

      .form-editor-wrapper {
        height: 150px;
      }

      .dynamic-group-tree--wrapper {
        min-height: 200px;
      }

      .form-editor-wrapper {
        flex: none;
      }

      .dynamic-group-tree--wrapper {
        width: 100%;
        border-left: none;
        border-top: 1px solid #eee;
      }
    }
  }

  .dynamic-group-members-popper {
    width: 100% !important;
    left: unset !important;
  }

  .el-dialog.delete-dynamic-group-dialog {
    .el-dialog__body {
      padding: 10px 20px;
    }
  }
</style>
