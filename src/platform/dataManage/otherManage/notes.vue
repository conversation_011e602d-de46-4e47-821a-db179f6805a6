<template>
  <div id="bfnotes" class="flex h-[calc(100%_-_90px)] mt-[16px] w-full" @click="noteTitleClick">
    <el-card
      ref="notes"
      class="box-card flex-1 !bg-black/40 !text-[#FF811D] font-bold leading-[60px] tracking-[2px] !border-none !rounded-none overflow-auto h-full mx-auto shadow-none"
    >
      <div class="text-left leading-[22px]">
        <div v-for="(item, index) in notes" :key="index" class="flex">
          <span class="flex-none mr-1.5" v-text="item.time" />
          <span class="flex-auto" v-html="item.content" />
        </div>
      </div>
    </el-card>

    <el-card v-if="showSystemLog" class="system-log-card flex-1 bg-transparent !border-[10px] !border-black/40] !rounded-none">
      <template #header>
        <div class="flex justify-center items-center text-white font-bold">
          <span>{{ dialogTitle }}</span>
          <!-- <el-button icon="close" circle @click="showSystemLog = false" /> -->
        </div>
      </template>
      <system-log />
    </el-card>
  </div>
</template>

<script>
  import { defineAsyncComponent } from 'vue'
  import bfTime from '@/utils/time'

  export default {
    name: 'BfNotes',
    data() {
      return {
        notes: [],
        noteTitleClickCount: 0,
        noteTitleClickTimer: null,
        showSystemLog: false,
      }
    },
    methods: {
      noteTitleClick() {
        if (this.noteTitleClickTimer === null) {
          // 2秒内连续点击标题5次以上,则打开系统日志窗口
          this.noteTitleClickTimer = setTimeout(() => {
            this.noteTitleClickCount = 0
            this.noteTitleClickTimer = null
          }, 2 * 1000)
        }
        // 统计点击次数
        this.noteTitleClickCount++
        if (this.noteTitleClickCount >= 5) {
          this.noteTitleClickCount = 0
          clearTimeout(this.noteTitleClickTimer)
          this.noteTitleClickTimer = null

          setTimeout(() => {
            this.showSystemLog = true
          }, 100)
        }
      },
      addnote(msg) {
        this.notes.push(Object.freeze({ time: bfTime.nowLocalTime(), content: msg }))
        // 保持日志滚动条在底部
        this.scrollbar_keep_bottom()
      },
      // 定时清除日志，只保留最后的200条记录
      checked_notes_logs() {
        const len = this.notes.length
        if (len > 400) {
          this.notes.splice(0, len - 200)
        }
      },
      scrollbar_keep_bottom() {
        var notes = this.$refs.notes
        if (!notes) {
          return
        }
        var el = notes.$el
        if (!el) {
          return
        }
        el.scrollTop = el.scrollHeight
      },
    },
    mounted() {
      bfglob.off('addnote')
      this.notes = bfglob.cacheNotes
      delete bfglob.cacheNotes
      bfglob.on(
        'addnote',
        function (msg) {
          this.addnote(msg)
        }.bind(this)
      )
      // 监听日志滚动条保持在底部消息
      bfglob.on(
        'notes_scrollbar_keep_bottom',
        function () {
          setTimeout(this.scrollbar_keep_bottom, 50)
        }.bind(this)
      )
      bfglob.emit('notes_scrollbar_keep_bottom')
      bfglob.emit('notesLoaded')
      bfglob.on('remove-tag', tag => {
        if (tag.nav.index === 'notes') {
          this.showSystemLog = false
        }
      })
    },
    activated() {
      bfglob.emit('notes_scrollbar_keep_bottom')
    },
    computed: {
      fullscreen() {
        return this.bfmaxi ? true : !(this.$root.layoutLevel > 0)
      },
      dialogTitle() {
        return this.$t('nav.systemLog')
      },
    },
    beforeMount() {
      // 10分钟检测一次日志数量
      setInterval(this.checked_notes_logs, 10 * 60 * 1000)
    },
    components: {
      SystemLog: defineAsyncComponent(() => import('@/components/secondary/systemLog.vue')),
    },
  }
</script>

<style lang="scss">
  .system-log-card {
    .el-card__body {
      padding: 0;
      width: 100%;
      height: 98%;
      color: white;
    }

    .el-card__header {
      padding: 4px 4px 4px 12px;

      span {
        font-size: 18px;
        line-height: 28px;
      }
    }
  }
</style>
