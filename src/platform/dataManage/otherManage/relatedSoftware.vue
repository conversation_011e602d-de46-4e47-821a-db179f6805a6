<template>
  <!-- 模板部分保持不变 -->
  <el-main class="text-center related-software" v-html="softwareHtml" />
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue'
  // 假设您使用了 vue-i18n，如果未使用，可以移除这两行并修改错误处理逻辑
  import { useI18n } from 'vue-i18n'
  const { t } = useI18n()

  // --- 常量定义 ---
  // 将请求间隔定义为常量，便于维护
  const REQUEST_SOFTWARE_INTERVAL = 10 * 60 * 1000 // 10 分钟
  // 软件资源的基础 URL
  const SOFTWARE_URL = '/bf8100/assets/software/'

  // --- 响应式状态 ---
  // 使用 ref 创建响应式变量来存储 HTML 内容，并指定类型为 string
  const softwareHtml = ref<string>('')

  // --- 定时器变量 ---
  // 定义一个变量来持有定时器的 ID，以便后续可以清除它
  // ReturnType<typeof setInterval> 是一个健壮的类型，可以跨平台（Node.js, Browser）
  let timer: ReturnType<typeof setInterval> | null = null

  // --- 方法定义 ---
  /**
   * 异步获取并处理软件相关的 HTML 内容
   */
  const getSoftwareHtml = async () => {
    console.log('Fetching related software HTML...')
    try {
      // 使用 Date.now() 作为查询参数来防止浏览器缓存
      const response = await fetch(`${SOFTWARE_URL}?${Date.now()}`)

      // 检查请求是否成功
      if (!response.ok) {
        throw new Error(`Network response was not ok, status: ${response.status}`)
      }

      const htmlStr = await response.text()

      // 更新 softwareHtml 的值。注意在 <script setup> 中需要通过 .value 来访问 ref 的值
      // 使用正则表达式将相对路径的 href 替换为完整的下载链接
      softwareHtml.value = htmlStr.replace(/href="/gi, `download href="${SOFTWARE_URL}`)
    } catch (error) {
      // 捕获并打印错误
      // 假设 bfglob.console 是一个全局对象，这里简化为标准 console.error
      // 使用 t 函数来显示国际化的错误消息
      console.error(t('software.refreshFailed'), error)
    }
  }

  // --- 生命周期钩子 ---
  onMounted(() => {
    // 1. 组件首次挂载时，立即执行一次数据请求
    getSoftwareHtml()

    // 2. 设置一个定时器，以固定的间隔重复请求数据
    timer = setInterval(getSoftwareHtml, REQUEST_SOFTWARE_INTERVAL)
  })

  onUnmounted(() => {
    // 当组件被卸载时，清除定时器，防止内存泄漏
    if (timer) {
      clearInterval(timer)
      console.log('RelatedSoftware timer cleared.')
    }
  })
</script>

<style lang="scss">
  /* 样式部分保持不变 */
  .related-software {
    a {
      color: #007bff;
    }
  }
</style>
