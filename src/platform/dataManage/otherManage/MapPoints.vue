<template>
  <div class="map-points-wrapper">
    <data-form-editor
      ref="formEditor"
      class="page-mapPoints"
      :title="dlgTitle"
      :tableName="dataTable.name"
      :column="dthead"
      :data="dataTable.body"
      :get-new-data="getNewData"
      :parseDataForEdit="parseDataForEdit"
      :beforeClose="beforeClose"
      :getFormRef="() => $refs.mapPointDataEditorForm"
      @row-new="onNew"
      @row-update="onUpdate"
      @row-delete="onDelete"
      @row-dblclick="tableRowDbclick"
      @close="customImageLoaded = false"
    >
      <template #form="{ formData }">
        <el-form
          ref="mapPointDataEditorForm"
          :model="formData"
          :label-width="labelWidth"
          :validate-on-rule-change="false"
          :rules="rules"
          class="mapPointForm grid grid-cols-1"
        >
          <el-form-item :label="$t('dialog.parentOrg')" prop="orgId">
            <div class="parent-org-selector">
              <el-select
                v-model="formData.orgId"
                :placeholder="$t('dialog.select')"
                filterable
                clearable
                :disabled="orgAddOrUpdateMapPoint"
                :no-match-text="$t('dialog.noMatchText')"
              >
                <el-option v-for="item in selOrgList" :key="item.rid" :label="item.label" :value="item.rid" />
              </el-select>

              <el-button v-if="orgAddOrUpdateMapPoint" type="danger" icon="delete" circle @click="cancelEditPointWithOrg" />
            </div>
          </el-form-item>
          <el-form-item :label="$t('dialog.serialNo')" prop="selfId">
            <el-input v-model="formData.selfId" :maxlength="16" />
          </el-form-item>
          <el-form-item :label="$t('dialog.tagName')" prop="pointName">
            <el-input v-model="formData.pointName" :maxlength="16" />
          </el-form-item>
          <el-form-item class="lngLat-form-item">
            <template #label>
              <span class="form-item-label" :title="$t('dialog.lngLat')">{{ $t('dialog.lngLat') }}</span>
            </template>
            <lon-lat v-model:lon="formData.lon" v-model:lat="formData.lat" />
          </el-form-item>
          <el-form-item :label="$t('dialog.showLevel')" prop="startShowLevel">
            <el-input-number v-model="formData.startShowLevel" class="mapMarkerHW" :min="1" :max="18" />
          </el-form-item>
          <el-form-item :label="$t('dialog.mapMarkerW')" prop="markerWidth">
            <el-input-number v-model="formData.markerWidth" class="mapMarkerHW" :step="16" :min="16" :max="128" />
          </el-form-item>
          <el-form-item :label="$t('dialog.mapMarkerH')" prop="markerHeight">
            <el-input-number v-model="formData.markerHeight" class="mapMarkerHW" :step="16" :min="16" :max="128" />
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item prop="imgOrColorPoint" class="form-item-ellipsis">
                <template #label>
                  <span class="form-item-label" :title="$t('dialog.mapPointType')">
                    {{ $t('dialog.mapPointType') }}
                  </span>
                </template>
                <el-select v-model="formData.imgOrColorPoint" :placeholder="$t('dialog.select')">
                  <el-option v-for="(item, index) in mapPointTypes" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="!formData.imgOrColorPoint" :label="$t('dialog.selectColor')" prop="colorRGB" class="checkedCorlor">
                <el-color-picker v-model="formData.colorRGB" color-format="rgb" />
              </el-form-item>
              <el-form-item v-else class="image-select" :label="$t('dialog.selectImage')" prop="image">
                <el-tooltip effect="dark" :content="$t('dialog.clickCheckImg')" placement="top">
                  <div class="image-preview" @click="openSelectImage">
                    <img :src="image.fileContent" class="h-full w-full" />
                  </div>
                </el-tooltip>
                <el-tooltip effect="dark" :content="$t('dialog.clearCheckedImg')" placement="top">
                  <el-button circle icon="delete" class="clear_user_btn" @click="clearUserImage" />
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item :label="$t('dialog.notes')" prop="note">
            <el-input v-model="formData.note" type="textarea" resize="none" :rows="3" :maxlength="128" />
          </el-form-item>
        </el-form>
      </template>
    </data-form-editor>

    <custom-image v-if="customImageLoaded" v-model="image" v-model:visible="imageVisible" class="map-image-manager" />
  </div>
</template>

<script>
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfprocess from '@/utils/bfprocess'
  import bfutil from '@/utils/bfutil'
  import bfCrypto from '@/utils/crypto'
  import maputil from '@/utils/map'
  import bfNotify from '@/utils/notify'
  import { v1 as uuid } from 'uuid'
  import vueMixin from '@/utils/vueMixin'
  import DataFormEditor from '@/components/common/DataFormEditor.vue'
  import validateRules from '@/utils/validateRules'
  import { useRouteParams } from '@/router'
  import { defineAsyncComponent } from 'vue'

  const dbSubject = `db.${bfglob.sysId}`
  const defaultImage = {
    rid: '*************-2222-2222-************',
    fileName: 'default_user.png',
    fileContent: bfutil.default_user(),
    hash: bfCrypto.sha256(bfutil.default_user()),
  }
  const defaultData = {
    rid: '',
    orgId: bfutil.getBaseDataOrgId(),
    selfId: bfutil.getBaseSelfId(),
    pointName: '',
    lon: '',
    lat: '',
    startShowLevel: 15,
    markerWidth: 16,
    markerHeight: 16,
    imgOrColorPoint: 0,
    colorRGB: 'rgb(255, 0, 0)',
    imageObj: null,
    src: bfutil.default_user(),
    note: '',
  }
  const { getRouteParams } = useRouteParams()

  export default {
    mixins: [vueMixin],
    data() {
      const allMapPointData = bfglob.gmapPoints.getAll()
      return {
        allMapPointData,
        defaultData,
        dataTable: {
          name: 'mapPointsTable',
          body: bfutil.objToArray(bfglob.gmapPoints.getAll()),
        },
        imageVisible: false,
        customImageLoaded: false,
        image: { ...defaultImage },
        selOrgList: bfglob.gorgData.getList(),
        orgAddOrUpdateMapPoint: false,
      }
    },
    methods: {
      // 返回一个新的默认参数对象
      getNewData() {
        return { ...defaultData }
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        row.imageObj = this.image
        if (row.imgOrColorPoint === 1 && !row.imageObj.rid) {
          // 有选中图片(图片还未上传到数据库不存在rid)
          // 上传图片到数据库并返回包装过的数据 row
          row.imageObj = await this.setDBImg(row)
        }
        const isOk = await this.add_mapPoint_data(row, dbCmd.DB_MAP_POINT_INSERT)
        if (!isOk) return

        // 如果是添加单位相关的标记点，则不再继续添加新的一行，返回到单位页面
        if (this.orgAddOrUpdateMapPoint) {
          this.toOrgs()
          done()
          return
        }

        if (addNewCb) {
          const __data = this.getNewData()
          __data.orgId = row.orgId
          __data.selfId = bfutil.customNumberIncrement(row.selfId)
          __data.startShowLevel = row.startShowLevel
          __data.imgOrColorPoint = row.imgOrColorPoint
          // 重置标签页数据
          bfutil.resetForm(this, 'mapPointDataEditorForm')
          addNewCb(__data)
          return
        }
        done()
      },
      async onDelete(row) {
        await this.delete_mapPoint_data(row, dbCmd.DB_MAP_POINT_DELETE)
      },
      async onUpdate(row, done) {
        row.imageObj = this.image
        if (row.imgOrColorPoint === 1 && !row.imageObj.rid) {
          // 图片标记点 且更换了图片
          row.imageObj = await this.setDBImg(row)
        }
        const isOk = await this.update_mapPoint_data(row, dbCmd.DB_MAP_POINT_UPDATE)
        if (!isOk) return

        // 如果是更新单位相关的标记点，则返回到单位页面
        if (this.orgAddOrUpdateMapPoint) {
          this.toOrgs()
        }

        done()
      },
      parseDataForEdit(formData) {
        this.image = bfglob.gimages.get(formData.pointImg) ?? { ...defaultImage }
        formData.colorRGB = `rgb(${formData.colorR},${formData.colorG},${formData.colorB})`
      },
      beforeClose(onClose, editStatus) {
        if (this.orgAddOrUpdateMapPoint) {
          this.toOrgs()
        }
        onClose()
      },
      // 同步dataTable数据
      upsetDataTableBody() {
        this.dataTable.body = bfutil.objToArray(bfglob.gmapPoints.getAll())
      },
      openSelectImage() {
        this.customImageLoaded = true
        this.imageVisible = true
      },
      setDBImg(row) {
        return bfprocess.set_db_image(row.orgId, row.imageObj.fileName, row.imageObj.fileContent, dbCmd.DB_IMAGE_INSERT).catch(e => {
          return defaultImage
        })
      },
      cancelEditPointWithOrg() {
        this.unBindWittOrg()
        bfglob.emit('close_org_editor_dialog')
      },
      unBindWittOrg() {
        this.orgAddOrUpdateMapPoint = false
      },
      tableRowDbclick(data) {
        // this.$router.push('/main')
        bfNotify.messageBox(this.$t('dialog.mapPointJumpTip'))
        maputil.dbclickJumpToMarker(data, 3)
      },
      add_mapPoint_data(data, add_cmd) {
        const rgb = data.colorRGB || 'rgb(255,0,0)'
        const __rgb = data.colorRGB.slice(4, -1).split(',')
        const msgObj = {
          rid: this.orgAddOrUpdateMapPoint ? data.rid : uuid(),
          orgId: data.orgId || '00000000-0000-0000-0000-000000000000',
          selfId: data.selfId,
          pointName: data.pointName,
          mapDisplayName: data.pointName,
          lon: data.lon,
          lat: data.lat,
          startShowLevel: data.startShowLevel,
          colorRGB: rgb,
          colorR: parseInt(__rgb[0]),
          colorG: parseInt(__rgb[1]),
          colorB: parseInt(__rgb[2]),
          imgOrColorPoint: data.imgOrColorPoint,
          pointImg: data.imageObj.rid,
          markerWidth: data.markerWidth,
          markerHeight: data.markerHeight,
          note: data.note,
          fileContent: data.imageObj.fileContent,
          markerType: this.orgAddOrUpdateMapPoint ? data.markerType : 0,
        }
        return bfproto
          .sendMessage(add_cmd, msgObj, 'db_map_point', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('add mapPoint res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
              bfglob.emit('add_global_mapPointData', msgObj)
              defaultData.orgId = msgObj.orgId
              defaultData.selfId = bfutil.customNumberIncrement(msgObj.selfId)
              defaultData.startShowLevel = msgObj.startShowLevel
              defaultData.imgOrColorPoint = msgObj.imgOrColorPoint
              // 添加查询日志
              const note = this.$t('dialog.add') + msgObj.selfId + ' / ' + msgObj.pointName + this.$t('msgbox.mapPointData')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_map_point_org_id_fkey')) {
                bfNotify.warningBox(this.$t('msgbox.notHasOrg'))
              } else if (rpc_cmd_obj.resInfo.includes('db_map_point_self_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatNo'))
              } else {
                bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
              }
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('add mapPoint timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            return Promise.resolve(false)
          })
      },
      update_mapPoint_data(data, up_db_cmd) {
        const rgb = data.colorRGB || 'rgb(255,0,0)'
        const __rgb = data.colorRGB.slice(4, -1).split(',')
        const msgObj = {
          ...data,
          mapDisplayName: data.pointName,
          colorRGB: rgb,
          colorR: parseInt(__rgb[0]),
          colorG: parseInt(__rgb[1]),
          colorB: parseInt(__rgb[2]),
          pointImg: data.imageObj.rid,
          fileContent: data.imageObj.fileContent,
        }

        return bfproto
          .sendMessage(up_db_cmd, msgObj, 'db_map_point', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('update mapPoint res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
              // 更新全局组织机构数据
              bfglob.emit('update_global_mapPointData', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.update') + msgObj.selfId + ' / ' + msgObj.pointName + this.$t('msgbox.mapPointData')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_map_point_org_id_fkey')) {
                bfNotify.warningBox(this.$t('msgbox.notHasOrg'))
              } else if (rpc_cmd_obj.resInfo.includes('db_map_point_self_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatNo'))
              } else {
                bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
              }
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('update mapPoint timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            return Promise.resolve(false)
          })
      },
      delete_mapPoint_data(data, del_cmd) {
        const msgObj = {
          ...data,
        }

        return bfproto
          .sendMessage(del_cmd, msgObj, 'db_map_point', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('update mapPoint res:', rpc_cmd_obj)

            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.emit('delete_global_mapPointData', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.delete') + msgObj.selfId + ' / ' + msgObj.pointName + this.$t('msgbox.mapPointData')
              bfglob.emit('addnote', note)
            } else {
              bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            }
          })
          .catch(err => {
            bfglob.console.warn('delete mapPoint timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            return Promise.resolve(false)
          })
      },
      clearUserImage() {
        this.image = { ...defaultImage }
      },
      toOrgs() {
        this.unBindWittOrg()
        this.$router.back()
      },
    },
    components: {
      CustomImage: defineAsyncComponent(() => import('@/components/common/customImage.vue')),
      LonLat: defineAsyncComponent(() => import('@/components/common/lonLat.vue')),
      DataFormEditor,
    },
    computed: {
      dlgTitle() {
        return this.$t('dialog.mapMarker')
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.picture'),
            data: null,
            render: function (data, type, row, meta) {
              if (row.imgOrColorPoint === 1) {
                return `<img src='${row.fileContent}' class='custorm_pic_style'>`
              } else {
                return `<b style='background-color:${row.colorRGB}' class='mapPoint_bg'></b>`
              }
            },
            width: '50px',
          },
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '90px',
          },
          {
            title: this.$t('dialog.serialNo'),
            data: 'selfId',
            width: '90px',
          },
          {
            title: this.$t('dialog.tagName'),
            data: 'pointName',
            width: this.isFR ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.lon'),
            data: 'lon',
            width: '120px',
          },
          {
            title: this.$t('dialog.lat'),
            data: 'lat',
            width: '120px',
          },
          {
            title: this.$t('dialog.showLevel'),
            data: 'startShowLevel',
            width: this.isFR ? '130px' : this.isEN ? '90px' : '60px',
          },
          {
            title: this.$t('dialog.mapMarkerW'),
            data: 'markerWidth',
            width: '60px',
          },
          {
            title: this.$t('dialog.mapMarkerH'),
            data: 'markerHeight',
            width: '60px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '150px',
          },
        ]
      },
      rules() {
        return {
          orgId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'change',
            },
          ],
          selfId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              max: 16,
              message: this.$t('msgbox.maxLen') + '16' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
          pointName: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              max: 16,
              message: this.$t('msgbox.maxLen') + '16' + this.$t('msgbox.character'),
              trigger: 'blur',
            },
          ],
          lon: [
            validateRules.required('blur'),
            {
              validator: function (rule, value, callback) {
                bfutil.mustNumberRule(rule, value, callback)
              },
              trigger: 'blur',
            },
          ],
          lat: [
            validateRules.required('blur'),
            {
              validator: function (rule, value, callback) {
                bfutil.mustNumberRule(rule, value, callback)
              },
              trigger: 'blur',
            },
          ],
        }
      },
      mapPointTypes() {
        return [
          {
            value: 0,
            label: this.$t('dialog.colorMapPoint'),
          },
          {
            value: 1,
            label: this.$t('dialog.imageMapPoint'),
          },
        ]
      },
      labelWidth() {
        return this.isFR ? '150px' : this.isEN ? '130px' : '100px'
      },
      toOrgsMsg() {
        return this.$route.params.addOrUpdate ? this.$t('dialog.mapPointsToOrgsEdit') : this.$t('dialog.mapPointsToOrgsAdd')
      },
    },
    mounted() {
      bfglob.on('add_global_mapPointData', this.upsetDataTableBody)
      bfglob.on('update_global_mapPointData', this.upsetDataTableBody)
      bfglob.on('delete_global_mapPointData', this.upsetDataTableBody)
    },
    activated() {
      this.$route.params = getRouteParams(this.$route.name)
      const addOrgMapPoint = this.$route.params.addOrgMapPoint
      if (addOrgMapPoint) {
        this.orgAddOrUpdateMapPoint = addOrgMapPoint
        // addOrUpdate :  0: 添加, 1: 修改
        if (this.$route.params.addOrUpdate) {
          this.$refs.formEditor?.onEdit(this.$route.params.mapPointData)
        } else {
          this.$refs.formEditor?.addNewData(this.$route.params.dbMapPoint)
          if (this.$route.params.orgNoMapPointMsg) {
            setTimeout(() => {
              bfNotify.messageBox(this.$t('dialog.orgNoMapPointMsg'), 'warning')
            })
          }
        }
      }
    },
  }
</script>

<style lang="scss">
  .el-form.mapPointForm {
    width: 460px;

    .el-form-item.checkedCorlor {
      .el-form-item__content {
        height: 32px;
      }
    }

    .el-form-item {
      .el-form-item__content {
        .mapMarkerHW.el-input-number.el-input-number--small {
          width: 100%;
        }
      }
    }
  }

  .image-select {
    .el-form-item.lngLat-form-item {
      .el-form-item__content {
        display: flex;
        flex-wrap: nowrap;
      }
    }

    .el-form-item__content {
      display: flex;
      align-items: center;
      gap: 10px;

      .image-preview {
        width: 32px;
        height: 32px;
        border: 1px solid #eef0f3;
        border-radius: 4px;
        padding: 1px;
        cursor: pointer;
      }

      @media (min-width: 2560px) {
        .image-preview {
          width: 40px;
          height: 40px;
        }
      }
    }
  }

  .parent-org-selector {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    & > .el-select {
      flex: auto;
    }

    & > .el-button {
      flex: none;
      margin-left: 10px;
    }
  }

  .el-color-picker__trigger,
  .el-color-picker__color {
    border: none;
  }

  .el-color-picker__color-inner {
    border-radius: 50%;
  }

  .el-color-picker {
    cursor: pointer;
  }

  .checkedCorlor .el-form-item__content {
    line-height: 26px;
  }

  .mapPoint_bg,
  .custorm_pic_style {
    width: 16px;
    height: 16px;
    display: inline-block;
    border-radius: 50%;
    vertical-align: sub;
  }

  .custorm_pic_style {
    width: 18px;
    height: 18px;
  }
</style>
