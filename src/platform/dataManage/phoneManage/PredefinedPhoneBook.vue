<template>
  <data-form-editor
    ref="formEditor"
    class="page-predefinedPhoneBook"
    :title="$t('dialog.predefinedPhoneBook')"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :getNewData="getNewData"
    :getFormRef="() => $refs.preDataEditorForm"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
  >
    <template #form="{ formData }">
      <el-form ref="preDataEditorForm" :model="formData" :label-width="formLabelWidth" :rules="rules" :validate-on-rule-change="false" class="grid grid-cols-1">
        <el-form-item :label="$t('dialog.parentOrg')" prop="orgId">
          <el-select v-model="formData.orgId" :placeholder="$t('dialog.select')" filterable clearable :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="(item, index) in orgDataList" :key="index" :label="item.label" :value="item.rid" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('dialog.name')" prop="phoneName">
          <el-input v-model="formData.phoneName" :maxlength="16" />
        </el-form-item>

        <el-form-item :label="$t('dialog.telephoneNo')" prop="phoneNo">
          <el-input v-model="formData.phoneNo" :maxlength="16" />
        </el-form-item>
      </el-form>
    </template>
  </data-form-editor>
</template>

<script>
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfutil, { getDbSubject } from '@/utils/bfutil'

  import bfNotify from '@/utils/notify'
  import bfTime from '@/utils/time'
  import validateRules from '@/utils/validateRules'
  import vueMixin from '@/utils/vueMixin'
  import { v1 as uuid } from 'uuid'
  import DataFormEditor from '@/components/common/DataFormEditor.vue'

  const compName = 'vpredefinedPhoneBook'
  const defaultData = {
    rid: '',
    orgId: '',
    phoneName: '',
    phoneNo: '',
  }
  export default {
    name: compName,
    mixins: [vueMixin],
    data() {
      return {
        compName: compName,
        dataTable: {
          body: bfutil.objToArray(bfglob.gphoneBook.getAll()),
          name: `${compName}Table`,
        },
        orgDataList: bfglob.gorgData.getList(),
      }
    },
    methods: {
      async onDelete(row) {
        await this.delete_predefinedPhoneBook_data(row, dbCmd.DB_PHONE_NO_LIST_DELETE)
      },
      async onUpdate(row, done) {
        const isOk = await this.update_predefinedPhoneBook_data(row, dbCmd.DB_PHONE_NO_LIST_UPDATE)
        if (!isOk) return
        done()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        const isOk = await this.add_predefinedPhoneBook_data(row, dbCmd.DB_PHONE_NO_LIST_INSERT)
        if (!isOk) return
        if (addNewCb) {
          // 重置标签页数据
          const __data = this.getNewData()
          __data.orgId = bfutil.getBaseDataOrgId()
          bfutil.resetForm(this, 'preDataEditorForm')
          addNewCb(__data)
          return
        }
        done()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return { ...defaultData }
      },
      // 数据功能操作
      add_predefinedPhoneBook_data(data, add_cmd) {
        const msgObj = {
          ...data,
          rid: uuid(),
          lastModifyTime: bfTime.nowUtcTime(),
          setting: '{}',
        }

        return bfproto
          .sendMessage(add_cmd, msgObj, 'db_phone_no_list', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('add db_phone_no_list res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
              bfglob.emit('add_global_db_phone_no_list', msgObj)

              // 添加日志
              const note = this.$t('dialog.add') + msgObj.phoneName + this.$t('dialog.predefinedPhoneBook') + this.$t('dialog.contact')
              bfglob.emit('addnote', note)
            } else {
              // if (rpc_cmd_obj.resInfo.bfhas('db_device_self_id_key')) {
              //   bfNotify.warningBox(this.$t("msgbox.repeatDevsName"));
              //   return
              // }
              // if (rpc_cmd_obj.resInfo.bfhas('db_device_dmr_id_key')) {
              //   bfNotify.warningBox(this.$t("msgbox.repeatDMRID"));
              //   return
              // }
              if (rpc_cmd_obj.resInfo.includes('db_phone_no_list_phone_name_key')) {
                bfNotify.warningBox(this.$t('msgbox.phoneBookNameNo'))
              }
              // this.showAddErrorMsg()
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('add db_phone_no_list timeout:', err)
            this.showAddErrorMsg()
            return Promise.resolve(false)
          })
      },
      update_predefinedPhoneBook_data(data, up_db_cmd) {
        const msgObj = {
          ...data,
          lastModifyTime: bfTime.nowUtcTime(),
        }
        const oldData = bfglob.gphoneBook.get(data.rid)
        return bfproto
          .sendMessage(up_db_cmd, msgObj, 'db_phone_no_list', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('update db_phone_no_list res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
              bfglob.emit('update_global_db_phone_no_list', msgObj)

              // 添加日志
              let targetStr = msgObj.phoneName
              if (oldData.phoneName) {
                targetStr = oldData.phoneName + ' / ' + targetStr
              }
              const note = this.$t('dialog.update') + targetStr + this.$t('dialog.predefinedPhoneBook') + this.$t('dialog.contact')
              bfglob.emit('addnote', note)
            } else {
              // if (rpc_cmd_obj.resInfo.bfhas('db_device_self_id_key')) {
              //   bfNotify.warningBox(this.$t("msgbox.repeatDevsName"));
              //   return
              // }
              // if (rpc_cmd_obj.resInfo.bfhas('db_device_dmr_id_key')) {
              //   bfNotify.warningBox(this.$t("msgbox.repeatDMRID"));
              //   return
              // }
              if (rpc_cmd_obj.resInfo.includes('db_phone_no_list_phone_name_key')) {
                bfNotify.warningBox(this.$t('msgbox.phoneBookNameNo'))
              }
              this.showAddErrorMsg()
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('update db_phone_no_list timeout:', err)
            this.showUpdateErrorMsg()
            return Promise.resolve(false)
          })
      },
      delete_predefinedPhoneBook_data(data, del_cmd) {
        const msgObj = {
          ...data,
        }
        bfproto
          .sendMessage(del_cmd, msgObj, 'db_phone_no_list', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('delete db_phone_no_list res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.emit('delete_global_db_phone_no_list', msgObj)

              // 添加查询日志
              const note = this.$t('dialog.delete') + msgObj.phoneName + this.$t('dialog.predefinedPhoneBook') + this.$t('dialog.contact')
              bfglob.emit('addnote', note)
            } else {
              this.showDeleteErrorMsg()
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('delete db_phone_no_list timeout:', err)
            this.showDeleteErrorMsg()
            return Promise.resolve(false)
          })
      },
      // 同步dataTable数据
      upsetDataTableBody() {
        this.dataTable.body = bfutil.objToArray(bfglob.gphoneBook.getAll())
      },
      add_global_phone_no_list() {
        this.upsetDataTableBody()
      },
      update_global_phone_no_list() {
        this.upsetDataTableBody()
      },
      delete_global_phone_no_list() {
        this.upsetDataTableBody()
      },
    },
    computed: {
      dlgTitle() {
        return this.$t('dialog.predefinedPhoneBook')
      },
      formLabelWidth() {
        return this.isFR ? '160px' : this.isEN ? '130px' : '90px'
      },
      rules() {
        return {
          orgId: [validateRules.required()],
          phoneName: [validateRules.required()],
          phoneNo: [validateRules.required(), validateRules.mustNumber(), validateRules.telephoneNumber()],
        }
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.name'),
            data: 'phoneName',
            width: '120px',
          },
          {
            title: this.$t('dialog.telephoneNo'),
            data: 'phoneNo',
            width: this.isFR ? '160px' : '120px',
          },
        ]
      },
    },
    mounted() {
      // 监听数据变化同步datatable数据源
      bfglob.on('add_global_phone_no_list', this.add_global_phone_no_list)
      bfglob.on('update_global_phone_no_list', this.update_global_phone_no_list)
      bfglob.on('delete_global_phone_no_list', this.delete_global_phone_no_list)
    },
    components: {
      DataFormEditor,
    },
  }
</script>

<style></style>
