const env = import.meta.env

export const DEV = env.DEV

export const Enable_License = env.VITE_Enable_License ?? true

export const BASE_URL = env.BASE_URL

const DefaultProxy = {
  protocol: window.location.protocol.replace(':', ''),
  hostname: window.location.hostname,
  webPort:
    +window.location.port || (window.location.protocol === 'https:' ? 443 : 80),
}
export const ProxyServer = Object.freeze({
  protocol: env.VITE_protocol || DefaultProxy.protocol,
  hostname: env.VITE_hostname || DefaultProxy.hostname,
  webPort: env.VITE_webPort || DefaultProxy.webPort,
})

export const ProxyHref = `${ProxyServer.protocol}://${ProxyServer.hostname}:${ProxyServer.webPort}`

if (DEV) {
  console.log('envConfig.js', env, ProxyHref)
}
