import './polyfill'
import { ProxyServer } from './envConfig'
import { createApp } from 'vue'
import router from './router'
import App from '@/App.vue'
import Logs from '@/utils/logs'
import bfUserSettings from '@/utils/userSettings'
import i18n from './modules/i18n'
import '@mdi/font/css/materialdesignicons.css'
import '@/css/tailwind.css'
import '@/css/element.scss'
import '@/assets/fonts/fonts.css'
import '@/utils/setRem'
import '@/assets/css/common.css'
// import './detectNewUi'
import emitter from '@/utils/eventBus'
import { bfDrag } from '@/utils/direct'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { getLayoutLevel, isPC, getSysId } from '@/utils/bfutil'

const log = new Logs()
window.bfLogsConfigure = opts => {
  if (!opts || typeof opts === 'string') {
    opts = {
      level: ['debug', 'log', 'warn', 'error', 'none'].includes(opts)
        ? opts
        : 'debug',
    }
  }

  Logs.configure(log, opts)
}

window.bfglob = Object.assign(window.bfglob || {}, {
  console: log,
  emit(subject, ...params) {
    emitter.emit(subject, ...params)
  },
  on(subject, callback) {
    emitter.on(subject, callback)
  },
  once(subject, callback) {
    emitter.once(subject, callback)
  },
  off(subject, callback) {
    emitter.off(subject, callback)
  },
  isLogin: false,
  userInfo: {
    name: '',
    rid: '',
    orgId: '',
    origData: null,
    isSuper: false,
    setting: { ...bfUserSettings },
  },
  systemSetting: {
    clientLogo: '',
    logoConfKey: 'clientLogo',
    logoRid: '',
    clientTitle: '',
    titleConfKey: '',
    titleRid: '',
  },
  isPC: isPC(),
  sysId: null,
  sessionId: '',
  salerId: '',
  layout: getLayoutLevel(),
  timeInterval: 0,
  // 本地时间与服务器时间的差值
  serverTimeOffset: null,
  // utc time
  serverTime: '',
  allTrees: {},
  cmdReqId: new Set(), // 缓存数据操作的请求uuid
  reqMaximum: isPC() ? 20000 : 5000,
  serverRestarted: false,
  // 系统是否有设置过地图最大、小级别
  mapSetting: {
    signale: false,
    maxZoom: 18,
    minZoom: 1.5,
    rid: 'bb0716a2-dbd1-46b0-a724-e3d83cebde29',
  },
  webConfig: ProxyServer,
  fullCallDmrId: '80FFFFFF',
  systemDmrId: '00000000',
  sysIniConfig: {
    iotEnable: false,
  },
  // 运行日志缓存，打开运行日志页面后删除，日志交由运行日志页面控制
  cacheNotes: [],
})

window.onresize = () => {
  bfglob.emit('mapResize')
  bfglob.isPC = isPC()
  bfglob.emit('bflayout', getLayoutLevel())
  bfglob.map && bfglob.map.resize()
  bfglob.emit('tableResize')
}

const app = createApp(App)
// 注册@element-plus/icons-vue所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
await getSysId()
app.use(router)
app.use(i18n)
// 2. 手动挂载全局属性 (为了兼容 Options API)
// 当 legacy: false 时，vue-i18n 不会自动注入 $t, $i18n 等。
// 我们需要手动将它们添加到 app.config.globalProperties。
// i18n.global 是 `createI18n` 返回的实例的核心部分，包含了 t, d, n, locale 等所有全局 API。
app.config.globalProperties.$t = i18n.global.t
app.config.globalProperties.$i18n = i18n.global

app.directive('bfdrag', bfDrag)
app.mount('#app')
