@font-face {
  font-family: "iconfont"; /* Project id 4662181 */
  /* Color fonts */
  src: 
       url('iconfont.woff2?t=1753168379965') format('woff2'),
       url('iconfont.woff?t=1753168379965') format('woff'),
       url('iconfont.ttf?t=1753168379965') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-xz10:before {
  content: "\e6b8";
}

.icon-xz18:before {
  content: "\e6b9";
}

.icon-xz32:before {
  content: "\e6ba";
}

.icon-xz27:before {
  content: "\e6bb";
}

.icon-xz7:before {
  content: "\e6bc";
}

.icon-xz19:before {
  content: "\e6bd";
}

.icon-xz14:before {
  content: "\e6be";
}

.icon-xz2:before {
  content: "\e6bf";
}

.icon-xz11:before {
  content: "\e6c0";
}

.icon-xz28:before {
  content: "\e6c1";
}

.icon-xz12:before {
  content: "\e6c2";
}

.icon-xz13:before {
  content: "\e6c3";
}

.icon-xz0:before {
  content: "\e6c4";
}

.icon-xz3:before {
  content: "\e6c5";
}

.icon-xz1:before {
  content: "\e6c6";
}

.icon-xz4:before {
  content: "\e6c7";
}

.icon-xz38:before {
  content: "\e6ae";
}

.icon-xz35:before {
  content: "\e6af";
}

.icon-xz16:before {
  content: "\e6b0";
}

.icon-xz8:before {
  content: "\e6b1";
}

.icon-xz15:before {
  content: "\e6b2";
}

.icon-xz6:before {
  content: "\e6b3";
}

.icon-xz26:before {
  content: "\e6b4";
}

.icon-xz17:before {
  content: "\e6b5";
}

.icon-xz30:before {
  content: "\e6b6";
}

.icon-xz23:before {
  content: "\e6b7";
}

.icon-xz5:before {
  content: "\e6a7";
}

.icon-xz29:before {
  content: "\e6a8";
}

.icon-xz34:before {
  content: "\e6a9";
}

.icon-xz20:before {
  content: "\e6aa";
}

.icon-xz24:before {
  content: "\e6ab";
}

.icon-xz31:before {
  content: "\e6ac";
}

.icon-xz22:before {
  content: "\e6ad";
}

.icon-xz36:before {
  content: "\e6a4";
}

.icon-xz37:before {
  content: "\e6a3";
}

.icon-xz25:before {
  content: "\e6a2";
}

.icon-xz9:before {
  content: "\e6a0";
}

.icon-xz33:before {
  content: "\e6a5";
}

.icon-xz21:before {
  content: "\e6a1";
}

.icon-xz99:before {
  content: "\e6a6";
}

