<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_27661)">
<g clip-path="url(#clip1_0_27661)">
<g filter="url(#filter0_d_0_27661)">
<circle cx="36" cy="36" r="36" fill="#0A3E5C" fill-opacity="0.498039"/>
<circle cx="36" cy="36" r="34.8" stroke="#0398D5" stroke-opacity="0.631373" stroke-width="2.4"/>
</g>
<g filter="url(#filter1_d_0_27661)">
<circle cx="35.8081" cy="35.808" r="26.208" fill="url(#paint0_linear_0_27661)"/>
<circle cx="35.8081" cy="35.808" r="25.608" stroke="url(#paint1_linear_0_27661)" stroke-width="1.2"/>
</g>
</g>
<g clip-path="url(#clip2_0_27661)">
<circle cx="36" cy="36" r="34.8" fill="url(#paint2_linear_0_27661)" stroke="url(#paint3_linear_0_27661)" stroke-width="2.4"/>
<circle opacity="0.2" cx="35.7796" cy="35.7796" r="24.9796" fill="url(#paint4_linear_0_27661)"/>
</g>
<rect opacity="0.01" x="13.6553" y="13.6552" width="44.6897" height="44.6897" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M33.1573 22.725C32.6344 22.3842 32.0595 22.2269 31.496 22.2269C30.1209 22.227 28.814 23.1636 28.4998 24.6542L28.2106 26.026C27.961 27.2103 27.0357 28.1356 25.8514 28.3852L24.4796 28.6744C22.3782 29.1173 21.3777 31.5329 22.5503 33.3319L23.3159 34.5064C23.9768 35.5204 23.9768 36.8289 23.3159 37.8428L22.5503 39.0173C21.3777 40.8164 22.3782 43.2319 24.4796 43.6748L25.8514 43.964C27.0358 44.2136 27.9611 45.1389 28.2106 46.3232L28.4998 47.695C28.814 49.1858 30.1207 50.1224 31.496 50.1224C32.0594 50.1224 32.6346 49.965 33.1573 49.6242L34.3318 48.8587C34.8388 48.5282 35.4194 48.3629 36 48.3629C36.5806 48.3629 37.1612 48.5282 37.6682 48.8587L38.8427 49.6242C39.3655 49.9651 39.9405 50.1224 40.504 50.1224C41.8792 50.1224 43.1859 49.1856 43.5001 47.695L43.7893 46.3232C44.0388 45.1389 44.9641 44.2136 46.1484 43.964L47.5202 43.6748C49.6216 43.232 50.6222 40.8164 49.4496 39.0173L48.684 37.8428C48.0231 36.8289 48.0231 35.5204 48.684 34.5064L49.4496 33.3319C50.6221 31.5329 49.6216 29.1173 47.5202 28.6744L46.1484 28.3852C44.9641 28.1356 44.0388 27.2103 43.7893 26.026L43.5001 24.6542C43.1859 23.1634 41.8793 22.2269 40.504 22.2269C39.9406 22.2269 39.3654 22.3842 38.8427 22.725L37.6682 23.4906C37.172 23.8142 36.5924 23.9864 36 23.9863C35.4076 23.9864 34.828 23.8142 34.3318 23.4906L33.1573 22.725ZM31.1121 36.1746C31.1121 38.8741 33.3005 41.0625 36.0001 41.0625C38.6996 41.0625 40.888 38.8741 40.888 36.1746C40.888 33.475 38.6996 31.2866 36.0001 31.2866C33.3005 31.2866 31.1121 33.475 31.1121 36.1746Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_0_27661" x="-7" y="-7" width="86" height="86" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.247059 0 0 0 0 0.780392 0 0 0 0 1 0 0 0 0.21 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_27661"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_27661" result="shape"/>
</filter>
<filter id="filter1_d_0_27661" x="7.6001" y="7.60001" width="54.416" height="54.416" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0135764 0 0 0 0 0.1308 0 0 0 0 0.191667 0 0 0 0.43 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_27661"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_27661" result="shape"/>
</filter>
<linearGradient id="paint0_linear_0_27661" x1="-1.94391" y1="37.056" x2="31.7521" y2="72.624" gradientUnits="userSpaceOnUse">
<stop stop-color="#197CB3" stop-opacity="0.552941"/>
<stop offset="0.828164" stop-color="#197CB3" stop-opacity="0.01"/>
<stop offset="1" stop-color="#197CB3" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint1_linear_0_27661" x1="-1.94391" y1="37.056" x2="31.7521" y2="72.624" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E8AC6" stop-opacity="0.564706"/>
<stop offset="0.871691" stop-color="#1E8AC6" stop-opacity="0.01"/>
<stop offset="1" stop-color="#1E8AC6" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_0_27661" x1="0" y1="0" x2="0" y2="72" gradientUnits="userSpaceOnUse">
<stop stop-color="#50D6FF" stop-opacity="0.423529"/>
<stop offset="0.0763019" stop-color="#50D6FF" stop-opacity="0.423529"/>
<stop offset="0.189446" stop-color="#4FD5FF" stop-opacity="0.31"/>
<stop offset="0.326616" stop-color="#50D3FF" stop-opacity="0.160784"/>
<stop offset="0.427761" stop-color="#55DAFF" stop-opacity="0.0823529"/>
<stop offset="0.534106" stop-color="#4ED7FF" stop-opacity="0.0509804"/>
<stop offset="0.639888" stop-color="#48DAFF" stop-opacity="0.027451"/>
<stop offset="0.75001" stop-color="#3FBFFF" stop-opacity="0.0156863"/>
<stop offset="1" stop-color="#50D6FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint3_linear_0_27661" x1="0" y1="0" x2="0" y2="72" gradientUnits="userSpaceOnUse">
<stop stop-color="#50D5FF" stop-opacity="0.631373"/>
<stop offset="0.403544" stop-color="#50D5FF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#50D5FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint4_linear_0_27661" x1="10.8" y1="10.8" x2="10.8" y2="60.7592" gradientUnits="userSpaceOnUse">
<stop stop-color="#4DD6FF" stop-opacity="0.372549"/>
<stop offset="0.272235" stop-color="#4AD3FF" stop-opacity="0.160784"/>
<stop offset="0.611564" stop-color="#55DDFF" stop-opacity="0.03"/>
<stop offset="1" stop-color="#55DDFF" stop-opacity="0.01"/>
</linearGradient>
<clipPath id="clip0_0_27661">
<rect width="72" height="72" fill="white"/>
</clipPath>
<clipPath id="clip1_0_27661">
<rect width="72" height="72" fill="white"/>
</clipPath>
<clipPath id="clip2_0_27661">
<rect width="72" height="72" fill="white"/>
</clipPath>
</defs>
</svg>
