<?xml version="1.0" encoding="UTF-8"?>
<svg width="82px" height="61px" viewBox="0 0 82 61" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>用户管理-默认</title>
    <defs>
        <radialGradient cx="66.4988611%" cy="7.25124484%" fx="66.4988611%" fy="7.25124484%" r="142.769356%" gradientTransform="translate(0.664989,0.072512),scale(1.000000,0.820069),rotate(115.650545),translate(-0.664989,-0.072512)" id="radialGradient-1">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#011C76" offset="36.21%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="32.5368933%" cy="0.168034602%" fx="32.5368933%" fy="0.168034602%" r="115.56134%" gradientTransform="translate(0.325369,0.001680),scale(1.000000,0.820069),rotate(81.411501),translate(-0.325369,-0.001680)" id="radialGradient-2">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#2E5DF7" offset="3.773442e-06%"></stop>
            <stop stop-color="#133DD2" offset="30.1%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="50.0691343%" y1="-39.3490202%" x2="50.0691343%" y2="100.444248%" id="linearGradient-3">
            <stop stop-color="#203138" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#A2A9AC" stop-opacity="0.16" offset="40.38%"></stop>
            <stop stop-color="#1C46EF" stop-opacity="0.1964" offset="44.13%"></stop>
            <stop stop-color="#4F7EFA" stop-opacity="0.7292" offset="81.17%"></stop>
            <stop stop-color="#BAD0FD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.2192405%" y1="14.2603685%" x2="50.2192405%" y2="100.013889%" id="linearGradient-4">
            <stop stop-color="#203138" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#A2A9AC" stop-opacity="0.16" offset="51.52%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.27" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0038523%" y1="37.8767156%" x2="50.0038523%" y2="100.228322%" id="linearGradient-5">
            <stop stop-color="#203138" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#A2A9AC" stop-opacity="0.16" offset="18.33%"></stop>
            <stop stop-color="#1C46EF" stop-opacity="0.1964" offset="23.47%"></stop>
            <stop stop-color="#4F7EFA" stop-opacity="0.7292" offset="74.21%"></stop>
            <stop stop-color="#BAD0FD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="182.077524%" y1="57.6783756%" x2="-59.1575597%" y2="43.7863288%" id="linearGradient-6">
            <stop stop-color="#E2E7FF" offset="0%"></stop>
            <stop stop-color="#A2B7FF" offset="26.49%"></stop>
            <stop stop-color="#133DD2" offset="83.18%"></stop>
            <stop stop-color="#0556AB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100.714938%" y1="53.5529094%" x2="260.572041%" y2="42.4854005%" id="linearGradient-7">
            <stop stop-color="#E2E7FF" offset="0%"></stop>
            <stop stop-color="#A2B7FF" offset="26.49%"></stop>
            <stop stop-color="#133DD2" offset="60.26%"></stop>
            <stop stop-color="#0556AB" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="66.494183%" cy="6.42734669%" fx="66.494183%" fy="6.42734669%" r="150.784811%" gradientTransform="translate(0.664942,0.064273),scale(0.573840,1.000000),rotate(135.584050),translate(-0.664942,-0.064273)" id="radialGradient-8">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#011C76" offset="36.21%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="32.5655815%" cy="-0.0450350735%" fx="32.5655815%" fy="-0.0450350735%" r="98.3959633%" gradientTransform="translate(0.325656,-0.000450),scale(0.573840,1.000000),rotate(72.203976),translate(-0.325656,0.000450)" id="radialGradient-9">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#2E5DF7" offset="3.773442e-06%"></stop>
            <stop stop-color="#133DD2" offset="30.1%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="53.9126379%" cy="76.4689352%" fx="53.9126379%" fy="76.4689352%" r="297.930374%" gradientTransform="translate(0.539126,0.764689),scale(0.351695,1.000000),rotate(-165.123939),translate(-0.539126,-0.764689)" id="radialGradient-10">
            <stop stop-color="#2957F7" offset="0%"></stop>
            <stop stop-color="#08258B" stop-opacity="0.93" offset="31.79%"></stop>
            <stop stop-color="#010821" stop-opacity="0.8" offset="99.13%"></stop>
            <stop stop-color="#010821" stop-opacity="0.8" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="71.25272%" y1="28.8766086%" x2="8.46771546%" y2="159.403825%" id="linearGradient-11">
            <stop stop-color="#B4CDFF" offset="0%"></stop>
            <stop stop-color="#0E5FFF" offset="54.5105398%"></stop>
            <stop stop-color="#0E5FFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="所有页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图画板" transform="translate(-92.000000, -483.000000)">
            <g id="用户管理-默认" transform="translate(103.000000, 483.000000)">
                <g id="编组">
                    <rect id="矩形" x="0" y="0" width="60" height="60"></rect>
                    <g transform="translate(1.579258, 23.062710)">
                        <g transform="translate(6.568036, 10.272126)" fill-rule="nonzero" id="路径" stroke="#4F7EFA" stroke-opacity="0.08" stroke-width="0.6876">
                            <polygon fill="url(#radialGradient-1)" points="0 0 21.7764165 7.71822358 21.7764165 26.5543645 0 18.9280245"></polygon>
                            <polygon fill="url(#radialGradient-2)" points="43.7366003 0 21.9601838 7.71822358 21.9601838 26.5543645 43.7366003 18.8361409"></polygon>
                        </g>
                        <polyline id="路径" stroke="url(#linearGradient-3)" stroke-width="8" points="49.7090352 17.8254211 56.7840735 20.3062787 28.3920368 30.3215926 0 20.3062787 7.07503828 17.8254211"></polyline>
                        <polygon id="路径" stroke="url(#linearGradient-4)" stroke-width="2.8396" points="28.3920368 7.1669219 53.7519142 16.0796325 28.3920368 24.992343 3.03215926 16.0796325 6.61562021 14.7932619 8.72894334 14.058193 15.3445636 11.7611026"></polygon>
                        <polygon id="路径" stroke="url(#linearGradient-5)" stroke-width="2.8396" points="28.3920368 16.4471669 53.7519142 25.3598775 28.3920368 34.2725881 3.03215926 25.3598775"></polygon>
                        <g transform="translate(6.476152, 10.970112)" fill-rule="nonzero">
                            <g>
                                <polygon id="path-89" fill="#000000" points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                                <polygon id="路径" fill="url(#linearGradient-6)" points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                            </g>
                            <g transform="translate(32.914206, 6.431853) scale(-1, 1) translate(-32.914206, -6.431853) translate(21.934115, 0.091884)">
                                <g fill="#000000" id="path-91">
                                    <polygon points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                                </g>
                                <g fill="url(#linearGradient-7)" id="路径">
                                    <polygon points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                                </g>
                            </g>
                        </g>
                        <g transform="translate(6.568036, 0.000000)" fill-rule="nonzero" id="路径" stroke-width="0.6876">
                            <polygon stroke-opacity="0.08" stroke="#4F7EFA" fill="url(#radialGradient-8)" points="0 7.8101072 21.7764165 15.5283308 21.7764165 20.3062787 0 12.5880551"></polygon>
                            <polygon stroke-opacity="0.08" stroke="#4F7EFA" fill="url(#radialGradient-9)" points="43.7366003 7.8101072 21.9601838 15.5283308 21.9601838 20.3062787 43.7366003 12.5880551"></polygon>
                            <polygon stroke-opacity="0.36" stroke="#237190" fill="url(#radialGradient-10)" points="22.0520674 0 43.7366003 7.62633997 22.0520674 15.2526799 0.367534456 7.62633997"></polygon>
                        </g>
                    </g>
                </g>
                <g id="用户设置" transform="translate(16.000000, 2.000000)" fill-rule="nonzero">
                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="30" height="30"></rect>
                    <path d="M12.821543,0.0970605469 C13.3616584,0.102214287 13.9000959,0.167169461 14.4250488,0.295488281 C14.9414351,0.418019427 15.4441675,0.592248281 15.9256348,0.815537109 C16.3672326,1.01797553 16.7858386,1.26720425 17.1742676,1.55894531 C17.4923468,1.7979499 17.7774165,2.07797586 18.0220605,2.39173828 C18.5004085,3.02031263 18.8612984,3.73011311 19.0873535,4.48696289 C19.2969284,5.20274816 19.4539476,5.93288354 19.5571582,6.67154297 C19.6586819,7.4693902 19.7083857,8.27297894 19.705957,9.07725586 C19.8362477,9.18156897 19.941906,9.31335663 20.0153906,9.46321289 C20.0945362,9.63377938 20.152232,9.81349943 20.1871582,9.99826172 C20.2365314,10.2627789 20.2442712,10.5333862 20.2100977,10.800293 C20.1825409,11.1426537 20.1013553,11.4785575 19.9695117,11.7957129 C19.8665304,12.0112944 19.7270211,12.2074301 19.5571582,12.3754395 C19.4092464,12.5237403 19.2288258,12.6355515 19.0301953,12.7020117 C18.9438161,13.0204412 18.8522021,13.3374274 18.7553906,13.6528418 C18.652207,13.9105371 18.5489941,14.1625488 18.4345605,14.4111035 C18.3316715,14.6267851 18.1920965,14.8229534 18.0220605,14.9908594 C17.726799,15.237732 17.4365829,15.4905771 17.1515918,15.7492383 C16.8945156,16.0446466 16.7380313,16.4141475 16.7047559,16.8043359 C16.6512174,17.1031706 16.6319913,17.407143 16.6474512,17.7103418 C16.6740826,18.0274472 16.755483,18.337536 16.8880371,18.6268359 C17.0443474,18.9570112 17.2620801,19.2544138 17.5296094,19.5031641 C17.5580859,19.5283594 17.6385059,19.5466406 17.6162988,19.5775488 C14.9334082,23.325 16.2958594,27.5500488 17.9353418,29.5705371 C17.9353418,29.5705371 17.3464746,29.6279883 17.0485254,29.6539453 C16.2781123,29.7209453 15.5066755,29.7756008 14.7345117,29.8178906 C14.0012988,29.8579395 13.3599023,29.8774512 12.8214258,29.8774512 C12.2829492,29.8774512 11.6528906,29.8579395 10.93125,29.8178906 C10.2094629,29.7776953 9.47625,29.7238477 8.70884766,29.6539453 C7.94790169,29.584812 7.18792933,29.5053516 6.42914062,29.4155859 C5.67298828,29.3262012 4.98568359,29.2323047 4.36722656,29.1338965 C3.74865234,29.0353418 3.23308594,28.9357617 2.79779297,28.8361523 C2.51261308,28.7939127 2.2394204,28.6924933 1.99576172,28.5384082 C1.70978179,28.1068206 1.55828163,27.6000868 1.56043945,27.0823535 C1.46118147,26.0309511 1.50356215,24.9709809 1.68644531,23.9308594 C1.73408489,23.2855078 2.02969033,22.6836507 2.51129883,22.2514453 C2.98554136,21.8568207 3.52421851,21.546839 4.10370117,21.3350977 C4.72363814,21.1018335 5.35441251,20.898455 5.99384766,20.7256641 C6.6086058,20.5635307 7.19285503,20.3023581 7.72365234,19.9524023 C8.05541126,19.7483929 8.35630581,19.4980092 8.61720703,19.2088477 C8.80638951,18.9986807 8.9575423,18.7571852 9.06389648,18.4951758 C9.16452385,18.2603203 9.2152447,18.0071151 9.2128418,17.7516211 C9.2128418,17.4941895 9.20129883,17.2066406 9.17835937,16.8889453 C9.16591172,16.485069 9.00709805,16.0994828 8.73149414,15.8039941 C8.4520202,15.5201989 8.16165596,15.247338 7.86105469,14.9860254 C7.69523259,14.8195476 7.56297694,14.6227306 7.47149414,14.4062988 C7.36651118,14.1543188 7.2634126,13.9015579 7.16220703,13.6480371 C7.06525736,13.3326639 6.97364273,13.0156753 6.88740234,12.697207 C6.74620077,12.651487 6.61431823,12.5808725 6.49798828,12.4887012 C6.35964297,12.3652161 6.23657084,12.2256203 6.13139648,12.0728906 C5.97753255,11.8358912 5.86518214,11.574404 5.79916992,11.2996582 C5.68908007,11.0074982 5.64601829,10.6943416 5.67316406,10.3833105 C5.69253446,10.1400358 5.74257959,9.90018533 5.82210937,9.66946289 C5.90479458,9.44054702 6.02907955,9.22888613 6.18870117,9.04514648 C6.16964939,8.29033701 6.20791358,7.53517014 6.30316406,6.78615234 C6.38521815,6.09350339 6.51928823,5.40801258 6.70423828,4.73548828 C6.88538367,4.03629454 7.18360192,3.37281687 7.58625,2.77318359 C7.90628034,2.25972712 8.30883505,1.80259174 8.77769531,1.42019531 C9.18632135,1.08949307 9.63699771,0.814439313 10.1179395,0.602226563 C10.5569733,0.409968032 11.0189973,0.275211035 11.4925488,0.201298828 C11.9322832,0.133078331 12.3765497,0.0982327453 12.821543,0.0970605469 Z M23.5937988,17.9293359 L23.904873,19.4591895 C24.2875971,19.5613073 24.655138,19.7135841 24.9979395,19.9120605 L26.2991895,19.0475977 L27.5295117,20.2777441 L26.6695605,21.58125 C26.8687971,21.9253103 27.021096,22.294485 27.1224023,22.6789453 L28.65,22.9899023 L28.65,24.7300488 L27.1201465,25.0415918 C27.0187718,25.4260291 26.8664766,25.795195 26.6673047,26.1392871 L27.5317383,27.4405371 L26.3014453,28.6707129 L24.9979688,27.8086523 C24.6553128,28.0073808 24.2877296,28.159616 23.9049023,28.2613477 L23.5937988,29.7912012 L21.8536523,29.7912012 L21.5425488,28.2613477 C21.1598224,28.1591811 20.7922468,28.0069695 20.4493359,27.8086523 L19.1458301,28.6729395 L17.9133105,27.4403906 L18.7775977,26.1391406 C18.5792564,25.7946674 18.4270543,25.4256178 18.3249023,25.0414746 L16.7950488,24.7303418 L16.7950488,22.9902246 L18.3249023,22.6790918 C18.4259539,22.2942198 18.5790038,21.9249209 18.7798242,21.5813965 L17.9155664,20.2801465 L19.1480859,19.0475977 L20.4493359,19.9098047 C20.7921724,19.7113013 21.1597666,19.5590327 21.5425488,19.4569629 L21.8536523,17.9293359 L23.5937988,17.9293359 Z M22.7315918,21.7960535 C21.5937012,21.7948535 20.670293,22.7162988 20.6689441,23.8542188 C20.6677441,24.9922559 21.5892188,25.9158105 22.7271094,25.9170132 C23.8651465,25.9183594 24.7887012,24.9967383 24.7899035,23.8588477 C24.7911035,22.7208105 23.8696582,21.7974023 22.7315918,21.7960535 Z" id="形状结合" fill="url(#linearGradient-11)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>