<?xml version="1.0" encoding="UTF-8"?>
<svg width="82px" height="61px" viewBox="0 0 82 61" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>巡逻交班相关-默认</title>
    <defs>
        <radialGradient cx="66.4988611%" cy="7.25124484%" fx="66.4988611%" fy="7.25124484%" r="142.769356%" gradientTransform="translate(0.664989,0.072512),scale(1.000000,0.820069),rotate(115.650545),translate(-0.664989,-0.072512)" id="radialGradient-1">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#011C76" offset="36.21%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="32.5368933%" cy="0.168034602%" fx="32.5368933%" fy="0.168034602%" r="115.56134%" gradientTransform="translate(0.325369,0.001680),scale(1.000000,0.820069),rotate(81.411501),translate(-0.325369,-0.001680)" id="radialGradient-2">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#2E5DF7" offset="3.773442e-06%"></stop>
            <stop stop-color="#133DD2" offset="30.1%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="50.0691343%" y1="-39.3490202%" x2="50.0691343%" y2="100.444248%" id="linearGradient-3">
            <stop stop-color="#203138" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#A2A9AC" stop-opacity="0.16" offset="40.38%"></stop>
            <stop stop-color="#1C46EF" stop-opacity="0.1964" offset="44.13%"></stop>
            <stop stop-color="#4F7EFA" stop-opacity="0.7292" offset="81.17%"></stop>
            <stop stop-color="#BAD0FD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.2192405%" y1="14.2603685%" x2="50.2192405%" y2="100.013889%" id="linearGradient-4">
            <stop stop-color="#203138" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#A2A9AC" stop-opacity="0.16" offset="51.52%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.27" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0038523%" y1="37.8767156%" x2="50.0038523%" y2="100.228322%" id="linearGradient-5">
            <stop stop-color="#203138" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#A2A9AC" stop-opacity="0.16" offset="18.33%"></stop>
            <stop stop-color="#1C46EF" stop-opacity="0.1964" offset="23.47%"></stop>
            <stop stop-color="#4F7EFA" stop-opacity="0.7292" offset="74.21%"></stop>
            <stop stop-color="#BAD0FD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="182.077524%" y1="57.6783756%" x2="-59.1575597%" y2="43.7863288%" id="linearGradient-6">
            <stop stop-color="#E2E7FF" offset="0%"></stop>
            <stop stop-color="#A2B7FF" offset="26.49%"></stop>
            <stop stop-color="#133DD2" offset="83.18%"></stop>
            <stop stop-color="#0556AB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100.714938%" y1="53.5529094%" x2="260.572041%" y2="42.4854005%" id="linearGradient-7">
            <stop stop-color="#E2E7FF" offset="0%"></stop>
            <stop stop-color="#A2B7FF" offset="26.49%"></stop>
            <stop stop-color="#133DD2" offset="60.26%"></stop>
            <stop stop-color="#0556AB" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="66.494183%" cy="6.42734669%" fx="66.494183%" fy="6.42734669%" r="150.784811%" gradientTransform="translate(0.664942,0.064273),scale(0.573840,1.000000),rotate(135.584050),translate(-0.664942,-0.064273)" id="radialGradient-8">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#011C76" offset="36.21%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="32.5655815%" cy="-0.0450350735%" fx="32.5655815%" fy="-0.0450350735%" r="98.3959633%" gradientTransform="translate(0.325656,-0.000450),scale(0.573840,1.000000),rotate(72.203976),translate(-0.325656,0.000450)" id="radialGradient-9">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#2E5DF7" offset="3.773442e-06%"></stop>
            <stop stop-color="#133DD2" offset="30.1%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="53.9126379%" cy="76.4689352%" fx="53.9126379%" fy="76.4689352%" r="297.930374%" gradientTransform="translate(0.539126,0.764689),scale(0.351695,1.000000),rotate(-165.123939),translate(-0.539126,-0.764689)" id="radialGradient-10">
            <stop stop-color="#2957F7" offset="0%"></stop>
            <stop stop-color="#08258B" stop-opacity="0.93" offset="31.79%"></stop>
            <stop stop-color="#010821" stop-opacity="0.8" offset="99.13%"></stop>
            <stop stop-color="#010821" stop-opacity="0.8" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="75.5857825%" y1="33.5539636%" x2="0%" y2="135.178523%" id="linearGradient-11">
            <stop stop-color="#B4CDFF" offset="0%"></stop>
            <stop stop-color="#0E5FFF" offset="54.5105398%"></stop>
            <stop stop-color="#0E5FFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="所有页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图画板" transform="translate(-1188.000000, -94.000000)">
            <g id="巡逻交班相关-默认" transform="translate(1199.000000, 94.000000)">
                <g id="编组">
                    <rect id="矩形" x="0" y="0" width="60" height="60"></rect>
                    <g transform="translate(1.579258, 23.062710)">
                        <g transform="translate(6.568036, 10.272126)" fill-rule="nonzero" id="路径" stroke="#4F7EFA" stroke-opacity="0.08" stroke-width="0.6876">
                            <polygon fill="url(#radialGradient-1)" points="0 0 21.7764165 7.71822358 21.7764165 26.5543645 0 18.9280245"></polygon>
                            <polygon fill="url(#radialGradient-2)" points="43.7366003 0 21.9601838 7.71822358 21.9601838 26.5543645 43.7366003 18.8361409"></polygon>
                        </g>
                        <polyline id="路径" stroke="url(#linearGradient-3)" stroke-width="8" points="49.7090352 17.8254211 56.7840735 20.3062787 28.3920368 30.3215926 0 20.3062787 7.07503828 17.8254211"></polyline>
                        <polygon id="路径" stroke="url(#linearGradient-4)" stroke-width="2.8396" points="28.3920368 7.1669219 53.7519142 16.0796325 28.3920368 24.992343 3.03215926 16.0796325 6.61562021 14.7932619 8.72894334 14.058193 15.3445636 11.7611026"></polygon>
                        <polygon id="路径" stroke="url(#linearGradient-5)" stroke-width="2.8396" points="28.3920368 16.4471669 53.7519142 25.3598775 28.3920368 34.2725881 3.03215926 25.3598775"></polygon>
                        <g transform="translate(6.476152, 10.970112)" fill-rule="nonzero">
                            <g>
                                <polygon id="path-89" fill="#000000" points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                                <polygon id="路径" fill="url(#linearGradient-6)" points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                            </g>
                            <g transform="translate(32.914206, 6.431853) scale(-1, 1) translate(-32.914206, -6.431853) translate(21.934115, 0.091884)">
                                <g fill="#000000" id="path-91">
                                    <polygon points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                                </g>
                                <g fill="url(#linearGradient-7)" id="路径">
                                    <polygon points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                                </g>
                            </g>
                        </g>
                        <g transform="translate(6.568036, 0.000000)" fill-rule="nonzero" id="路径" stroke-width="0.6876">
                            <polygon stroke-opacity="0.08" stroke="#4F7EFA" fill="url(#radialGradient-8)" points="0 7.8101072 21.7764165 15.5283308 21.7764165 20.3062787 0 12.5880551"></polygon>
                            <polygon stroke-opacity="0.08" stroke="#4F7EFA" fill="url(#radialGradient-9)" points="43.7366003 7.8101072 21.9601838 15.5283308 21.9601838 20.3062787 43.7366003 12.5880551"></polygon>
                            <polygon stroke-opacity="0.36" stroke="#237190" fill="url(#radialGradient-10)" points="22.0520674 0 43.7366003 7.62633997 22.0520674 15.2526799 0.367534456 7.62633997"></polygon>
                        </g>
                    </g>
                </g>
                <g id="法警工作台" transform="translate(15.000000, 1.363636)" fill-rule="nonzero">
                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="30" height="30"></rect>
                    <path d="M13.2597656,8.83300781 L16.7431641,8.83300781 L16.7431641,9.14941406 L13.2597656,9.14941406 L13.2597656,8.83300781 Z M20.25,24.1435547 L9.75,24.1435547 C9.02636719,24.1435547 8.4375,24.6884766 8.4375,25.3623047 C8.4375,26.0361328 9.02636719,26.5810547 9.75,26.5810547 L20.25,26.5810547 C20.9736328,26.5810547 21.5625,26.0361328 21.5625,25.3623047 C21.5625,24.6884766 20.9736328,24.1435547 20.25,24.1435547 Z M14.2646484,8.34082031 C14.5664062,8.46972656 14.7070312,8.56347656 14.7158203,8.56933594 C14.8037109,8.64257812 14.90625,8.66894531 14.9970703,8.67480469 C15.0878906,8.66894531 15.1933594,8.64257812 15.2783203,8.56933594 C15.2871094,8.56347656 15.4277344,8.46972656 15.7294922,8.34082031 C15.7441406,8.33496094 15.7412109,8.31152344 15.7236328,8.31152344 C15.6591797,8.30566406 15.5537109,8.30273437 15.4306641,8.30859375 C15.4013672,8.30859375 15.3779297,8.28808594 15.3779297,8.25585937 C15.3779297,8.04492187 15.2138672,7.87207031 14.9941406,7.83691406 C14.7744141,7.86914062 14.6103516,8.04492187 14.6103516,8.25585937 C14.6103516,8.28515625 14.5869141,8.30859375 14.5576172,8.30859375 C14.4345703,8.30273437 14.3291016,8.30566406 14.2646484,8.31152344 C14.2529297,8.31152344 14.25,8.33496094 14.2646484,8.34082031 L14.2646484,8.34082031 Z M25.5,3.41894531 L4.5,3.41894531 C3.04980469,3.41894531 1.875,4.51171875 1.875,5.85644531 L1.875,19.265625 C1.875,20.6132812 3.04980469,21.703125 4.5,21.703125 L25.5,21.703125 C26.9501953,21.703125 28.125,20.6103516 28.125,19.265625 L28.125,5.85644531 C28.125,4.51171875 26.9501953,3.41894531 25.5,3.41894531 L25.5,3.41894531 Z M14.9648438,6.93457031 L15.0351563,6.93457031 C16.3974609,6.88769531 17.8417969,7.67871094 17.8300781,8.25878906 C17.8212891,8.7890625 17.2148438,9.04394531 17.1181641,9.08203125 C17.109375,9.08496094 17.1064453,9.09375 17.109375,9.09960938 C17.1416016,9.22851562 17.1240234,9.62109375 17.1240234,9.62109375 C17.1240234,9.62109375 17.0917969,9.68847656 17.0332031,9.77050781 C17.0302734,9.7734375 17.0302734,9.77636719 17.0302734,9.78222656 C17.0302734,10.6347656 16.8076172,11.2353516 16.5087891,11.6572266 C16.3300781,11.9121094 16.125,12.1025391 15.9228516,12.2431641 C15.4804688,12.5537109 15.0615234,12.6269531 15.0058594,12.6328125 L15.0029297,12.6328125 C14.9501953,12.6269531 14.53125,12.5537109 14.0859375,12.2431641 C13.8837891,12.1025391 13.6787109,11.9121094 13.5,11.6572266 C13.2011719,11.2353516 12.9785156,10.6347656 12.9785156,9.78222656 C12.9785156,9.77929688 12.9785156,9.7734375 12.9755859,9.77050781 C12.9140625,9.68847656 12.8847656,9.62109375 12.8847656,9.62109375 C12.8847656,9.62109375 12.8671875,9.22851562 12.8994141,9.09960938 C12.9023438,9.09375 12.8964844,9.08496094 12.890625,9.08203125 C12.7939453,9.04394531 12.1904297,8.7890625 12.1787109,8.25878906 C12.1582031,7.67871094 13.6025391,6.88769531 14.9648438,6.93457031 Z M15.2929687,17.0625 L14.7070312,17.0625 C12.7939453,17.0273437 10.8867188,16.6757812 10.6054688,16.5996094 C10.6142578,16.4912109 10.6230469,16.3798828 10.6347656,16.2685547 C10.7080078,15.5244141 10.8222656,14.6777344 11.015625,13.8837891 C11.0712891,13.5996094 11.0625,13.5234375 11.2734375,13.3535156 C11.4404297,13.2216797 12.5039062,13.1923828 13.0283203,13.1865234 C13.1015625,13.1865234 13.1748047,13.1835938 13.2480469,13.1806641 C13.3828125,13.1748047 13.5205078,13.1660156 13.6552734,13.1542969 C13.640625,13.2509766 13.6259766,13.3623047 13.6171875,13.4824219 C13.6142578,13.546875 13.6113281,13.6171875 13.6113281,13.6845703 C13.6113281,13.7783203 13.6171875,13.875 13.6259766,13.9716797 C13.640625,14.0859375 13.6611328,14.2001953 13.6992187,14.3085938 C13.8632812,14.8271484 14.1298828,15.3369141 14.4873047,15.6445313 L14.6748047,14.4052734 C14.6865234,14.3291016 14.7539062,14.2705078 14.8300781,14.2705078 L15.1611328,14.2705078 C15.2402344,14.2705078 15.3046875,14.3291016 15.3164062,14.4052734 L15.5039062,15.6445313 C15.8613281,15.3398438 16.125,14.8242188 16.2919922,14.3085938 C16.3271484,14.2001953 16.3505859,14.0859375 16.3652344,13.9716797 C16.3769531,13.875 16.3798828,13.7783203 16.3798828,13.6845703 C16.3798828,13.6142578 16.3769531,13.546875 16.3740234,13.4824219 C16.3652344,13.3623047 16.3535156,13.2509766 16.3359375,13.1542969 C16.4707031,13.1660156 16.6083984,13.1777344 16.7431641,13.1806641 C16.8164062,13.1835938 16.8896484,13.1865234 16.9628906,13.1865234 C17.4873047,13.1923828 18.5537109,13.2216797 18.7177734,13.3535156 C18.9316406,13.5234375 18.9228516,13.5996094 18.9755859,13.8837891 C19.1689453,14.6777344 19.2861328,15.5244141 19.3564453,16.2685547 C19.3710937,16.3828125 19.3798828,16.4941406 19.3886719,16.5996094 C19.1132812,16.6757812 17.2060547,17.0273437 15.2929687,17.0625 L15.2929687,17.0625 Z" id="形状" fill="url(#linearGradient-11)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>