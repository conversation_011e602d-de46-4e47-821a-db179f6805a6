<svg width="420" height="120" viewBox="0 0 420 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_1354)">
<g clip-path="url(#clip1_0_1354)">
<g clip-path="url(#clip2_0_1354)">
<mask id="mask0_0_1354" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="420" height="120">
<rect width="420" height="120" fill="url(#paint0_linear_0_1354)"/>
</mask>
<g mask="url(#mask0_0_1354)">
</g>
<g clip-path="url(#clip3_0_1354)">
<g filter="url(#filter0_f_0_1354)">
<path d="M-28.1143 12H288.695C291.746 12 294.602 13.5046 296.33 16.022L339.176 78.5076C343.388 84.6507 338.99 93 331.541 93H22.7999L-28.1143 12V12Z" fill="black"/>
</g>
<g filter="url(#filter1_d_0_1354)">
<path d="M-84 0H326.679C330.636 0 334.338 1.95045 336.576 5.21367L392.118 86.2137C397.578 94.1769 391.878 105 382.221 105H-18L-84 0V0Z" fill="#2D74B4" fill-opacity="0.619608"/>
</g>
<g filter="url(#filter2_i_0_1354)">
<path d="M-84 0H326.679C330.636 0 334.338 1.95045 336.576 5.21367L392.118 86.2137C397.578 94.1769 391.878 105 382.221 105H-18L-84 0V0Z" fill="black" fill-opacity="0.01"/>
</g>
<mask id="mask1_0_1354" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-84" y="0" width="479" height="105">
<path d="M-84 0H326.679C330.636 0 334.338 1.95045 336.576 5.21367L392.118 86.2137C397.578 94.1769 391.878 105 382.221 105H-18L-84 0V0Z" fill="#0073D8" fill-opacity="0.901961"/>
</mask>
<g mask="url(#mask1_0_1354)">
<g opacity="0.4" filter="url(#filter3_f_0_1354)">
<ellipse cx="300" cy="90" rx="96" ry="60" fill="#7CD0F8" fill-opacity="0.298039"/>
</g>
</g>
<path d="M326.679 1.5C330.141 1.5 333.38 3.20631 335.339 6.06152L390.881 87.0615C395.659 94.0295 390.671 103.5 382.221 103.5H-17.1719L-81.2861 1.5H326.679Z" stroke="#3897C0" stroke-opacity="0.368627" stroke-width="3"/>
<path d="M350.679 16.5C354.141 16.5 357.38 18.2063 359.339 21.0615L414.881 102.062C419.659 109.03 414.671 118.5 406.221 118.5H6.82812L-57.2861 16.5H350.679Z" stroke="url(#paint1_linear_0_1354)" stroke-width="3"/>
<g opacity="0.2" filter="url(#filter4_f_0_1354)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M-91.6198 4.78953L-100.287 -9H-84H326.679C333.603 -9 340.083 -5.58672 343.998 0.123904L399.54 81.1239C409.095 95.0595 399.117 114 382.221 114H-18H-22.9731L-25.6197 109.79L-91.6198 4.78953ZM329.154 10.3034C328.593 9.48762 327.669 9 326.679 9H-67.7126L-76.3803 -4.78953L-84 0V9H-67.7126L-13.0269 96H-18V105L-10.3803 100.21L-13.0269 96H382.221C384.636 96 386.061 93.2943 384.696 91.3035L329.154 10.3034Z" fill="url(#paint2_linear_0_1354)"/>
</g>
<path d="M326.679 1.5C330.141 1.5 333.38 3.20631 335.339 6.06152L390.881 87.0615C395.659 94.0295 390.671 103.5 382.221 103.5H-17.1719L-81.2861 1.5H326.679Z" stroke="url(#paint3_linear_0_1354)" stroke-width="3"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_f_0_1354" x="-46.1029" y="-5.98863" width="404.905" height="116.977" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.99432" result="effect1_foregroundBlur_0_1354"/>
</filter>
<filter id="filter1_d_0_1354" x="-84" y="0" width="499.242" height="126" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="21" dy="21"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.0901961 0 0 0 0 0.156863 0 0 0 0.709804 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_1354"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_1354" result="shape"/>
</filter>
<filter id="filter2_i_0_1354" x="-84" y="0" width="478.242" height="105" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.133333 0 0 0 0 0.709804 0 0 0 0 0.956863 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_1354"/>
</filter>
<filter id="filter3_f_0_1354" x="108.061" y="-65.9394" width="383.879" height="311.879" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="47.9697" result="effect1_foregroundBlur_0_1354"/>
</filter>
<filter id="filter4_f_0_1354" x="-112.28" y="-20.9924" width="527.529" height="146.985" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5.99621" result="effect1_foregroundBlur_0_1354"/>
</filter>
<linearGradient id="paint0_linear_0_1354" x1="210" y1="42" x2="0" y2="42" gradientUnits="userSpaceOnUse">
<stop stop-color="#D9D9D9"/>
<stop offset="1" stop-color="#D9D9D9" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1354" x1="42.6061" y1="43.7945" x2="36.3011" y2="138.392" gradientUnits="userSpaceOnUse">
<stop stop-color="#1BA5E1"/>
<stop offset="1" stop-color="#1BA5E1" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_0_1354" x1="384.721" y1="75.5154" x2="69.6199" y2="75.5158" gradientUnits="userSpaceOnUse">
<stop stop-color="#08ACF0" stop-opacity="0.913725"/>
<stop offset="0.560765" stop-color="#08ACF0" stop-opacity="0.54902"/>
<stop offset="1" stop-color="#08ACF0" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint3_linear_0_1354" x1="376.638" y1="72.1473" x2="77.3699" y2="72.1478" gradientUnits="userSpaceOnUse">
<stop stop-color="#49BDED" stop-opacity="0.913725"/>
<stop offset="0.560765" stop-color="#49BDED" stop-opacity="0.54902"/>
<stop offset="1" stop-color="#49BDED" stop-opacity="0.913725"/>
</linearGradient>
<clipPath id="clip0_0_1354">
<rect width="420" height="120" fill="white"/>
</clipPath>
<clipPath id="clip1_0_1354">
<rect width="420" height="120" fill="white"/>
</clipPath>
<clipPath id="clip2_0_1354">
<rect width="420" height="120" fill="white"/>
</clipPath>
<clipPath id="clip3_0_1354">
<rect width="518.529" height="129" fill="white" transform="translate(-100.287 -9)"/>
</clipPath>
</defs>
</svg>
