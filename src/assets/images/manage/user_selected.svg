<?xml version="1.0" encoding="UTF-8"?>
<svg width="108px" height="89px" viewBox="0 0 108 89" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>用户管理-选中</title>
    <defs>
        <radialGradient cx="66.4988611%" cy="7.25124484%" fx="66.4988611%" fy="7.25124484%" r="142.769356%" gradientTransform="translate(0.664989,0.072512),scale(1.000000,0.820069),rotate(115.650545),translate(-0.664989,-0.072512)" id="radialGradient-1">
            <stop stop-color="#F9AC2C" offset="0%"></stop>
            <stop stop-color="#794D03" offset="36.21%"></stop>
            <stop stop-color="#492E02" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="32.5368933%" cy="0.168034602%" fx="32.5368933%" fy="0.168034602%" r="115.56134%" gradientTransform="translate(0.325369,0.001680),scale(1.000000,0.820069),rotate(81.411501),translate(-0.325369,-0.001680)" id="radialGradient-2">
            <stop stop-color="#F8AE31" offset="0%"></stop>
            <stop stop-color="#D98F13" offset="26.9824902%"></stop>
            <stop stop-color="#342101" offset="99.4560615%"></stop>
        </radialGradient>
        <linearGradient x1="50.0691343%" y1="-57.0304998%" x2="50.0691343%" y2="98.9547346%" id="linearGradient-3">
            <stop stop-color="#382F1F" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#AFAAA3" stop-opacity="0.16" offset="44.431545%"></stop>
            <stop stop-color="#FDBE55" stop-opacity="0.7292" offset="81.17%"></stop>
            <stop stop-color="#FFE8C1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.2192405%" y1="14.2603685%" x2="50.2192405%" y2="100.013889%" id="linearGradient-4">
            <stop stop-color="#34311E" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#AFADA3" stop-opacity="0.16" offset="51.52%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.27" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0038523%" y1="37.8767156%" x2="50.0038523%" y2="100.228322%" id="linearGradient-5">
            <stop stop-color="#342B1D" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#AFAAA3" stop-opacity="0.16" offset="18.33%"></stop>
            <stop stop-color="#F3A21C" stop-opacity="0.1964" offset="23.47%"></stop>
            <stop stop-color="#FFBB4A" stop-opacity="0.7292" offset="74.21%"></stop>
            <stop stop-color="#FDE3B9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="182.077524%" y1="57.6783756%" x2="-59.1575597%" y2="43.7863288%" id="linearGradient-6">
            <stop stop-color="#FFF3E0" offset="0%"></stop>
            <stop stop-color="#FCDBA3" offset="26.49%"></stop>
            <stop stop-color="#D58D14" offset="83.18%"></stop>
            <stop stop-color="#AA6B03" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100.714938%" y1="53.5529094%" x2="260.572041%" y2="42.4854005%" id="linearGradient-7">
            <stop stop-color="#FBEFDB" offset="0%"></stop>
            <stop stop-color="#FFDFA9" offset="26.49%"></stop>
            <stop stop-color="#DB9115" offset="60.26%"></stop>
            <stop stop-color="#B06F03" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="87.7778752%" cy="33.5538611%" fx="87.7778752%" fy="33.5538611%" r="129.896877%" gradientTransform="translate(0.877779,0.335539),scale(0.573840,1.000000),rotate(107.797572),scale(1.000000,1.037296),translate(-0.877779,-0.335539)" id="radialGradient-8">
            <stop stop-color="#F0D2A1" offset="0%"></stop>
            <stop stop-color="#B17208" offset="30.1%"></stop>
            <stop stop-color="#8D5A05" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="24.0059161%" cy="14.091471%" fx="24.0059161%" fy="14.091471%" r="98.3959633%" gradientTransform="translate(0.240059,0.140915),scale(0.573840,1.000000),rotate(72.203976),translate(-0.240059,-0.140915)" id="radialGradient-9">
            <stop stop-color="#F1D4A2" offset="0%"></stop>
            <stop stop-color="#B07209" offset="52.3385417%"></stop>
            <stop stop-color="#8F5B05" offset="98.4277685%"></stop>
        </radialGradient>
        <radialGradient cx="53.9126379%" cy="76.4689352%" fx="53.9126379%" fy="76.4689352%" r="297.930374%" gradientTransform="translate(0.539126,0.764689),scale(0.351695,1.000000),rotate(-165.123939),translate(-0.539126,-0.764689)" id="radialGradient-10">
            <stop stop-color="#FFEBCA" offset="0%"></stop>
            <stop stop-color="#AC6C01" offset="31.79%"></stop>
            <stop stop-color="#4F3201" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="72.4777579%" cy="15.7146618%" fx="72.4777579%" fy="15.7146618%" r="150.583119%" gradientTransform="translate(0.724778,0.157147),scale(1.000000,0.911398),rotate(98.584707),scale(1.000000,1.002007),translate(-0.724778,-0.157147)" id="radialGradient-11">
            <stop stop-color="#FFA50E" offset="0%"></stop>
            <stop stop-color="#FFF5E6" offset="74.9042218%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </radialGradient>
    </defs>
    <g id="所有页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图画板" transform="translate(-93.000000, -617.000000)">
            <g id="用户管理-选中" transform="translate(103.000000, 617.000000)">
                <g id="编组">
                    <rect id="矩形" x="0" y="0" width="88" height="88"></rect>
                    <g transform="translate(2.316245, 33.825308)">
                        <g transform="translate(9.633119, 15.065785)" fill-rule="nonzero" id="路径" stroke-opacity="0.08" stroke-width="0.6876">
                            <polygon stroke="#FCE250" fill="url(#radialGradient-1)" points="0 0 31.9387443 11.3200613 31.9387443 38.9464012 0 27.7611026"></polygon>
                            <polygon stroke="#F7DD4C" fill="url(#radialGradient-2)" points="64.1470138 0 32.2082695 11.3200613 32.2082695 38.9464012 64.1470138 27.62634"></polygon>
                        </g>
                        <polyline id="路径" stroke="url(#linearGradient-3)" stroke-width="8" points="72.906585 26.143951 83.2833078 29.7825421 41.6416539 44.4716692 0 29.7825421 10.3767228 26.143951"></polyline>
                        <polygon id="路径" stroke="url(#linearGradient-4)" stroke-width="2.8396" points="41.6416539 10.5114855 78.8361409 23.5834609 41.6416539 36.6554364 4.44716692 23.5834609 9.70290965 21.6967841 12.8024502 20.618683 22.5053599 17.2496172"></polygon>
                        <polygon id="路径" stroke="url(#linearGradient-5)" stroke-width="2.8396" points="41.6416539 24.1225115 78.8361409 37.194487 41.6416539 50.2664625 4.44716692 37.194487"></polygon>
                        <g transform="translate(9.498356, 16.089498)" fill-rule="nonzero">
                            <g>
                                <polygon id="path-89" fill="#000000" points="0 0 32.2082695 11.3200613 32.2082695 18.5972435 0 7.27718224"></polygon>
                                <polygon id="路径" fill="url(#linearGradient-6)" points="0 0 32.2082695 11.3200613 32.2082695 18.5972435 0 7.27718224"></polygon>
                            </g>
                            <g transform="translate(48.274169, 9.433384) scale(-1, 1) translate(-48.274169, -9.433384) translate(32.170035, 0.134763)">
                                <g fill="#000000" id="path-91">
                                    <polygon points="0 0 32.2082695 11.3200613 32.2082695 18.5972435 0 7.27718224"></polygon>
                                </g>
                                <g fill="url(#linearGradient-7)" id="路径">
                                    <polygon points="0 0 32.2082695 11.3200613 32.2082695 18.5972435 0 7.27718224"></polygon>
                                </g>
                            </g>
                        </g>
                        <g transform="translate(9.633119, 0.000000)" fill-rule="nonzero" id="路径" stroke-width="0.6876">
                            <polygon stroke-opacity="0.08" stroke="#FAE14E" fill="url(#radialGradient-8)" points="0 11.4548239 31.9387443 22.7748851 31.9387443 29.7825421 0 18.4624809"></polygon>
                            <polygon stroke-opacity="0.08" stroke="#FDE34D" fill="url(#radialGradient-9)" points="64.1470138 11.4548239 32.2082695 22.7748851 32.2082695 29.7825421 64.1470138 18.4624809"></polygon>
                            <polygon stroke-opacity="0.36" stroke="#918022" fill="url(#radialGradient-10)" points="32.3430322 0 64.1470138 11.1852986 32.3430322 22.3705972 0.539050536 11.1852986"></polygon>
                        </g>
                    </g>
                </g>
                <g id="用户设置" transform="translate(22.000000, 2.000000)" fill-rule="nonzero">
                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="43.9977859" height="43.9977859"></rect>
                    <path d="M18.8039834,0.142348305 C19.5961129,0.149906743 20.3857814,0.245169538 21.1556737,0.433361004 C21.9130021,0.613064308 22.6503058,0.868587103 23.3564223,1.1960609 C24.0040664,1.49295564 24.6179911,1.85847271 25.1876583,2.28633807 C25.654151,2.63686049 26.0722321,3.04754456 26.4310254,3.50770629 C27.1325671,4.42956895 27.6618457,5.47055727 27.9933764,6.58054775 C28.3007375,7.63031331 28.5310207,8.70112466 28.6823886,9.7844373 C28.8312825,10.9545544 28.9041778,12.1330919 28.9006159,13.3126387 C29.0916994,13.4656235 29.2466569,13.6589024 29.354429,13.8786805 C29.4705033,14.1288321 29.5551196,14.3924082 29.6063421,14.6633793 C29.6787525,15.0513182 29.6901037,15.4481891 29.639985,15.8396326 C29.5995704,16.3417364 29.4805043,16.8343706 29.2871434,17.2995083 C29.1361117,17.6156787 28.9315084,17.9033298 28.6823886,18.1497312 C28.4654623,18.3672281 28.2008586,18.5312097 27.9095486,18.6286797 C27.7828654,19.0956861 27.648505,19.5605759 27.506522,20.0231603 C27.3551937,20.4010944 27.2038224,20.770693 27.0359949,21.1352216 C26.8850987,21.4515386 26.680399,21.7392376 26.4310254,21.9854874 C25.9979969,22.347549 25.572368,22.7183699 25.1544021,23.0977205 C24.7773761,23.5309642 24.5478772,24.0728716 24.4990757,24.6451192 C24.4205567,25.0833879 24.3923597,25.5291917 24.4150331,25.9738609 C24.4540905,26.4389254 24.5734718,26.8936994 24.7678747,27.3179846 C24.9971183,27.8022173 25.3164435,28.2383858 25.7088,28.6032012 C25.7505635,28.6401525 25.8685068,28.6669636 25.8359381,28.7122934 C21.9012299,34.2082785 23.8993911,40.404705 26.3038443,43.3679387 C26.3038443,43.3679387 26.0218432,43.3954513 25.6985941,43.4261367 L25.4521851,43.4493654 C25.2877722,43.4647406 25.1280943,43.4793879 25.0032457,43.4902646 C23.8733634,43.5885263 22.7419796,43.6686836 21.6095297,43.7307056 C20.5342049,43.7894409 19.5935374,43.8180567 18.8038115,43.8180567 C18.0140857,43.8180567 17.0900462,43.7894409 16.0316932,43.7307056 C14.9731254,43.6717554 13.8978006,43.5927828 12.7723338,43.4902646 C11.6563359,43.3888742 10.5417658,43.2723381 9.42893176,43.1406884 C8.31996412,43.0095973 7.31196798,42.8718894 6.40494331,42.7275647 C5.49774677,42.5830251 4.74162076,42.4369816 4.1032232,42.2908952 C3.68498041,42.2289468 3.28431798,42.0802058 2.92696989,41.8542258 C2.50755377,41.2212625 2.28536471,40.4780903 2.28852936,39.7187864 C2.14295832,38.1768071 2.20511352,36.6222624 2.47332866,35.0968276 C2.54319653,34.1503595 2.97672935,33.2676802 3.68305294,32.6338109 C4.37857365,32.0550572 5.16859372,31.6004403 6.01845885,31.289902 C6.92765398,30.9477984 7.85274317,30.649525 8.79053419,30.396111 C9.69213411,30.1583273 10.5489899,29.7752935 11.3274534,29.2620509 C11.8140087,28.9628521 12.2552985,28.5956411 12.6379343,28.1715589 C12.915388,27.8633295 13.1370676,27.509154 13.2930459,27.1248928 C13.4406253,26.7804554 13.5150121,26.4091065 13.511488,26.0344008 C13.511488,25.6568534 13.4945592,25.2351363 13.4609164,24.7692067 C13.4426607,24.1768845 13.2097457,23.6113865 12.805547,23.178025 C12.3956725,22.761813 11.9698264,22.3616371 11.5289667,21.9783979 C11.2857732,21.7342427 11.091808,21.4455924 10.95764,21.128175 C10.8036727,20.758623 10.6524691,20.3879256 10.5040417,20.0161138 C10.361856,19.5535898 10.2274947,19.0886966 10.1010151,18.6216332 C9.8939299,18.5545805 9.7005119,18.4510178 9.52990324,18.31584 C9.32700699,18.1347377 9.14651028,17.9300074 8.99226232,17.7060152 C8.76660657,17.3584335 8.60183426,16.9749383 8.50502122,16.5719981 C8.34356423,16.1435183 8.28041013,15.6842451 8.32022192,15.2280891 C8.34863042,14.8713041 8.42202625,14.5195411 8.53866406,14.1811653 C8.65992959,13.8454389 8.84220504,13.5350185 9.07630497,13.2655473 C9.04836376,12.1585491 9.10448175,11.0510267 9.24417543,9.95252259 C9.36451536,8.93668858 9.56114158,7.93135265 9.83238801,6.94503332 C10.0980545,5.91960077 10.5354193,4.94654914 11.1259401,4.06713127 C11.5952943,3.31409966 12.1856782,2.64366818 12.8733053,2.08284831 C13.4725933,1.59784276 14.1335521,1.19445088 14.8388978,0.883221178 C15.4827817,0.601256189 16.1603828,0.40362254 16.8548901,0.295223425 C17.4998014,0.19517173 18.1513595,0.144067443 18.8039834,0.142348305 Z M34.602497,26.2950361 L35.0587162,28.5387084 C35.6200165,28.6884737 36.1590494,28.9118018 36.6617996,29.2028859 L38.5702036,27.9350708 L40.3745854,29.7391948 L39.1133872,31.6509072 C39.4055861,32.1555036 39.6289466,32.6969326 39.7775217,33.2607793 L42.0178855,33.7168267 L42.0178855,36.2689131 L39.7742133,36.7258198 C39.6255379,37.2896328 39.4021829,37.8310488 39.1100787,38.3356919 L40.3778509,40.2440959 L38.573512,42.0482629 L36.6618426,40.7839711 C36.1593058,41.0754247 35.6202109,41.2986918 35.0587592,41.4478908 L34.602497,43.691563 L32.0504106,43.691563 L31.5941484,41.4478908 C31.0328444,41.2980541 30.4937608,41.0748216 29.9908501,40.7839711 L28.0791377,42.0515284 L26.2715334,40.243881 L27.5390907,38.3354771 C27.2482048,37.8302751 27.0249864,37.2890296 26.875171,36.7256479 L24.6314987,36.2693428 L24.6314987,33.7172993 L26.875171,33.2609942 C27.0233725,32.6965437 27.2478344,32.1549325 27.5423562,31.6511221 L26.2748418,29.7427181 L28.0824462,27.9350708 L29.9908501,29.1995775 C30.4936516,28.9084538 31.0327627,28.6851377 31.5941484,28.5354429 L32.0504106,26.2950361 L34.602497,26.2950361 Z M33.3379903,31.9659365 C31.669168,31.9641766 30.3149041,33.3155617 30.312926,34.984427 C30.311166,36.6534641 31.6625941,38.0079428 33.3314164,38.0097066 C35.0004535,38.0116809 36.3549322,36.660038 36.3566956,34.9912157 C36.3584555,33.3221786 35.0070704,31.9679147 33.3379903,31.9659365 Z" id="形状结合" fill="url(#radialGradient-11)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>