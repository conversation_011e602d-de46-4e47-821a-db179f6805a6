<?xml version="1.0" encoding="UTF-8"?>
<svg width="82px" height="61px" viewBox="0 0 82 61" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>物联网相关-默认</title>
    <defs>
        <radialGradient cx="66.4988611%" cy="7.25124484%" fx="66.4988611%" fy="7.25124484%" r="142.769356%" gradientTransform="translate(0.664989,0.072512),scale(1.000000,0.820069),rotate(115.650545),translate(-0.664989,-0.072512)" id="radialGradient-1">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#011C76" offset="36.21%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="32.5368933%" cy="0.168034602%" fx="32.5368933%" fy="0.168034602%" r="115.56134%" gradientTransform="translate(0.325369,0.001680),scale(1.000000,0.820069),rotate(81.411501),translate(-0.325369,-0.001680)" id="radialGradient-2">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#2E5DF7" offset="3.773442e-06%"></stop>
            <stop stop-color="#133DD2" offset="30.1%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="50.0691343%" y1="-39.3490202%" x2="50.0691343%" y2="100.444248%" id="linearGradient-3">
            <stop stop-color="#203138" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#A2A9AC" stop-opacity="0.16" offset="40.38%"></stop>
            <stop stop-color="#1C46EF" stop-opacity="0.1964" offset="44.13%"></stop>
            <stop stop-color="#4F7EFA" stop-opacity="0.7292" offset="81.17%"></stop>
            <stop stop-color="#BAD0FD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.2192405%" y1="14.2603685%" x2="50.2192405%" y2="100.013889%" id="linearGradient-4">
            <stop stop-color="#203138" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#A2A9AC" stop-opacity="0.16" offset="51.52%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.27" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0038523%" y1="37.8767156%" x2="50.0038523%" y2="100.228322%" id="linearGradient-5">
            <stop stop-color="#203138" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#A2A9AC" stop-opacity="0.16" offset="18.33%"></stop>
            <stop stop-color="#1C46EF" stop-opacity="0.1964" offset="23.47%"></stop>
            <stop stop-color="#4F7EFA" stop-opacity="0.7292" offset="74.21%"></stop>
            <stop stop-color="#BAD0FD" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="182.077524%" y1="57.6783756%" x2="-59.1575597%" y2="43.7863288%" id="linearGradient-6">
            <stop stop-color="#E2E7FF" offset="0%"></stop>
            <stop stop-color="#A2B7FF" offset="26.49%"></stop>
            <stop stop-color="#133DD2" offset="83.18%"></stop>
            <stop stop-color="#0556AB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100.714938%" y1="53.5529094%" x2="260.572041%" y2="42.4854005%" id="linearGradient-7">
            <stop stop-color="#E2E7FF" offset="0%"></stop>
            <stop stop-color="#A2B7FF" offset="26.49%"></stop>
            <stop stop-color="#133DD2" offset="60.26%"></stop>
            <stop stop-color="#0556AB" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="66.494183%" cy="6.42734669%" fx="66.494183%" fy="6.42734669%" r="150.784811%" gradientTransform="translate(0.664942,0.064273),scale(0.573840,1.000000),rotate(135.584050),translate(-0.664942,-0.064273)" id="radialGradient-8">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#011C76" offset="36.21%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="32.5655815%" cy="-0.0450350735%" fx="32.5655815%" fy="-0.0450350735%" r="98.3959633%" gradientTransform="translate(0.325656,-0.000450),scale(0.573840,1.000000),rotate(72.203976),translate(-0.325656,0.000450)" id="radialGradient-9">
            <stop stop-color="#2E5DF7" offset="0%"></stop>
            <stop stop-color="#2E5DF7" offset="3.773442e-06%"></stop>
            <stop stop-color="#133DD2" offset="30.1%"></stop>
            <stop stop-color="#021144" offset="99.13%"></stop>
            <stop stop-color="#021144" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="53.9126379%" cy="76.4689352%" fx="53.9126379%" fy="76.4689352%" r="297.930374%" gradientTransform="translate(0.539126,0.764689),scale(0.351695,1.000000),rotate(-165.123939),translate(-0.539126,-0.764689)" id="radialGradient-10">
            <stop stop-color="#2957F7" offset="0%"></stop>
            <stop stop-color="#08258B" stop-opacity="0.93" offset="31.79%"></stop>
            <stop stop-color="#010821" stop-opacity="0.8" offset="99.13%"></stop>
            <stop stop-color="#010821" stop-opacity="0.8" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="75.5857825%" y1="28.8837102%" x2="0%" y2="159.367044%" id="linearGradient-11">
            <stop stop-color="#B4CDFF" offset="0%"></stop>
            <stop stop-color="#0E5FFF" offset="54.5105398%"></stop>
            <stop stop-color="#0E5FFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="所有页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图画板" transform="translate(-1344.000000, -94.000000)">
            <g id="物联网相关-默认" transform="translate(1355.000000, 94.000000)">
                <g id="编组">
                    <rect id="矩形" x="0" y="0" width="60" height="60"></rect>
                    <g transform="translate(1.579258, 23.062710)">
                        <g transform="translate(6.568036, 10.272126)" fill-rule="nonzero" id="路径" stroke="#4F7EFA" stroke-opacity="0.08" stroke-width="0.6876">
                            <polygon fill="url(#radialGradient-1)" points="0 0 21.7764165 7.71822358 21.7764165 26.5543645 0 18.9280245"></polygon>
                            <polygon fill="url(#radialGradient-2)" points="43.7366003 0 21.9601838 7.71822358 21.9601838 26.5543645 43.7366003 18.8361409"></polygon>
                        </g>
                        <polyline id="路径" stroke="url(#linearGradient-3)" stroke-width="8" points="49.7090352 17.8254211 56.7840735 20.3062787 28.3920368 30.3215926 0 20.3062787 7.07503828 17.8254211"></polyline>
                        <polygon id="路径" stroke="url(#linearGradient-4)" stroke-width="2.8396" points="28.3920368 7.1669219 53.7519142 16.0796325 28.3920368 24.992343 3.03215926 16.0796325 6.61562021 14.7932619 8.72894334 14.058193 15.3445636 11.7611026"></polygon>
                        <polygon id="路径" stroke="url(#linearGradient-5)" stroke-width="2.8396" points="28.3920368 16.4471669 53.7519142 25.3598775 28.3920368 34.2725881 3.03215926 25.3598775"></polygon>
                        <g transform="translate(6.476152, 10.970112)" fill-rule="nonzero">
                            <g>
                                <polygon id="path-89" fill="#000000" points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                                <polygon id="路径" fill="url(#linearGradient-6)" points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                            </g>
                            <g transform="translate(32.914206, 6.431853) scale(-1, 1) translate(-32.914206, -6.431853) translate(21.934115, 0.091884)">
                                <g fill="#000000" id="path-91">
                                    <polygon points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                                </g>
                                <g fill="url(#linearGradient-7)" id="路径">
                                    <polygon points="0 0 21.9601838 7.71822358 21.9601838 12.6799387 0 4.96171516"></polygon>
                                </g>
                            </g>
                        </g>
                        <g transform="translate(6.568036, 0.000000)" fill-rule="nonzero" id="路径" stroke-width="0.6876">
                            <polygon stroke-opacity="0.08" stroke="#4F7EFA" fill="url(#radialGradient-8)" points="0 7.8101072 21.7764165 15.5283308 21.7764165 20.3062787 0 12.5880551"></polygon>
                            <polygon stroke-opacity="0.08" stroke="#4F7EFA" fill="url(#radialGradient-9)" points="43.7366003 7.8101072 21.9601838 15.5283308 21.9601838 20.3062787 43.7366003 12.5880551"></polygon>
                            <polygon stroke-opacity="0.36" stroke="#237190" fill="url(#radialGradient-10)" points="22.0520674 0 43.7366003 7.62633997 22.0520674 15.2526799 0.367534456 7.62633997"></polygon>
                        </g>
                    </g>
                </g>
                <g id="大数据,科技,数据流转" transform="translate(15.000000, 1.363636)" fill-rule="nonzero">
                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="29.9949321" height="29.9949321"></rect>
                    <path d="M28.1595,19.3912549 C27.7992093,18.2078611 27.0464068,16.913158 25.968464,15.5950213 C25.6843323,15.2493766 25.6843323,14.7514139 25.968464,14.4057691 C28.1272789,11.7694958 28.9826031,9.22109826 27.9896068,7.50166222 C27.2455919,6.21281748 25.5818105,5.59182865 23.3790577,5.59182865 C22.6409012,5.59182865 21.8441608,5.66212927 20.9976241,5.79980132 C20.5582453,5.87303114 20.1247248,5.62404977 19.9694775,5.20517523 C18.7655794,2.0152845 16.9846303,0 14.9986377,0 C13.0126451,0 11.2316961,2.0152845 10.0307271,5.20224603 C9.8725507,5.62112057 9.44195939,5.86717275 8.9996513,5.79687213 C8.15604384,5.65920008 7.35637426,5.58889946 6.61821773,5.58889946 C5.8800612,5.58889946 5.20341771,5.65920008 4.60000404,5.79980132 C4.08446614,5.3750684 3.41953943,5.12901622 2.69602886,5.15830815 C1.23143256,5.216892 0.0451095658,6.41493177 0.00117167703,7.88538644 C-0.033978634,9.13029329 0.742257402,10.1994486 1.83777543,10.6066064 C2.19806612,11.7900002 2.95086861,13.0847033 4.02881148,14.40284 C4.31294317,14.7484847 4.31294317,15.2464474 4.02881148,15.5920921 C1.86999655,18.2283655 1.01467231,20.776763 2.0076686,22.4961991 C2.75168352,23.7850438 4.41546491,24.4060326 6.61821773,24.4060326 C7.35637426,24.4060326 8.15311465,24.335732 8.9996513,24.19806 C9.43903019,24.1248301 9.8725507,24.3738115 10.0277979,24.7926861 C11.2316961,27.9767184 13.0126451,29.9949321 14.9986377,29.9949321 C16.9846303,29.9949321 18.7655794,27.9767184 19.9665484,24.7926861 C20.1247248,24.3738115 20.5553161,24.1277593 20.9976241,24.19806 C21.8412316,24.335732 22.6409012,24.4060326 23.3790577,24.4060326 C24.1172142,24.4060326 24.7938577,24.335732 25.3972714,24.1951308 C25.9098801,24.6198637 26.577736,24.8659159 27.3012466,24.8366239 C28.7687721,24.7780401 29.9550951,23.5800003 29.999033,22.1095456 C30.0312541,20.867568 29.255018,19.7954835 28.1595,19.3912549 Z M9.01136808,22.2940848 C8.15311465,22.4522612 7.34758669,22.5313494 6.62114692,22.5313494 C5.7599643,22.5313494 5.01594939,22.4141817 4.47111957,22.1944922 C4.06689099,22.0304574 3.7856885,21.8166264 3.63629968,21.5559282 C3.44590216,21.2249295 3.42539781,20.7504003 3.57478663,20.1469866 C3.77690092,19.3414587 4.27193447,18.3894711 5.00716181,17.3906164 C5.21806367,17.1035555 5.4465407,16.8194238 5.68966368,16.532363 C5.77753946,16.4298412 5.92985747,16.4210536 6.02652083,16.5118586 C6.72073947,17.1621394 7.48818793,17.8036325 8.31714943,18.4275506 C8.52219291,18.5827978 8.65400658,18.8112748 8.6862277,19.0661145 C8.81218298,20.0942611 8.98793453,21.081399 9.20176559,22.0070239 C9.22812833,22.1388376 9.14318174,22.267722 9.01136808,22.2940848 L9.01136808,22.2940848 Z M9.1988364,7.98497899 C8.98500534,8.91060385 8.80925379,9.89481256 8.68329851,10.9258884 C8.65107739,11.1807281 8.51926372,11.4092051 8.31422024,11.5644523 C7.48525874,12.1883704 6.72073947,12.8327927 6.02359164,13.4801443 C5.92692828,13.5709493 5.77168107,13.5621617 5.68673449,13.4596399 C5.4436115,13.1725791 5.21513448,12.8855182 5.00423262,12.6013865 C4.48576553,11.8954511 4.08739534,11.2129492 3.823768,10.5919604 C4.87534814,10.1848026 5.62522144,9.16251441 5.62522144,7.96740384 C5.62522144,7.81801502 5.61350467,7.66862619 5.59007113,7.52509576 C5.90642393,7.48408706 6.25206866,7.46358271 6.62114692,7.46358271 C7.34758669,7.46358271 8.15311465,7.5456001 9.01136808,7.7037765 C9.14318174,7.72721004 9.22812833,7.85609452 9.1988364,7.98497899 L9.1988364,7.98497899 Z M15.9359794,14.997466 C15.9359794,15.5159331 15.5171048,15.9348077 14.9986377,15.9348077 C14.4801706,15.9348077 14.0612961,15.5159331 14.0612961,14.997466 C14.0612961,14.478999 14.4801706,14.0601244 14.9986377,14.0601244 C15.5171048,14.0601244 15.9359794,14.478999 15.9359794,14.997466 Z M11.6740041,6.1659504 C11.7999594,5.81151809 11.9347023,5.46880256 12.0782327,5.14366218 C12.5761955,4.00713546 13.1532464,3.10201495 13.7478725,2.52496401 C14.196039,2.0943727 14.6149135,1.87468326 14.9986377,1.87468326 C15.382362,1.87468326 15.8012365,2.0943727 16.249403,2.5278932 C16.8440291,3.10494414 17.4240092,4.01006465 17.9190427,5.14659138 C18.0625732,5.47173175 18.197316,5.81151809 18.3232713,6.1659504 C18.3672092,6.29190568 18.2998378,6.42957773 18.1709533,6.46765723 C17.3595669,6.71370941 16.5276763,7.01248705 15.6811396,7.36106097 C15.2367844,7.54202546 14.7400483,7.54621732 14.2927023,7.37277774 C13.6014129,7.10329202 12.5469035,6.69906345 11.823393,6.47058643 C11.6974377,6.42664854 11.6271371,6.28897648 11.6740041,6.1659504 L11.6740041,6.1659504 Z M18.3232713,23.8319109 C18.197316,24.1863432 18.0625732,24.5261295 17.9190427,24.8512699 C17.42108,25.9877966 16.8440291,26.8929171 16.249403,27.4699681 C15.8012365,27.9005594 15.382362,28.1202488 14.9986377,28.1202488 C14.6149135,28.1202488 14.196039,27.9005594 13.7478725,27.4670389 C13.1532464,26.889988 12.5732663,25.9848674 12.0782327,24.8483407 C11.9347023,24.5232003 11.7999594,24.183414 11.6740041,23.8289817 C11.6300662,23.7030264 11.6974377,23.5653544 11.8263222,23.5272749 C12.6289209,23.2841519 13.452024,22.9883034 14.2927023,22.6455879 C14.7467272,22.4610488 15.2564067,22.4610488 15.7104315,22.6455879 C16.5481806,22.9883034 17.3742129,23.2841519 18.1738825,23.5272749 C18.2998378,23.5682836 18.3701384,23.7059556 18.3232713,23.8319109 L18.3232713,23.8319109 Z M20.9859074,7.70084731 C21.8441608,7.54267091 22.6496888,7.46358271 23.3761285,7.46065352 C24.2373111,7.46065352 24.9813261,7.57782122 25.5261559,7.79751067 C25.9303845,7.96154545 26.2115869,8.17537651 26.3609758,8.43607465 C26.5513733,8.76707341 26.5718776,9.24160261 26.4224888,9.84501629 C26.2203745,10.6505442 25.725341,11.6025318 24.9901136,12.6013865 C24.7792118,12.8884474 24.5507348,13.1725791 24.3076118,13.4596399 C24.219736,13.5621617 24.067418,13.5709493 23.9707546,13.4801443 C23.276536,12.8298635 22.5090875,12.1883704 21.680126,11.5644523 C21.4750825,11.4092051 21.3432689,11.1807281 21.3110477,10.9258884 C21.1850925,9.89774175 21.0093409,8.91060385 20.7955099,7.98497899 C20.7691471,7.85609452 20.8540937,7.72721004 20.9859074,7.70084731 L20.9859074,7.70084731 Z M24.372054,22.0275283 C24.372054,22.1769171 24.3837708,22.3263059 24.4072043,22.4698363 C24.0908515,22.510845 23.7452068,22.5313494 23.3761285,22.5313494 C22.6496888,22.5313494 21.8441608,22.449332 20.9859074,22.2940848 C20.8540937,22.2706512 20.7691471,22.1417668 20.8013682,22.0099531 C21.0151993,21.0843282 21.1909509,20.1001195 21.3169061,19.0690437 C21.3491273,18.814204 21.4809409,18.585727 21.6859844,18.4304798 C22.5149459,17.8065617 23.2794652,17.1621394 23.976613,16.5147878 C24.0732764,16.4239828 24.2285236,16.4327704 24.3134702,16.5352922 C24.5565931,16.822353 24.7850702,17.1094139 24.995972,17.3935456 C25.5144391,18.099481 25.9128093,18.7819829 26.1764366,19.4029717 C25.1189981,19.8101295 24.372054,20.8353469 24.372054,22.0275283 L24.372054,22.0275283 Z" id="形状" fill="url(#linearGradient-11)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>