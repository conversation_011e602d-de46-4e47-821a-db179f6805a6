<?xml version="1.0" encoding="UTF-8"?>
<svg width="108px" height="89px" viewBox="0 0 108 89" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>巡逻交班相关-选中</title>
    <defs>
        <radialGradient cx="66.4988611%" cy="7.25124484%" fx="66.4988611%" fy="7.25124484%" r="142.769356%" gradientTransform="translate(0.664989,0.072512),scale(1.000000,0.820069),rotate(115.650545),translate(-0.664989,-0.072512)" id="radialGradient-1">
            <stop stop-color="#F9AC2C" offset="0%"></stop>
            <stop stop-color="#794D03" offset="36.21%"></stop>
            <stop stop-color="#492E02" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="32.5368933%" cy="0.168034602%" fx="32.5368933%" fy="0.168034602%" r="115.56134%" gradientTransform="translate(0.325369,0.001680),scale(1.000000,0.820069),rotate(81.411501),translate(-0.325369,-0.001680)" id="radialGradient-2">
            <stop stop-color="#F8AE31" offset="0%"></stop>
            <stop stop-color="#D98F13" offset="26.9824902%"></stop>
            <stop stop-color="#342101" offset="99.4560615%"></stop>
        </radialGradient>
        <linearGradient x1="50.0691343%" y1="-57.0304998%" x2="50.0691343%" y2="98.9547346%" id="linearGradient-3">
            <stop stop-color="#382F1F" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#AFAAA3" stop-opacity="0.16" offset="44.431545%"></stop>
            <stop stop-color="#FDBE55" stop-opacity="0.7292" offset="81.17%"></stop>
            <stop stop-color="#FFE8C1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.2192405%" y1="14.2603685%" x2="50.2192405%" y2="100.013889%" id="linearGradient-4">
            <stop stop-color="#34311E" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#AFADA3" stop-opacity="0.16" offset="51.52%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.27" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50.0038523%" y1="37.8767156%" x2="50.0038523%" y2="100.228322%" id="linearGradient-5">
            <stop stop-color="#342B1D" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#AFAAA3" stop-opacity="0.16" offset="18.33%"></stop>
            <stop stop-color="#F3A21C" stop-opacity="0.1964" offset="23.47%"></stop>
            <stop stop-color="#FFBB4A" stop-opacity="0.7292" offset="74.21%"></stop>
            <stop stop-color="#FDE3B9" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="182.077524%" y1="57.6783756%" x2="-59.1575597%" y2="43.7863288%" id="linearGradient-6">
            <stop stop-color="#FFF3E0" offset="0%"></stop>
            <stop stop-color="#FCDBA3" offset="26.49%"></stop>
            <stop stop-color="#D58D14" offset="83.18%"></stop>
            <stop stop-color="#AA6B03" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100.714938%" y1="53.5529094%" x2="260.572041%" y2="42.4854005%" id="linearGradient-7">
            <stop stop-color="#FBEFDB" offset="0%"></stop>
            <stop stop-color="#FFDFA9" offset="26.49%"></stop>
            <stop stop-color="#DB9115" offset="60.26%"></stop>
            <stop stop-color="#B06F03" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="87.7778752%" cy="33.5538611%" fx="87.7778752%" fy="33.5538611%" r="129.896877%" gradientTransform="translate(0.877779,0.335539),scale(0.573840,1.000000),rotate(107.797572),scale(1.000000,1.037296),translate(-0.877779,-0.335539)" id="radialGradient-8">
            <stop stop-color="#F0D2A1" offset="0%"></stop>
            <stop stop-color="#B17208" offset="30.1%"></stop>
            <stop stop-color="#8D5A05" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="24.0059161%" cy="14.091471%" fx="24.0059161%" fy="14.091471%" r="98.3959633%" gradientTransform="translate(0.240059,0.140915),scale(0.573840,1.000000),rotate(72.203976),translate(-0.240059,-0.140915)" id="radialGradient-9">
            <stop stop-color="#F1D4A2" offset="0%"></stop>
            <stop stop-color="#B07209" offset="52.3385417%"></stop>
            <stop stop-color="#8F5B05" offset="98.4277685%"></stop>
        </radialGradient>
        <radialGradient cx="53.9126379%" cy="76.4689352%" fx="53.9126379%" fy="76.4689352%" r="297.930374%" gradientTransform="translate(0.539126,0.764689),scale(0.351695,1.000000),rotate(-165.123939),translate(-0.539126,-0.764689)" id="radialGradient-10">
            <stop stop-color="#FFEBCA" offset="0%"></stop>
            <stop stop-color="#AC6C01" offset="31.79%"></stop>
            <stop stop-color="#4F3201" offset="100%"></stop>
        </radialGradient>
        <radialGradient cx="72.4777579%" cy="15.7146618%" fx="72.4777579%" fy="15.7146618%" r="138.073829%" gradientTransform="translate(0.724778,0.157147),scale(0.882366,1.000000),rotate(100.631895),scale(1.000000,1.002007),translate(-0.724778,-0.157147)" id="radialGradient-11">
            <stop stop-color="#FFA50E" offset="0%"></stop>
            <stop stop-color="#FFF5E6" offset="74.9042218%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </radialGradient>
    </defs>
    <g id="所有页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图画板" transform="translate(-1182.000000, -277.000000)">
            <g id="巡逻交班相关-选中" transform="translate(1192.000000, 277.000000)">
                <g id="编组">
                    <rect id="矩形" x="0" y="0" width="88" height="88"></rect>
                    <g transform="translate(2.316245, 33.825308)">
                        <g transform="translate(9.633119, 15.065785)" fill-rule="nonzero" id="路径" stroke-opacity="0.08" stroke-width="0.6876">
                            <polygon stroke="#FCE250" fill="url(#radialGradient-1)" points="0 0 31.9387443 11.3200613 31.9387443 38.9464012 0 27.7611026"></polygon>
                            <polygon stroke="#F7DD4C" fill="url(#radialGradient-2)" points="64.1470138 0 32.2082695 11.3200613 32.2082695 38.9464012 64.1470138 27.62634"></polygon>
                        </g>
                        <polyline id="路径" stroke="url(#linearGradient-3)" stroke-width="8" points="72.906585 26.143951 83.2833078 29.7825421 41.6416539 44.4716692 0 29.7825421 10.3767228 26.143951"></polyline>
                        <polygon id="路径" stroke="url(#linearGradient-4)" stroke-width="2.8396" points="41.6416539 10.5114855 78.8361409 23.5834609 41.6416539 36.6554364 4.44716692 23.5834609 9.70290965 21.6967841 12.8024502 20.618683 22.5053599 17.2496172"></polygon>
                        <polygon id="路径" stroke="url(#linearGradient-5)" stroke-width="2.8396" points="41.6416539 24.1225115 78.8361409 37.194487 41.6416539 50.2664625 4.44716692 37.194487"></polygon>
                        <g transform="translate(9.498356, 16.089498)" fill-rule="nonzero">
                            <g>
                                <polygon id="path-89" fill="#000000" points="0 0 32.2082695 11.3200613 32.2082695 18.5972435 0 7.27718224"></polygon>
                                <polygon id="路径" fill="url(#linearGradient-6)" points="0 0 32.2082695 11.3200613 32.2082695 18.5972435 0 7.27718224"></polygon>
                            </g>
                            <g transform="translate(48.274169, 9.433384) scale(-1, 1) translate(-48.274169, -9.433384) translate(32.170035, 0.134763)">
                                <g fill="#000000" id="path-91">
                                    <polygon points="0 0 32.2082695 11.3200613 32.2082695 18.5972435 0 7.27718224"></polygon>
                                </g>
                                <g fill="url(#linearGradient-7)" id="路径">
                                    <polygon points="0 0 32.2082695 11.3200613 32.2082695 18.5972435 0 7.27718224"></polygon>
                                </g>
                            </g>
                        </g>
                        <g transform="translate(9.633119, 0.000000)" fill-rule="nonzero" id="路径" stroke-width="0.6876">
                            <polygon stroke-opacity="0.08" stroke="#FAE14E" fill="url(#radialGradient-8)" points="0 11.4548239 31.9387443 22.7748851 31.9387443 29.7825421 0 18.4624809"></polygon>
                            <polygon stroke-opacity="0.08" stroke="#FDE34D" fill="url(#radialGradient-9)" points="64.1470138 11.4548239 32.2082695 22.7748851 32.2082695 29.7825421 64.1470138 18.4624809"></polygon>
                            <polygon stroke-opacity="0.36" stroke="#918022" fill="url(#radialGradient-10)" points="32.3430322 0 64.1470138 11.1852986 32.3430322 22.3705972 0.539050536 11.1852986"></polygon>
                        </g>
                    </g>
                </g>
                <g id="法警工作台" transform="translate(22.000000, 2.000000)" fill-rule="nonzero">
                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="44" height="44"></rect>
                    <path d="M19.4476563,12.9550781 L24.5566406,12.9550781 L24.5566406,13.4191406 L19.4476563,13.4191406 L19.4476563,12.9550781 Z M29.7,35.4105469 L14.3,35.4105469 C13.2386719,35.4105469 12.375,36.2097656 12.375,37.1980469 C12.375,38.1863281 13.2386719,38.9855469 14.3,38.9855469 L29.7,38.9855469 C30.7613281,38.9855469 31.625,38.1863281 31.625,37.1980469 C31.625,36.2097656 30.7613281,35.4105469 29.7,35.4105469 Z M20.9214844,12.2332031 C21.3640625,12.4222656 21.5703125,12.5597656 21.5832031,12.5683594 C21.7121094,12.6757812 21.8625,12.7144531 21.9957031,12.7230469 C22.1289062,12.7144531 22.2835937,12.6757812 22.4082031,12.5683594 C22.4210937,12.5597656 22.6273437,12.4222656 23.0699219,12.2332031 C23.0914062,12.2246094 23.0871094,12.1902344 23.0613281,12.1902344 C22.9667969,12.1816406 22.8121094,12.1773437 22.6316406,12.1859375 C22.5886719,12.1859375 22.5542969,12.1558594 22.5542969,12.1085937 C22.5542969,11.7992187 22.3136719,11.5457031 21.9914062,11.4941406 C21.6691406,11.5414062 21.4285156,11.7992187 21.4285156,12.1085937 C21.4285156,12.1515625 21.3941406,12.1859375 21.3511719,12.1859375 C21.1707031,12.1773437 21.0160156,12.1816406 20.9214844,12.1902344 C20.9042969,12.1902344 20.9,12.2246094 20.9214844,12.2332031 L20.9214844,12.2332031 Z M37.4,5.01445312 L6.6,5.01445312 C4.47304687,5.01445312 2.75,6.6171875 2.75,8.58945312 L2.75,28.25625 C2.75,30.2328125 4.47304687,31.83125 6.6,31.83125 L37.4,31.83125 C39.5269531,31.83125 41.25,30.2285156 41.25,28.25625 L41.25,8.58945312 C41.25,6.6171875 39.5269531,5.01445312 37.4,5.01445312 L37.4,5.01445312 Z M21.9484375,10.1707031 L22.0515625,10.1707031 C24.0496094,10.1019531 26.1679688,11.2621094 26.1507813,12.1128906 C26.1378906,12.890625 25.2484375,13.2644531 25.1066406,13.3203125 C25.09375,13.3246094 25.0894531,13.3375 25.09375,13.3460937 C25.1410156,13.5351562 25.1152344,14.1109375 25.1152344,14.1109375 C25.1152344,14.1109375 25.0679688,14.2097656 24.9820313,14.3300781 C24.9777344,14.334375 24.9777344,14.3386719 24.9777344,14.3472656 C24.9777344,15.5976562 24.6511719,16.4785156 24.2128906,17.0972656 C23.9507813,17.4710937 23.65,17.7503906 23.3535156,17.9566406 C22.7046875,18.4121094 22.0902344,18.5195312 22.0085938,18.528125 L22.0042969,18.528125 C21.9269531,18.5195312 21.3125,18.4121094 20.659375,17.9566406 C20.3628906,17.7503906 20.0621094,17.4710937 19.8,17.0972656 C19.3617188,16.4785156 19.0351563,15.5976562 19.0351563,14.3472656 C19.0351563,14.3429688 19.0351563,14.334375 19.0308594,14.3300781 C18.940625,14.2097656 18.8976563,14.1109375 18.8976563,14.1109375 C18.8976563,14.1109375 18.871875,13.5351562 18.9191406,13.3460937 C18.9234375,13.3375 18.9148438,13.3246094 18.90625,13.3203125 C18.7644531,13.2644531 17.8792969,12.890625 17.8621094,12.1128906 C17.8320313,11.2621094 19.9503906,10.1019531 21.9484375,10.1707031 Z M22.4296875,25.025 L21.5703125,25.025 C18.7644531,24.9734375 15.9671875,24.4578125 15.5546875,24.3460937 C15.5675781,24.1871094 15.5804688,24.0238281 15.5976563,23.8605469 C15.7050781,22.7691406 15.8726563,21.5273437 16.15625,20.3628906 C16.2378906,19.9460937 16.225,19.834375 16.534375,19.5851562 C16.7792969,19.3917969 18.3390625,19.3488281 19.1082031,19.3402344 C19.215625,19.3402344 19.3230469,19.3359375 19.4304687,19.3316406 C19.628125,19.3230469 19.8300781,19.3101562 20.0277344,19.2929688 C20.00625,19.4347656 19.9847656,19.5980469 19.971875,19.7742187 C19.9675781,19.86875 19.9632812,19.971875 19.9632812,20.0707031 C19.9632812,20.2082031 19.971875,20.35 19.9847656,20.4917969 C20.00625,20.659375 20.0363281,20.8269531 20.0921875,20.9859375 C20.3328125,21.7464844 20.7238281,22.4941406 21.2480469,22.9453125 L21.5230469,21.1277344 C21.5402344,21.0160156 21.6390625,20.9300781 21.7507812,20.9300781 L22.2363281,20.9300781 C22.3523437,20.9300781 22.446875,21.0160156 22.4640625,21.1277344 L22.7390625,22.9453125 C23.2632812,22.4984375 23.65,21.7421875 23.8949219,20.9859375 C23.9464844,20.8269531 23.9808594,20.659375 24.0023437,20.4917969 C24.0195312,20.35 24.0238281,20.2082031 24.0238281,20.0707031 C24.0238281,19.9675781 24.0195312,19.86875 24.0152344,19.7742187 C24.0023437,19.5980469 23.9851562,19.4347656 23.959375,19.2929688 C24.1570312,19.3101562 24.3589844,19.3273438 24.5566406,19.3316406 C24.6640625,19.3359375 24.7714844,19.3402344 24.8789062,19.3402344 C25.6480469,19.3488281 27.2121094,19.3917969 27.4527344,19.5851562 C27.7664062,19.834375 27.7535156,19.9460937 27.8308594,20.3628906 C28.1144531,21.5273438 28.2863281,22.7691406 28.3894531,23.8605469 C28.4109375,24.028125 28.4238281,24.1914062 28.4367187,24.3460937 C28.0328125,24.4578125 25.2355469,24.9734375 22.4296875,25.025 L22.4296875,25.025 Z" id="形状" fill="url(#radialGradient-11)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>