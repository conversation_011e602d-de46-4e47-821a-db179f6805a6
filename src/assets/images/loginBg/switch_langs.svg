<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_0_11822)">
<g clip-path="url(#clip1_0_11822)">
<g filter="url(#filter0_d_0_11822)">
<circle cx="30" cy="30" r="30" fill="#0A3E5C" fill-opacity="0.498039"/>
<circle cx="30" cy="30" r="29" stroke="#0398D5" stroke-opacity="0.631373" stroke-width="2"/>
</g>
<g filter="url(#filter1_d_0_11822)">
<circle cx="29.84" cy="29.84" r="21.84" fill="url(#paint0_linear_0_11822)"/>
<circle cx="29.84" cy="29.84" r="21.34" stroke="url(#paint1_linear_0_11822)"/>
</g>
</g>
<g clip-path="url(#clip2_0_11822)">
<circle cx="30" cy="30" r="29" fill="url(#paint2_linear_0_11822)" stroke="url(#paint3_linear_0_11822)" stroke-width="2"/>
<circle opacity="0.2" cx="29.8163" cy="29.8163" r="20.8163" fill="url(#paint4_linear_0_11822)"/>
</g>
<rect opacity="0.01" x="11.3794" y="11.3794" width="35.6403" height="35.6403" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.6624 20.1124C31.5957 18.5842 30.3375 17.3795 28.8079 17.3794H20.2369L20.1124 17.3824C18.5842 17.449 17.3795 18.7073 17.3794 20.2369V28.8079L17.3824 28.9324C17.449 30.4606 18.7073 31.6652 20.2369 31.6654H28.8079L28.9324 31.6624C30.4606 31.5957 31.6652 30.3375 31.6654 28.8079V20.2369L31.6624 20.1124ZM38.5219 27.0934C40.0515 27.0935 41.3097 28.2982 41.3764 29.8264L41.3794 29.9509V38.5219C41.38 40.0518 40.1749 41.3105 38.6464 41.3764L38.5219 41.3794H29.9509C28.4213 41.3792 27.163 40.1746 27.0964 38.6464L27.0934 38.5219V32.8069H29.4934C30.3726 32.8077 31.216 32.4589 31.8378 31.8374C32.4596 31.2158 32.8088 30.3726 32.8084 29.4934V27.0934H38.5219ZM19.6939 32.7769C20.0032 32.7764 20.2997 32.9008 20.516 33.1219L24.6548 40.1991L24.6529 40.1989L24.6549 40.1992C21.3825 39.8502 18.8094 37.2194 18.5464 33.9244C18.5464 33.3184 19.0144 32.8219 19.6084 32.7784L19.6939 32.7754V32.7769ZM24.6549 40.1992L24.6548 40.1991L24.7933 40.2124H24.7924L24.7905 40.2123C24.7452 40.2084 24.7 40.204 24.6549 40.1992ZM33.5614 31.0939H35.2924L37.6864 37.9084H36.2134L35.6974 36.4699H33.0079L32.4934 37.9069H31.0939L33.5614 31.0939ZM34.4089 32.4199L35.3284 35.3299H33.3784L34.3714 32.4199H34.4089ZM24.9904 20.2369H23.7649V22.0294H20.8084V26.3149H22.0339V25.6549H23.8009V28.8094H25.0984V25.6594H26.9044V26.2084H28.2394V22.0309H25.0969V21.1159C25.0969 20.8459 25.1329 20.6149 25.2064 20.4199C25.2257 20.3862 25.238 20.349 25.2424 20.3104C25.2424 20.285 25.1575 20.2611 24.9892 20.2357L24.9889 20.2354H24.9874L24.9892 20.2357L24.9904 20.2369ZM26.9014 24.6694V23.0584H25.0999V24.6694H26.9014ZM22.0324 23.0584H23.8009V24.6679H22.0324V23.0584ZM32.928 20.2001C33.1188 20.5898 33.5131 20.8385 33.9469 20.8429H33.9514C36.0227 21.105 37.6538 22.7361 37.9159 24.8074V24.8089C37.9165 25.1325 38.0536 25.4408 38.2935 25.6578C38.5335 25.8749 38.8539 25.9806 39.1759 25.9489C39.4981 25.9181 39.7922 25.7526 39.9857 25.4931C40.1791 25.2336 40.2539 24.9045 40.1914 24.5869C39.8224 21.4267 37.3293 18.9348 34.1689 18.5674C33.7424 18.4879 33.3075 18.6557 33.045 19.0011C32.7825 19.3465 32.7372 19.8105 32.928 20.2001Z" fill="#C8C8C8"/>
</g>
<defs>
<filter id="filter0_d_0_11822" x="-6" y="-6" width="72" height="72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.247059 0 0 0 0 0.780392 0 0 0 0 1 0 0 0 0.21 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_11822"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_11822" result="shape"/>
</filter>
<filter id="filter1_d_0_11822" x="6" y="6" width="45.6799" height="45.6799" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0135764 0 0 0 0 0.1308 0 0 0 0 0.191667 0 0 0 0.43 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_11822"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_11822" result="shape"/>
</filter>
<linearGradient id="paint0_linear_0_11822" x1="-1.62" y1="30.88" x2="26.46" y2="60.52" gradientUnits="userSpaceOnUse">
<stop stop-color="#197CB3" stop-opacity="0.552941"/>
<stop offset="0.828164" stop-color="#197CB3" stop-opacity="0.01"/>
<stop offset="1" stop-color="#197CB3" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint1_linear_0_11822" x1="-1.62" y1="30.88" x2="26.46" y2="60.52" gradientUnits="userSpaceOnUse">
<stop stop-color="#1E8AC6" stop-opacity="0.564706"/>
<stop offset="0.871691" stop-color="#1E8AC6" stop-opacity="0.01"/>
<stop offset="1" stop-color="#1E8AC6" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint2_linear_0_11822" x1="0" y1="0" x2="0" y2="60" gradientUnits="userSpaceOnUse">
<stop stop-color="#50D6FF" stop-opacity="0.423529"/>
<stop offset="0.0763019" stop-color="#50D6FF" stop-opacity="0.423529"/>
<stop offset="0.189446" stop-color="#4FD5FF" stop-opacity="0.31"/>
<stop offset="0.326616" stop-color="#50D3FF" stop-opacity="0.160784"/>
<stop offset="0.427761" stop-color="#55DAFF" stop-opacity="0.0823529"/>
<stop offset="0.534106" stop-color="#4ED7FF" stop-opacity="0.0509804"/>
<stop offset="0.639888" stop-color="#48DAFF" stop-opacity="0.027451"/>
<stop offset="0.75001" stop-color="#3FBFFF" stop-opacity="0.0156863"/>
<stop offset="1" stop-color="#50D6FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint3_linear_0_11822" x1="0" y1="0" x2="0" y2="60" gradientUnits="userSpaceOnUse">
<stop stop-color="#50D5FF" stop-opacity="0.631373"/>
<stop offset="0.403544" stop-color="#50D5FF" stop-opacity="0.01"/>
<stop offset="1" stop-color="#50D5FF" stop-opacity="0.01"/>
</linearGradient>
<linearGradient id="paint4_linear_0_11822" x1="9" y1="9" x2="9" y2="50.6327" gradientUnits="userSpaceOnUse">
<stop stop-color="#4DD6FF" stop-opacity="0.372549"/>
<stop offset="0.272235" stop-color="#4AD3FF" stop-opacity="0.160784"/>
<stop offset="0.611564" stop-color="#55DDFF" stop-opacity="0.03"/>
<stop offset="1" stop-color="#55DDFF" stop-opacity="0.01"/>
</linearGradient>
<clipPath id="clip0_0_11822">
<rect width="60" height="60" fill="white"/>
</clipPath>
<clipPath id="clip1_0_11822">
<rect width="60" height="60" fill="white"/>
</clipPath>
<clipPath id="clip2_0_11822">
<rect width="60" height="60" fill="white"/>
</clipPath>
</defs>
</svg>
