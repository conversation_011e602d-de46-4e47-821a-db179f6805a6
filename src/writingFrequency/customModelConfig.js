export const DeviceModelConfig = bfglob.modelConfig?.device ?? []
export const RepeaterModelConfig = bfglob.modelConfig?.repeater ?? []
export const DeviceNoLocale = bfglob.modelConfig?.deviceNoLocale === true
const ModelSeparator = bfglob.modelConfig?.modelSeparator ?? ':'

const DeviceModelList = []
const DeviceModelName = {}

export function getDeviceModelList() {
  return DeviceModelList
}

export function getDeviceModelName(model) {
  return DeviceModelName[model]
}

const RepeaterModelList = []
const RepeaterModelName = {}

export function getRepeaterModelList() {
  return RepeaterModelList
}

export function getRepeaterModelName(model) {
  return RepeaterModelName[model]
}

function decodeModelString(modelStr) {
  let [model, name] = modelStr.split(ModelSeparator)
  model = model?.trim() ?? ''
  name = name?.trim() ?? ''

  return { model, name }
}

function initModelConfig() {
  DeviceModelConfig.forEach(m => {
    const { model, name } = decodeModelString(m)

    if (model) {
      DeviceModelList.push(model)
    }
    if (name) {
      DeviceModelName[model] = name
    }
  })
  RepeaterModelConfig.forEach(m => {
    const { model, name } = decodeModelString(m)

    if (model) {
      RepeaterModelList.push(model)
    }
    if (name) {
      RepeaterModelName[model] = name
    }
  })
}

initModelConfig()
