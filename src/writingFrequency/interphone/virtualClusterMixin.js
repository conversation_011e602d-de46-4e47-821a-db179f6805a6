/**
 * 虚拟集群公共组件的vue实例混合配置
 */
export const VirtualClusterMixin = {
  computed: {
    // 由终端管理定义，只作显示处理
    vcGroupIdLabel() {
      const org = bfglob.gorgData.getDataByIndex(
        this.selectDeviceData?.devGroup,
      )
      if (org) {
        return org.orgShortName
      }

      if (
        this.virtualCluster.vcGroupId === 0xffff ||
        this.virtualCluster.vcGroupId === 0
      ) {
        // return this.$t('dialog.nothing')
        return this.$t('writeFreq.basedOnChannelDefaultGroupCall')
      }

      return (
        this.selectedAddressBook.find(
          book => book.id === this.virtualCluster.vcGroupId,
        )?.name ?? this.virtualCluster.vcGroupId
      )
    },
  },
}
