import {
  TD811CmdIo,
  CmdTable,
  VerifyResponse,
  CommonResponse,
  WorkingMode,
  IndDbOpt,
  DB_OPER_CODER,
  EncryptEnum,
  IND_READ_RECORD_BLOCK_CODER,
} from '@/writingFrequency/interphone/TD811CmdIo'
import { buf2base64, base642buf } from '@/writingFrequency/interphone/common'
import { getAuthenticationKey } from '@/writingFrequency/interphone/aesEncrypt'
import bfNotify from '@/utils/notify'
import commonToolMixin from '@/writingFrequency/interphone/commonToolMixin'

// 读取数据最大时间，15s
const ReadTimeout = 15 * 1000

// 根据是密码和工作模式确认加密方式
export function setEncryptType(instance, mode) {
  // instance.encrypt = EncryptEnum.None
  if (mode === WorkingMode.READ || mode === WorkingMode.FILE_READ) {
    instance.encrypt = EncryptEnum.RAes
  } else if (mode === WorkingMode.WRITE) {
    instance.encrypt = EncryptEnum.WAes
  } else {
    instance.encrypt = EncryptEnum.None
  }
}

export default {
  mixins: [commonToolMixin],
  data() {
    return {
      isReading: false,
      isWriting: false,
      readWriteFlag: '',
      readCount: 0,
    }
  },
  methods: {
    reopenUSBDevice() {
      return new Promise((resolve, reject) => {
        // 旧版本的TC918写频程序没有sl_reopenBfdxUSBDevice方法，不需要重新打开USB设备连接
        if (!this.QWebServer.sl_reopenBfdxUSBDevice) {
          return resolve(true)
        }
        this.QWebServer.sl_reopenBfdxUSBDevice(data => {
          if (data) {
            resolve(data)
          } else {
            reject(data)
          }
        })
      })
    },
    read(instance) {
      return new Promise((resolve, reject) => {
        const resetState = () => {
          this.setReadState(false)
          this.setWriteState(false)
        }
        if (!this.canRead()) {
          resetState()
          return reject()
        }

        const startReadTime = Date.now()
        const readData = () => {
          if (Date.now() - startReadTime > ReadTimeout) {
            resetState()
            console.error('can not read data')
            this.readCount = 0
            return reject()
          }

          this.readCount++
          this.QWebServer.sl_read_device_usb_data(base64Str => {
            if (!base64Str && this.readCount <= 10) {
              setTimeout(() => {
                readData()
              }, 100)
              return
            }

            this.readCount = 0
            if (!instance) {
              instance = new TD811CmdIo()
            }
            instance.getDbCoder = this.getClassInstance
            resolve(
              instance.decode(
                base642buf(base64Str),
                this.Model,
                this.ioPassword,
              ),
            )
          })
        }
        readData()
      })
    },
    async authVerify() {
      // 每次读、写操作前都先重新打开设备连接，如果失败则停止后续操作
      const isOpen = await this.reopenUSBDevice()
      if (!isOpen) {
        bfNotify.messageBox(this.$t('msgbox.disconnectDevice'), 'error')
        return Promise.reject('can not open USB device')
      }

      return new Promise((resolve, reject) => {
        const instance = new TD811CmdIo()
        instance.type = CmdTable.CC_IND_SIGNATURE_VERIFY
        instance.data = getAuthenticationKey()
        instance.encrypt = EncryptEnum.None
        this.write(instance.encode(this.Model, this.ioPassword))
        bfglob.console.log('CC_IND_SIGNATURE_VERIFY bytes:', instance.bytes)
        this.read(instance)
          .then(res => {
            if (!res.data || res.data.statusCode !== VerifyResponse.Success) {
              return reject('SIGNATURE_VERIFY_ERROR')
            }
            return res
          })
          .then(resolve)
          .catch(reject)
      })
    },
    setWorkMode(mode = WorkingMode.READ) {
      return new Promise((resolve, reject) => {
        const instance = new TD811CmdIo()
        instance.type = CmdTable.CC_IND_SET_WORK_MODE
        instance.data = [mode, 0, 0, 0]
        setEncryptType(instance, mode)
        this.write(instance.encode(this.Model, this.ioPassword))
        bfglob.console.log('CC_IND_SET_WORK_MODE bytes:', instance.bytes)
        this.read(instance)
          .then(res => {
            if (!res.data || res.data.statusCode !== CommonResponse.Success) {
              // 提示编程密码错误
              this.showWriteInFailInfo(this.$t('msgbox.programmingPwdError'))
              return reject('password error')
            }
            // 同步读频编程密码
            if (mode === WorkingMode.READ && this.ioPassword) {
              this.programReadPassword.md5Key = this.ioPassword
            }
            return res
          })
          .then(resolve)
          .catch(reject)
      })
    },
    getDbDataCount(tableId) {
      return new Promise((resolve, reject) => {
        const instance = new TD811CmdIo()
        instance.type = CmdTable.CC_IND_DB_OPER
        setEncryptType(instance, WorkingMode.READ)
        const dbOperCoder = new DB_OPER_CODER()
        dbOperCoder.data = [
          {
            tableId,
            operation: IndDbOpt.GET_COUNT,
          },
        ]
        instance.data = dbOperCoder.encode()
        this.write(instance.encode(this.Model, this.ioPassword))
        bfglob.console.log('GET_COUNT bytes:', instance.bytes)
        this.read(instance)
          .then(res => {
            if (res.data.operation === 3) {
              return resolve(res)
            }
            return reject()
          })
          .then(resolve)
          .catch(reject)
      })
    },
    readDbData({ tableId = this.TableIndex.deviceInfo, startIndex = 0 }) {
      return new Promise((resolve, reject) => {
        const instance = new TD811CmdIo()
        instance.type = CmdTable.CC_IND_DB_OPER
        setEncryptType(instance, WorkingMode.READ)
        const dbOperCoder = new DB_OPER_CODER()
        dbOperCoder.data = [
          {
            tableId,
            operation: IndDbOpt.SELECT,
            startIndex,
          },
        ]
        instance.data = dbOperCoder.encode()
        this.write(instance.encode(this.Model, this.ioPassword))
        bfglob.console.log('SELECT bytes:', instance.bytes)
        this.read(instance).then(resolve).catch(reject)
      })
    },
    whileReadData() {
      const IndexIterator = this.OperationOrderTable[Symbol.iterator]()
      // 不需要读取的数据表结构ID
      const exclude = [
        this.TableIndex.WriteFreqWritePassword,
        this.TableIndex.WriteFreqReadPassword,
      ]
      // eslint-disable-next-line
      return new Promise(async (resolve, reject) => {
        let iterator = IndexIterator.next()
        while (!iterator.done) {
          // 跳过排除的表数据查询
          const tableId = iterator.value
          if (exclude.includes(tableId)) {
            iterator = IndexIterator.next()
            continue
          }

          // 读取数据前，先读取数据总条数和单条数据大小，确定循环次数，再下发查询指令
          const dataCountRes = await this.getDbDataCount(tableId)
          const { structCount } = dataCountRes.data
          // 没有查询到数据表对应的数据，不下发查询指令，继续下一个数据表查询
          if (structCount === 0) {
            iterator = IndexIterator.next()
            continue
          }

          // 累计查询到的数据数量，计算循环查询条件
          let startIndex = 0
          while (startIndex < structCount) {
            const config = await this.readDbData({
              tableId,
              startIndex,
            })
            // 检验机型是否匹配
            if (
              tableId === this.TableIndex.DeviceInfo &&
              config.data.subData &&
              config.data.subData[0] &&
              !this.checkDeviceModel(config.data.subData[0])
            ) {
              return reject()
            }
            this.asyncLocalConfig(config.data)

            // 计算下次循环条件
            startIndex += config.data.structCount
          }

          // 继续查询下一个结构数据
          iterator = IndexIterator.next()
        }

        if (iterator.done) {
          resolve()
        } else {
          reject()
        }
      })
    },

    write(bytes) {
      if (!this.canWrite()) {
        return
      }

      this.QWebServer.sl_write_device_usb_data(buf2base64(bytes))
    },
    wrapperWriteData(coder, data) {
      const instance = new TD811CmdIo()
      instance.type = CmdTable.CC_IND_DB_OPER
      setEncryptType(instance, WorkingMode.WRITE)
      const dbOperCoder = new DB_OPER_CODER()
      dbOperCoder.data = [
        {
          tableId: coder.type,
          operation: this.InsertDataAfterClear.includes(coder.type)
            ? IndDbOpt.INSERT
            : IndDbOpt.UPDATE,
          structCount: (data.length / coder.structLen) | 0,
          structLen: coder.structLen,
          startIndex: 0,
          subData: data,
        },
      ]
      instance.data = dbOperCoder.encode()
      return instance.encode(this.Model, this.ioPassword)
    },
    encodeIteratorData({ coder, dataList, limit }) {
      const result = []
      if (dataList.length) {
        while (dataList.length) {
          coder.data = dataList.splice(0, limit)
          result.push(this.wrapperWriteData(coder, coder.encode()))
        }
      } else {
        result.push(this.wrapperWriteData(coder, coder.encode()))
      }
      return result
    },
    getBytesByType(type, option) {
      if (typeof type !== 'number') {
        return []
      }
      const opt = {
        iteration: true,
        limit: this.wfCountLimit || 3,
        ...option,
      }
      const ClassInstance = this.getClassInstance({ type })
      ClassInstance.type = type
      const data = this.getBeforeEncodeData(type)

      if (opt.iteration) {
        let list = data
        if (!Array.isArray(data)) {
          list = [data]
        }
        return this.encodeIteratorData({
          coder: ClassInstance,
          dataList: list,
          limit: opt.limit,
        })
      }

      return this.wrapperWriteData(ClassInstance, ClassInstance.encode())
    },
    writeIteratorArrayData(dataList) {
      return new Promise((resolve, reject) => {
        const iterator = dataList[Symbol.iterator]()
        const writeOneData = item => {
          if (!item) {
            this.setWriteState(false)
            return reject('write data not found')
          }
          if (item.done) {
            return resolve()
          }

          bfglob.console.log('writeOneData', item.value)
          this.write(item.value)
          // 如果下发的数据刚好为64的倍数，则需要下发空数据包
          if (item.value.length % 64 === 0) {
            this.write([])
          }

          this.read()
            .then(res => {
              switch (res.data.structCount) {
                case CommonResponse.Success:
                  return res
              }
              return reject('not read ACK')
            })
            .then(res => {
              writeOneData(iterator.next())
            })
            .catch(reject)
        }
        writeOneData(iterator.next())
      })
    },
    clearDbTable(tableId) {
      return new Promise((resolve, reject) => {
        const instance = new TD811CmdIo()
        instance.type = CmdTable.CC_IND_DB_OPER
        setEncryptType(instance, WorkingMode.WRITE)
        const dbOperCoder = new DB_OPER_CODER()
        dbOperCoder.data = [
          {
            tableId,
            operation: IndDbOpt.CLEAR,
            structCount: 0,
            structLen: 0,
            startIndex: 0,
          },
        ]
        instance.data = dbOperCoder.encode()
        const bytes = instance.encode(this.Model, this.ioPassword)
        bfglob.console.log('clearDbTable', bytes)
        this.write(bytes)
        this.read(instance)
          .then(res => {
            switch (res.data.structCount) {
              case CommonResponse.Success:
                return res
            }
            return reject('clear table error:' + tableId)
          })
          .then(resolve)
          .catch(reject)
      })
    },
    writeInData() {
      return new Promise((resolve, reject) => {
        if (!Array.isArray(this.writeDataOption)) {
          reject('not found write option')
          return
        }

        const iterator = this.writeDataOption[Symbol.iterator]()
        const writeIteratorData = item => {
          if (item.done) {
            resolve(item.done)
            return
          }
          const config = item.value
          if (!config) {
            reject('not found write option')
            return
          }

          // 判断是单项数据，还是多项数据列表
          // 单项数据只能执行更新
          let promise = Promise.resolve()
          // 多项数据列表需要先清除数据，再重新写入
          if (this.InsertDataAfterClear.includes(config.type)) {
            promise = this.clearDbTable(config.type)
          }
          promise
            .then(() => {
              // 如果是列表类型的数据，空数据则不再写入，如空的联系人、预制短信等
              if (
                this.InsertDataAfterClear.includes(config.type) &&
                this.getBeforeEncodeData(config.type).length === 0
              ) {
                return Promise.resolve()
              }

              return this.writeIteratorArrayData(
                this.getBytesByType(config.type, config.option),
              )
            })
            .then(res => {
              return writeIteratorData(iterator.next())
            })
            .catch(e => {
              reject(e)
              const message = config.failedMsg || this.$t('msgbox.writeInFail')
              this.showWriteInFailInfo(message, e)
            })
        }
        writeIteratorData(iterator.next())
      })
    },
    writeDataConfig() {
      return (
        Promise.resolve()
          .then(() => {
            // 开始写入取数据提示
            bfNotify.messageBox(this.$t('msgbox.startWriting'))
            this.setWriteState(true)
            return this.authVerify()
          })
          .then(() => {
            return this.setWorkMode(WorkingMode.WRITE)
          })
          .then(this.writeInData)
          .then(() => {
            // 写完数据后，需要下发空闲工作模式
            return this.setWorkMode(WorkingMode.IDLE)
          })
          .then(() => {
            // 写频完成
            bfNotify.messageBox(this.$t('msgbox.writeInSuccess'), 'success')
            bfglob.console.log('Write succeeded')
            // 更新终端最后写频时间
            this.updateDeviceLastRfWriteTime()
          })
          .catch(err => {
            bfglob.console.error(err)
          })
          // 不管读写成功，还是失败，都要恢复按钮状态
          .finally(() => {
            this.setWriteState(false)
          })
      )
    },
    // readItem：读一项数据时为true
    // itemTableId：读一项数据时，需要传入tableId
    readDataFlow(readItemParams = { readItem: false, itemTableId: 0 }) {
      const { readItem, itemTableId } = readItemParams
      this.setReadState(true)
      return (
        this.authVerify()
          .then(() => {
            return this.setWorkMode(WorkingMode.READ)
          })
          .then(readItem ? this.readDataItem(itemTableId) : this.whileReadData)
          .then(() => {
            // 读完数据后，需要下发空闲工作模式
            return this.setWorkMode(WorkingMode.IDLE)
          })
          .then(() => {
            // 读取完成
            bfNotify.messageBox(this.$t('msgbox.readSuccess'), 'success')
            bfglob.console.log('Read succeeded')
          })
          .catch(err => bfglob.console.error(err))
          // 不管读写成功，还是失败，都要恢复按钮状态
          .finally(() => {
            this.setReadState(false)
          })
      )
    },
    // 读取一项数据
    async readDataItem(tableId) {
      // 读取数据前，先读取数据总条数和单条数据大小，确定循环次数，再下发查询指令
      const dataCountRes = await this.getDbDataCount(tableId)
      const { structCount } = dataCountRes.data
      // 没有查询到数据表对应的数据，不下发查询指令，直接退出
      if (structCount === 0) {
        return
      }

      // 累计查询到的数据数量，计算循环查询条件
      let startIndex = 0
      while (startIndex < structCount) {
        const config = await this.readDbData({
          tableId,
          startIndex,
        })
        // 检验机型是否匹配
        if (
          tableId === this.TableIndex.DeviceInfo &&
          config.data.subData &&
          config.data.subData[0] &&
          !this.checkDeviceModel(config.data.subData[0])
        ) {
          return Promise.reject()
        }
        this.asyncLocalConfig(config.data)

        // 计算下次循环条件
        startIndex += config.data.structCount
      }
      return Promise.resolve()
    },

    async readRecordFrame(recordId, frameCount) {
      const frameData = []
      const dataLen = frameCount * 32
      for (let i = 0; i < dataLen / 256; i++) {
        const instance = new TD811CmdIo()
        instance.type = CmdTable.CC_IND_READ_RECORD_BLOCK
        const readRecordBlock = new IND_READ_RECORD_BLOCK_CODER()
        readRecordBlock.data = [
          {
            recordId,
            dataOffset: i * 256,
          },
        ]
        instance.data = readRecordBlock.encode()
        setEncryptType(instance, WorkingMode.READ)
        this.write(instance.encode(this.Model, this.ioPassword))
        const res = await this.read(instance)
        if (!res || !res.data.nData) {
          return Promise.resolve(false)
        }
        const blockData = res.data.nData.slice(4)
        frameData.push(blockData)
      }
      return Promise.resolve(frameData)
    },
  },
  computed: {
    menuTreeRef() {
      return this.$refs[this.menuTreeId]
    },
  },
  watch: {},
}
