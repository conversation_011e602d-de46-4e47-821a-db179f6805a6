// 手台终端机型
export const TD800SDCModel = 'TD081000'
export const TD510SDCModel = '510SDC00'
export const TD510SVTModel = '510SVT00'
export const TD511SDCModel = '511SDC00'
export const TD511FRModel = '511FR000'
export const TD511SVTModel = '511SVT00'
export const TD818SDCModel = '818SDC00'
export const TD818SVTModel = '818SVT00'
export const TD910SDCModel = 'DP109SDC'
export const TD910PSDCModel = '109PSDC1'
export const TD920Model = 'DP140100'
export const TD930SDCModel = '930SDC00'
export const TD930SDCR7F = 'R930SDC0'
export const BP750SDC = 'R935SDC0'
export const TD930SVTModel = '930SVT00'
export const TD930SVTR7F = 'R930SVT0'
export const BP750SVT = 'R935SVT0'
export const TD880SDCModel = '880SC200' // 880SC100 & 880SC200
export const TD880SVTModel = '880SVT00'
export const BP660Model = 'BP660100'
export const BP860SDCModel = 'D860SD00'
export const BP860SVTModel = 'D860SV00'
export const BP610Model = 'BP660200'
export const BP620Model = 'BP660300'

// 车载台
export const TM825SDCModel = '825SDC00'
export const TM825FRModel = '8250FR00'
export const TM8250SDCR7F = 'R82500S0'
export const TM8250SVTR7F = 'R82500V0'

// 中继设备机型
export const TR805005 = 'TR805005'
export const TR900M = 'TR900M'
export const TR90FR = 'TR90FR'
export const TR925M = 'TR925M'
export const TR925D = 'TR925D'
export const TR925R = 'TR925R'
export const TR850M = 'TR850M' // BF-TR8500M
export const RR850M = 'RR850M' // BF-TR8500M(R7F)
export const RR900M = 'RR900M' // BF-TR900M(R7F)
export const BR105M = 'BR105M'

// 同频同播控制器
export const DZ1480 = 'DZ1480'

// 虚拟集群控制器
export const DZ148SVT = 'DZ1481'
