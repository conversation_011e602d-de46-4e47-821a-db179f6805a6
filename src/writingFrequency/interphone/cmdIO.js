import {
  DefModel,
  base642buf,
  buf2base64,
  readU16LE,
  writeU16LE,
} from './common'
import {
  Encrypt,
  Decrypt,
  encryptedDataTo16Byte,
  AuthenticationKeys,
} from './aesEncrypt'
// 数据帧类型
export const DataTypes = {
  read: 0x52,
  write: 0x57,
  compare: 0x43,
  data: 0x44,
  V: 0x56,
}
// 确定(ACK) 否定(NCK) 结束(EOT) 取消(CAN)
export const AnswerCode = {
  ACK: 0x06,
  NCK: 0x15,
  EOT: 0x04,
  CAN: 0x18,
  RECORD_EOT: 0x4e,
}
// 数据帧头
const cmdHead = 0x02
// 数据帧尾
const cmdFoot = 0x03

export function getBase64AuthCode(model = DefModel) {
  return buf2base64(AuthenticationKeys[model] || AuthenticationKeys[DefModel])
}

/*
数据帧格式 帧的数据都是以小端的方式存放，即低位数据在前（低地址），高位数据在后（高地址）。
帧头      帧类型    数据长度          数据区	     数据区校验和   帧尾
(0x02)  (1 Byte)  (2 Byte)  (长度由数据长度指定)    (1Byte)	 (0x03)
帧头：标识一帧开始。
帧类型：帧的类型标识字，标识该帧是读数据还是写数据等。
数据长度：帧中数据区的长度。可以为0。
数据区：帧中有效的数据。
数据区校验和：数据区的累加和。当数据长度为0，则该值也为0。
帧尾：标识一帧结束。
*/

// 编解码指令数据帧
export class CmdIO {
  constructor() {
    this.type = DataTypes.read
    this.data = []
    this.checkSum = 0
  }

  static aesEncrypt(buffer, model = DefModel, pwd = '') {
    try {
      return buf2base64(Encrypt(buffer, model, pwd))
    } catch (e) {
      return buf2base64(new ArrayBuffer(0))
    }
  }

  static aesDecrypt(buffer, model = DefModel, pwd = '') {
    try {
      return Decrypt(buffer, model, pwd)
    } catch (e) {
      return new Uint8Array([])
    }
  }

  static decode(base64String, model = DefModel, pwd = '') {
    // 解密
    const buf = base642buf(base64String)
    const oneFrameData = CmdIO.aesDecrypt(buf, model, pwd)

    // 解析成一个对象结构
    const cmdObj = {
      origin: oneFrameData,
    }
    // 指令类型
    cmdObj.type = oneFrameData[1]
    // 数据长度
    cmdObj.dataLen = readU16LE(oneFrameData, 2)
    // 数据区数据
    cmdObj.data = oneFrameData.subarray(4, cmdObj.dataLen + 4)
    // 数据区校验和
    cmdObj.checkSum = oneFrameData[4 + cmdObj.dataLen]

    return cmdObj
  }

  static readDecryptBytes(base64String, model = DefModel, pwd = '') {
    // 解密
    const buf = base642buf(base64String)
    return CmdIO.aesDecrypt(buf, model, pwd)
  }

  // dataLen:数据长度 data:数据区 checkSum:数据区校验和
  encode(model = DefModel, pwd = '') {
    let bytes = []
    bytes.push(cmdHead)
    bytes.push(this.type)
    bytes = bytes.concat(writeU16LE(this.data.length))
    bytes = bytes.concat(this.data.slice(0, this.data.length))
    bytes.push(this.checkSum)
    bytes.push(cmdFoot)

    // 补齐16字节的倍数
    bytes = encryptedDataTo16Byte(bytes)
    bfglob.console.log('CmdIO encode:', bytes)

    // 加密成base64格式字符串
    return CmdIO.aesEncrypt(bytes, model, pwd)
  }
}
