import { cloneDeep, merge } from 'lodash'
import { getModelName } from '../modelInfo'
import {
  bool,
  <PERSON>ceBase,
  DeviceInfo,
  DeviceInfoProto,
  DigitalAddressProto,
  generateEnumObject,
  IdentityInfoProto,
  loadProtocol,
  marshalProto,
  Password,
  PasswordProto,
  PatrolConfigProto,
  PhoneBookProto,
  SecondIdentityInfo,
  subTable,
  TrackMonitorProto,
  unmarshalProto,
  Unset,
} from './common'
import { TD880SVTModel } from './models'

export const Model = TD880SVTModel
const BaseOptions = {
  model: Model,
  version: 0,
  structLen: 0,
}

// 生成软按键功能枚举对象
export const SoftKeyFuncDefine = generateEnumObject({
  // 未定义软按键功能
  NONE: 0,
  // 单键功能呼叫1
  SPDCALL1: 1,
  // 单键功能呼叫2
  SPDCALL2: 2,
  // 单键功能呼叫3
  SPDCALL3: 3,
  //  单键功能呼叫4
  SPDCALL4: 4,
  // 单键功能呼叫5
  SPDCALL5: 5,
  // 单键功能呼叫6
  SPDCALL6: 6,
  // 单呼手动拨号
  SCALL_DIAL: 7,
  // 紧急模式开启
  WARNING_ON: 8,
  // 紧急模式关闭
  WARNING_OFF: 9,
  // 监听
  MONI: 10,
  // 背光灯自动开/关
  BACKLIGNT: 11,
  // 键盘锁
  KEYLOCK: 12,
  // 扫描开/关
  SCAN: 13,
  // 所有提示音开关
  TONE_MUTE: 14,
  // 电池电量提示
  BATTERY_CHARGE: 15,
  // 短信
  MSG_MENU: 16,
  // 区域切换
  ZONE_SWITCH: 17,
  // GPS 开/关
  GPS_SWITCH: 18,
  // 优先打断
  DISCONNECT: 19,
  // 数字模拟切换
  A_D_SWITCH: 20,
  // 强光手电开/关
  GLARE_FLASHLIGHT: 21,
  // 通讯录
  CONTAS_MENU: 22,
  // 对讲机激活
  DEV_ACTIVE: 23,
  // 对讲机遥毙
  DEV_DIE: 24,
  // 对讲机检测
  DEV_DETECT: 25,
  // 远程监听
  RMT_MONITOR: 26,
  // 高/低功率
  PWRMODE_CHG: 27,
  // 中继/脱网
  NETMODE_CHG: 28,
  // 声控开关
  VOX: 29,
  // 无用信道删除
  DEL_INVALID_CHL: 30,
  // 永久监听(只长按有效)
  LONGMONI: 31,
  // 录音开/关
  RECORD_SWITCH: 32,
  /* 以下选项紧限于生产调试 */
  // 误码率测试接收(不显示)
  ERRNUMBER_RX: 33,
  // 误码率测试发射(不显示)
  ERRNUMBER_TX: 34,
  // 基带固件升级(不显示)
  BASEBAND_FIRMWARE: 35,
  // 发射测试
  TX_TEST: 36,
  // 站点锁定开关(支持漫游功能设备显示)
  SITE_LOCK_SWITCH: 37,
  // 手动站点漫游(支持漫游功能设备显示)
  ROAM_MANUAL: 38,
  // 快速切换到区域1 16信道
  ZONE1_16: 39,
  // 系统状态查询
  SystemStatusQuery: 40,

  // 加密开关
  EncryptionSwitch: 47,
  // 信道锁定开关
  CH_LOCK_SW: 48,
})

// 单键呼叫类型定义
export const SoftKeyCallType = generateEnumObject({
  SINGLE: 0, // 单呼
  GROUP: 1, // 组呼
  TIP: 2, // 呼叫提示
  MSG: 3, // 短信
})
// 通讯录呼叫类型值
export const AddressBookCallTypes = generateEnumObject({
  // 无呼叫
  NONE: 0,
  // 组呼
  GROUP: 1,
  // 单呼
  SINGLE: 2,
  // 全呼
  BROADCAST: 3,
  // 调度呼叫
  DISPATCH: 4,
  // PC呼叫
  PC: 5,
  // 中继呼叫
  REPEATER: 6,
})

loadProtocol(Model, {
  // 设备信息
  1: cloneDeep(DeviceInfoProto),
  // 身份信息 IdentityInfo
  2: cloneDeep(IdentityInfoProto),
  // 通信密码
  3: cloneDeep(PasswordProto),
  // 总体设置
  4: {
    2: {
      // ch: '设备名称',
      deviceName: {
        type: 'stringU16',
        len: 32,
      },
      // ch: '设备ID',
      ids: {
        type: 'u32',
        offset: 32,
      },
      // ch: '中继id',
      repeaterId: {
        type: 'u32',
        offset: 36,
      },
      // ch: '声控等级', [关,1-8] 默认值 0
      soundCtrlLevel: {
        type: 'u8',
        offset: 40,
      },
      // ch: '声控延迟',[500,10000]  默认值 500 ms 间隔 500
      soundCtrlDelay: {
        type: 'u8',
        interval: 500,
        offset: 41,
      },
      // ch: '语音加密类型',0 未加密  1 静态加密 2 动态加密  3 高级动态加密
      soundEncryptType: {
        type: 'u8',
        offset: 42,
      },
      // ch: '语音加密值',
      soundEncryptValue: {
        type: 'string',
        len: 10,
        offset: 43,
      },
      // ch: '发射前导码持续时间',[0,8640] 默认值 960 ms  间隔 240
      sendLeadCodeTime: {
        type: 'u8',
        interval: 240,
        offset: 53,
      },
      // ch: '脱网组呼挂起时间',[0,7000] 默认值 3000 ms  间隔 500
      offlineGroupCallHungTime: {
        type: 'u8',
        interval: 500,
        offset: 54,
      },
      // ch: '脱网单呼挂起时间',[0,7000]  默认值 3000 ms  间隔 500
      offlineSingleCallHungTime: {
        type: 'u8',
        interval: 500,
        offset: 55,
      },
      powerBluetooth: {
        type: 'u8',
        offset: 56,
        bits: {
          // ch: '省电模式', 下拉列表 0 1:1, 1 1:2, 2 1:3 (默认值), 3 1:4
          savePowerMode: {
            offset: 0,
            len: 3,
          },
          // 蓝牙开关，默认0
          blueToothSw: {
            offset: 3,
            len: 1,
            bool,
          },
          // 蓝牙PTT保持模式使能，0关闭 1开启 默认0
          blueToothPttKeepEn: {
            offset: 4,
            len: 1,
            bool,
          },
          // 预留 default = 00
        },
      },
      // ch: '语言环境,禁用所有LED, 信道显示模式,拒绝陌生呼叫,直通模式,菜单键关闭,显示呼叫ID和别名
      soundAndDisplayTip: {
        type: 'u8',
        offset: 57,
        bits: {
          // ch: '语言环境',
          locale: {
            len: 1,
            offset: 0,
          },
          // ch: '禁用所有LED',
          disabledAllLED: {
            len: 1,
            bool,
            offset: 1,
          },
          // ch: '信道显示模式',0 频率显示 1 信道显示 2 频率+信息显示
          freqDisplay: {
            len: 2,
            offset: 2,
          },
          // ch: '拒绝陌生呼叫',
          rejectUnfamiliarCall: {
            len: 1,
            bool,
            offset: 4,
          },
          // ch: '直通模式',
          directMode: {
            len: 1,
            bool,
            offset: 5,
          },
          // ch: '菜单键关闭',
          menuOff: {
            len: 1,
            bool,
            offset: 6,
          },
          // ch: '显示呼叫ID和别名',0 未选择  1选中 默认值 1  TD505A才有此项
          showAlias: {
            len: 1,
            bool,
            offset: 7,
          },
        },
      },
      // ch: '全部禁音,语音指示,信道空闲指示,呼叫允许指示,模拟发射信令侧音开关,成功解码信号后提示音开关
      channelSignal: {
        type: 'u8',
        offset: 58,
        bits: {
          // ch: '全部静音',
          muteAll: {
            len: 1,
            bool,
            offset: 0,
          },
          // ch: '语音提示',
          voiceNotice: {
            len: 1,
            bool,
            offset: 1,
          },
          // ch: '信道空闲提示',
          channelFreeNotice: {
            len: 1,
            bool,
            offset: 2,
          },
          // ch: '呼叫允许指示',000 无  001 模拟  010 模拟和数字 011 数字
          allowCallInstruction: {
            len: 3,
            offset: 3,
          },
          // 模拟发射信令侧音开关 0 关  1 开
          analogTransmitSignaling: {
            len: 1,
            bool,
            offset: 6,
          },
          // 成功解码信号后提示音开关 0 关  1 开
          afterDecodeSignalNotify: {
            len: 1,
            bool,
            offset: 7,
          },
        },
      },
      // ch: '接收低电池电量提示间隔',[0,635] 间隔 5
      powerInfoAlert: {
        type: 'u8',
        interval: 5,
        offset: 59,
      },
      // ch: '开机密码',6位数字密码 值为数字对应的ASCII码 无密码时值为0
      powerOnPassword: {
        type: 'password',
        len: 6,
        offset: 60,
      },
      // ch: 录音使能,录音压缩比,允许擦除
      recordFunc: {
        type: 'u8',
        offset: 66,
        bits: {
          // ch: '录音使能',默认值 0
          recordEnable: {
            len: 1,
            bool,
            offset: 0,
          },
          // ch: '录音压缩比',0 不压缩, 1 3.5倍  当录音使能==0时不可用
          recordCompressionRatio: {
            len: 1,
            offset: 1,
          },
          // ch: '允许擦除',
          allowErasing: {
            len: 1,
            bool,
            offset: 2,
          },
          //  GPS，默认1
          gpsEnable: {
            len: 1,
            bool,
            offset: 3,
          },
          // 北斗，默认1
          bdsEnable: {
            len: 1,
            bool,
            offset: 4,
          },
          // 格洛纳斯，默认0
          glonassEnable: {
            len: 1,
            bool,
            offset: 5,
          },
          // 伽利略，默认0
          galileoEnable: {
            len: 1,
            bool,
            offset: 6,
          },
        },
      },
      // ch: 时区小时 [-12,12] 默认值 8
      timeZoneHour: {
        type: 'int8',
        offset: 67,
      },
      // ch: 时区分钟 [0,59]  默认值 0
      timeZoneMinute: {
        type: 'u8',
        offset: 68,
      },
      // ch: 年
      year: {
        type: 'u16',
        offset: 69,
      },
      // ch: 月
      month: {
        type: 'u16',
        offset: 71,
      },
      // ch: 日
      day: {
        type: 'u16',
        offset: 73,
      },
      // ch: 时
      hour: {
        type: 'u16',
        offset: 75,
      },
      // ch: 分
      minute: {
        type: 'u16',
        offset: 77,
      },
      // ch: 秒
      second: {
        type: 'u16',
        offset: 79,
      },
      // ch: 信道配置密码 6位数字密码 值为数字对应的ASCII码 无密码时值为0
      channelConfigPassword: {
        type: 'password',
        len: 6,
        offset: 81,
      },
      // ch: U盘模式密码 6位数字密码 值为数字对应的ASCII码 无密码时值为0
      uDiskModePassword: {
        type: 'password',
        len: 6,
        offset: 87,
      },
    },
  },
  // 按键设置
  5: {
    1: {
      // ch: '单键功能呼叫', x取值范围 [0,5]
      oneTouchFuncCall: {
        type: 'byteArray',
        subFields: {
          addrId: {
            type: 'u16',
            offset: 0,
          },
          optType: {
            type: 'u8',
            offset: 2,
          },
          smsId: {
            type: 'u8',
            offset: 3,
          },
        },
        len: 4,
        repeated: 6,
        offset: 0,
      },
      // 长按键确认时间 [250,3750]  间隔 250
      longPressDuration: {
        type: 'u8',
        interval: 250,
        offset: 24,
      },
      // ch: '短-长按功能', x取值范围 [0,2]
      shortLongPressFuncDefine: {
        type: 'byteArray',
        subFields: {
          short: {
            type: 'u8',
            offset: 0,
          },
          long: {
            type: 'u8',
            offset: 1,
          },
        },
        len: 2,
        repeated: 3,
        offset: 25,
      },
    },
  },
  // 短信
  6: {
    0: {
      // ch: '短信id',
      msgId: {
        type: 'u8',
      },
      // ch: '短信内容',
      msgContent: {
        type: 'stringU16',
        len: 280,
        offset: 1,
      },
    },
  },
  // 菜单配置
  9: {
    2: {
      menuConfig: {
        type: 'u8',
        bits: {
          // 菜单挂起时间,[0,30] 默认值 10s
          menuHangTime: {
            len: 5,
            offset: 0,
          },
          // ID最小显示位数。 范围0~7，显示1~8 default = 0
          idShownMinLen: {
            len: 3,
            offset: 5,
          },
        },
      },
      // 短信,呼叫提示,编辑通讯录,手动拨号,设备检测,远程监听,设备激活,设备遥毙
      addressSetting: {
        type: 'u8',
        offset: 1,
        bits: {
          // ch: '短信',0 禁用 1 可用 默认值 1
          message: {
            len: 1,
            bool,
            offset: 0,
          },
          // ch: '呼叫提示',
          callTip: {
            len: 1,
            bool,
            offset: 1,
          },
          // ch: '编辑通讯录',
          editAddressBook: {
            len: 1,
            bool,
            offset: 2,
          },
          // ch: '手动拨号',
          manualDialing: {
            len: 1,
            bool,
            offset: 3,
          },
          // ch: '设备检测',
          deviceDetect: {
            len: 1,
            bool,
            offset: 4,
          },
          // ch: '远程监听',
          remoteMonitor: {
            len: 1,
            bool,
            offset: 5,
          },
          // ch: '设备激活',
          deviceActive: {
            len: 1,
            bool,
            offset: 6,
          },
          // ch: '设备遥毙',
          deviceRemoteDeath: {
            len: 1,
            bool,
            offset: 7,
          },
        },
      },
      // 扫描开关,编辑扫描列表,未接记录,已接记录,呼出记录,脱网,音调提示,发射功率
      scanAndCallRecord: {
        type: 'u8',
        offset: 2,
        bits: {
          // ch: '扫描开关',
          scanEnable: {
            len: 1,
            bool,
            offset: 0,
          },
          // ch: '编辑扫描列表',
          editScanList: {
            len: 1,
            bool,
            offset: 1,
          },
          // ch: '未接记录',
          missedRecord: {
            len: 1,
            bool,
            offset: 2,
          },
          // ch: '已接记录',
          receivedRecord: {
            len: 1,
            bool,
            offset: 3,
          },
          // ch: '呼出记录',
          outgoingRecord: {
            len: 1,
            bool,
            offset: 4,
          },
          // ch: '脱网',
          offline: {
            len: 1,
            bool,
            offset: 5,
          },
          // ch: '音调提示',
          toneTip: {
            len: 1,
            bool,
            offset: 6,
          },
          // ch: '发射功率',
          transmitPower: {
            len: 1,
            bool,
            offset: 7,
          },
        },
      },
      // 背光,开机界面,键盘锁,LED 指示灯,静噪,开机密码,语言环境,声控
      soundLightPassword: {
        type: 'u8',
        offset: 3,
        bits: {
          // ch: '背光',
          backLight: {
            len: 1,
            bool,
            offset: 0,
          },
          // ch: '开机界面',
          bootInterface: {
            len: 1,
            bool,
            offset: 1,
          },
          // ch: '键盘锁',
          keyboardLock: {
            len: 1,
            bool,
            offset: 2,
          },
          // ch: 'LED指示灯',
          ledIndicator: {
            len: 1,
            bool,
            offset: 3,
          },
          // ch: '静噪',
          quieting: {
            len: 1,
            bool,
            offset: 4,
          },
          // ch: '开机密码',
          powerOnPassword: {
            len: 1,
            bool,
            offset: 5,
          },
          // ch: '语言环境',
          locale: {
            len: 1,
            bool,
            offset: 6,
          },
          // ch: '声控',
          soundCtrl: {
            len: 1,
            bool,
            offset: 7,
          },
        },
      },
      // 录音,U盘模式,GPS,时间设置,漫游界面
      timeModeGps: {
        type: 'u8',
        offset: 4,
        bits: {
          // ch: '录音',
          recording: {
            len: 1,
            bool,
            offset: 0,
          },
          // ch: 'U盘模式',
          uDiskMode: {
            len: 1,
            bool,
            offset: 1,
          },
          // ch: 'GPS',
          gps: {
            len: 1,
            bool,
            offset: 2,
          },
          // ch: '时间设置',
          timeSetting: {
            len: 1,
            bool,
            offset: 3,
          },
          // ch: '漫游界面',
          roamInterface: {
            len: 1,
            bool,
            offset: 4,
          },
          // 保留2位，其他机型参数
          // 信道锁定 1 Enable, 0 Disable. default = 1
          chLockEnable: {
            len: 1,
            bool,
            offset: 7,
            default: true,
          },
        },
      },
      // 信道配置总开关,接收频率,发射频率,信道名称,发射限时,亚音频设置,发射联系人,彩色码
      channelSetting: {
        type: 'u8',
        offset: 5,
        bits: {
          // ch: '信道配置总开关',
          chConfigEnable: {
            len: 1,
            bool,
            offset: 0,
          },
          // ch: '接收频率',
          receivingFrequency: {
            len: 1,
            bool,
            offset: 1,
          },
          // ch: '发射频率',
          transmittingFrequency: {
            len: 1,
            bool,
            offset: 2,
          },
          // ch: '信道名称',
          channelName: {
            len: 1,
            bool,
            offset: 3,
          },
          // ch: '发射限时',
          roamInterface: {
            len: 1,
            bool,
            offset: 4,
          },
          // ch: '亚音频设置',
          subAudioSetting: {
            len: 1,
            bool,
            offset: 5,
          },
          // ch: '发射联系人',
          launchContact: {
            len: 1,
            bool,
            offset: 6,
          },
          // ch: '彩色码',
          colorCode: {
            len: 1,
            bool,
            offset: 7,
          },
        },
      },
      // 时隙,虚拟集群发射时隙,接收列表
      timeSlotSetting: {
        type: 'u8',
        offset: 6,
        bits: {
          // ch: '时隙',
          timeSlot: {
            len: 1,
            bool,
            offset: 0,
          },
          // ch: '虚拟集群发射时隙',
          virtualClusterTimeSlot: {
            len: 1,
            bool,
            offset: 1,
          },
          // ch: '接收列表',
          receivingList: {
            len: 1,
            bool,
            offset: 2,
          },
          // 主界面快捷拨号, 1 Enable, 0 Disable. default = 1
          quickDailEnable: {
            len: 1,
            bool,
            offset: 3,
          },
        },
      },
    },
  },
  // GPS设置
  10: {
    0: {
      // 时钟差数，分钟差数，地区ID,比GMT标准时早(为1)或晚(为0,GPS开关,GPS工作模式
      gpsSettings: {
        type: 'u32',
        offset: 0,
        bits: {
          // 时钟差数 隐藏
          hourDiff: {
            len: 8,
            offset: 0,
          },
          // 分钟差数 隐藏
          minuteDiff: {
            len: 8,
            offset: 8,
          },
          // 地区ID 隐藏
          areaId: {
            len: 8,
            offset: 16,
          },
          // 比GMT标准时早(为1)或晚(为0) 隐藏
          earlyOrLate: {
            len: 1,
            offset: 24,
          },
          // GPS开关 0:关, 1:开
          enable: {
            len: 1,
            bool,
            offset: 25,
          },
          // GPS工作模式 0:省电模式, 1:高性能模式
          mode: {
            len: 1,
            offset: 26,
          },
        },
      },
      // 控制中心ID.
      centerId: {
        type: 'u32',
        offset: 4,
      },
      // 连接次数. 0-255次.
      connectionCount: {
        type: 'u8',
        offset: 8,
      },
      // PTT次数.0-255次.
      pttCount: {
        type: 'u8',
        offset: 9,
      },
      // 查询命令,需包含截止符,默认为空,不允许查询
      queryCmd: {
        type: 'string',
        len: 25,
        offset: 10,
      },
    },
  },
  // 信令系统
  11: {
    1: {
      // 遥毙解码 0 不允许 1 允许
      remoteDeathDecode: {
        type: 'u8',
        bool,
        offset: 0,
      },
      // 远程监听解码 0 不允许 1 允许
      remoteMonitorDecode: {
        type: 'u8',
        bool,
        offset: 1,
      },
      // 紧急远程监听解码 0 不允许 1 允许 远程监听解码使能时，本项限定为使能。  界面不显示
      urgentRemoteMonitorDecode: {
        type: 'u8',
        offset: 2,
      },
      // 远程监听持续时间 [10,120] 间隔 10
      remoteMonitorDuration: {
        type: 'u8',
        offset: 3,
      },
      // 单独工作使能 以下参数当此使能=0时不可用  默认值 0
      aloneWorkEnable: {
        type: 'u8',
        bool,
        offset: 4,
      },
      // 单独工作响应时间 单位 分钟  [0,255]
      aloneWorkTime: {
        type: 'u8',
        offset: 5,
      },
      // 单独工作提醒时间 单位 秒  [0,255]
      aloneWorkRemindTime: {
        type: 'u8',
        offset: 6,
      },
      // 单独工作响应操作 0 按钮  1 语音发射
      aloneWorkResOpt: {
        type: 'u8',
        offset: 7,
      },
      // 倒放使能 以下参数当此使能位=0时不可用 默认值 0
      reversePlayEnable: {
        type: 'u8',
        bool,
        offset: 8,
      },
      // 进入延时 单位 秒  [5,255]  默认值 10s
      entryDelay: {
        type: 'u8',
        offset: 9,
      },
      // 退出延时 单位 秒  [0,254]  255 无限  默认值 10s
      quitDelay: {
        type: 'u8',
        offset: 10,
      },
      // 倒放预提示时间,触发倾斜度,倒放触发方式
      reversePlayOption: {
        type: 'u8',
        offset: 11,
        bits: {
          // ch: '倒放预提示时间',单位 秒  [0,10] 默认值 5秒
          rewindTime: {
            len: 4,
            offset: 0,
          },
          // ch: '触发倾斜度',倒放触发方式=2时，不可用  0 60度  1 45度 2 30度
          triggerTilt: {
            len: 2,
            offset: 4,
          },
          // ch: '倒放触发方式',0 仅倾斜  1 仅运动检测  2 倾斜或运动检测
          triggerMode: {
            len: 2,
            offset: 6,
          },
        },
      },
    },
  },
  // 数字警报
  13: {
    1: {
      // 报警ID
      alarmID: {
        type: 'u8',
        offset: 0,
      },
      // 警报类型 0 禁止  1 常规 2 静默  3 静默带语音
      alarmType: {
        type: 'u8',
        offset: 1,
      },
      // 警报模式 0 紧急报警 1 紧急报警和呼叫 2 紧急报警和语音
      alarmMode: {
        type: 'u8',
        offset: 2,
      },
      // 回复信道
      replyChannel: {
        type: 'u16',
        offset: 3,
      },
      // 不礼貌重试 [1,15]
      impoliteRetry: {
        type: 'u8',
        offset: 5,
      },
      // 礼貌重试 [0,14]  0xFF: 无限
      politeRetry: {
        type: 'u8',
        offset: 6,
      },
      // Hot Mic 持续时间 [10,120]  间隔 10
      hotMicDuration: {
        type: 'u8',
        offset: 7,
      },
      // 数字报警名称
      alarmName: {
        type: 'stringU16',
        len: 32,
        offset: 8,
      },
    },
  },
  // 数字通信录
  15: merge(cloneDeep(DigitalAddressProto), {
    0: {
      setting: Unset,
      // 呼叫类型
      callType: {
        type: 'u8',
        offset: 37,
      },
    },
  }),
  // 接收组列表
  16: {
    1: {
      // 接收组 ID
      groupId: {
        type: 'u16',
        offset: 0,
      },
      // 成员列表	数字通讯录 组呼ID 索引
      listenGroup: {
        type: 'u16',
        repeated: 16,
        offset: 2,
      },
      // 通讯录 号码
      count: {
        type: 'u8',
        offset: 34,
      },
      // 接收组名称
      groupName: {
        type: 'stringU16',
        len: 32,
        offset: 35,
      },
    },
  },
  // 区域数据
  17: {
    2: {
      // 区域 ID
      areaId: {
        type: 'u8',
        offset: 0,
      },
      // 区域名称
      areaName: {
        type: 'stringU16',
        len: 32,
        offset: 1,
      },
      // 信道使用标志
      chFlag: {
        type: 'u16',
        offset: 33,
      },
      // 包含的信道ID列表
      chIdList: {
        type: 'u16',
        len: 2,
        repeated: 16,
        offset: 35,
      },
      // 区域有效标志 1 有效  其它无效
      validFlag: {
        type: 'u8',
        offset: 67,
      },
    },
  },
  // 信道数据
  18: {
    9: {
      // 信道 ID
      chId: {
        type: 'u16',
        offset: 0,
      },
      // 信道类型 0：数字 1：模拟 2：数字兼容模拟 3：模拟兼容数字
      chType: {
        type: 'u8',
        offset: 2,
      },
      // 接收频率 单位Hz，小端存储
      receivingFrequency: {
        type: 'u32',
        offset: 3,
      },
      // 发射频率 单位Hz，小端存储
      transmittingFrequency: {
        type: 'u32',
        offset: 7,
      },
      // 扫描列表/漫游列表	默认值 255(无)
      scanList: {
        type: 'u8',
        offset: 11,
      },
      // 模拟信道参数 / 数字信道参数 / 数模兼容信道参数	根据信道类型采用不同的数据结构
      subChannelData: {
        type: 'byteArray',
        len: 26,
        subTable,
        offset: 12,
      },
      // 自动扫描标志, 高低功率标志, 只接收标志, 静噪等级
      powerAndFlag: {
        type: 'u8',
        offset: 38,
        bits: {
          // 自动扫描标志
          autoScan: {
            len: 1,
            bool,
            offset: 0,
          },
          // 高低功率标志	2高功率，1中功率，0低功率 default=2
          powerSign: {
            len: 2,
            offset: 1,
            default: 2,
          },
          // 只接收标志	0 正常 1 只接收 默认值 0
          onlyReceiveSign: {
            len: 1,
            bool,
            offset: 3,
            default: false,
          },
          // 静噪等级	[0,9] 默认值 3
          squelchLevel: {
            len: 4,
            offset: 4,
          },
        },
      },
      // 扫描列表类型, IP站点连接, 自动漫游标识
      roamConfig: {
        type: 'u8',
        offset: 39,
        bits: {
          // 扫描列表类型 0扫描列表，1漫游列表，漫游列表只支持数字信道
          scanType: {
            len: 1,
            offset: 0,
          },
          // IP站点连接，0关闭，1使能，只支持数字信道异频
          ipSiteConnect: {
            len: 1,
            offset: 1,
            bool,
          },
          // 自动漫游标识
          autoRoam: {
            len: 1,
            offset: 2,
            bool,
          },
          // 保留位 5b
        },
      },
      // PBA ID，范围0~65535，默认0，暂不开放配置
      PBAID: {
        type: 'u16',
        offset: 40,
      },
      // 发射限时 取值范围 [1,33] 表示范围 [15,495]秒 间隔 15秒 默认值 300秒
      transmissionLimit: {
        type: 'u8',
        interval: 15,
        offset: 42,
      },
      // TOT密钥更新延迟	[0,255]秒
      totKeyUpdateDelay: {
        type: 'u8',
        offset: 43,
      },
      // 信道名称
      chName: {
        type: 'stringU16',
        len: 32,
        offset: 44,
      },
    },
    10: {
      // 信道 ID
      chId: {
        type: 'u16',
        offset: 0,
      },
      // 信道类型 0：数字 1：模拟 2：数字兼容模拟 3：模拟兼容数字
      chType: {
        type: 'u8',
        offset: 2,
      },
      // 接收频率 单位Hz，小端存储
      receivingFrequency: {
        type: 'u32',
        offset: 3,
      },
      // 发射频率 单位Hz，小端存储
      transmittingFrequency: {
        type: 'u32',
        offset: 7,
      },
      // 扫描列表/漫游列表	默认值 255(无)
      scanList: {
        type: 'u8',
        offset: 11,
      },
      // 模拟信道参数 / 数字信道参数 / 数模兼容信道参数	根据信道类型采用不同的数据结构
      subChannelData: {
        type: 'byteArray',
        len: 27,
        subTable,
        offset: 12,
      },
      // 自动扫描标志, 高低功率标志, 只接收标志, 静噪等级
      powerAndFlag: {
        type: 'u8',
        offset: 39,
        bits: {
          // 自动扫描标志
          autoScan: {
            len: 1,
            bool,
            offset: 0,
          },
          // 高低功率标志	2高功率，1中功率，0低功率 default=2
          powerSign: {
            len: 2,
            offset: 1,
            default: 2,
          },
          // 只接收标志	0 正常 1 只接收 默认值 0
          onlyReceiveSign: {
            len: 1,
            bool,
            offset: 3,
            default: false,
          },
          // 静噪等级	[0,9] 默认值 3
          squelchLevel: {
            len: 4,
            offset: 4,
          },
        },
      },
      // 扫描列表类型, IP站点连接, 自动漫游标识
      roamConfig: {
        type: 'u8',
        offset: 40,
        bits: {
          // 扫描列表类型 0扫描列表，1漫游列表，漫游列表只支持数字信道
          scanType: {
            len: 1,
            offset: 0,
          },
          // IP站点连接，0关闭，1使能，只支持数字信道异频
          ipSiteConnect: {
            len: 1,
            offset: 1,
            bool,
          },
          // 自动漫游标识
          autoRoam: {
            len: 1,
            offset: 2,
            bool,
          },
          // 保留位 5b
        },
      },
      // PBA ID，范围0~65535，默认0，暂不开放配置
      PBAID: {
        type: 'u16',
        offset: 41,
      },
      // 发射限时 取值范围 [1,33] 表示范围 [15,495]秒 间隔 15秒 默认值 300秒
      transmissionLimit: {
        type: 'u8',
        interval: 15,
        offset: 43,
      },
      // TOT密钥更新延迟	[0,255]秒
      totKeyUpdateDelay: {
        type: 'u8',
        offset: 44,
      },
      // 信道名称
      chName: {
        type: 'stringU16',
        len: 32,
        offset: 45,
      },
    },
  },
  // 扫描设置
  19: {
    0: {
      // 扫描挂起时间	[500,10000] 默认值 4000 ms 间隔 500
      scanHangTime: {
        type: 'u8',
        interval: 500,
        offset: 0,
      },
      // 优先提示音	0 无效 1 有效
      priorityTone: {
        type: 'u8',
        offset: 1,
      },
    },
  },
  // 扫描列表
  20: {
    1: {
      // 接收组 ID
      groupId: {
        type: 'u8',
        offset: 0,
      },
      // 成员列表	信道 ID 索引
      membersList: {
        type: 'u16',
        repeated: 16,
        offset: 1,
      },
      // 第一优先信道	信道 ID 索引
      firstPriorityChannel: {
        type: 'u16',
        offset: 33,
      },
      // 第二优先信道	信道 ID 索引
      secondPriorityChannel: {
        type: 'u16',
        offset: 35,
      },
      // 优先采样时间	单元 毫秒 [750,7750] 间隔 250毫秒
      prioritySamplingTime: {
        type: 'u8',
        interval: 250,
        offset: 37,
      },
      // 信道数量
      channelCount: {
        type: 'u8',
        offset: 38,
      },
      // 应答	0 不允许 1 允许
      answer: {
        type: 'u8',
        bool,
        offset: 39,
      },
      // 指定的发送信道ID值
      specifiedTrChID: {
        type: 'u16',
        offset: 40,
      },
      // 名称
      name: {
        type: 'stringU16',
        len: 32,
        offset: 42,
      },
    },
  },
  // 漫游设置
  23: {
    1: {
      // 活动站点搜索使能，1有效，0无效，默认0
      activeSiteEnable: {
        type: 'u8',
        offset: 0,
        bool,
      },
      // 站点搜索计时器，单位秒，步进1，范围0~255，默认0
      siteSearchTime: {
        type: 'u8',
        offset: 1,
      },
      // 自动搜索计时器，单位秒，步进1，范围1~300，默认60
      autoSiteSearchTime: {
        type: 'u16',
        offset: 2,
      },
    },
  },
  // 漫游列表
  24: {
    0: {
      // 标识当前为第几组
      roamId: {
        type: 'u8',
        offset: 0,
      },
      // 漫游channel的数量
      memberCount: {
        type: 'u8',
        offset: 1,
      },
      // RSSI阈值，范围-120~-80，步进1，绝对值传输
      rssi: {
        type: 'u8',
        offset: 2,
      },
      // 信道的ID值,默认有一个选定的 成员
      memberList: {
        type: 'u16',
        repeated: 16,
        offset: 3,
      },
      // 名称
      name: {
        type: 'stringU16',
        len: 32,
        offset: 35,
      },
    },
  },
  // 电话本，支持 100 个电话本
  44: cloneDeep(PhoneBookProto),
  // 系统配置
  45: cloneDeep(PatrolConfigProto),
  // 紧急报警
  47: {
    0: {
      // 功能开关,报警类型,
      alarmConfig: {
        type: 'u8',
        offset: 0,
        bits: {
          // 紧急警报功能开关， default=1
          alarmEnable: {
            len: 1,
            bool,
            offset: 0,
          },
          // 报警类型 0--常规，1--静默，default=0
          alarmType: {
            len: 1,
            offset: 1,
          },
        },
      },
      // ch: '发送次数', 范围:0~14，不停的。 步进:1，0xff表示不停的，default=3
      sendCount: {
        type: 'u8',
        offset: 1,
      },
      // ch: '呼叫联系人',选定-0xffff，default=0xffff
      callingContact: {
        type: 'u16',
        offset: 2,
      },
      // ch: '自动监听时间',单位秒，范围0-99，0表示报警后不自动监听，default=20
      autoListenTime: {
        type: 'u8',
        offset: 4,
      },
      // ch: '自动追踪时间',单位秒，范围0-99，0表示报警后不自动开跟踪定位时间，default=30
      autoTrackTime: {
        type: 'u8',
        offset: 5,
      },
    },
  },
  // 自动定位监控
  48: merge(cloneDeep(TrackMonitorProto), {
    0: {
      // ch: 自动定位监控开关， default=1
      trackEnable: {
        bool,
        type: 'u8',
      },
    },
  }),
  // 加密配置
  54: {
    0: {
      // 功能开关
      config: {
        type: 'u8',
        offset: 0,
        bits: {
          // 加密使能，当未使能时，各信道加密使能关闭 1 Enable, 0 Disable. default = 0
          enable: {
            len: 1,
            bool,
            offset: 0,
            default: false,
          },
        },
      },
    },
  },
  // 基础密钥列表，密钥列表数量：32
  55: {
    0: {
      // 列表序号
      index: {
        type: 'u8',
        offset: 0,
      },
      // 密钥ID
      id: {
        type: 'u8',
        offset: 1,
      },
      // 密钥值,所有可见字符
      value: {
        type: 'string',
        offset: 2,
        len: 10,
      },
      // 密钥名称
      name: {
        type: 'stringU16',
        offset: 12,
        len: 32,
      },
    },
  },
  // ARC4密钥列表，密钥列表数量：32
  56: {
    0: {
      // 列表序号
      index: {
        type: 'u8',
        offset: 0,
      },
      // 密钥ID
      id: {
        type: 'u8',
        offset: 1,
      },
      // 密钥值,0~9,A~F
      value: {
        type: 'string',
        offset: 2,
        len: 10,
      },
      // 密钥名称
      name: {
        type: 'stringU16',
        offset: 12,
        len: 32,
      },
    },
  },
  // AES256密钥列表，密钥列表数量：32
  57: {
    0: {
      // 列表序号
      index: {
        type: 'u8',
        offset: 0,
      },
      // 密钥ID
      id: {
        type: 'u8',
        offset: 1,
      },
      // 密钥值,0~9,A~F
      value: {
        type: 'string',
        offset: 2,
        len: 64,
      },
      // 密钥名称
      name: {
        type: 'stringU16',
        offset: 66,
        len: 32,
      },
    },
  },
  // 虚拟集群
  58: {
    0: {
      // 所属归属组，从组呼联系人选择
      vcGroupId: {
        type: 'u16',
        offset: 0,
        default: 0xffff,
      },
      // RSSI阈值，范围-120~-80，步进1，绝对值传输
      rssiValue: {
        type: 'u8',
        offset: 2,
        default: 0,
      },
      // 站点成员数量
      siteListMemCount: {
        type: 'u8',
        offset: 3,
        default: 1,
      },
      // 密钥值,0~9,A~F,没填满默认补F
      authKey: {
        type: 'string',
        len: 32,
        offset: 4,
        default: ''.padEnd(32, 'F'),
      },
      // 站点列表(信道)，固定包含"选定的"站点 默认：0xFFFE
      // "0xFFFF:无
      // 0xFFFE:选定的信道
      // 0~1023：指定已配置信道"
      siteList: {
        type: 'u16',
        offset: 36,
        repeated: 16,
        default: [0xfffe],
      },
    },
    1: {
      // 所属归属组，从组呼联系人选择
      vcGroupId: {
        type: 'u16',
        offset: 0,
        default: 0xffff,
      },
      // 测机信号间隔，单位MS，范围960~18000，步进120
      testMachineInterval: {
        type: 'u16',
        offset: 2,
        // interval: 120,
        default: 3600,
      },
      // RSSI阈值，范围-120~-80，步进1，绝对值传输
      rssiValue: {
        type: 'u8',
        offset: 4,
        default: 0,
      },
      // 站点成员数量
      siteListMemCount: {
        type: 'u8',
        offset: 5,
        default: 1,
      },
      // 密钥值,0~9,A~F,没填满默认补F
      authKey: {
        type: 'string',
        len: 32,
        offset: 6,
        default: ''.padEnd(32, 'F'),
      },
      // 站点列表(信道)，固定包含"选定的"站点 默认：0xFFFE
      // "0xFFFF:无
      // 0xFFFE:选定的信道
      // 0~1023：指定已配置信道"
      siteList: {
        type: 'u16',
        offset: 38,
        repeated: 16,
        default: [0xfffe],
      },
    },
  },
  // 虚拟集群站点信息
  59: {
    0: {
      // 站点信息ID	DBID_C_SITE	范围: 0~15
      id: {
        type: 'u8',
        offset: 0,
        default: 0,
      },
      // 频点列表的数量
      count: {
        type: 'u8',
        offset: 1,
        default: 0,
      },
      // 发射频点列表
      txFreqList: {
        type: 'u32',
        offset: 2,
        repeated: 16,
      },
      // 接收频点列表
      rxFreqList: {
        type: 'u32',
        offset: 66,
        repeated: 16,
      },
      // 站点名称	uint16[16+1]	"支持最长16个unicode字符"
      name: {
        type: 'stringU16',
        offset: 130,
        len: 32,
        default: '',
      },
    },
  },
})

/* 信道参数协议表 */
// 模拟信道参数
const analogChannelParamProto = {
  9: {
    // 保留位 11B 数字信道参数专用
    // 信令类型，0-两音，1-五音，2-DTMF ，0XFF-无，SIGNAL_TYPE_E
    signalType: {
      type: 'u8',
      offset: 11,
    },
    // 特性列表 索引ID
    propertyId: {
      type: 'u8',
      offset: 12,
    },
    // 信令系统索引ID,范围：选择两音信令时，0-3;五音或者DTMF信令时，0-7;  0XFF表示无
    signalSysId: {
      type: 'u8',
      offset: 13,
    },
    // 接收静噪类型，CHL_WORK_SQL_MODE_E
    rxSqType: {
      type: 'u8',
      offset: 14,
    },
    // 接收自动复位时间，仅在接收静噪类型选择包含信令模式时显示，1-255，单位秒
    autoResetTime: {
      type: 'u8',
      offset: 15,
    },
    // 接收自动复位模式，仅在接收静噪类型选择包含信令模式时显示，CHL_AUTO_RESET_MODE_E
    autoResetMode: {
      type: 'u8',
      offset: 16,
    },
    // 模拟报警系统ID
    emergencyID: {
      type: 'u8',
      offset: 17,
    },
    // 带宽标志, 拍频, BCL, 预加重, 压扩, 扰频开关, 允许脱网标志
    flagConfig: {
      type: 'u8',
      offset: 18,
      bits: {
        // 带宽标志	0 宽带 1 窄带 默认值 1
        bandwidthFlag: {
          len: 1,
          offset: 0,
        },
        // 拍频	0 关 1 开 默认值 0
        beatFrequency: {
          len: 1,
          bool,
          offset: 1,
        },
        // 繁忙信道锁定	0 关(默认值) 1 载波 2 CT/DCS
        busyChannelLock: {
          len: 2,
          offset: 2,
        },
        // 预加重	0 关(默认值) 1 开 界面不显示
        preEmphasis: {
          len: 1,
          offset: 4,
        },
        // 压扩	0 关(默认值) 1 开 界面不显示
        pressureDiffusion: {
          len: 1,
          offset: 5,
        },
        // 扰频开关	0 关(默认值) 1 开 界面不显示
        scrambleSwitch: {
          len: 1,
          offset: 6,
        },
        // 允许脱网标志	0 关(默认值) 1 开
        allowOfflineSign: {
          len: 1,
          bool,
          offset: 7,
        },
      },
    },
    // 尾音消除, 爆破音, 接收尾音
    soundConfig: {
      type: 'u8',
      offset: 19,
      bits: {
        // 尾音消除	0 关 1 开(默认值)
        tailCancellation: {
          len: 1,
          bool,
          offset: 0,
        },
        // 爆破音	0 关亚音 1 标准相位 2 非标准相位
        plosive: {
          len: 3,
          offset: 1,
        },
        // 接收尾音	0 无亚音 1 标准相位 2 非标准相位
        receivingTailTone: {
          len: 3,
          offset: 4,
        },
      },
    },
    // 亚音解码  亚音编码规则 支持自定义亚音
    subsonicDecode: {
      type: 'u16',
      offset: 20,
    },
    // 亚音编码
    subsonicEncode: {
      type: 'u16',
      offset: 22,
    },
    // 扰频值	[0,6000] 保留字段 界面不显示
    scramblingValue: {
      type: 'u16',
      offset: 24,
    },
  },
  10: {
    // 保留位 12B 数字信道参数专用
    // 信令类型，0-两音，1-五音，2-DTMF ，0XFF-无，SIGNAL_TYPE_E
    signalType: {
      type: 'u8',
      offset: 12,
    },
    // 特性列表 索引ID
    propertyId: {
      type: 'u8',
      offset: 13,
    },
    // 信令系统索引ID,范围：选择两音信令时，0-3;五音或者DTMF信令时，0-7;  0XFF表示无
    signalSysId: {
      type: 'u8',
      offset: 14,
    },
    // 接收静噪类型，CHL_WORK_SQL_MODE_E
    rxSqType: {
      type: 'u8',
      offset: 15,
    },
    // 接收自动复位时间，仅在接收静噪类型选择包含信令模式时显示，1-255，单位秒
    autoResetTime: {
      type: 'u8',
      offset: 16,
    },
    // 接收自动复位模式，仅在接收静噪类型选择包含信令模式时显示，CHL_AUTO_RESET_MODE_E
    autoResetMode: {
      type: 'u8',
      offset: 17,
    },
    // 模拟报警系统ID
    emergencyID: {
      type: 'u8',
      offset: 18,
    },
    // 带宽标志, 拍频, BCL, 预加重, 压扩, 扰频开关, 允许脱网标志
    flagConfig: {
      type: 'u8',
      offset: 19,
      bits: {
        // 带宽标志	0 宽带 1 窄带 默认值 1
        bandwidthFlag: {
          len: 1,
          offset: 0,
        },
        // 拍频	0 关 1 开 默认值 0
        beatFrequency: {
          len: 1,
          bool,
          offset: 1,
        },
        // 繁忙信道锁定	0 关(默认值) 1 载波 2 CT/DCS
        busyChannelLock: {
          len: 2,
          offset: 2,
        },
        // 预加重	0 关(默认值) 1 开 界面不显示
        preEmphasis: {
          len: 1,
          offset: 4,
        },
        // 压扩	0 关(默认值) 1 开 界面不显示
        pressureDiffusion: {
          len: 1,
          offset: 5,
        },
        // 扰频开关	0 关(默认值) 1 开 界面不显示
        scrambleSwitch: {
          len: 1,
          offset: 6,
        },
        // 允许脱网标志	0 关(默认值) 1 开
        allowOfflineSign: {
          len: 1,
          bool,
          offset: 7,
        },
      },
    },
    // 尾音消除, 爆破音, 接收尾音
    soundConfig: {
      type: 'u8',
      offset: 20,
      bits: {
        // 尾音消除	0 关 1 开(默认值)
        tailCancellation: {
          len: 1,
          bool,
          offset: 0,
        },
        // 爆破音	0 关亚音 1 标准相位 2 非标准相位
        plosive: {
          len: 3,
          offset: 1,
        },
        // 接收尾音	0 无亚音 1 标准相位 2 非标准相位
        receivingTailTone: {
          len: 3,
          offset: 4,
        },
      },
    },
    // 亚音解码  亚音编码规则 支持自定义亚音
    subsonicDecode: {
      type: 'u16',
      offset: 21,
    },
    // 亚音编码
    subsonicEncode: {
      type: 'u16',
      offset: 23,
    },
    // 扰频值	[0,6000] 保留字段 界面不显示
    scramblingValue: {
      type: 'u16',
      offset: 25,
    },
  },
}

// 数字信道参数
const digitalChannelParamProto = {
  9: {
    // 彩色码	[0,15]
    colorCode: {
      type: 'u8',
      offset: 0,
    },
    // 允许脱网标志,脱网标志,紧急警报指示,紧急警报确认,紧急呼叫指示,优先打断使能,准许条件
    alarmSettings: {
      type: 'u8',
      offset: 1,
      bits: {
        // 允许脱网标志	0 不允许 1 允许 默认值 0
        allowOfflineSign: {
          len: 1,
          bool,
          offset: 0,
        },
        // 脱网标志 默认值 0 隐藏
        offlineSign: {
          len: 1,
          bool,
          offset: 1,
        },
        // 紧急警报指示	0 未选择 1 选中 默认值 0
        emergencyAlertTip: {
          len: 1,
          bool,
          offset: 2,
        },
        // 紧急警报确认
        emergencyAlertConfirm: {
          len: 1,
          bool,
          offset: 3,
        },
        // 紧急呼叫指示	0 未选择 1 选中 默认值 0
        emergencyCallTip: {
          len: 1,
          bool,
          offset: 4,
        },
        // 优先打断使能	默认值 0 隐藏
        priorityInterrupt: {
          len: 1,
          bool,
          offset: 5,
        },
        // 准许条件	0 可用彩色码 1 始终 2 信道空闲
        permitConditions: {
          len: 2,
          offset: 6,
        },
      },
    },
    // 紧急系统ID值	默认值 8(无)
    emergencySysId: {
      type: 'u8',
      offset: 2,
    },
    // 接收组	默认值 MAX_RECEIVE_GROUP_LIST(无)
    receiveGroup: {
      type: 'u16',
      offset: 3,
    },
    // 默认通讯录地址
    defaultAddress: {
      type: 'u16',
      offset: 5,
    },
    // TDMA直通模式,单呼应答
    funcSettings: {
      type: 'u8',
      offset: 7,
      bits: {
        // TDMA直通模式	0 未选择 1 选中  默认值 0
        TDMAThroughMode: {
          len: 1,
          bool,
          offset: 0,
        },
        // 单呼应答	0 未选择 1 选中 默认值 0
        singleCallRes: {
          len: 1,
          bool,
          offset: 1,
        },
        // 联网: 1-允许，0-不允许	default=0，数模信道不存在联网功能
        // 数模信道不存在联网功能，当联网功能开启后，信道内的扫描、警报相关界面变灰不可选
        // 联网与虚拟集群只能2选1
        networking: {
          len: 1,
          bool,
          offset: 2,
        },
        // 本地呼叫：1-勾选，0-不勾选（联网不勾选时为灰色）
        // 联网和虚拟集群同时不勾选时为灰色
        localCall: {
          len: 1,
          bool,
          offset: 3,
        },
        // 直通模式使能，1-勾选，0-不勾选（非异频时为灰色不可选） 隐藏
        directMode: {
          len: 1,
          bool,
          offset: 4,
        },
        // 虚拟集群使能，1-允许，0-不允许 default=0，非中继模式为灰色不可选
        // 数模信道不存在虚拟集群功能，当虚拟集群功能开启后，信道内的扫描、漫游、警报、脱网相关界面变灰不可选
        // 联网与虚拟集群只能2选1
        // 虚拟集群开启后，时隙默认虚拟集群，虚拟集群发射时隙默认无
        svtEnable: {
          len: 1,
          bool,
          offset: 5,
        },
        // 保留位 2b
      },
    },
    // 时隙,虚拟集群发射时隙,语音优先级,信道时隙校准器
    slotConfig: {
      type: 'u8',
      offset: 8,
      bits: {
        // 时隙	0 1,1 2,2 虚拟集群 (隐藏) 默认值 1
        slot: {
          len: 2,
          offset: 0,
        },
        // 虚拟集群发射时隙	0 无,1 1,2 2 默认值 0 当TDMA直通模式==1时此项才可选 隐藏
        virtualTimeSlot: {
          len: 2,
          offset: 2,
        },
        // 语音优先级	[0,3] 默认值 0 隐藏
        voicePriority: {
          len: 2,
          offset: 4,
        },
        // 信道时隙校准器	0 不合格 1 合格 2 首选  默认值 0 当TDMA直通模式==1时此项才可选  隐藏
        channelSlotCalibrator: {
          len: 2,
          offset: 6,
        },
      },
    },
    // 加密相关配置
    encryptConfig: {
      type: 'u8',
      offset: 9,
      bits: {
        // 加密使能，1 Enable, 0 Disable. default = 0
        // 当未使能时，以下参数无效
        enable: {
          len: 1,
          offset: 0,
          bool,
        },
        // 加密类型，0 基础，1 高级 default = 0
        type: {
          len: 1,
          offset: 1,
        },
        // 加密算法，
        // 基础类型：0 异或，1 增强异或，2 ARC4，3 AES256，default=0
        // 高级类型：0 ARC4，1 AES256
        encryptAlgorithm: {
          len: 3,
          offset: 2,
        },
        // 多密钥加密，0未选择，1选中 default=0   当加密类型为基础时，该选项不可选
        randomKeyEncrypt: {
          len: 1,
          offset: 5,
          bool,
        },
        // 多密钥解密，0未选择，1选中 default=0   当加密类型为基础时，该选项不可选
        multiKeyDecrypt: {
          len: 1,
          offset: 6,
          bool,
        },
        // 短信是否加密, 0未选择，1选中 default=0   当加密类型为基础时，该选项不可选
        smsEncrypt: {
          len: 1,
          offset: 7,
          bool,
        },
      },
    },
    // 密钥列表序号，根据加密类型不同选择不同密钥列表
    // 基础类型：对应基础密钥列表
    // 高级类型：ARC4对应ARC4密钥列表；AES256对应AES256列表
    encryptListId: {
      type: 'u8',
      offset: 10,
    },
    // 保留位 15B 模拟信道参数专用
  },
  10: {
    // 彩色码	[0,15]
    colorCode: {
      type: 'u8',
      offset: 0,
    },
    // 允许脱网标志,脱网标志,紧急警报指示,紧急警报确认,紧急呼叫指示,优先打断使能,准许条件
    alarmSettings: {
      type: 'u8',
      offset: 1,
      bits: {
        // 允许脱网标志	0 不允许 1 允许 默认值 0
        allowOfflineSign: {
          len: 1,
          bool,
          offset: 0,
        },
        // 脱网标志 默认值 0 隐藏
        offlineSign: {
          len: 1,
          bool,
          offset: 1,
        },
        // 紧急警报指示	0 未选择 1 选中 默认值 0
        emergencyAlertTip: {
          len: 1,
          bool,
          offset: 2,
        },
        // 紧急警报确认
        emergencyAlertConfirm: {
          len: 1,
          bool,
          offset: 3,
        },
        // 紧急呼叫指示	0 未选择 1 选中 默认值 0
        emergencyCallTip: {
          len: 1,
          bool,
          offset: 4,
        },
        // 优先打断使能	默认值 0 隐藏
        priorityInterrupt: {
          len: 1,
          bool,
          offset: 5,
        },
        // 准许条件	0 可用彩色码 1 始终 2 信道空闲
        permitConditions: {
          len: 2,
          offset: 6,
        },
      },
    },
    // 紧急系统ID值	默认值 8(无)
    emergencySysId: {
      type: 'u8',
      offset: 2,
    },
    // 虚拟集群站点信息，站点信息列表ID，默认0xFF，表示无
    // 虚拟集群未使能不勾选时此项为灰不可选 混合信道无此选项，即数字信道才有该参数
    svtSiteInfo: {
      type: 'u8',
      offset: 3,
      default: 0xff,
    },
    // 接收组	默认值 MAX_RECEIVE_GROUP_LIST(无)
    receiveGroup: {
      type: 'u16',
      offset: 4,
    },
    // 默认通讯录地址
    defaultAddress: {
      type: 'u16',
      offset: 6,
    },
    // TDMA直通模式,单呼应答
    funcSettings: {
      type: 'u8',
      offset: 8,
      bits: {
        // TDMA直通模式	0 未选择 1 选中  默认值 0
        TDMAThroughMode: {
          len: 1,
          bool,
          offset: 0,
        },
        // 单呼应答	0 未选择 1 选中 默认值 0
        singleCallRes: {
          len: 1,
          bool,
          offset: 1,
        },
        // 联网: 1-允许，0-不允许	default=0，数模信道不存在联网功能
        // 数模信道不存在联网功能，当联网功能开启后，信道内的扫描、警报相关界面变灰不可选
        // 联网与虚拟集群只能2选1
        networking: {
          len: 1,
          bool,
          offset: 2,
        },
        // 本地呼叫：1-勾选，0-不勾选（联网不勾选时为灰色）
        // 联网和虚拟集群同时不勾选时为灰色
        localCall: {
          len: 1,
          bool,
          offset: 3,
        },
        // 直通模式使能，1-勾选，0-不勾选（非异频时为灰色不可选） 隐藏
        directMode: {
          len: 1,
          bool,
          offset: 4,
        },
        // 虚拟集群使能，1-允许，0-不允许 default=0，非中继模式为灰色不可选
        // 数模信道不存在虚拟集群功能，当虚拟集群功能开启后，信道内的扫描、漫游、警报、脱网相关界面变灰不可选
        // 联网与虚拟集群只能2选1
        // 虚拟集群开启后，时隙默认虚拟集群，虚拟集群发射时隙默认无
        svtEnable: {
          len: 1,
          bool,
          offset: 5,
        },
        // 保留位 2b
      },
    },
    // 时隙,虚拟集群发射时隙,语音优先级,信道时隙校准器
    slotConfig: {
      type: 'u8',
      offset: 9,
      bits: {
        // 时隙	0 1,1 2,2 虚拟集群 (隐藏) 默认值 1
        slot: {
          len: 2,
          offset: 0,
        },
        // 虚拟集群发射时隙	0 无,1 1,2 2 默认值 0 当TDMA直通模式==1时此项才可选 隐藏
        virtualTimeSlot: {
          len: 2,
          offset: 2,
        },
        // 语音优先级	[0,3] 默认值 0 隐藏
        voicePriority: {
          len: 2,
          offset: 4,
        },
        // 信道时隙校准器	0 不合格 1 合格 2 首选  默认值 0 当TDMA直通模式==1时此项才可选  隐藏
        channelSlotCalibrator: {
          len: 2,
          offset: 6,
        },
      },
    },
    // 加密相关配置
    encryptConfig: {
      type: 'u8',
      offset: 10,
      bits: {
        // 加密使能，1 Enable, 0 Disable. default = 0
        // 当未使能时，以下参数无效
        enable: {
          len: 1,
          offset: 0,
          bool,
        },
        // 加密类型，0 基础，1 高级 default = 0
        type: {
          len: 1,
          offset: 1,
        },
        // 加密算法，
        // 基础类型：0 异或，1 增强异或，2 ARC4，3 AES256，default=0
        // 高级类型：0 ARC4，1 AES256
        encryptAlgorithm: {
          len: 3,
          offset: 2,
        },
        // 多密钥加密，0未选择，1选中 default=0   当加密类型为基础时，该选项不可选
        randomKeyEncrypt: {
          len: 1,
          offset: 5,
          bool,
        },
        // 多密钥解密，0未选择，1选中 default=0   当加密类型为基础时，该选项不可选
        multiKeyDecrypt: {
          len: 1,
          offset: 6,
          bool,
        },
        // 短信是否加密, 0未选择，1选中 default=0   当加密类型为基础时，该选项不可选
        smsEncrypt: {
          len: 1,
          offset: 7,
          bool,
        },
      },
    },
    // 密钥列表序号，根据加密类型不同选择不同密钥列表
    // 基础类型：对应基础密钥列表
    // 高级类型：ARC4对应ARC4密钥列表；AES256对应AES256列表
    encryptListId: {
      type: 'u8',
      offset: 11,
    },
    // 保留位 15B 模拟信道参数专用
  },
}

// 数模兼容信道参数
const digital2AnalogCompatibleChannelParamProto = {
  9: {
    ...digitalChannelParamProto[9],
    ...analogChannelParamProto[9],
  },
  10: {
    ...digitalChannelParamProto[10],
    ...analogChannelParamProto[10],
  },
}

function covertBitToBoolean(val, radix = 0) {
  return ((val >> radix) & 0x01) === 1
}

export class TD511DeviceInfo extends DeviceInfo {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 1
    this.structLen = options.structLen || 16
  }

  // 机型解析， 最后2字节前4位为机型拥有的功能表。基础数值0x30
  // 第一字节：bit0: 漫游， bit1: 蓝牙功能
  // 第二字节: bit0: 定位功能，bit1: 录音功能，bit2: 倒地报警， bit3: 单独工作
  // 根据选配的功能形成机型号码
  static decodeModel(model) {
    const byte1 = model.charCodeAt(6) & 0x0f
    const byte2 = model.charCodeAt(7) & 0x0f
    return {
      roaming: covertBitToBoolean(byte1, 0),
      bluetooth: covertBitToBoolean(byte1, 1),

      locate: covertBitToBoolean(byte2, 0),
      recording: covertBitToBoolean(byte2, 1),
      runBackward: covertBitToBoolean(byte2, 2),
      workAlone: covertBitToBoolean(byte2, 3),
    }
  }

  decode(structData) {
    const decodeData = super.decode(structData)
    const config = decodeData.result[0]
    if (config) {
      let modelCode = config.model
      decodeData.result[0].config = TD511DeviceInfo.decodeModel(modelCode)
      if (modelCode.startsWith('511SDC')) {
        modelCode = decodeData.result[0].model = '511SDC00'
      }
      config.modelName = getModelName(modelCode)
      decodeData.result[0] = config
    }

    return decodeData
  }

  static encodeModel(model, config) {
    const defBit = 0x30
    const byte1 = defBit | (config.roaming << 0) | (config.bluetooth << 1)
    const byte2 =
      defBit |
      (config.locate << 0) |
      (config.recording << 1) |
      (config.runBackward << 2) |
      (config.workAlone << 3)

    const bytes = model
      .split('')
      .slice(0, -2)
      .concat([String.fromCharCode(byte1), String.fromCharCode(byte2)])
    return bytes.join('')
  }

  encode(data) {
    if (!data) {
      return super.encode(data)
    }
    if (!Array.isArray(data)) {
      data = [data]
    }
    return super.encode(
      data.map(item => {
        item.model = TD511DeviceInfo.encodeModel(item.model, item.config)
        return item
      }),
    )
  }
}

class GeneralSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 4
    this.version = options.version || 2
    this.structLen = options.structLen || 93
  }
}

class ButtonSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 5
    this.version = options.version || 1
    this.structLen = options.structLen || 31
  }
}

class ShortMessage extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 6
    this.version = options.version || 0
    this.structLen = options.structLen || 282
  }
}

class MenuSettings extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 9
    this.version = options.version || 2
    this.structLen = options.structLen || 7
  }
}

class GPSData extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 10
    this.version = options.version || 0
    this.structLen = options.structLen || 35
  }
}

class SignalingSystem extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 11
    this.version = options.version || 1
    this.structLen = options.structLen || 12
  }
}

class DigitWarning extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 13
    this.version = options.version || 1
    this.structLen = options.structLen || 40
  }
}

class DigitalAddressBook extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 15
    this.version = options.version || 0
    this.structLen = options.structLen || 39
  }
}

class RxGroup extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 16
    this.version = options.version || 1
    this.structLen = options.structLen || 67
  }
}

class ZoneData extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 17
    this.version = options.version || 2
    this.structLen = options.structLen || 68
  }
}

function getChannelParamProto(chType = 0, version = 9) {
  switch (chType) {
    case 0:
      return digitalChannelParamProto[version]
    case 1:
      return analogChannelParamProto[version]
    default:
      return digital2AnalogCompatibleChannelParamProto[version]
  }
}

export class ChannelData extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 各版本的数据结构长度
    this.structLengthInfo = {
      9: 76,
      10: 77,
    }
    this.type = options.type || 18
    // 使用新的信道版本协议
    this.version = options.version || 9
    this.structLen =
      this.structLengthInfo[this.version] ?? (options.structLen || 76)
  }

  getSubTableField() {
    const proto = this.getProtoInfo(this.type, this.version)
    if (!proto) {
      return undefined
    }

    for (const key in proto) {
      const item = proto[key]
      if (item === Unset || !item.subTable) {
        continue
      }

      return {
        ...item,
        __name: key,
      }
    }

    return undefined
  }

  decode(structData) {
    const res = super.decode(structData)
    const subTable = this.getSubTableField()
    const subTableName = subTable.__name
    let protoInfo, lastChType
    res.result = res.result.map(channel => {
      if (subTableName) {
        if (lastChType !== channel.chType) {
          protoInfo = getChannelParamProto(channel.chType, this.version)
          lastChType = channel.chType
        }
        channel[subTableName] = unmarshalProto(channel[subTableName], protoInfo)
      }
      return channel
    })
    return res
  }

  encode(data) {
    if (typeof data === 'undefined') {
      return super.encode(data)
    }
    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }
    const subTable = this.getSubTableField()
    const subTableName = subTable.__name
    let protoInfo, lastChType
    const result = data.map(channel => {
      if (subTableName) {
        if (lastChType !== channel.chType) {
          protoInfo = getChannelParamProto(channel.chType, this.version)
          lastChType = channel.chType
        }
        channel[subTableName] = marshalProto(
          channel[subTableName],
          protoInfo,
          subTable.len,
        )
      }
      return channel
    })

    return super.encode(result)
  }
}

class ScanSet extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 19
    this.version = options.version || 0
    this.structLen = options.structLen || 2
  }
}

class ScanList extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 20
    this.version = options.version || 1
    this.structLen = options.structLen || 74
  }
}

export class RoamSet extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 23
    this.version = options.version || 1
    this.structLen = options.structLen || 4
  }
}

export class RoamList extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 24
    this.version = options.version || 0
    this.structLen = options.structLen || 67
  }

  decode(structData) {
    const data = super.decode(structData)
    data.result = data.result.map(item => {
      // 解RSSI阈值绝对值
      item.rssi = item.rssi * -1
      return item
    })
    return data
  }

  encode(data) {
    if (typeof data === 'undefined') {
      return super.encode(data)
    }
    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }

    const list = data.map(item => {
      //  RSSI阈值，绝对值传输
      item.rssi = Math.abs(item.rssi)
      return item
    })

    return super.encode(list)
  }
}

class PhoneBook extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 44
    this.version = options.version || 0
    this.structLen = options.structLen || 54
  }
}

class DtdConfig extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 45
    this.version = options.version || 0
    this.structLen = options.structLen || 3
  }
}

class EmergencyAlarm extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 47
    this.version = options.version || 0
    this.structLen = options.structLen || 6
  }
}

class AutoPosMonitor extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 48
    this.version = options.version || 0
    this.structLen = options.structLen || 4
  }
}

class CryptCfg extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 54
    this.version = options.version || 0
    this.structLen = options.structLen || 1
  }
}

class CryptCfgList extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 55
    this.version = options.version || 0
    this.structLen = options.structLen || 44
  }
}

class CryptCfgArc4List extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 56
    this.version = options.version || 0
    this.structLen = options.structLen || 44
  }
}

class CryptCfgAes256List extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 57
    this.version = options.version || 0
    this.structLen = options.structLen || 98
  }
}

// 虚拟集群
export class VirtualCluster extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 各版本的数据结构长度
    this.structLengthInfo = {
      0: 68,
      1: 70,
    }
    this.type = options.type || 58
    this.version = options.version || 1
    this.structLen =
      this.structLengthInfo[this.version] ?? (options.structLen || 70)
  }
}

export class SiteInfo extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    this.type = options.type || 59
    this.version = options.version || 0
    this.structLen = options.structLen || 162
  }
}

const StructIndex = {
  // 设备信息
  1: TD511DeviceInfo,
  // 身份信息
  2: SecondIdentityInfo,
  // 通信密码
  3: Password,
  // 总体设置
  4: GeneralSettings,
  // 按键设置
  5: ButtonSettings,
  // 短信
  6: ShortMessage,
  // // 遥控遥测
  // 7:RemoteTestConfig,
  // // 附件设置
  // 8:AttachmentSetting,

  // 菜单配置
  9: MenuSettings,
  // GPS设置
  10: GPSData,
  // 信令系统
  11: SignalingSystem,
  // // 模拟警报
  // 12:AnalogWarning,

  // 数字警报
  13: DigitWarning,
  // // 模拟通讯录
  // 14:AnalogAddressBook,

  // 数字通信录
  15: DigitalAddressBook,
  // 接收组列表
  16: RxGroup,
  // 区域数据
  17: ZoneData,
  // 信道数据
  18: ChannelData,
  // 扫描设置
  19: ScanSet,
  // 扫描列表
  20: ScanList,
  // // 网络设置
  // 21:NetworkSetting,
  // // USB切换
  // 22:USBSwitch,
  // 漫游设置
  23: RoamSet,
  // 漫游列表
  24: RoamList,
  // // 两音系统
  // 25:TwoToneSystem,
  // // 两音联系人
  // 26:TwoToneContact,
  // // 两音特性
  // 27:TwoToneProperty,
  // // 五音信令配置
  // 28:FiveToneSignalConfig,
  // // 五音序列
  // 29:FiveToneSequence,
  // // 五音信息
  // 30:FiveToneMsgData,
  // // 五音解码定义
  // 31:FiveToneDecodeDefine,
  // // 五音自动应答
  // 32:FiveToneAutoReply,
  // // 五音系统
  // 33:FiveToneSystem,
  // // 五音联系人
  // 34:FiveToneContact,
  // // 五音特性
  // 35:FiveToneProperty,
  // // DTMF信令配置
  // 36:DtmfSignalConfig,
  // // DTMF信息
  // 37:DtmfMsgData,
  // // DTMF解码定义
  // 38:DtmfDecodeDefine,
  // // DTMF自动回复
  // 39:DtmfAutoReply,
  // // DTMF系统
  // 40:DtmfSystem,
  // // DTMF联系人
  // 41:DtmfContact,
  // // DTMF特性
  // 42:DtmfProperty,
  // // 清华声码器KEY
  // 43:SelpKeyData,

  // 电话本
  44: PhoneBook,
  // 配置
  45: DtdConfig,
  // // 遥毙锁机
  // 46:RemoteShut,

  // 紧急报警
  47: EmergencyAlarm,
  // 自动定位监控
  48: AutoPosMonitor,
  // // 区域电子围栏监控
  // 49:AreaMonitor,
  // // 岗哨监控
  // 50:SentryMonitor,
  // // 移动监控
  // 51:MoveMonitor,
  // // GPS巡更
  // 52:GPSPetrol,
  // // 有源RFID
  // 53:RFIDActive

  // 加密配置
  54: CryptCfg,
  // 基础密钥列表
  55: CryptCfgList,
  // ARC4密钥列表
  56: CryptCfgArc4List,
  // AES256密钥列表
  57: CryptCfgAes256List,
  // 虚拟集群配置
  58: VirtualCluster,
  // 虚拟集群站点信息
  59: SiteInfo,
}

// 不导出密码、接收组等配置
const NotExportStructIndex = [3, 16]

export const ExportStructIndex = Object.keys(StructIndex)
  .filter(item => !NotExportStructIndex.includes(+item))
  .map(key => +key)

export function getClassInstance(options) {
  const opts = {
    ...BaseOptions,
    ...options,
  }
  const DataClass = StructIndex[opts.type]
  if (!DataClass) {
    return null
  }

  return new DataClass(opts)
}

// 纯模拟亚音
const AnalogCode = {
  '67.0': 0x0670,
  69.3: 0x0693,
  71.9: 0x0719,
  74.4: 0x0744,
  '77.0': 0x0770,
  79.7: 0x0797,
  82.5: 0x0825,
  85.4: 0x0854,
  88.5: 0x0885,
  91.5: 0x0915,
  94.8: 0x0948,
  97.4: 0x0974,
  '100.0': 0x1000,
  103.5: 0x1035,
  107.2: 0x1072,
  110.9: 0x1109,
  114.8: 0x1148,
  118.8: 0x1188,
  '123.0': 0x1230,
  127.3: 0x1273,
  131.8: 0x1318,
  136.5: 0x1365,
  141.3: 0x1413,
  146.2: 0x1462,
  151.4: 0x1514,
  156.7: 0x1567,
  162.2: 0x1622,
  167.9: 0x1679,
  173.8: 0x1738,
  179.9: 0x1799,
  186.2: 0x1862,
  192.8: 0x1928,
  203.5: 0x2035,
  210.7: 0x2107,
  218.1: 0x2181,
  225.7: 0x2257,
  233.6: 0x2336,
  241.8: 0x2418,
  250.3: 0x2503,
}
// 纯数字亚音
const DigitalCode = {
  D023N: 0x8023,
  D025N: 0x8025,
  D026N: 0x8026,
  D031N: 0x8031,
  D032N: 0x8032,
  D043N: 0x8043,
  D047N: 0x8047,
  D051N: 0x8051,
  D054N: 0x8054,
  D065N: 0x8065,
  D071N: 0x8071,
  D072N: 0x8072,
  D073N: 0x8073,
  D074N: 0x8074,
  D114N: 0x8114,
  D115N: 0x8115,
  D116N: 0x8116,
  D125N: 0x8125,
  D131N: 0x8131,
  D132N: 0x8132,
  D134N: 0x8134,
  D143N: 0x8143,
  D152N: 0x8152,
  D155N: 0x8155,
  D156N: 0x8156,
  D162N: 0x8162,
  D165N: 0x8165,
  D172N: 0x8172,
  D174N: 0x8174,
  D205N: 0x8205,
  D223N: 0x8223,
  D226N: 0x8226,
  D243N: 0x8243,
  D244N: 0x8244,
  D245N: 0x8245,
  D251N: 0x8251,
  D261N: 0x8261,
  D263N: 0x8263,
  D265N: 0x8265,
  D271N: 0x8271,
  D306N: 0x8306,
  D311N: 0x8311,
  D315N: 0x8315,
  D331N: 0x8331,
  D343N: 0x8343,
  D346N: 0x8346,
  D351N: 0x8351,
  D364N: 0x8364,
  D365N: 0x8365,
  D371N: 0x8371,
  D411N: 0x8411,
  D412N: 0x8412,
  D413N: 0x8413,
  D423N: 0x8423,
  D431N: 0x8431,
  D432N: 0x8432,
  D445N: 0x8445,
  D464N: 0x8464,
  D465N: 0x8465,
  D466N: 0x8466,
  D503N: 0x8503,
  D506N: 0x8506,
  D516N: 0x8516,
  D532N: 0x8532,
  D546N: 0x8546,
  D565N: 0x8565,
  D606N: 0x8606,
  D612N: 0x8612,
  D624N: 0x8624,
  D627N: 0x8627,
  D631N: 0x8631,
  D632N: 0x8632,
  D654N: 0x8654,
  D662N: 0x8662,
  D664N: 0x8664,
  D703N: 0x8703,
  D712N: 0x8712,
  D723N: 0x8723,
  D731N: 0x8731,
  D732N: 0x8732,
  D734N: 0x8734,
  D743N: 0x8743,
  D754N: 0x8754,
  D023I: 0xc023,
  D025I: 0xc025,
  D026I: 0xc026,
  D031I: 0xc031,
  D032I: 0xc032,
  D043I: 0xc043,
  D047I: 0xc047,
  D051I: 0xc051,
  D054I: 0xc054,
  D065I: 0xc065,
  D071I: 0xc071,
  D072I: 0xc072,
  D073I: 0xc073,
  D074I: 0xc074,
  D114I: 0xc114,
  D115I: 0xc115,
  D116I: 0xc116,
  D125I: 0xc125,
  D131I: 0xc131,
  D132I: 0xc132,
  D134I: 0xc134,
  D143I: 0xc143,
  D152I: 0xc152,
  D155I: 0xc155,
  D156I: 0xc156,
  D162I: 0xc162,
  D165I: 0xc165,
  D172I: 0xc172,
  D174I: 0xc174,
  D205I: 0xc205,
  D223I: 0xc223,
  D226I: 0xc226,
  D243I: 0xc243,
  D244I: 0xc244,
  D245I: 0xc245,
  D251I: 0xc251,
  D261I: 0xc261,
  D263I: 0xc263,
  D265I: 0xc265,
  D271I: 0xc271,
  D306I: 0xc306,
  D311I: 0xc311,
  D315I: 0xc315,
  D331I: 0xc331,
  D343I: 0xc343,
  D346I: 0xc346,
  D351I: 0xc351,
  D364I: 0xc364,
  D365I: 0xc365,
  D371I: 0xc371,
  D411I: 0xc411,
  D412I: 0xc412,
  D413I: 0xc413,
  D423I: 0xc423,
  D431I: 0xc431,
  D432I: 0xc432,
  D445I: 0xc445,
  D464I: 0xc464,
  D465I: 0xc465,
  D466I: 0xc466,
  D503I: 0xc503,
  D506I: 0xc506,
  D516I: 0xc516,
  D532I: 0xc532,
  D546I: 0xc546,
  D565I: 0xc565,
  D606I: 0xc606,
  D612I: 0xc612,
  D624I: 0xc624,
  D627I: 0xc627,
  D631I: 0xc631,
  D632I: 0xc632,
  D654I: 0xc654,
  D662I: 0xc662,
  D664I: 0xc664,
  D703I: 0xc703,
  D712I: 0xc712,
  D723I: 0xc723,
  D731I: 0xc731,
  D732I: 0xc732,
  D734I: 0xc734,
  D743I: 0xc743,
  D754I: 0xc754,
}
// 模数或数模亚音
const DigitalCode2 = {
  62.5: 0x0625,
  '67.0': 0x0670,
  69.3: 0x0693,
  71.9: 0x0719,
  74.4: 0x0744,
  '77.0': 0x0770,
  79.7: 0x0797,
  82.5: 0x0825,
  85.4: 0x0854,
  88.5: 0x0885,
  91.5: 0x0915,
  94.8: 0x0948,
  97.4: 0x0974,
  '100.0': 0x1000,
  103.5: 0x1035,
  107.2: 0x1072,
  110.9: 0x1109,
  114.8: 0x1148,
  118.8: 0x1188,
  '123.0': 0x1230,
  127.3: 0x1273,
  131.8: 0x1318,
  136.5: 0x1365,
  141.3: 0x1413,
  146.2: 0x1462,
  151.4: 0x1514,
  156.7: 0x1567,
  159.8: 0x1598,
  162.2: 0x1622,
  165.5: 0x1655,
  167.9: 0x1679,
  171.3: 0x1713,
  173.8: 0x1738,
  177.3: 0x1773,
  179.9: 0x1799,
  183.5: 0x1835,
  186.2: 0x1862,
  189.9: 0x1899,
  192.8: 0x1928,
  196.6: 0x1966,
  199.5: 0x1995,
  203.5: 0x2035,
  206.5: 0x2065,
  210.7: 0x2107,
  218.1: 0x2181,
  225.7: 0x2257,
  229.1: 0x2291,
  233.6: 0x2336,
  241.8: 0x2418,
  250.3: 0x2503,
  254.1: 0x2541,
}

const _AnalogCodeData = {
  ...AnalogCode,
  ...DigitalCode,
}
const _AnalogDigitalCodeData = {
  ...DigitalCode2,
  ...DigitalCode,
}
// 亚音编码、解码数据值
export const AnalogCodeData = generateEnumObject(_AnalogCodeData)
export const AnalogDigitalCodeData = generateEnumObject(_AnalogDigitalCodeData)
