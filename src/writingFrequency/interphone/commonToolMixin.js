import bfproto from '@/modules/protocol'
import dbCmd from '@/modules/protocol/db.pb.cmd'
import bfutil, { getDbSubject } from '@/utils/bfutil'
import bfNotify from '@/utils/notify'
import { nowLocalTime, nowUtcTime } from '@/utils/time'
import { v1 as uuid } from 'uuid'
import { SupportedLang, SupportedLangList } from '@/modules/i18n'
import { DeviceNoLocale } from '../customModelConfig'

import { getModelName } from '../modelInfo'
import { checkDeviceModel } from './common'

export default {
  props: {
    QWebServer: {
      required: true,
    },
    noDevice: {
      type: Boolean,
      default: true,
    },
    ioPassword: {
      type: String,
      default: '',
    },
    isFullscreen: {
      type: Boolean,
      default: false,
    },
    expectedModel: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isReading: false,
      isWriting: false,
      readWriteFlag: '',
      deviceModel: '',
    }
  },
  methods: {
    getRootZoneConfig(curZone) {
      let zone = bfglob.gchannelZone.get(curZone.zoneParent)
      while (zone && zone.zoneLevel !== 1) {
        zone = bfglob.gchannelZone.get(zone.zoneParent)
      }
      return zone
    },
    // 导入、导出方法
    exportData(jsonData) {
      try {
        jsonData = JSON.stringify(jsonData)
        const modelName =
          this.deviceWriteInfo.modelName ||
          getModelName(this.deviceWriteInfo.model || this.Model)
        const filename = `wf_${modelName}_${nowLocalTime()}.json`
        bfutil.saveAsFile(filename, jsonData)

        bfNotify.messageBox(this.$t('msgbox.exportSuccess'), 'success')
      } catch (e) {
        bfNotify.warningBox(this.$t('msgbox.exportError'), 'error')
      }
    },
    importConfig(jsonData) {
      for (const k in jsonData) {
        const data = jsonData[k]
        this.asyncLocalConfig({
          type: parseInt(k) || 0,
          result: data || [],
        })
      }
      bfNotify.messageBox(this.$t('msgbox.importSuccess'), 'success')
    },
    checkDeviceModel(deviceInfo) {
      if (deviceInfo && !checkDeviceModel(deviceInfo.model, this.Model)) {
        this.setReadState(false)
        this.showWriteInFailInfo(this.$t('msgbox.deviceModelError'))
        return false
      }
      return true
    },
    getRxGroupId(chId) {
      for (let i = 0; i < this.rxGroupList.length; i++) {
        const item = this.rxGroupList[i]
        if (item.chId === chId) {
          return item.groupId
        }
      }

      return 0xff
    },
    createDataListIndex(dataList, key = 'id') {
      if (!Array.isArray(dataList)) {
        return {}
      }

      return dataList
        .map(data => {
          const prop = data[key]
          return { [prop]: data }
        })
        .reduce((p, c) => {
          return Object.assign(p, c)
        }, {})
    },
    formValidate(ref) {
      const form = this.$refs[ref]
      // 没有找到表单元素，则默认为不验证
      if (!form?.validate) {
        return Promise.resolve(true)
      }

      return Promise.resolve(form.validate() ?? true)
    },

    // 判断能否读写方法
    hasQWebServer() {
      if (!this.QWebServer) {
        bfglob.console.warn('can not found usb manager')
        return false
      }

      return true
    },
    canWrite() {
      if (!this.hasQWebServer()) {
        return false
      }
      if (!this.QWebServer.sl_write_device_usb_data) {
        bfglob.console.warn(
          'can not found sl_write_device_usb_data func in usb manager:',
          this.QWebServer,
        )
        return false
      }

      return true
    },
    canRead() {
      if (!this.hasQWebServer()) {
        return false
      }
      if (!this.QWebServer.sl_read_device_usb_data) {
        bfglob.console.warn(
          'can not found sl_read_device_usb_data func in usb manager:',
          this.QWebServer,
        )
        return false
      }
      return true
    },
    // 设置读取状态
    resetReadWriteFlag() {
      this.readWriteFlag = uuid()
    },
    setReadState(state = false) {
      this.isReading = state
      if (!state) {
        this.resetReadWriteFlag()
      }
    },
    setWriteState(state = false) {
      this.isWriting = state
      if (!state) {
        this.resetReadWriteFlag()
      }
    },
    // 显示读写状态方法
    showWriteInFailInfo(msg = this.$t('msgbox.writeInFail'), err) {
      this.resetReadWriteFlag()
      bfNotify.messageBox(msg, 'error')
      err && bfglob.console.error('writeInFail:', err)
    },
    showReadDataFailedMessage() {
      // 判断是否在读取数据中
      if (this.isReading) {
        this.isReading = false
        bfNotify.messageBox(this.$t('msgbox.readDataFailed'), 'warning')
      }
    },
    showWriteDataFailedMessage() {
      // 判断是否在写入数据中
      if (this.isWriting) {
        this.isWriting = false
        bfNotify.messageBox(this.$t('msgbox.writeInFail'), 'warning')
      }
    },
    // 写入数据后，需要更新对应手台的数据
    updateDeviceLastRfWriteTime() {
      if (!this.selectDeviceData) {
        return
      }

      const msgObj = {
        orgId: this.selectDeviceData.orgId,
        rid: this.selectDeviceData.rid,
        lastRfWriteTime: nowUtcTime(),
        note: this.selectDeviceData.note,
      }

      const msgOpts = {
        rpcCmdFields: {
          origReqId: 'rid',
          resInfo: 'org_id,last_rf_write_time,note',
        },
      }
      bfproto
        .sendMessage(
          dbCmd.DB_DEVICE_PUPDATE,
          msgObj,
          'db_device',
          getDbSubject(),
          msgOpts,
        )
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo === '+OK') {
            // 更新全局组织机构数据
            bfglob.gdevices.get(msgObj.rid).lastRfWriteTime =
              msgObj.lastRfWriteTime
          } else {
            bfglob.console.log(
              'updateDeviceLastRfWriteTime failed:',
              rpc_cmd_obj,
              msgObj,
              msgOpts,
            )
          }
        })
        .catch(err => {
          bfglob.console.warn('updateDeviceLastRfWriteTime timeout:', err)
        })
    },
  },
  computed: {
    isMobile() {
      return this.$root.layoutLevel === 0
    },
    fullscreen() {
      return this.bfmaxi ? true : this.isMobile
    },
    responseColumn() {
      return this.isFullscreen ? 12 : 24
    },
    locale() {
      return this.$i18n.locale
    },
    isCN() {
      return this.locale === SupportedLang.zhCN
    },
    isEN() {
      return this.locale === SupportedLang.enUS
    },
    isEn() {
      return this.isEN
    },
    isFR() {
      return this.locale === SupportedLang.fr
    },
    selectedDeviceLabelWidth() {
      return this.isFR || this.isEN ? '180px' : '120px'
    },
    menuHangTimeLabelWidth() {
      return this.isFR ? '230px' : this.isEN ? '160px' : '120px'
    },
    buttonDefineLabelWidth() {
      return this.isFR ? '200px' : this.isEN ? '170px' : '130px'
    },
    gpsDataLabelWidth() {
      return this.isFR ? '180px' : this.isEN ? '130px' : '90px'
    },
    scanConfigLabelWidth() {
      return this.isFR ? '180px' : '120px'
    },

    inAction() {
      return this.isReading || this.isWriting
    },
    /**
     * @return {string}
     */
    Model() {
      return ''
    },
    selectDeviceData() {
      return bfglob.gdevices.getDataByIndex(this.selectedDeviceDmrId)
    },
    notZoneModels() {
      return ['TD081000']
    },
    selectedChannels() {
      if (!this.selectDeviceData) {
        return []
      }

      const validChannels = []
      // 缓存区域匹配信息
      const validZoneRid = {}
      const invalidZoneRid = {}
      const deviceModel = this.deviceModel || this.Model

      // 过滤非本机型的信道数据，通讯录、接收组、区域信道等数据受影响
      for (let i = 0; i < this.selectDeviceData.channels.length; i++) {
        const channel = this.selectDeviceData.channels[i]
        // 不是每个机型都有区域，notZoneModels为没有区域配置的机型数组
        if (this.notZoneModels.includes(deviceModel)) {
          validChannels.push(channel)
          continue
        }

        // 跳过没有配置区域的信道数据
        if (channel.zoneRid === bfutil.DefOrgRid) {
          continue
        }

        // 该区域已经验证过为本机型有效区域配置
        if (validZoneRid[channel.zoneRid]) {
          validChannels.push(channel)
          continue
        }

        // 过滤本写频机型与信道区域配置不符的信道
        if (invalidZoneRid[channel.zoneRid]) {
          continue
        }

        // 查找信道的区域配置，如果不是本机型的区域，则跳过，不进行写频处理
        const zoneConfig = bfglob.gchannelZone.get(channel.zoneRid)
        if (!zoneConfig) {
          continue
        }
        // 找到区域根节点，如果根节点不是当前机型的区域，则跳过该区域下的所信道
        const rootZone = this.getRootZoneConfig(zoneConfig)
        if (!rootZone) {
          invalidZoneRid[channel.zoneRid] = true
          continue
        }
        if (JSON.parse(rootZone.setting).model !== deviceModel) {
          invalidZoneRid[channel.zoneRid] = true
          continue
        }

        validChannels.push(channel)
        validZoneRid[channel.zoneRid] = true
      }

      return validChannels
    },
    localeList() {
      const locales = []
      if (SupportedLangList.includes(SupportedLang.zhCN)) {
        locales.push({
          label: this.$t('header.CN'),
          value: 0,
        })
      }
      if (SupportedLangList.includes(SupportedLang.enUS)) {
        locales.push({
          label: this.$t('header.EN'),
          value: 1,
        })
      }

      return locales
    },
    DeviceNoLocale() {
      return DeviceNoLocale
    },
  },
  mounted() {
    // 常规配置的语言选项，根据语言选择重置默认值
    if (this.generalSettings?.locale !== undefined) {
      const localeList = this.localeList ?? []
      const localeOption = localeList.find(
        opt => opt.value === this.generalSettings.locale,
      )
      this.generalSettings.locale =
        localeOption?.value ?? localeList[0]?.value ?? 0
    }
  },
  watch: {
    inAction(val) {
      this.$emit('progressAction', val)
    },
  },
}
