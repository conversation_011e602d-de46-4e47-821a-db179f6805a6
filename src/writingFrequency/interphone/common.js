import base64js from 'base64-js'
import Long from 'long'
import bfutil from '@/utils/bfutil'
import { getMd5EncryptKey } from '@/writingFrequency/interphone/aesEncrypt'
import {
  getModelName,
  checkModel,
  checkDeviceModel,
} from '@/writingFrequency/modelInfo'
import { SnNumber } from '@/writingFrequency/SnNmber'

export { checkModel, checkDeviceModel }

/*
类似于 google protocol buffer 协议 json 结构
type: ["u8","int8","u16","u32","u64","longInt","byteArray","stringU16","string","password","mulInt","bcd"]
len: 字段长度
offset: 字段偏移量
bits: 字段按位计算协议
interval：字段在编、解码中的倍数
bool: 字段值为布尔类型
repeated: 字段重复次数
subFields: 字段子表协议，按字节计算
subTable: 子表结构协议有不同的结构，需要额外处理
unset： 空字节，未定义
*/

export const subTable = true
export const bool = true
export const Unset = 'unset'
export const DefModel = 'TD081000'
const BaseOptions = {
  model: DefModel,
  version: 0,
}

/**
 * 将指定位转换为boolean
 * @param {number} value
 * @param {number} bit
 * @returns {boolean}
 */
export function covertBitToBoolean(value, bit) {
  return ((value >> bit) & 0x01) === 1
}

/**
 * 将boolean值转换到指定位上
 * @param {boolean} value
 * @param {number} bit
 * @returns {number}
 */
export function coverBooleanToBit(value, bit) {
  return (value ? 1 : 0) << bit
}

/** 各结构版本协议 **/
// 设备信息 DeviceInfo
export const DeviceInfoProto = {
  0: {
    // ch: '型号',
    model: {
      type: 'string',
      len: 8,
    },
    // ch: '最小频率'
    minFrequency: {
      type: 'u16',
      offset: 8,
    },
    // ch: '最大频率'
    maxFrequency: {
      type: 'u16',
      offset: 10,
    },
    // ch: '固件版本'
    firmwareVersion: {
      type: 'byteArray',
      len: 4,
      offset: 12,
    },
  },
}
// 身份信息 IdentityInfo
export const IdentityInfoProto = {
  0: {
    // ch: '序列号',
    serizeNumber: {
      type: 'byteArray',
      len: 16,
    },
    // ch: '区域密文',
    areaCiphertext: {
      type: 'byteArray',
      len: 16,
      offset: 16,
    },
    // ch: '区域签名',
    areaSign: {
      type: 'byteArray',
      len: 128,
      offset: 32,
    },
  },
}
// 通讯密码 Password
export const PasswordProto = {
  0: {
    // ch: '通讯密钥',
    md5Key: {
      type: 'byteArray',
      len: 16,
    },
  },
}
// 总体设置 GeneralSettings
export const GeneralSettingsProto = {
  0: {
    // ch: '设备名称',
    deviceName: {
      type: 'stringU16',
      len: 32,
    },
    // ch: '设备id，信道id， 基站id， 系统id，代理id',
    ids: {
      type: 'u32',
      offset: 32,
    },
    // ch: '终端级别,是否值班机,是否指挥机	',
    clientType: {
      type: 'u8',
      offset: 36,
      bits: {
        clientLevel: {
          /// ch: '终端级别',
          len: 2,
          offset: 0,
        },
        // ch: '是否终端机器',
        isDutyMachine: Unset,
        // ch: '是否指挥机',
        isDirectMachine: Unset,
      },
    },
    // ch: '注册码',
    regCode: {
      type: 'byteArray',
      len: 8,
      offset: 37,
    },
    // ch: '中继id',
    repeaterId: {
      type: 'u32',
      offset: 45,
    },
    // ch: '声控等级',
    soundCtrlLevel: {
      type: 'u8',
      offset: 49,
    },
    // ch: '声控延迟',
    soundCtrlDelay: {
      type: 'u8',
      interval: 500,
      offset: 50,
    },
    // ch: '语音加密类型',
    soundEncryptType: {
      type: 'u8',
      offset: 51,
    },
    // ch: '语音加密值',
    soundEncryptValue: {
      type: 'string',
      len: 10,
      offset: 52,
    },
    // ch: '发射前导码持续时间',
    sendLeadCodeTime: {
      type: 'u8',
      interval: 240,
      offset: 62,
    },
    // ch: '脱网组呼挂起时间',
    offlineGroupCallHungTime: {
      type: 'u8',
      interval: 500,
      offset: 63,
    },
    // ch: '脱网单呼挂起时间',
    offlineSingleCallHungTime: {
      type: 'u8',
      interval: 500,
      offset: 64,
    },
    // ch: '省电模式',
    savePowerMode: {
      type: 'u8',
      bool,
      offset: 65,
    },
    // ch: '禁用所有LED, 频率显示,拒绝陌生呼叫,直通模式,菜单键关闭,全部静音,语音提示,信道空闲提示,呼叫允许指示',
    soundAndDisplayTip: {
      type: 'u16',
      offset: 66,
      bits: {
        disabledAllLED: {
          /// ch: '禁用所有LED',
          len: 1,
          bool,
          offset: 1,
        },
        freqDisplay: {
          // ch: '频率显示',
          len: 1,
          bool,
          offset: 2,
        },
        rejectUnfamiliarCall: {
          /// ch: '拒绝陌生呼叫',
          len: 1,
          bool,
          offset: 3,
        },
        directMode: {
          /// ch: '直通模式',
          len: 1,
          bool,
          offset: 4,
        },
        menuOff: {
          // ch: '菜单键关闭',
          len: 1,
          bool,
          offset: 5,
        },
        allSlient: {
          // ch: '全部静音',
          len: 1,
          bool,
          offset: 8,
        },
        voiceNotice: {
          // ch: '语音提示',
          len: 1,
          bool,
          offset: 9,
        },
        channelFreeNotice: {
          // ch: '信道空闲提示',
          len: 1,
          bool,
          offset: 10,
        },
        allowCallInstruction: {
          // ch: '呼叫允许指示',
          len: 1,
          bool,
          offset: 11,
        },
      },
    },
    // ch: '接收低电池电量提示间隔',
    powerInfoAlert: {
      type: 'u8',
      interval: 5,
      offset: 68,
    },
    // ch: '开机密码',
    powerOnPassword: {
      type: 'password',
      len: 6,
      offset: 69,
    },
  },
}
// 按键设置 ButtonSettings
export const ButtonSettingsProto = {
  0: {
    // ch: '长按持续时间',
    longPressLastTime: {
      type: 'u8',
      interval: 250,
    },
    // ch: '侧键1-短按',
    key1ShortPress: {
      type: 'u8',
      offset: 1,
    },
    // ch: '侧键2-长按',
    key1LongPress: {
      type: 'u8',
      offset: 2,
    },
    // ch: '侧键3-短按',
    key2ShortPress: {
      type: 'u8',
      offset: 3,
    },
    // ch: '侧键2-长按',
    key2LongPress: {
      type: 'u8',
      offset: 4,
    },
    // ch: '侧键3-短按',
    key3ShortPress: {
      type: 'u8',
      offset: 5,
    },
    // ch: '侧键3-长按',
    key3LongPress: {
      type: 'u8',
      offset: 6,
    },
    // ch: '侧键4-短按',
    key4ShortPress: {
      type: 'u8',
      offset: 7,
    },
    // ch: '侧键4-长按',
    key4LongPress: {
      type: 'u8',
      offset: 8,
    },
  },
}
// 短信 ShortMessage
export const ShortMessageProto = {
  0: {
    // ch: '短信id',
    msgId: {
      type: 'u16',
    },
    // ch: '短信内容',
    msgContent: {
      type: 'stringU16',
      len: 280,
      offset: 2,
    },
  },
}
// 菜单设置 MenuSettings
export const MenuSettingsProto = {
  0: {
    // ch: '菜单挂起时间',
    hangTime: {
      type: 'u8',
    },
    // ch: '短信,编辑通讯录,手动拨号,未接记录,已接记录,呼出记录,脱网,音调/提示,发射功率,背光,开机界面,,键盘锁,LED 指示灯,静噪,开机密码,声控'
    settings: {
      type: 'byteArray',
      len: 3,
      offset: 1,
      bits: {
        message: {
          // ch: '短信',
          len: 1,
          bool,
          offset: 0,
        },
        editAddressBook: {
          // ch: '编辑通讯录',
          len: 1,
          bool,
          offset: 1,
        },
        manualDialing: {
          // ch: '手动拨号',
          len: 1,
          bool,
          offset: 2,
        },
        unrecorded: {
          // ch: '未接记录',
          len: 1,
          bool,
          offset: 8,
        },

        record: {
          // ch: '已接记录',
          len: 1,
          bool,
          offset: 9,
        },
        outgoingCall: {
          // ch: '呼出记录',
          len: 1,
          bool,
          offset: 10,
        },
        offline: {
          // ch: '脱网',
          len: 1,
          bool,
          offset: 11,
        },
        toneCue: {
          // ch: '音调/提示',
          len: 1,
          bool,
          offset: 12,
        },
        emissivePower: {
          // ch: '发射功率',
          len: 1,
          bool,
          offset: 13,
        },
        // 定位开关 1 Enable, 0 Disable. default = 1
        locationEnable: {
          len: 1,
          bool,
          offset: 14,
        },
        backLight: {
          // ch: '背光',
          len: 1,
          bool,
          offset: 16,
        },
        poweronDesktop: {
          // ch: '开机界面',
          len: 1,
          bool,
          offset: 17,
        },
        keyboardLock: {
          // ch: '键盘锁',
          len: 1,
          bool,
          offset: 18,
        },

        ledIndicator: {
          // ch: 'LED 指示灯',
          len: 1,
          bool,
          offset: 19,
        },
        // quieting: {
        //   // ch: '静噪',
        //           //   len: 1,
        //   bool,
        //   offset: 20
        // },
        quieting: Unset,
        poweronPass: {
          // ch: '开机密码',
          len: 1,
          bool,
          offset: 21,
        },
        soundCtrl: {
          // ch: '声控',
          len: 1,
          bool,
          offset: 23,
        },
      },
    },
  },
}
// 数字通讯录 DigitalAddress
export const DigitalAddressProto = {
  0: {
    // ch: '通讯录 ID',
    id: {
      type: 'u16',
    },
    // ch: '名称',
    name: {
      type: 'stringU16',
      len: 32,
      offset: 2,
    },
    // ch: '号码',
    number: {
      type: 'mulInt',
      len: 3,
      offset: 34,
    },
    // ch: '呼叫类型,是否隐藏,是否锁定',
    setting: {
      type: 'u8',
      offset: 37,
      bits: {
        callType: {
          // ch: '呼叫类型',
          len: 6,
          offset: 0,
        },
        isHide: {
          // ch: '是否隐藏',
          len: 1,
          offset: 6,
        },
        isLock: {
          // ch: '是否锁定',
          len: 1,
          offset: 7,
        },
      },
    },
    // ch: '铃声类型',
    ringType: {
      type: 'u8',
      offset: 38,
    },
  },
}
// 接收组列表 RxGroup
export const RxGroupProto = {
  0: {
    groupId: {
      // ch: '接收组 ID',
      type: 'u16',
    },
    listenGroup: {
      // ch: '成员列表',
      type: 'u16',
      repeated: 16,
      offset: 2,
    },
    count: {
      // ch: '成员数量',
      type: 'u8',
      offset: 34,
    },
    groupName: {
      // ch: '名称',
      type: 'stringU16',
      len: 32,
      offset: 35,
    },
  },
}
// 信道参数协议表
const digitalChannelPara = {
  colorCode: {
    // ch: '彩色码',
    type: 'u8',
  },
  setting: {
    // ch: '允许脱网,脱网标志,中继台时隙,准许条件',
    type: 'u8',
    offset: 1,
    bits: {
      allowOffline: {
        // ch: '允许脱网',
        len: 1,
        bool,
        offset: 0,
      },
      offlineSign: Unset,
      // repeaterTimeSlot: {
      //   // ch: '中继台时隙',
      //   len: 1,
      //   offset: 5
      // },
      repeaterTimeSlot: Unset,
      allowCondition: {
        // ch: '准许条件',
        len: 2,
        offset: 6,
      },
    },
  },
  recvGroupId: {
    // ch: '接受组ID',
    type: 'u8',
    offset: 3,
  },
  defaultDigitalAddress: {
    // ch: '默认通讯录',
    type: 'u16',
    offset: 4,
  },
  switches: {
    // ch: '双工开关,单呼确认',
    type: 'u8',
    offset: 6,
    bits: {
      duplexSwitch: Unset,
      singleConfirm: {
        // ch: '单呼确认',
        len: 1,
        bool,
        offset: 1,
      },
      registerToSystem: {
        // ch: '注册到网络系统',
        len: 1,
        bool,
        offset: 2,
      },
      localCall: {
        // ch: '本地呼叫',
        len: 1,
        bool,
        offset: 3,
      },
      slotMode: {
        // ch: '时隙模式',
        len: 2,
        offset: 4,
      },
      virtualTimeSlot: {
        // ch: '虚拟集群发射时隙',
        len: 2,
        offset: 6,
      },
    },
  },
}
// 信道 Channel
export const ChannelProto = {
  0: {
    channelId: {
      // ch: '信道 ID',
      type: 'u16',
    },
    signalType: {
      // ch: '信号类型',
      type: 'u8',
      offset: 2,
    },
    recvFreq1: {
      // ch: '接收频率1',
      type: 'u32',
      offset: 3,
    },
    sendFreq1: {
      // ch: '发射频率1',
      type: 'u32',
      offset: 7,
    },
    recvFreq2: {
      // ch: '接收频率2',
      type: 'u32',
      offset: 11,
    },
    sendFreq2: {
      // ch: '发射频率2',
      type: 'u32',
      offset: 15,
    },
    recvFreq3: {
      // ch: '接收频率3',
      type: 'u32',
      offset: 19,
    },
    sendFreq3: {
      // ch: '发射频率3',
      type: 'u32',
      offset: 23,
    },
    digitalChannelPara: {
      // ch: '数字信道参数',
      type: 'byteArray',
      len: 15,
      subFields: digitalChannelPara,
      offset: 27,
    },
    channelSetting: {
      // ch: '发射功率,只接收,静噪',
      type: 'u8',
      offset: 42,
      bits: {
        sendPower: {
          // ch: '发射功率',
          len: 1,
          offset: 0,
        },
        recvOnly: {
          // ch: '只接收',
          len: 1,
          bool,
          offset: 1,
        },
        // quieting: {
        //   // ch: '静噪',
        //           //   len: 5,
        //   offset: 2
        // },
        quieting: Unset,
      },
    },
    sendTimeLimter: {
      // ch: '发射限时器',
      type: 'u8',
      interval: 15,
      offset: 43,
    },
    keyUpdateDelay: {
      // ch: 'TOT 密钥更新延迟',
      type: 'u8',
      offset: 44,
    },
    channelName: {
      // ch: '信道名称',
      type: 'stringU16',
      len: 32,
      offset: 45,
    },
  },
}
// 巡更系统配置 PatrolConfig
export const PatrolConfigProto = {
  0: {
    // ch: '应答超时',范围[2500 - 10000]毫秒,步进:500,默认5000[5];
    replyTimeout: {
      type: 'u8',
      interval: 500,
    },
    // ch: '时区-小时',范围-12～+12， default=8，隐藏
    timezoneHour: {
      type: 'int8',
      offset: 1,
    },
    // ch: '时区-分钟',范围0～59， default=0，隐藏
    timezoneMinute: {
      type: 'u8',
      offset: 2,
    },
  },
}
// 紧急报警 EmergencyAlarm
export const EmergencyAlarmProto = {
  0: {
    alarmConfig: {
      type: 'u8',
      offset: 0,
      bits: {
        // 紧急警报功能开关， default=1
        // 以下参数，当紧急警报功能开关=0时为不可选状态，设置时默认发送0；
        alarmEnable: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 报警类型，0--常规，1--静默，default=0
        alarmType: {
          len: 1,
          offset: 1,
        },
      },
    },
    // 发送次数, 范围:0~14，不停的。 步进:1，0xff表示不停的，default=3
    sendCount: {
      type: 'u8',
      offset: 1,
      default: 3,
    },
    // 呼叫联系人，选定-0xffff，default=0xffff
    callingContact: {
      type: 'u16',
      offset: 2,
      default: 0xffff,
    },
    // 警报后自动开监听时间，单位秒，范围0-99，0表示报警后不自动监听，default=20
    autoListenTime: {
      type: 'u8',
      offset: 4,
      default: 20,
    },
    // 警报后自动开跟踪定位时间，单位秒，范围0-99，0表示报警后不自动开跟踪定位时间，default=30
    autoTrackTime: {
      type: 'u8',
      offset: 5,
      default: 30,
    },
  },
}
// 自动定位监控 TrackMonitor
export const TrackMonitorProto = {
  0: {
    // ch: 自动定位监控开关， default=1
    trackEnable: {
      type: 'u8',
    },
    // ch: '轮询时间',单位秒，步进5，范围0-9995秒，默认300秒（60）
    rollTime: {
      type: 'u16',
      interval: 5,
      offset: 1,
    },
    // ch: '轮询距离',单位米，步进5，范围0-495米，默认50米（10）
    rollDistant: {
      type: 'u8',
      interval: 5,
      offset: 3,
    },
  },
}
// 有源RFID ActiveRFID
export const ActiveRFIDProto = {
  0: {
    settings: {
      // ch: '功能开关,模式,功率,应答机制',
      type: 'u8',
      bits: {
        enable: {
          // ch: '功能开关',
          offset: 0,
          len: 1,
        },
        mode: {
          // ch: '模式',
          offset: 1,
          len: 1,
        },
        power: {
          // ch: '功率',
          offset: 2,
          len: 2,
        },
        replyMechanism: {
          // ch: '应答机制',
          offset: 4,
          len: 1,
        },
      },
    },
    workChannel: {
      // ch: '工作频道',
      type: 'u8',
      offset: 1,
    },
    address: {
      // ch: '数据通道x地址',
      type: 'u64',
      repeated: 6,
      offset: 2,
    },
  },
}
// 可用信道 AvailableChannels
export const AvailableChannelsProto = {
  0: {
    areaChannelIndexTable: {
      // ch: '本区域信道索引表',
      type: 'u16',
      repeated: 38,
    },
  },
}
// 电话本，PhoneBook 支持 100 个电话本
export const PhoneBookProto = {
  0: {
    phoneId: {
      // 电话本ID
      type: 'u16',
    },
    phoneName: {
      // 电话本名字
      type: 'stringU16',
      len: 32,
      offset: 2,
    },
    // ASCII码“0123456789*#”
    phoneNo: {
      // 电话本号码
      type: 'string',
      len: 20,
      offset: 34,
    },
  },
}

const ProtocolCache = Object.create(null)

export function loadProtocol(modelCode, protocol) {
  ProtocolCache[modelCode] = protocol
}

function getProto(modelCode, type) {
  const protoModule = ProtocolCache[modelCode]
  if (!protoModule) {
    return undefined
  }

  return protoModule[type]
}

// 编解码指令数据帧中的数据区
export class DeviceBase {
  constructor({ model = '', version = 0 } = BaseOptions) {
    // 数据区参数
    // 数据结构类型
    this.type = 0
    // 数据结构版本
    this.version = version
    // 结构数据量=结构数据区总长度/结构长度
    this.volume = 0
    // 结构数据区
    this.data = []
    // 结构长度
    this.structLen = 0
    // 设备的型号
    this.model = model
  }

  getProtoInfo(type = this.type, version = this.version) {
    const proto = getProto(this.model, type)
    if (!proto) {
      return undefined
    }
    return proto[version]
  }

  marshal(data) {
    if (typeof data === 'undefined') {
      return []
    }

    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }

    const protoInfo = this.getProtoInfo(this.type, this.version)
    if (this.structLen === 0) {
      this.structLen = getProtoSize(protoInfo)
    }

    return data
      .map(item => {
        return marshalProto(item, protoInfo, this.structLen)
      })
      .reduce((p, c) => [...p, ...c], [])
  }

  encode(data) {
    this.data = this.marshal(data)
    let bytes = []
    bytes[0] = this.type
    bytes[1] = this.version

    this.volume = ~~(this.data.length / this.structLen)
    bytes = bytes.concat(writeU16LE(this.volume))
    bytes = bytes.concat(this.data.slice(0, this.volume * this.structLen))

    return bytes
  }

  unmarshal(structObj) {
    // 根据结构类型和版本获取对应结构属性
    const protoInfo = this.getProtoInfo(structObj.type, structObj.version)
    if (!protoInfo) {
      structObj.result = []
      return structObj
    }

    // 计算对应协议的长度
    const size = getProtoSize(protoInfo)
    this.structLen = size
    const res = []
    for (let i = 0; i < structObj.volume; i++) {
      const start = i * size
      res.push(
        unmarshalProto(structObj.data.subarray(start, start + size), protoInfo),
      )
    }
    structObj.result = res

    return structObj
  }

  decode(structData) {
    // 解析成一个对象结构
    const cmdObj = {
      origin: structData,
    }
    // 指令类型
    cmdObj.type = structData[0]
    // 结构版本
    cmdObj.version = structData[1]
    // 数据长度
    cmdObj.volume = readU16LE(structData, 2)
    // 数据值
    cmdObj.data = structData.subarray(4)

    return this.unmarshal(cmdObj)
  }
}

// 设备信息 DeviceInfo = 1,
export class DeviceInfo extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 1
    this.structLen = options.structLen || 16
  }

  decode(structData) {
    const decodeData = super.decode(structData)
    const config = decodeData.result[0]
    if (config) {
      config.firmwareVersion = config.firmwareVersion.join('.')
      config.modelName = getModelName(config.model)
      decodeData.result[0] = config
    }

    return decodeData
  }

  encode(data) {
    if (!data) {
      return super.encode(data)
    }
    if (!Array.isArray(data)) {
      data = [data]
    }
    return super.encode(
      data.map(item => {
        const firmwareVersion = item.firmwareVersion
          .split('.')
          .map(v => parseInt(v))
        firmwareVersion.push(0)
        item.firmwareVersion = firmwareVersion
        return item
      }),
    )
  }
}

export function getDefaultByteString(length, code = 0xff) {
  return new Array(length).fill(code).toString()
}

// 二代为8位ASCII码，后8位为0xFF或0x00
export function isSecondIdentity(codeArray) {
  const end16Bytes = codeArray.splice(8)
  const end16BytesStr = end16Bytes.toString()

  return (
    end16BytesStr === getDefaultByteString(end16Bytes.length, 0xff) ||
    end16BytesStr === getDefaultByteString(end16Bytes.length, 0x00)
  )
}

// 身份信息 IdentityInfo = 2
// 一代序列号规则是16位ASCII码，0-9、A-Z
export class IdentityInfo extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 2
    this.structLen = options.structLen || 160
  }

  decode(structData) {
    const res = super.decode(structData)
    res.result = res.result.map(info => {
      info.serizeNumber = this.decodeSerialNumber(info.serizeNumber)

      Object.keys(info)
        .filter(key => {
          return (
            Object.prototype.toString.call(info[key]) === '[object Uint8Array]'
          )
        })
        .forEach(key => {
          info[key] = bfutil.uint8Array2Array(info[key])
        })

      return info
    })
    return res
  }

  decodeSerialNumber(code) {
    return SnNumber.TryDecodeSnNumber(code)
  }
}

// 身份信息 IdentityInfo = 2
// 二代序列号规则是8位ASCII码，0-9、A-Z，为原16字节的前8位，后8位填充0xFF或0x00
export class SecondIdentityInfo extends IdentityInfo {
  constructor(options = BaseOptions) {
    super(options)
  }

  decodeSerialNumber(code) {
    return SnNumber.TryDecodeSnNumber(code)
  }
}

// 通讯密码 Password = 3,
export class Password extends DeviceBase {
  constructor(options = BaseOptions) {
    super(options)
    // 重写不同结构的类型、版本等参数
    this.type = options.type || 3
    this.structLen = options.structLen || 16
  }

  encode(data) {
    if (!data) {
      return super.encode()
    }
    // 需要编码多个数据
    if (!Array.isArray(data)) {
      data = [data]
    }
    data = data.map(item => {
      item.md5Key = getMd5EncryptKey(item.md5Key)
      return item
    })
    return super.encode(data)
  }
}

export function writeU32LE(num) {
  return [
    num & 0xff,
    (num & 0xff00) >> 8,
    (num & 0xff0000) >> 16,
    (num & 0xff000000) >> 24,
  ]
}

export function readInt8LE(buf, offset) {
  return (buf[offset] << 24) >> 24
}

export function readU32LE(buf, offset) {
  return (
    buf[offset] +
    (buf[offset + 1] << 8) +
    (buf[offset + 2] << 16) +
    (buf[offset + 3] << 24)
  )
}

export function readU64LE(buf, offset) {
  return parseInt(readU64LEHex(buf, offset), 16)
}

export function readU64LEHex(buf, offset) {
  const arr = buf.subarray(offset, offset + 8)
  return Array.from(arr)
    .reverse()
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
    .toUpperCase()
}

export function writeU64LE(bigNum) {
  return writeU64LEHex(bigNum.toString(16))
}

export function writeLongIntLE({ low, high, unsigned = true }) {
  return Long.fromBits(low, high, unsigned).toBytesLE()
}

export function readLongIntLE(data, offset) {
  const bytes = readByteArray(data, offset, 8)
  return Long.fromBytesLE(bytes, true)
}

export function writeU64LEHex(numberHex) {
  const len = numberHex.length
  if (len % 2 !== 0) {
    numberHex = '0' + numberHex
  }
  let bytes = []

  for (let i = 0; i < len; i += 2) {
    const n = parseInt(numberHex.substr(i, 2), 16)
    bytes.unshift(n)
  }

  // 固定为8个字节，不足补0
  bytes = getArrayBySpecifiedLength(bytes, 8)

  return bytes
}

export function readString(buf, offset, len) {
  // 在写频中，字符串编码成ascii码后有补充0,在解码时需要过滤掉
  const u8_data = buf.subarray(offset, len + offset).filter(b => {
    return b > 0
  })
  return bfutil.uint8array2string(u8_data)
}

export function readByteArray(buf, offset, len) {
  return buf.subarray(offset, len + offset)
}

export function getArrayBySpecifiedLength(arr = [], len = 0, num = 0) {
  const old_len = arr.length

  arr.length = len
  arr.fill(num, old_len)

  return arr
}

export function writeStringU16(str, len) {
  var buf = new ArrayBuffer(len || str.length * 2)
  var bufView = new Uint16Array(buf)
  for (var i = 0, strLen = str.length; i < strLen; i++) {
    bufView[i] = str.charCodeAt(i)
  }
  return new Uint8Array(buf)
}

function __readUTF16String(bytes) {
  var ix = 0
  var offset1 = 1
  var offset2 = 0

  var string = ''
  for (; ix < bytes.length; ix += 2) {
    var byte1 = bytes[ix + offset1]
    var byte2 = bytes[ix + offset2]
    var word1 = (byte1 << 8) + byte2
    if (byte1 < 0xd8 || byte1 >= 0xe0) {
      if (word1 === 0) {
        return string
      }
      string += String.fromCharCode(word1)
    } else {
      ix += 2
      var byte3 = bytes[ix + offset1]
      var byte4 = bytes[ix + offset2]
      var word2 = (byte3 << 8) + byte4
      string += String.fromCharCode(word1, word2)
    }
  }

  return string
}

export function readStringU16(buf, offset, len) {
  const u16_data = buf.subarray(offset, len + offset)
  return __readUTF16String(u16_data)
}

export function string2bytes(str, separator = '') {
  return str.split(separator).map(s => {
    return s.charCodeAt(0)
  })
}

export function writeByteArrayFromString(str, byteLen) {
  const bytes = string2bytes(str)
  return getArrayBySpecifiedLength(bytes, byteLen)
}

export function buf2base64(buf) {
  return base64js.fromByteArray(buf)
}

export function base642buf(buf) {
  return base64js.toByteArray(buf)
}

export function readU16LE(buf, offset) {
  return buf[offset] + (buf[offset + 1] << 8)
}

export function writeU16LE(num) {
  return [num & 0xff, (num & 0xff00) >> 8]
}

export function hex2base64(hex) {
  const bytes = bfutil.hex2bin(hex)
  return buf2base64(bytes)
}

export function base642hex(base64Str) {
  const bytes = base64js.toByteArray(base64Str)
  return bfutil.uint8Array2hex(bytes)
}

export function getProtoPropertyLen(item) {
  switch (item.type) {
    case 'u8':
    case 'int8':
      return 1
    case 'u16':
      return 2
    case 'u32':
      return 4
    case 'u64':
    case 'longInt':
      return 8
    default:
      if (item.len) {
        return item.len
      }

      throw new Error('bad struct info:' + JSON.stringify(item))
  }
}

export function bit2boolean(bit) {
  return !!bit
}

export function boolean2bit(bool) {
  return bool ? 1 : 0
}

export function getProtoSize(proto) {
  let size = 0
  for (const k in proto) {
    const item = proto[k]
    if (item === Unset) {
      continue
    }
    let len = getProtoPropertyLen(item)
    len *= item.repeated || 1
    len += item.offset || 0
    if (size < len) {
      size = len
    }
  }

  return size
}

export function unmarshalDataByType(item, data, offset = 0, len = 1) {
  let modelVal = readFromBytes(item, data, offset, len)

  if (item.bits) {
    modelVal = unmarshalBits(modelVal, item.bits)
  }

  if (item.interval) {
    modelVal = modelVal * item.interval
  }

  if (item.bool) {
    modelVal = bit2boolean(modelVal)
  }

  if (item.subFields) {
    const subFieldsCache = {}
    for (const k in item.subFields) {
      const field = item.subFields[k]
      const fieldLen = getProtoPropertyLen(field)
      subFieldsCache[k] = unmarshalDataByType(
        field,
        modelVal,
        field.offset,
        fieldLen,
      )
    }

    modelVal = subFieldsCache
  }

  return modelVal
}

// 注册码为只读，写入时以0字节填充
export function setRegistrationCode(len = 8) {
  const code = new Array(len)
  code.fill(0)

  return code
}

export function getRegistrationCode(codeArray = []) {
  if (typeof codeArray === 'string' || codeArray.length === 0) {
    return ''
  }
  return Array.from(codeArray)
    .reverse()
    .map(s => {
      return s.toString(16).padStart(2, '0').toUpperCase()
    })
    .reduce((p, c) => {
      return p + c
    })
}

export function readPassword(data, offset, len) {
  const bytes = readByteArray(data, offset, len)
  if (!bytes || bytes.toString() === new Array(len).fill(0).toString()) {
    return ''
  }
  return String.fromCharCode.apply(null, bytes)
}

export function writePassword(pwd = '', len = 6) {
  if (typeof pwd !== 'string' || pwd.length !== len) {
    return new Array(len).fill(0)
  }

  return pwd.split('').map(s => {
    return s.charCodeAt(0)
  })
}

export function writeInBytes(val, item) {
  let bytes = []
  switch (item.type) {
    case 'u8':
    case 'int8':
      bytes.push(val || 0)
      break
    case 'u16':
      bytes = bytes.concat(writeU16LE(val))
      break
    case 'u32':
      bytes = bytes.concat(writeU32LE(val))
      break
    case 'u64':
      bytes = bytes.concat(writeU64LE(val))
      break
    case 'longInt':
      bytes = bytes.concat(writeLongIntLE(val))
      break
    case 'byteArray':
      switch (typeof val) {
        case 'number':
          val = writeU32LE(val)
          break
      }
      bytes = bytes.concat(
        getArrayBySpecifiedLength(bfutil.uint8Array2Array(val), item.len),
      )
      break
    case 'string':
      bytes = bytes.concat(writeByteArrayFromString(val, item.len))
      break
    case 'stringU16':
      bytes = bytes.concat(
        bfutil.uint8Array2Array(writeStringU16(val, item.len)),
      )
      break
    case 'password':
      bytes = bytes.concat(writePassword(val, item.len))
      break
    case 'mulInt':
      bytes = bytes.concat(writeMultipleInt(val, item.len))
      break
    case 'bcd':
      bytes = bytes.concat(writeBCDBytes(val, item.len))
      break
    default:
      throw new Error('can not marshal data of type:' + item.type)
  }

  return bytes
}

export function readFromBytes(item, data, offset, len) {
  let modelVal = 0
  switch (item.type) {
    case 'int8':
      modelVal = readInt8LE(data, offset)
      break
    case 'u8':
      modelVal = data[offset]
      break
    case 'u16':
      modelVal = readU16LE(data, offset)
      break
    case 'u32':
      modelVal = readU32LE(data, offset)
      break
    case 'u64':
      modelVal = readU64LE(data, offset)
      break
    case 'longInt':
      modelVal = readLongIntLE(data, offset)
      break
    case 'string':
      modelVal = readString(data, offset, len)
      break
    case 'stringU16':
      modelVal = readStringU16(data, offset, len)
      break
    case 'byteArray':
      modelVal = readByteArray(data, offset, len)
      break
    case 'password':
      modelVal = readPassword(data, offset, len)
      break
    case 'mulInt':
      modelVal = readMultipleInt(data, offset, len)
      break
    case 'bcd':
      modelVal = readBCDString(data, offset, len)
      break
    default:
      throw new Error('unmarshal err,no type:' + JSON.stringify(item))
  }

  return modelVal
}

function writeBufferInBytes(bytes, offset, buffer, repeated = 0) {
  const bufferLen = buffer.length
  for (let i = 0; i < bufferLen; i++) {
    const buf = buffer[i]
    if (buf === 0) {
      continue
    }
    const index = offset + bufferLen * repeated + i
    bytes[index] = buf
  }
}

export function unmarshalProto(data, protoInfo) {
  const model = {}
  let offset = 0
  for (const k in protoInfo) {
    const item = protoInfo[k]
    if (item === Unset) {
      continue
    }

    const len = getProtoPropertyLen(item)
    // 标记需要重复处理的次数，默认1次
    const repeated = item.repeated || 1
    if (repeated > 1) {
      const r = []
      offset = item.offset || offset
      for (let i = 0; i < repeated; ++i) {
        r.push(unmarshalDataByType(item, data, offset, len))
        offset += len
      }
      model[k] = r
    } else {
      offset = item.offset || offset
      model[k] = unmarshalDataByType(item, data, offset, len)
    }
  }

  return model
}

function isObject(target) {
  return Array.prototype.toString.call(target) === '[object Object]'
}

function marshalBits(data, proto) {
  if (!isObject(data) || !isObject(proto)) {
    return 0
  }

  let val = 0
  for (const k in proto) {
    const item = proto[k]
    if (item === Unset) {
      continue
    }

    const offset = item.offset || 0
    let src = data[k]
    if (item.bool) {
      src = boolean2bit(src)
    }
    val += src << offset
  }

  return val
}

function unmarshalBits(data, proto) {
  if (!proto) {
    return {}
  }
  if (typeof data !== 'number') {
    if (typeof data.length === 'undefined') {
      return data
    }
    let sum = 0
    data.map((s, i) => {
      sum += s << (i * 8)
    })
    data = sum
  }

  const subCache = {}
  for (const k in proto) {
    const item = proto[k]
    const len = item.len || 1
    const offset = item.offset || 0
    subCache[k] = (data & (2 ** (offset + len) - 1)) >> offset

    if (item.bool) {
      subCache[k] = bit2boolean(subCache[k])
    }
  }

  return subCache
}

export function marshalDataByType(obj, item, val) {
  // 处理按位计算字段协议
  if (item.bits) {
    val = marshalBits(val, item.bits)
  }

  // 处理有间隔倍数数值
  if (item.interval) {
    val = val / item.interval
  }

  if (item.bool) {
    val = boolean2bit(val)
  }

  if (item.subFields) {
    if (!item.len) {
      item.len = getProtoSize(item.subFields)
    }
    // 协议定义要重复n次，但实际上不足n个数据时，val为undefined，需要设置一个默认值
    if (!val) {
      val = setSubFieldsDefaultValue(item.subFields)
    }
    const subFieldsCache = new Array(item.len).fill(0)
    // 按offset属性升序排序，确保编码位置正确
    for (const fieldName in item.subFields) {
      const field = item.subFields[fieldName]
      const subfieldsData = val[fieldName]
      const offset = field.offset || 0
      const v = marshalDataByType(val, field, subfieldsData)
      writeBufferInBytes(subFieldsCache, offset, v)
    }
    val = subFieldsCache
  }

  return writeInBytes(val, item)
}

export function setSubFieldsDefaultValue(subFields) {
  const val = {}
  for (const k in subFields) {
    val[k] = 0
  }
  return val
}

export function setDefaultValue(proto) {
  // 生成数组对象
  const result = []
  if (!proto.repeated) {
    proto.repeated = 1
  }
  for (let i = 0; i < proto.repeated; i++) {
    result.push(setSubFieldsDefaultValue(proto.subFields))
  }
  return result
}

export function marshalProto(data, protoInfo, structLen = 0) {
  let size = getProtoSize(protoInfo)
  if (size < structLen) {
    size = structLen
  }
  const bytes = new Array(size).fill(0)
  for (const name in protoInfo) {
    const item = protoInfo[name]
    // 跳过Unset
    if (item === Unset) {
      continue
    }

    let val = data[name]
    const repeated = item.repeated || 1
    const offset = item.offset || 0

    if (repeated > 1) {
      if (!val) {
        val = setDefaultValue(item)
      }
      for (let i = 0; i < repeated; ++i) {
        const v = marshalDataByType(data, item, val[i] ?? 0)
        writeBufferInBytes(bytes, offset, v, i)
      }
    } else {
      writeBufferInBytes(bytes, offset, marshalDataByType(data, item, val))
    }
  }
  return bytes
}

// 524295=>[7,0,8], [data & 0xff, (data & 0xff00) >> 8, (data & 0xff0000) >> 16, (data & 0xff000000) >> 24]
function writeMultipleInt(data, len = 0) {
  const bytes = new Array(len).fill(0)
  for (let i = 0; i < len; i++) {
    bytes[i] = (data & parseInt('FF'.padEnd(i * 2 + 2, '0'), 16)) >> (i * 8)
  }
  return bytes
}

// [7,0,8]=>524295, (8<<(2*8))+(0<<(1*8))+(7<<(0*8))
function readMultipleInt(data, offset, len) {
  const bytes = readByteArray(data, offset, len)
  let val = 0
  for (let i = 0; i < len; i++) {
    val += bytes[i] << (i * 8)
  }
  return val
}

export function generateEnumObject(source) {
  const ValuesById = {}
  const Values = Object.create(ValuesById)
  Object.keys(source).forEach(k => {
    const v = source[k]
    Values[(ValuesById[v] = k)] = v
  })

  return Values
}

// 如135 0933 2766，保存为0xFF 0xFF 0xF1 0x35 0x09 0x33 0x27 0x66
export function writeBCDBytes(data, len = 8) {
  const dataLen = len * 2
  data = data.slice(0, dataLen).padStart(dataLen, 'F')
  const bytes = new Array(len).fill(0xff)

  let start = len - 1
  let index = data.length
  while (index >= 2) {
    bytes[start--] = parseInt('0x' + data.substr(index - 2, 2), 16)
    index -= 2
  }

  return bytes
}

export function readBCDString(data, offset, len) {
  const bytes = readByteArray(data, offset, len)
  let str = ''
  for (let i = 0; i < bytes.length; i++) {
    const code = bytes[i]
    str += code.toString(16).padStart(2, '0').toUpperCase()
  }

  while (str.startsWith('F')) {
    str = str.slice(1)
  }

  return str
}

// TD811 TD825
export const BaseOptions2 = {
  version: 0,
  structLen: 0,
  proto: null,
}

function createSubBitsValue(bits, source) {
  const data = Object.create(null)
  for (const k in bits) {
    const proto = bits[k]
    const val = source[k]
    if (proto.bool) {
      data[k] = !!val
      continue
    }
    data[k] = val
  }
  console.log('createSubBitsValue:', data, bits, source)
  return data
}

function createFieldDefaultValue(field, source) {
  if (!field) {
    return undefined
  }
  // 有按拉计算属性，生成对象
  if (field.bits) {
    return createSubBitsValue(field.bits, source)
  }
  // 有子表结构对象，递归处理
  if (field.repeated > 1) {
    const list = []
    for (let i = 0; i < field.repeated; i++) {
      const subDataSource = source[i]
      if (field.subFields) {
        for (const key in field.subFields) {
          const subField = field.subFields[key]
          list.push(createFieldDefaultValue(subField, subDataSource[key]))
        }
      } else {
        list.push(Object.create(null))
      }
    }
    return list
  }

  switch (field.type) {
    case 'u8':
    case 'int8':
    case 'u16':
    case 'u32':
    case 'u64':
    case 'longInt':
      return source || 0
    case 'byteArray':
      return source || []
    case 'stringU16':
      return source || ''
    default:
      return 0
  }
}

export class Base {
  constructor(options = BaseOptions2) {
    // 协议版本
    this.version = options.version
    // 协议版本对应的单个数据结构长度
    this.structLen = options.structLen
    // 指令参数数据，每个元素为一个结构对象数据
    this.data = []
    // 对应的结构协议
    this.proto = options.proto || null
    // 原始指令数据容器
    this.bytes = []
  }

  encode() {
    if (!this.proto || this.structLen < 1) {
      return new Uint8Array(0)
    }

    this.bytes = this.data
      .map(data => marshalProto(data, this.proto, this.structLen))
      .reduce((p, c) => [...p, ...c], [])

    return this.bytes
  }

  decode(bytes) {
    if (bytes) {
      this.bytes = bytes
    }
    if (!this.proto || this.structLen < 1) {
      return (this.data = [])
    }

    // 当前数据区包含count个数据项
    const count = ~~(this.bytes.length / this.structLen)
    // 截取指定长度的数据区
    const data = []
    if (!(this.bytes instanceof Uint8Array)) {
      this.bytes = new Uint8Array(this.bytes)
    }
    for (let i = 0; i < count; i++) {
      const start = i * this.structLen
      data.push(this.bytes.subarray(start, start + this.structLen))
    }

    // 解码数据项
    this.data = data.map(item => unmarshalProto(item, this.proto))

    return this.data
  }

  // 从数据源中生成一个协议结构对象
  fromJson(source) {
    const data = {}
    if (!this.proto) {
      return data
    }

    // 没有数据源，则生成一个空的JSON对象
    if (!source) {
      source = {}
    }

    for (const key in this.proto) {
      data[key] = createFieldDefaultValue(this.proto[key], source[key] || 0)
    }

    return data
  }
}

function fixNumbersLetters(str) {
  return str.replace(/[^0-9a-zA-Z]/g, '')
}

// 编程密码规则：数字+字母
export function fixProgramPassword(str) {
  return fixNumbersLetters(str)
}

// 语音加密规则：数字+字母
export function fixVoiceEncryptKey(str) {
  return fixNumbersLetters(str)
}

// 开机密码规则：数字
export function fixPowerOnPassword(str, patten = /[^0-9]/g) {
  return str.replace(patten, '')
}

// 过滤扫描列表、漫游列表中不存在系统中的信道ID
// 返回有效的信道ID列表
export function filterChannelIdWhenDeviceChanged(
  channelIdList,
  sourceList,
  includes = [0xfffe],
) {
  // 不存在，相当于取消选择设备，清除所有选择信道结果
  if (channelIdList?.length === 0) {
    return sourceList.filter(chId => {
      return includes.includes(chId)
    })
  }

  return sourceList.filter(chId => {
    return includes.includes(chId) || channelIdList.includes(chId)
  })
}

// 数字报警重置回复信道ID
// 返回有效的信道ID
export function resetDigitalAlertReplyChannel(channelIdList, chId) {
  const noReplyChannel = 0xffff
  const defReplyChannel = 0xfffe
  // 不存在，相当于取消选择设备，清除所有选择信道结果
  if (channelIdList?.length === 0) {
    return noReplyChannel
  }

  if (channelIdList.includes(chId)) {
    return chId
  }

  return defReplyChannel
}

/**
 * 处理卫星定位配置的查询命令,需包含截止符,默认为空,不允许查询
 * @param {string} str 卫星定位配置的查询命令
 * @returns {string} 修正后的命令字符串
 */
export function gpsSettingsQueryCmdInputEvent(str) {
  if (!str) {
    return ''
  }

  // const reg = /[^\w~`!@#$%^&*()_=+\-*/.\\|[\]{};:'",<>/?]*/gi
  // 匹配中文,替换成空字符串
  const reg = /[\u4e00-\u9fa5]/gi
  return str.replace(reg, '')
}
