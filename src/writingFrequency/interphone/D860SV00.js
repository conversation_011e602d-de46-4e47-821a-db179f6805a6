import { getMd5EncryptKey } from '@/writingFrequency/interphone/aesEncrypt'
import { getModelName } from '@/writingFrequency/modelInfo'
import {
  Base,
  BaseOptions2 as BaseOptions,
  bool,
  covertBitToBoolean,
  generateEnumObject,
  getDefaultByteString,
  isSecondIdentity,
} from './common'

export const Model = 'D860SV00'

/*此BP860SVT协议是基于TD930机型协议做修改*/

// 机型生产信息
const DeviceInfoProto = {
  0: {
    // ch: '型号',
    model: {
      type: 'string',
      len: 8,
      default: '',
    },
    // ch: '最小频率'
    minFrequency: {
      type: 'u16',
      offset: 8,
      default: 0,
    },
    // ch: '最大频率'
    maxFrequency: {
      type: 'u16',
      offset: 10,
      default: 0,
    },
    // ch: '固件版本'
    firmwareVersion: {
      type: 'u16',
      offset: 12,
      bits: {
        mainVer: {
          len: 4,
          offset: 0,
          default: 0,
        },
        subVer: {
          len: 4,
          offset: 4,
          default: 0,
        },
        modVer: {
          len: 8,
          offset: 8,
          default: 0,
        },
      },
    },
    // UI版本
    uiVersion: {
      type: 'u8',
      offset: 14,
      bits: {
        mainVer: {
          len: 4,
          offset: 0,
          default: 0,
        },
        subVer: {
          len: 4,
          offset: 4,
          default: 0,
        },
      },
    },
    // 数据库版本
    dbVersion: {
      type: 'u8',
      offset: 15,
      default: 0,
    },
  },
}

export class TD930DeviceInfo extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 16
    this.proto = DeviceInfoProto[this.version]
  }

  // 机型解析， 最后2字节为机型拥有的功能表。基础数值0x40(bit6: 1, bit7: 0)
  // 高字节：bit0: 单独工作， bit1: roam
  // 低字节: bit0: 蓝牙，bit1: 录音功能，
  // 根据选配的功能形成机型号码
  decodeModel(model) {
    const byte1 = model.charCodeAt(6) & 0xff // 高字节
    const byte2 = model.charCodeAt(7) & 0xff // 低字节
    return {
      aloneWork: covertBitToBoolean(byte1, 0),
      roam: covertBitToBoolean(byte1, 1),

      bluetooth: covertBitToBoolean(byte2, 0),
      record: covertBitToBoolean(byte2, 1),
    }
  }

  decode(bytes) {
    const decodeData = super.decode(bytes)
    const config = decodeData[0]
    if (config) {
      config.config = this.decodeModel(config.model)
      config.firmwareVersion = `${config.firmwareVersion.mainVer}.${config.firmwareVersion.subVer}.${config.firmwareVersion.modVer}.${config.uiVersion.mainVer}`
      config.uiVersion = ''
      config.modelName = getModelName(config.model)
    }

    return decodeData
  }

  encode() {
    return super.encode(
      this.data.map(item => {
        const firmwareVersion = item.firmwareVersion.split('.')
        item.firmwareVersion = {
          mainVer: firmwareVersion[0],
          subVer: firmwareVersion[1],
          modVer: firmwareVersion[2],
        }
        const uiVersion = item.uiVersion.split('.')
        item.uiVersion = {
          mainVer: uiVersion[0],
          subVer: uiVersion[1],
        }
        return item
      }),
    )
  }
}

// 身份信息
const IdentityInfoProto = {
  0: {
    // ch: '序列号',
    serialNumber: {
      type: 'byteArray',
      len: 16,
      default: '',
    },
    // ch: '区域密文',
    areaCiphertext: {
      type: 'byteArray',
      len: 128,
      offset: 16,
    },
    // ch: '区域签名',
    areaSignature: {
      type: 'byteArray',
      len: 16,
      offset: 144,
    },
  },
}

function decodeSerialNumber(code) {
  if (!code) {
    return ''
  }

  let codeArray = Array.from(code)

  // 二代序列号
  if (isSecondIdentity(codeArray)) {
    codeArray = codeArray.splice(0, 8)
  }

  // 全0xFF或0x00为开发模式，没有序列号
  const codeArrayStr = codeArray.toString()
  if (
    codeArrayStr === getDefaultByteString(codeArrayStr.length, 0xff) ||
    codeArrayStr === getDefaultByteString(codeArrayStr.length, 0x00)
  ) {
    return ''
  }

  // 一代序列号, 为16位ASCII码
  return String.fromCharCode.apply(null, codeArray)
}

// 要兼容一代、二代序列号
export class IdentityInfo extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 160
    this.proto = IdentityInfoProto[this.version]
  }

  decode(bytes) {
    const decodeData = super.decode(bytes)
    const config = decodeData[0]
    if (config) {
      config.serialNumber = decodeSerialNumber(config.serialNumber)
    }
    return decodeData
  }
}

// 写频读密码
const PasswordProto = {
  0: {
    // ch: '通讯密钥',
    md5Key: {
      type: 'byteArray',
      len: 16,
      default: '',
    },
  },
}

export class Password extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 16
    this.proto = PasswordProto[this.version]
  }

  encode() {
    return super.encode(
      this.data.map(item => {
        item.md5Key = getMd5EncryptKey(item.md5Key)
        return item
      }),
    )
  }
}

const nowDate = new Date()
const timezoneOffset = nowDate.getTimezoneOffset()
// 总体设置
const GeneralSettingsProto = {
  0: {
    // ch: '设备名称',
    deviceName: {
      type: 'stringU16',
      len: 34,
      default: 'BF-BP860(SDC)',
    },
    // 开机密码，必须为6个数字0~9的ASCII字符，1字节结束符(终端需要)
    powerOnPwd: {
      type: 'password',
      len: 7,
      offset: 34,
      default: '',
    },
    // 开机密码错误次数限制，1~10，default 3, 超过设置值设备自毙
    powerOnPwdErrLimit: {
      type: 'u8',
      offset: 41,
      default: 3,
    },
    baseSettings: {
      type: 'u8',
      offset: 42,
      bits: {
        // 信道显示模式 0 频率显示 1 信道显示 2 频率+信道，默认2
        chDisplayMode: {
          len: 2,
          offset: 0,
          default: 2,
        },
        // 菜单键关闭 0 禁用 1 可用
        menuOff: {
          len: 1,
          bool,
          offset: 2,
          default: false,
        },
        // 开机界面
        bootInterface: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // ch: '省电模式', 下拉列表 0 无，1 1:1, 2 1:2, 3 1:3, 4 1:4, 默认值 1
        savePowerMode: {
          len: 3,
          offset: 4,
          default: 1,
        },
        // ch: '允许擦除',
        allowErasing: {
          len: 1,
          bool,
          offset: 7,
          default: false,
        },
      },
    },
    // 省电模式延迟开启时间, 5~60秒，步进5，默认值5，如果“省电模式”配置为“无”时，则不支持配置
    powerSavingModeDelayTime: {
      type: 'u8',
      offset: 43,
      default: 5,
    },
    // ch: '语言环境',"0:中文 1:英文"		默认1
    locale: {
      type: 'u8',
      offset: 44,
      default: 0,
    },
    // ch: '声控等级', [关,1-8] 默认值 0
    soundCtrlLevel: {
      type: 'u8',
      offset: 45,
      default: 0,
    },
    // ch: '声控延迟',[500,10000]  默认值 2000 ms 间隔 500 如果“声控等级”配置为“关闭”，则不支持配置
    soundCtrlDelay: {
      type: 'u16',
      offset: 46,
      default: 2000,
    },
    // 低电压提示,[0,635] 间隔 5 默认120
    powerInfoAlert: {
      type: 'u16',
      offset: 48,
      default: 120,
    },
    // 预设信道, x取值范围 [0,3]
    // 区域 "0xFF:无 0~49:指定已配置的区域ID"
    // 信道 "0xFFFF:无 0~1023：指定已配置信道" 预设信道1的信道ID	如果“区域”配置为“无”，则不支持配置
    preSetChannel: {
      type: 'byteArray',
      subFields: {
        zone: {
          type: 'u8',
          offset: 0,
          default: 0xff,
        },
        // 1字节保留位
        channel: {
          type: 'u16',
          offset: 2,
          default: 0xffff,
        },
      },
      len: 4,
      repeated: 4,
      offset: 50,
    },
    concatSettings: {
      type: 'u8',
      offset: 66,
      bits: {
        // 显示联系人内容 0：联系人别名和ID 1：联系人别名 2：联系人ID
        showContactContent: {
          len: 2,
          offset: 0,
          default: 0,
        },
        // 显示陌生号码
        showStrangeNumber: {
          len: 1,
          bool,
          offset: 2,
          default: false,
        },
        // 降噪功能
        noiseReduction: {
          len: 1,
          bool,
          offset: 3,
          default: false,
        },
        // 待机显示设备名称
        showDeviceNameDisplay: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 自动添加陌生人通讯录
        addStrangeNumberAuto: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
        // 别名显示
        aliasDisplay: {
          len: 1,
          bool,
          offset: 6,
          default: true,
        },
      },
    },
    // 1字节保留位 67
    // ch: 年
    year: {
      type: 'u16',
      offset: 68,
      default: nowDate.getUTCFullYear(),
    },
    // ch: 月
    month: {
      type: 'u8',
      offset: 70,
      default: nowDate.getUTCMonth() + 1,
    },
    // ch: 日
    date: {
      type: 'u8',
      offset: 71,
      default: nowDate.getUTCDate(),
    },
    // ch: 时
    hours: {
      type: 'u8',
      offset: 72,
      default: nowDate.getUTCHours(),
    },
    // ch: 分
    minutes: {
      type: 'u8',
      offset: 73,
      default: nowDate.getUTCMinutes(),
    },
    // ch: 秒
    seconds: {
      type: 'u8',
      offset: 74,
      default: nowDate.getUTCSeconds(),
    },
    // GMT标准 "0：晚 1：早" 比GMT标准时早(为1)或晚(为0）
    gmtStandard: {
      type: 'u8',
      offset: 75,
      default: timezoneOffset < 0 ? 1 : 0,
    },
    // 时钟差数
    hoursDiff: {
      type: 'u8',
      offset: 76,
      default: (Math.abs(timezoneOffset) / 60) | 0,
    },
    // 分钟差数
    minutesDiff: {
      type: 'u8',
      offset: 77,
      default: Math.abs(timezoneOffset) % 60,
    },
    // 时区ID
    timezoneId: {
      type: 'u8',
      offset: 78,
      default: 21,
    },
    // 保留位 79
    // 设备名称 设置对讲机别名，最长32个字符
    deviceAliasName: {
      type: 'stringU16',
      len: 34,
      offset: 80,
      default: '',
    },
    // 保留位 uint8[2] 81+32+2=115
  },
}

export class GeneralSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 116
    this.proto = GeneralSettingsProto[this.version]
  }
}

// UI设置
const UISettingsProto = {
  0: {
    callingSoundSettings: {
      type: 'u8',
      offset: 0,
      bits: {
        // 全部静音,"0:禁用 1:启用",默认0，禁用所有提示音
        muteAll: {
          len: 1,
          bool,
          offset: 0,
          default: false,
        },
        // 呼出提示音 "0:关闭 1:开启 2:模拟允许 3:数字允许"，默认值1 （即原 呼叫允许音）
        callingOutTone: {
          len: 2,
          offset: 1,
          default: 1,
        },
        // 语音结束音，默认值1（即原 信道空闲指示音
        voiceEndTone: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
        // 单呼提示音，默认值1
        singleCallTone: {
          len: 1,
          offset: 4,
          bool,
          default: true,
        },
        // 组呼提示音，默认值1
        groupCallTone: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
        // 呼叫结束音，默认值1
        callingEndTone: {
          len: 1,
          bool,
          offset: 6,
          default: true,
        },
        // 短信提示音，默认值1
        messageTone: {
          len: 1,
          offset: 7,
          bool,
          default: true,
        },
      },
    },
    promptSoundSettings: {
      type: 'u8',
      offset: 1,
      bits: {
        // 呼叫提示提示音，默认值1
        callPrompt: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 优先信道提示音，默认值1
        priorityChannelTone: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 信令侧音，默认值1
        signalingSideTone: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 按键提示音，默认值1
        buttonTone: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
        // 接收方报警提示音，默认值1
        alarmTone: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 低压告警提示音，默认值1
        lowVoltageAlarmTone: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
        // 开关机提示音，默认值1
        poweredTone: {
          len: 1,
          bool,
          offset: 6,
          default: true,
        },
        // 信道播报音，默认值1
        channelBroadcastTone: {
          len: 1,
          offset: 7,
          bool,
          default: true,
        },
      },
    },
    // 呼叫提示次数 "1~20 0：关"
    callPromptTimes: {
      type: 'u8',
      offset: 2,
      default: 0,
    },
    // 选择播音人 51：男声1 52：男声2 3：女声1 53：女声2 55：女童声，参数值为10进制
    chooseAnnouncer: {
      type: 'u8',
      offset: 3,
      default: 3,
    },
    // 呼出提示音:音量 "0:当前音量 1~8:音量等级"
    callingOutToneVolume: {
      type: 'u8',
      offset: 4,
      default: 4,
    },
    // 语音结束音:音量
    voiceEndToneVolume: {
      type: 'u8',
      offset: 5,
      default: 4,
    },
    // 单呼提示音:音量
    singleCallToneVolume: {
      type: 'u8',
      offset: 6,
      default: 4,
    },
    // 组呼提示音:音量
    groupCallToneVolume: {
      type: 'u8',
      offset: 7,
      default: 4,
    },
    // 呼叫结束音:音量
    callingEndToneVolume: {
      type: 'u8',
      offset: 8,
      default: 4,
    },
    // 短信提示音:音量
    messageToneVolume: {
      type: 'u8',
      offset: 9,
      default: 4,
    },
    // 呼叫提示提示音:音量
    callPromptVolume: {
      type: 'u8',
      offset: 10,
      default: 4,
    },
    // 优先信道提示音:音量
    priorityChannelToneVolume: {
      type: 'u8',
      offset: 11,
      default: 4,
    },
    // 信令侧音:音量
    signalingSideToneVolume: {
      type: 'u8',
      offset: 12,
      default: 4,
    },
    // 按键提示音:音量
    buttonToneVolume: {
      type: 'u8',
      offset: 13,
      default: 4,
    },
    // 报警提示音:音量
    alarmToneVolume: {
      type: 'u8',
      offset: 14,
      default: 4,
    },
    // 低压告警提示音:音量
    lowVoltageAlarmToneVolume: {
      type: 'u8',
      offset: 15,
      default: 4,
    },
    // 开关机提示音:音量
    poweredToneVolume: {
      type: 'u8',
      offset: 16,
      default: 4,
    },
    // 网内与网外提示音
    inOutNetworkTone: {
      type: 'u8',
      offset: 17,
      default: true,
      bool,
    },
    // 信道播报音:音量 0:当前音量 1~8:音量等级
    channelBroadcastToneVolume: {
      type: 'u8',
      offset: 18,
      default: 4,
    },
    // 背光灯自动时间,5~60(秒),默认5 当背光灯设置为“自动”时持续亮起的时间，超过该时间将自动熄灭
    autoBackLightTime: {
      type: 'u8',
      offset: 19,
      default: 5,
    },
    // LED相关配置
    ledSettings: {
      type: 'u8',
      offset: 20,
      bits: {
        // 全部所有LED指示灯 0:禁用 1:启用 default 1
        enableAllLED: {
          len: 1,
          bool,
          offset: 0,
          default: true,
        },
        // 发射LED使能
        emittingLed: {
          len: 1,
          bool,
          offset: 1,
          default: true,
        },
        // 接收LED使能
        receiveLed: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 扫描、漫游状态LED使能
        scanRoamStatusLed: {
          len: 1,
          bool,
          offset: 3,
          default: true,
        },
        // 电池低电量LED使能
        lowBatteryLed: {
          len: 1,
          bool,
          offset: 4,
          default: true,
        },
        // 呼叫挂起LED使能
        callHangsLed: {
          len: 1,
          bool,
          offset: 5,
          default: true,
        },
      },
    },
    // 振动相关配置
    vibrationSettings: {
      type: 'u8',
      offset: 21,
      bits: {
        // 背光灯模式 "0:常暗 1:常亮 2:自动"
        backLightMode: {
          len: 2,
          offset: 0,
          default: 2,
        },
        // 振动使能
        vibration: {
          len: 1,
          bool,
          offset: 2,
          default: false,
        },
        // 单呼振动使能
        singleCall: {
          len: 1,
          bool,
          offset: 3,
          default: false,
        },
        // 短信振动使能
        message: {
          len: 1,
          bool,
          offset: 4,
          default: false,
        },
        // 呼叫提示振动使能
        callTone: {
          len: 1,
          bool,
          offset: 5,
          default: false,
        },
      },
    },
    // 隐蔽模式
    stealthMode: {
      type: 'u8',
      offset: 22,
      bool,
      default: false,
    },
    // 隐蔽模式相关配置
    stealthModeSettings: {
      type: 'u8',
      offset: 23,
      bits: {
        // 隐蔽模式屏蔽指示灯
        shieldedLedLight: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 隐蔽模式屏蔽键盘锁
        shieldedKeyboardLock: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // 隐蔽模式背光灯
        stealthModeBacklight: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
        // 隐蔽模式屏蔽振动
        stealthModeVibrationShield: {
          len: 1,
          offset: 3,
          bool,
          default: false,
        },
        // 隐蔽模式屏蔽耳机 "0:禁用 1:启用" 默认0
        shieldedHeadphones: {
          len: 1,
          offset: 4,
          bool,
          default: false,
        },
        // 隐蔽模式屏蔽麦克风 "0:禁用 1:启用" 默认0
        shieldedMicrophone: {
          len: 1,
          offset: 5,
          bool,
          default: false,
        },
      },
    },
    // 自动键盘锁
    autoKeyboardLock: {
      type: 'u8',
      offset: 24,
      bool,
      default: false,
    },
    // 自动键盘锁延迟开启时间 5~60（秒）
    autoKeyboardLockDelayTime: {
      type: 'u8',
      offset: 25,
      interval: 1,
      default: 60,
    },
    // 可选锁键相关配置
    optionalLockSettings: {
      type: 'u8',
      offset: 26,
      bits: {
        // 功能键F1/橙色键
        orangeKey: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 功能键F2/侧键1
        func1Key: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // 功能键F3/侧键2
        func2Key: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
        // 拨号键 P1键
        dialKey: {
          len: 1,
          offset: 3,
          bool,
          default: false,
        },
        // 挂机键 P2键
        onHookKey: {
          len: 1,
          offset: 4,
          bool,
          default: false,
        },
        // PTT
        pttKey: {
          len: 1,
          offset: 5,
          bool,
          default: false,
        },
        // 功能键F4/信道旋钮
        channelKey: {
          len: 1,
          offset: 6,
          bool,
          default: false,
        },
        // 功能键F5/音量旋钮
        volumeKey: {
          len: 1,
          offset: 7,
          bool,
          default: false,
        },
      },
    },
    // 旋钮键相关配置
    knobKeySettings: {
      type: 'u8',
      offset: 27,
      bits: {
        // 旋钮键
        knobKey: {
          len: 1,
          offset: 0,
          bool,
        },
        // 保留位 1b
      },
    },
  },
}

export class UISettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 28
    this.proto = UISettingsProto[this.version]
  }
}

// 按键设置
const KeysSettingsProto = {
  0: {
    // 长按持续时间 500~5000(毫秒) 间隔500 默认1000
    longPressDuration: {
      type: 'u16',
      offset: 0,
      default: 1000,
    },
    // 紧急警报长按退出时间	500~5000(毫秒)	间隔500	默认2000
    longPressExitTime: {
      type: 'u16',
      offset: 2,
      default: 2000,
    },
    // 默认键盘输入 "0：无 1：单键呼叫 2：允许"		默认0
    defaultKeyboardInput: {
      type: 'u8',
      offset: 4,
      default: 0,
    },
    // 默认旋钮功能	"0：仅音量 1：仅信道 2：信道和音量"
    defaultKnobFunc: {
      type: 'u8',
      offset: 5,
      default: 2,
    },
    // 短-长按功能, x取值范围 [0,8], [橙色键,功能1键,功能2键,方向上键,方向下键,确认,返回,拨号,挂机]
    shortLongKeyDefine: {
      type: 'byteArray',
      len: 2,
      repeated: 9,
      offset: 6,
      subFields: {
        short: {
          type: 'u8',
          offset: 0,
          default: 0,
        },
        long: {
          type: 'u8',
          offset: 1,
          default: 0,
        },
      },
    },
    // 单键功能呼叫, x取值范围 [单键呼叫1-5,数字键盘0-9,*，#]
    oneTouchFuncCall: {
      type: 'byteArray',
      len: 8,
      repeated: 17,
      offset: 24,
      subFields: {
        // 呼叫对象ID 不得选择全呼通讯录ID
        callId: {
          type: 'u16',
          offset: 0,
          default: 0xffff,
        },
        // 0：数字制式
        callMode: {
          type: 'u8',
          offset: 2,
          default: 0,
        },
        // 呼叫类型	"0：组呼 1：单呼 2：短信 3：呼叫提示"
        // 当预制短信有配置时，可选择短信选项；
        // 当呼叫对象为组呼时候，显示组呼、短信；
        // 当呼叫对象为单呼时候，显示单呼、短信、呼叫提示;
        callType: {
          type: 'u8',
          offset: 3,
          default: 0xff,
        },
        // 预制短信ID
        msgId: {
          type: 'u8',
          offset: 4,
          default: 0xff,
        },
      },
    },
    // 方向键功能
    directionKeyDefine: {
      type: 'byteArray',
      offset: 160,
      len: 2,
      repeated: 2,
      subFields: {
        short: {
          type: 'u8',
          offset: 0,
          default: 0,
        },
        long: {
          type: 'u8',
          offset: 1,
          default: 0,
        },
      },
    },
  },
}
export const ButtonKeys = generateEnumObject({
  // 未定义软按键功能
  NONE: 0,
  // 紧急警报开
  WARNING_ON: 1,
  // 紧急警报关
  WARNING_OFF: 2,
  // 发射功率高/低
  TRANSMIT_POWER_HIGH_LOW: 3,
  // 静噪级别调整 仅模拟制式下有效
  SQUELCH_LEVEL_ADJUST: 4,
  // 呼叫提示 仅模拟制式下有效
  CALL_TONE: 5,
  // 电池电量指示
  BATTERY_CHANGE: 6,
  // 呼叫记录 仅模拟制式下有效
  CALL_RECORDS: 7,
  // 信道上调
  CH_UP: 8,
  // 信道下调
  CH_DOWN: 9,
  // DTMF键盘
  // DTMF_MENU: 10,
  // GPS上传开关(本项目不支持，需要隐藏该功能)
  // GPS_SWITCH: 11,
  // 常用联系人列表
  // COMMON_CONTACT_LIST: 12,
  // 待机界面
  STANDBY_INTERFACE: 13,
  // 菜单界面
  MENU_INTERFACE: 14,
  // 单独工作开关 仅数字制式下有效
  WORK_ALONE_SWITCH: 15,
  // 倒放开关 仅数字制式下有效
  WORK_DOWN_SWITCH: 16,
  // 通讯录 仅数字制式下有效
  CONTACTS: 17,
  // 短信 仅数字制式下有效
  SMS: 18,
  // 监听
  MONITOR_ON_OFF: 19,
  // 键盘锁
  KEYBOARD_LOCK: 20,
  // 静噪打开 仅模拟制式下有效
  SQUELCH_OPEN: 21,
  // 区域上调
  ZONE_UP: 22,
  // 区域下调
  ZONE_DOWN: 23,
  // 扫描开/关
  SCAN_SWITCH: 24,
  // 声控开关
  VOX_SWITCH: 25,
  // 加密 仅模拟制式下有效
  ENCRYPTION: 26,
  // 脱网开/关
  OFF_NETWORK_ON_OFF: 27,
  // // 遥测按键1 仅数字制式下有效
  // TELEMETRY_BUTTON1: 28,
  // // 遥测按键2
  // TELEMETRY_BUTTON2: 29,
  // // 遥测按键3
  // TELEMETRY_BUTTON3: 30,

  // 单键功能呼叫1 仅数字制式下有效
  SPDCALL1: 31,
  // 单键功能呼叫2 仅数字制式下有效
  SPDCALL2: 32,
  // 单键功能呼叫3 仅数字制式下有效
  SPDCALL3: 33,
  // 单键功能呼叫4 仅数字制式下有效
  SPDCALL4: 34,
  // 单键功能呼叫5 仅数字制式下有效
  SPDCALL5: 35,
  // 手动拨号
  MANUAL_DIAL: 36,
  // 隐蔽模式开/关
  STEALTH_MODE_ON_OFF: 37,
  // 优先打断 仅数字制式下有效
  PRIORITY_INTERRUPT: 38,
  // 预设信道1
  CH_PRESET1: 39,
  // 预设信道2
  CH_PRESET2: 40,
  // 预设信道3
  CH_PRESET3: 41,
  // 预设信道4
  CH_PRESET4: 42,
  // 暂态监听
  TRANSIENT_MONITOR: 43,
  // 暂态静噪打开 仅模拟制式下有效
  TRANSIENT_SQUELCH_OPEN: 44,
  // 无用信道删除
  DEL_USELESS_CH: 45,
  // // 暂态 2-TONE 仅模拟制式下有效
  // TRANSIENT_2_TONE: 46,
  // RFID登记 仅数字制式下有效（需求待定）
  // RFID_REGISTER: 47,
  // 振动设置
  VIBRATION_SETTINGS: 48,
  // 录音开/关
  RECORDING_ON_OFF: 49,
  // GPS开关 仅数字制式下有效
  GPS_ON_OFF: 50,
  // 挂断
  HANG_UP: 51,
  // 对讲机检测 仅数字制式下有效
  DEV_DETECT: 52,
  // 远程监听 仅数字制式下有效
  RMT_MONITOR: 53,
  // 对讲机遥毙 仅数字制式下有效
  DEV_DIE: 54,
  // 对讲机激活 仅数字制式下有效
  DEV_ACTIVE: 55,
  // 站点锁定开关，仅数字制式下有效
  SITE_LOCK_SWITCH: 56,
  // 手动漫游锁定，仅数字制式下有效
  MANUAL_ROAMING_LOCK: 57,
  // 数模转换
  DTA: 58,
  // 电话本
  PHONE_BOOK: 59,
  // 系统状态查询
  SystemStatusQuery: 60,
  // 联网开/关
  NetworkingOnOff: 61,
  // RFID积压查询
  // RFIDQuery: 62,
  // 无源RFID打卡
  // PassiveRFIDRead: 63,
  // 有源RFID打卡
  // ActiveRFIDPRead: 64,
  // 全部静音开/关
  AllMuteOnOff: 65,
  // 背靠背转发模式
  Back2BackForwardingMode: 66,
  // 信道锁
  SK_CH_LOCK: 67,
  // 蓝牙开关
  SK_BT_SWITCH: 68,
  // 降噪功能开关
  SK_DNS_SWITCH: 69,
  // 音量上调
  VOLUME_UP: 70,
  // 音量下调
  VOLUMN_DOWN: 71,

  /* 以下选项紧限于生产调试 */
  // // 误码率测试发射
  // ERRNUMBER_TX: 100,
  // // 误码率测试接收
  // ERRNUMBER_RX: 101,
  // // 老化功能测试开关
  // AGING_FUNC_TEST_ON_OFF: 102,
})
// 单键呼叫类型定义
export const SoftKeyCallType = generateEnumObject({
  GROUP: 0, // 组呼
  SINGLE: 1, // 单呼
  MSG: 2, // 短信
  TIP: 3, // 呼叫提示
})

export class KeysSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 164
    this.proto = KeysSettingsProto[this.version]
  }
}

// 菜单设置
const MenuSettingsProto = {
  0: {
    // ch: '菜单挂起时间',[0,30] 默认值 10s
    menuHangTime: {
      type: 'u8',
      offset: 0,
      default: 10,
    },
    // 设置
    menuEnable: {
      type: 'u8',
      offset: 1,
      bool,
      default: true,
    },
    // 设备
    interfaceSettings: {
      type: 'u8',
      offset: 2,
      bits: {
        // 设备设置 当“设置”菜单未开启时，默认关闭且不可配置
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 以下配置当“设备设置”菜单未开启，默认关闭且不可配置
        // ch: '语言环境',
        locale: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // ch: 'LED指示灯',
        ledIndicator: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // ch: '背光',
        backLight: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
        // 提示音
        tone: {
          len: 1,
          offset: 4,
          bool,
          default: true,
        },
        // ch: '键盘锁',
        keyboardLock: {
          len: 1,
          offset: 5,
          bool,
          default: true,
        },
        // ch: '声控',
        soundCtrl: {
          len: 1,
          offset: 6,
          bool,
          default: true,
        },
        // 振动
        vibration: {
          len: 1,
          offset: 7,
          bool,
          default: true,
        },
      },
    },
    // 设备2
    interface2Settings: {
      type: 'u8',
      offset: 3,
      bits: {
        // 以下配置当“设备设置”菜单未开启，默认关闭且不可配置
        // 倒放
        upsideDown: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 隐蔽模式
        stealthMode: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 单独工作
        aloneWork: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 开机密码 常规设置对应的开机密码未设置，则禁用且关闭
        powerOnPwd: {
          len: 1,
          offset: 3,
          bool,
          default: false,
        },
        // 开机界面
        bootInterface: {
          len: 1,
          offset: 4,
          bool,
          default: true,
        },
        // 语音播报
        voiceBroadcast: {
          len: 1,
          offset: 5,
          bool,
          default: true,
        },
        // 信道锁
        chLockEnable: {
          len: 1,
          offset: 6,
          bool,
          default: true,
        },
        // 降噪功能
        denoiseEnable: {
          len: 1,
          bool,
          offset: 7,
          default: true,
        },
      },
    },
    // 设备3
    interface3Settings: {
      type: 'u8',
      offset: 4,
      bits: {
        // 设置时间和日期
        dateTime: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 待机显示设备名称
        deviceNameDisplay: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 别名显示
        aliasDisplay: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 别名编辑
        aliasEdit: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
      },
    },
    // 信道配置
    channelSettings: {
      type: 'u8',
      offset: 5,
      bits: {
        // 信道设置 开关, 当“设置”菜单未开启时，默认关闭且不可配置
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // ch: '信道名称',
        channelName: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // ch: '发射限时',
        emissionLimit: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 功率级别
        powerLevel: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
        // ch: '发射频率',
        txFrequency: {
          len: 1,
          offset: 4,
          bool,
          default: true,
        },
        // ch: '接收频率',
        rxFrequency: {
          len: 1,
          offset: 5,
          bool,
          default: true,
        },
        // 脱网
        offline: {
          len: 1,
          offset: 6,
          bool,
          default: true,
        },
        // 静噪等级
        squelchLevel: {
          len: 1,
          offset: 7,
          bool,
          default: true,
        },
      },
    },
    // 信道配置
    channel2Settings: {
      type: 'u8',
      offset: 6,
      bits: {
        // 当“信道设置”菜单未开启，默认关闭且不可配置
        // 扰频
        scramble: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 亚音频
        subAudio: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // ch: '彩色码',
        colorCode: {
          len: 1,
          bool,
          offset: 2,
          default: true,
        },
        // 中继台时隙
        repeaterTimeSlot: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
        // ch: '发射联系人',
        launchContact: {
          len: 1,
          offset: 4,
          bool,
          default: false,
        },
        // 接收组列表
        rxGroupList: {
          len: 1,
          offset: 5,
          bool,
          default: false,
        },
      },
    },
    // 设备信息
    deviceInfoSettings: {
      type: 'u8',
      offset: 7,
      bits: {
        // 设备信息 当“设置”菜单未开启时，默认关闭且不可配置
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 当“设备信息”菜单未开启，默认关闭且不可配置
        // 电池信息
        batteryInfo: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 本机号码
        localNumber: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 本机名称
        localName: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
        // 固件版本
        firmwareVersion: {
          len: 1,
          offset: 4,
          bool,
          default: true,
        },
        // CP版本
        cpVersion: {
          len: 1,
          offset: 5,
          bool,
          default: true,
        },
        // UI版本
        uiVersion: {
          len: 1,
          offset: 6,
          bool,
          default: true,
        },
        // 显示蓝牙信息
        bluetoothInfo: {
          len: 1,
          offset: 7,
          bool,
          default: true,
        },
      },
    },
    // 设备信息2
    deviceInfo2Settings: {
      type: 'u8',
      offset: 8,
      bits: {
        // 当“设备信息”菜单未开启，默认关闭且不可配置
        // 显示场强信息
        fieldStrengthInfo: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 内存信息
        memoryInfo: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
      },
    },
    // 区域
    zone: {
      type: 'u8',
      offset: 9,
      bool,
      default: true,
    },
    // 扫描配置
    scanSettings: {
      type: 'u8',
      offset: 10,
      bits: {
        // 扫描启用配置
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 当“扫描”菜单未开启，默认关闭且不可配置
        // 扫描开关
        scanSwitch: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 扫描编辑
        scanEdit: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
      },
    },
    // 漫游配置
    roamSettings: {
      type: 'u8',
      offset: 11,
      bits: {
        // 漫游启用配置
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 当“漫游”菜单未开启，默认关闭且不可配置
        // 站点锁定/解锁
        siteLockUnlock: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // 手动站点漫游
        manualSiteRoam: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
      },
    },
    // 附件
    annex: {
      type: 'u8',
      offset: 12,
      bool,
      default: true,
    },
    // 卫星定位
    gpsSettings: {
      type: 'u8',
      offset: 13,
      bits: {
        // GPS 卫星定位启用配置 当“附件”菜单未开启，默认关闭且不可配置
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 当“GPS”菜单未开启，默认关闭且不可配置
        // GPS开关
        gpsSwitch: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // GPS位置信息
        gpsLocationInfo: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
        // 卫星定位上传
        // 本项目不使用，隐藏该配置项
        // gpsUpload: {
        //   len: 1,
        //   offset: 3,
        //   bool,
        //   default: false,
        // },
      },
    },
    // 巡更配置
    patrolSettings: {
      type: 'u8',
      offset: 14,
      bits: {
        // 巡更启用配置 当“附件”菜单未开启，默认关闭且不可配置
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 当“巡更”菜单未开启，默认关闭且不可配置
        // 巡更扫卡
        patrolSweepCard: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // 巡更记录
        patrolRecord: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
        // 巡更上报
        patrolReport: {
          len: 1,
          offset: 3,
          bool,
          default: false,
        },
      },
    },
    // 录音配置
    recordSettings: {
      type: 'u8',
      offset: 15,
      bits: {
        // 录音启用配置 当“附件”菜单未开启，默认关闭且不可配置
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 当“录音”菜单未开启，默认关闭且不可配置
        // 录音设置
        recordSetting: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // 录音列表
        recordList: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
        // 快捷查找
        recordQuickSearch: {
          len: 1,
          offset: 3,
          bool,
          default: false,
        },
      },
    },
    // 蓝牙配置
    bluetoothSettings: {
      type: 'u8',
      offset: 16,
      bits: {
        // 当“附件”菜单未开启，默认关闭且不可配置
        // 显示蓝牙
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 当“蓝牙”菜单未开启，默认关闭且不可配置
        // 显示蓝牙模式
        showBluetooth: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // 显示蓝牙连接管理
        showBluetoothConnect: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
      },
    },
    // 通讯录
    contacts: {
      type: 'u8',
      offset: 17,
      bool,
      default: true,
    },
    // 常用联系人 当“通讯录”菜单未开启，默认关闭且不可配置 (本项目不使用，隐藏该配置项)
    // favoriteContacts: {
    //   type: 'u8',
    //   offset: 18,
    //   bool,
    //   default: false,
    // },
    // offset: 19
    contactGroup: {
      type: 'u8',
      offset: 19,
      bits: {
        // 联系人分组 新增分组 共占用2bit (本项目不使用，隐藏该配置项)
        // 新建组呼联系人
        newGroupContact: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
      },
    },
    // 联系人列表 当“通讯录”菜单未开启，默认关闭且不可配置
    contactList: {
      type: 'u8',
      offset: 20,
      bool,
      default: true,
    },
    // 联系人配置
    contactSettings: {
      type: 'u8',
      offset: 21,
      bits: {
        // 当“联系人列表”菜单未开启，默认关闭且不可配置
        // 单呼联系人
        singleCall: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 组呼联系人
        groupCall: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 全呼联系人
        fullCall: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 查看联系人
        lookOver: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
        // 编辑联系人
        edit: {
          len: 1,
          offset: 4,
          bool,
          default: true,
        },
        // 删除联系人
        delete: {
          len: 1,
          offset: 5,
          bool,
          default: true,
        },
      },
    },
    // 当“通讯录”菜单未开启，默认关闭且不可配置
    // 新建联系人
    newContact: {
      type: 'u8',
      offset: 22,
      bool,
      default: true,
    },
    // 拨号配置
    dialSettings: {
      type: 'u8',
      offset: 23,
      bits: {
        // 手动拨号 当“通讯录”菜单未开启，默认关闭且不可配置
        manualDial: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 当“手动拨号”菜单未开启，默认关闭且不可配置
        // 单呼手动拨号
        singleCallDial: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 组呼手动拨号
        groupCallDial: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
      },
    },
    // 控制业务
    controlSettings: {
      type: 'u8',
      offset: 24,
      bits: {
        // 控制业务开关 当“通讯录”菜单未开启，默认关闭且不可配置
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 当“控制业务”菜单未开启，默认关闭且不可配置
        // 对讲机检测
        deviceDetect: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // 呼叫提示
        callPrompt: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 远程监听
        remoteMonitor: {
          len: 1,
          offset: 3,
          bool,
          default: false,
        },
        // 遥毙
        remoteKill: {
          len: 1,
          offset: 4,
          bool,
          default: false,
        },
        // 遥活
        remoteLive: {
          len: 1,
          offset: 5,
          bool,
          default: false,
        },
        // 背靠背转发模式
        back2BackMode: {
          len: 1,
          offset: 6,
          bool,
          default: false,
        },
      },
    },
    // 短信
    smsSettings: {
      type: 'u8',
      offset: 25,
      bits: {
        // 短信开关
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 当“短信”菜单未开启，默认关闭且不可配置
        // 新建短信
        newSms: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 预制短信
        preMadeSms: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 收件箱
        inbox: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
        // 发件箱
        outbox: {
          len: 1,
          offset: 4,
          bool,
          default: true,
        },
        // 草稿箱
        draftBox: {
          len: 1,
          offset: 5,
          bool,
          default: true,
        },
        // 系统信息
        systemInfo: {
          len: 1,
          offset: 6,
          bool,
          default: true,
        },
      },
    },
    // 呼叫记录
    callRecordSettings: {
      type: 'u8',
      offset: 26,
      bits: {
        // 呼叫记录
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 当“呼叫记录”菜单未开启，默认关闭且不可配置
        // 已拨呼叫
        dialedCall: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 已接呼叫
        receivedCall: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 未接呼叫
        missedCall: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
      },
    },
    // 两音联系人,两音联系人列表,五音联系人,五音联系人列表 共占用4字节，未启用
    // 加密
    encrypt: {
      type: 'u8',
      offset: 31,
      bool,
      default: true,
    },
    // 加密配置
    encryptSettings: {
      type: 'u8',
      offset: 32,
      bits: {
        // 当“加密”菜单未开启，默认关闭且不可配置
        // 密钥开关
        keyEnable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 新建秘钥
        newKey: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 加密列表
        keyList: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
      },
    },
    // 电话本
    phoneBookSettings: {
      type: 'u8',
      offset: 33,
      bits: {
        // 电话本
        enable: {
          len: 1,
          offset: 0,
          bool,
          default: true,
        },
        // 当“电话本”菜单未开启，默认关闭且不可配置
        // 所有联系人
        allContacts: {
          len: 1,
          offset: 1,
          bool,
          default: true,
        },
        // 查看联系人
        lookOverContact: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 编辑联系人
        editContact: {
          len: 1,
          offset: 3,
          bool,
          default: true,
        },
        // 新建联系人
        newContact: {
          len: 1,
          offset: 4,
          bool,
          default: true,
        },
        // 删除联系人
        deleteContact: {
          len: 1,
          offset: 5,
          bool,
          default: true,
        },
        // 电话拨号
        phoneDialer: {
          len: 1,
          offset: 6,
          bool,
          default: true,
        },
      },
    },
    // 保留位 1B
  },
}

export class MenuSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 36
    this.proto = MenuSettingsProto[this.version]
  }
}

// GPS设置
const GpsSettingsProto = {
  0: {
    mode: {
      type: 'u8',
      offset: 0,
      bits: {
        // GPS开关 "0:关 1:省电 2:高性能"
        enable: {
          len: 4,
          offset: 0,
          default: 0,
        },
        // GPS模块波特率 0：9600 1：115200
        baudRate: {
          offset: 4,
          len: 4,
          default: 0,
        },
      },
    },
    // 导航模式 隐藏 offset： 1
    // 时区设置 隐藏 offset： 2
    // 速度单位 隐藏 offset： 3
    // GPS更新时间 隐藏 offset： 4
    // gps设置 隐藏 offset：5
    // PTT次数.0-255次.
    pttTimes: {
      type: 'u8',
      offset: 6,
      default: 10,
    },
    // 连接次数. 0-255次.
    connectionTimes: {
      type: 'u8',
      offset: 7,
      default: 8,
    },
    // 上传距离间隔 offset：8 u16
    // 上传间隔时间:日 offset：10
    // 上传间隔时间:时 offset：11
    // 上传间隔时间:分 offset：12
    // 上传间隔时间:秒 offset：13
    // 查询命令,需包含截止符,默认为空,不允许查询
    queryCmd: {
      type: 'string',
      len: 25,
      offset: 14,
      default: '',
    },
    // 数据压缩 offset：39
    // 控制中心ID. 0x0000001~0x0FFFCDF
    centerId: {
      type: 'u32',
      offset: 40,
      default: 1,
    },
    // gps选择
    gpsSettings: {
      type: 'u8',
      offset: 44,
      bits: {
        // gps
        gps: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 北斗
        beidou: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // GLONASS
        glonass: {
          len: 1,
          offset: 2,
          bool,
          default: false,
        },
        // Galileo
        galileo: {
          len: 1,
          offset: 3,
          bool,
          default: false,
        },
      },
    },
    // 保留位 3 u8
  },
}

export class GpsSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 48
    this.proto = GpsSettingsProto[this.version]
  }
}

// 倒放设置 设置对讲机是否开启倒放功能。该功能可以在对讲机倾斜至一定的角度或者长时间保持静止状态后，自动开启报警。
const UpsideDownSettingsProto = {
  0: {
    // 倒放开关 "0：禁用 1：启用"
    enable: {
      type: 'u8',
      offset: 0,
      bool,
      default: false,
    },
    // 触发方式 "0：仅倾斜 1：仅静止 2：倾斜或静止"
    triggerMode: {
      type: 'u8',
      offset: 1,
      default: 0,
    },
    // 触发倾斜度 "0:30度 1:45度 2:60度"
    triggerTilt: {
      type: 'u8',
      offset: 2,
      default: 1,
    },
    // 进入延时 单位 秒  [0,255]  默认值 10s
    entryDelay: {
      type: 'u8',
      offset: 3,
      default: 10,
    },
    // 退出延时 单位 秒  [0,254]  0xFF:无限，即不会退出紧急报警模式，持续时间无限长。  默认值 10s
    quitDelay: {
      type: 'u8',
      offset: 4,
      default: 10,
    },
    // 倒放预提示时间  0~69（秒） 默认值 5秒" 预提示时间小于等于进入延迟时间
    preHintTime: {
      type: 'u8',
      offset: 5,
      default: 5,
    },
    // 保留2字节
  },
}

export class UpsideDownSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 8
    this.proto = UpsideDownSettingsProto[this.version]
  }
}

// 单独工作
const AloneWorkSettingsProto = {
  0: {
    // 单独工作开关 以下参数当此使能=0时不可用  默认值 0
    enable: {
      type: 'u8',
      offset: 0,
      bool,
      default: false,
    },
    // 单独工作响应时间  [1,255]分钟 默认10
    responseTime: {
      type: 'u8',
      offset: 1,
      default: 10,
    },
    // 单独工作提醒时间  [0,255]分钟 默认10
    remindTime: {
      type: 'u8',
      offset: 2,
      default: 10,
    },
    // 单独工作响应操作 0 按钮  1 语音发射
    responseAction: {
      type: 'u8',
      offset: 3,
      default: 0,
    },
  },
}

export class AloneWorkSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 4
    this.proto = AloneWorkSettingsProto[this.version]
  }
}

// 数字警报
const DigitalAlarmSettingsProto = {
  0: {
    // 报警ID "0xFF:表示无效ID 0~3:表示数字警报ID"
    id: {
      type: 'u8',
      offset: 0,
      default: 0,
    },
    // 警报类型 "0:禁止 1:仅鸣笛 2:常规 3:静默 4:静默加语音 5:警报鸣笛"
    type: {
      type: 'u8',
      offset: 1,
      default: 0,
    },
    // 警报模式 "0:紧急警报 1:紧急警报和呼叫 2:仅紧急呼叫"
    mode: {
      type: 'u8',
      offset: 2,
      default: 0,
    },
    // 自动紧急呼叫
    autoEmergencyCall: {
      type: 'u8',
      offset: 3,
      bool,
      default: false,
    },
    // 紧急呼叫次数 0~255
    emergencyCallTimes: {
      type: 'u8',
      offset: 4,
      default: 1,
    },
    // 接收持续时间(秒) 10~120 间隔10 默认10
    receiveDuration: {
      type: 'u8',
      offset: 5,
      default: 10,
    },
    // 麦克风激活时间(秒)10~120 默认10
    micActiveDuration: {
      type: 'u8',
      offset: 6,
      default: 10,
    },
    // 不礼貌重试 [1,15] 默认15
    impoliteRetry: {
      type: 'u8',
      offset: 7,
      default: 15,
    },
    // 礼貌重试 [0,14,15]  15: 无限 默认5
    politeRetry: {
      type: 'u8',
      offset: 8,
      default: 5,
    },
    // 本地紧急鸣笛
    localEmergencyHonk: {
      type: 'u8',
      offset: 9,
      bool,
      default: false,
    },
    // 保留位 u8 * 2 offset: 10
    // 回复信道
    replyChannel: {
      type: 'u16',
      offset: 12,
      default: 0xfffe,
    },
    // 紧急报警名称
    name: {
      type: 'stringU16',
      offset: 14,
      len: 34,
      default: '',
    },
    // 报警优先打断 1B (本项目不使用，隐藏该配置项)
    // 保留位 3B
  },
}

export class DigitalAlarmSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 52
    this.proto = DigitalAlarmSettingsProto[this.version]
  }
}

// 模拟紧急警报
const AnalogAlarmSettingsProto = {
  0: {
    // 报警ID "0xFF:表示无效ID 0~3:表示数字警报ID"
    id: {
      type: 'u8',
      offset: 0,
      default: 0,
    },
    // 警报类型 "0:禁止 1:仅鸣笛 2:常规 3:静默 4:静默加语音 5:警报鸣笛"
    type: {
      type: 'u8',
      offset: 1,
      default: 0,
    },
    // 警报模式 "0:紧急警报 1:紧急警报和呼叫 2:仅紧急呼叫"
    mode: {
      type: 'u8',
      offset: 2,
      default: 0,
    },
    // 自动紧急呼叫
    autoEmergencyCall: {
      type: 'u8',
      offset: 3,
      bool,
      default: false,
    },
    // 紧急呼叫次数 0~255
    emergencyCallTimes: {
      type: 'u8',
      offset: 4,
      default: 1,
    },
    // 接收持续时间(秒) 10~120 间隔10 默认10
    receiveDuration: {
      type: 'u8',
      offset: 5,
      default: 10,
    },
    // 麦克风激活时间(秒)10~120 默认10
    micActiveDuration: {
      type: 'u8',
      offset: 6,
      default: 10,
    },
    // 不礼貌重试 [1,15] 默认15
    impoliteRetry: {
      type: 'u8',
      offset: 7,
      default: 15,
    },
    // 礼貌重试 [0,14,15]  15: 无限 默认5
    politeRetry: {
      type: 'u8',
      offset: 8,
      default: 5,
    },
    // 保留位 3B
    // 回复信道
    replyChannel: {
      type: 'u16',
      offset: 12,
      default: 0xfffe,
    },
    // 紧急报警名称
    name: {
      type: 'stringU16',
      len: 34,
      offset: 14,
      default: '',
    },
    // 警报信令类型 "0:无 1:五音"
    signalingType: {
      type: 'u8',
      offset: 48,
      default: 0,
    },
    // 紧急警报报文 0~31 默认0xFF
    emergencyAlertMessage: {
      type: 'u8',
      offset: 49,
      default: 0xff,
    },
    // 紧急警报持续时间 1~255 默认10
    emergencyAlertDuration: {
      type: 'u8',
      offset: 50,
      default: 10,
    },
    // 紧急警报次数 "1~244 255:无限制" 默认1
    emergencyAlertTimes: {
      type: 'u8',
      offset: 51,
      default: 1,
    },
    // 本地紧急警报
    localEmergencyAlert: {
      type: 'u8',
      offset: 52,
      bool,
      default: false,
    },
    // 报警静噪模式 "0：载波 1：亚音"
    alarmSquelchMode: {
      type: 'u8',
      offset: 53,
      default: 0,
    },
    // 背景提示音
    backgroundPromptTone: {
      type: 'u8',
      offset: 54,
      bool,
      default: false,
    },
    // 保留位 1B
  },
}

export class AnalogAlarmSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 56
    this.proto = AnalogAlarmSettingsProto[this.version]
  }
}

// 扫描配置
const ScanConfigSettingsProto = {
  0: {
    // 保留位 2B
    // 扫描采样时间 100~1000(毫秒) 间隔100 默认100
    scanSamplingTime: {
      type: 'u16',
      offset: 2,
      default: 100,
    },
  },
}

export class ScanConfigSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 4
    this.proto = ScanConfigSettingsProto[this.version]
  }
}

// 扫描列表
const ScanListSettingsProto = {
  0: {
    // 扫描列表ID 0~31
    scanId: {
      type: 'u8',
      offset: 0,
      default: 0,
    },
    // 应答 "0：不应答 1：应答"
    reply: {
      type: 'u8',
      offset: 1,
      bool,
      default: true,
    },
    // 扫描挂起时间 500~10000(毫秒) 间隔500 默认4000
    scanHangTime: {
      type: 'u16',
      offset: 2,
      default: 4000,
    },
    // 第一优先信道 "0xFFFF：无，即不配置 0xFFFE：选定的 0~1023：指定的已配置信道" 默认0xFFFF
    priority1Ch: {
      type: 'u16',
      offset: 4,
      default: 0xffff,
    },
    // 第二优先信道 "0xFFFF：无，即不配置 0xFFFE：选定的 0~1023：指定的已配置信道" 默认0xFFFF
    priority2Ch: {
      type: 'u16',
      offset: 6,
      default: 0xffff,
    },
    // 指定的发送信道
    appointTxCh: {
      type: 'u16',
      offset: 8,
      default: 0xfffe,
    },
    // 信道列表 "0xFFFF:无 0xFFFE:选定的信道 0~1023：指定已配置信道"
    membersList: {
      type: 'u16',
      offset: 10,
      repeated: 16,
      default: [0xfffe],
    },
    // 名称
    name: {
      type: 'stringU16',
      len: 34,
      offset: 42,
      default: '',
    },
    // 信道数量
    memberCount: {
      type: 'u8',
      offset: 76,
      default: 1,
    },
    // 保留位 3B
  },
}

export class ScanListSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 80
    this.proto = ScanListSettingsProto[this.version]
  }
}

// 漫游配置
const RoamConfigSettingsProto = {
  0: {
    // 活动站点搜索
    activeSiteSearch: {
      type: 'u8',
      offset: 0,
      bool,
      default: false,
    },
    // 活动站点搜索计时 0~255（秒）默认0 步进1
    activeSiteSearchTiming: {
      type: 'u8',
      offset: 1,
      default: 0,
    },
    // RSSI检测周期	30~120（秒） 间隔5 默认60
    rssiDetectCycle: {
      type: 'u8',
      offset: 2,
      // interval: 5,
      default: 60,
    },
    /* // 自动漫游搜索时间 10~60（秒）默认10
    autoRoamSearchTime: {
      type: 'u8',
      offset: 3,
      default: 10,
    }, */
    // 自动漫游搜索间隔时间	10~60（秒）默认30
    autoRoamingSearchInterval: {
      type: 'u8',
      offset: 4,
      default: 30,
    },
    // 保留位 3B
  },
}

export class RoamConfigSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 8
    this.proto = RoamConfigSettingsProto[this.version]
  }
}

// 漫游列表
const RoamListSettingsProto = {
  0: {
    // 漫游ID 0~31:漫游列表ID
    roamId: {
      type: 'u8',
      offset: 0,
      default: 0,
    },
    // RSSI阀值(dBm) 0~40 信号强度阈值是用户设置接收信号强度的门限。对应显示范围 -120~-80
    rssi: {
      type: 'u8',
      offset: 1,
      default: 0,
    },
    // 信道个数 1~16
    roamChCount: {
      type: 'u8',
      offset: 2,
      default: 1,
    },
    // 保留位 1B
    // 信道列表 最大信道数: 16 "0xFFFF:无 0xFFFE:选定的信道 0~1023：指定已配置信道" 默认0xFFFE
    roamChList: {
      type: 'u16',
      repeated: 16,
      offset: 4,
      default: [0xfffe],
    },
    // 名称
    name: {
      type: 'stringU16',
      len: 34,
      offset: 36,
      default: '',
    },
    // 保留位 2B
  },
}

export class RoamListSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 72
    this.proto = RoamListSettingsProto[this.version]
  }
}

// DMR基础配置
const DmrConfigSettingsProto = {
  0: {
    // 设备ID 0x0000001~0x0FFFCDF 默认0x0000001 界面显示取值范围为1~16776415，步进1，默认值1
    dmrId: {
      type: 'u32',
      offset: 0,
      default: 1,
    },
    // 中继ID, [1,16777215]
    repeaterId: {
      type: 'u32',
      offset: 4,
      default: 16777215,
    },
    // 发射前导码持续时间(毫秒) [0,8640] 默认值 960 ms 间隔 60
    sendLeadCodeTime: {
      type: 'u16',
      offset: 8,
      default: 960,
    },
    // 脱网组呼挂起时间 [0,7000] 默认值 2000 ms  间隔 500
    offlineGroupCallHungTime: {
      type: 'u16',
      offset: 10,
      default: 2000,
    },
    // 脱网单呼挂起时间 [0,7000]  默认值 2000 ms  间隔 500
    offlineSingleCallHungTime: {
      type: 'u16',
      offset: 12,
      default: 2000,
    },
    // 远程监听持续时间(秒) 10~120 间隔10 默认10
    channelCount: {
      type: 'u8',
      offset: 14,
      default: 10,
    },
    // 优先打断、直通模式等配置
    baseSettings: {
      type: 'u8',
      offset: 15,
      bits: {
        // 拒绝陌生人呼叫
        rejectStrangerCall: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 直通模式
        directMode: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
      },
    },
    // 呼叫优先等级 1B (该配置项调整到信道配置中，这里不使用，需要隐藏)
    // 保留位 3B
    // 解码配置
    decodeSettings: {
      type: 'u8',
      offset: 20,
      bits: {
        // 遥毙/激活解码
        remoteDeathDecode: {
          len: 1,
          offset: 0,
          bool,
          default: false,
        },
        // 远程监听解码
        remoteMonitorDecode: {
          len: 1,
          offset: 1,
          bool,
          default: false,
        },
        // 紧急警报下远程监听解码
        underAlarmRemoteMonitorDecode: {
          len: 1,
          offset: 2,
          bool,
          default: true,
        },
        // 呼叫提示解码
        callToneDecode: {
          len: 1,
          offset: 3,
          bool,
          default: false,
        },
        // 对讲机检测解码
        deviceDetectDecode: {
          len: 1,
          offset: 4,
          bool,
          default: false,
        },
        // 遥毙/激活鉴权
        remoteDeathActiveAuth: {
          len: 1,
          offset: 5,
          bool,
          default: false,
        },
        // 远程监听鉴权
        remoteMonitorAuth: {
          len: 1,
          offset: 6,
          bool,
          default: false,
        },
      },
    },
    // 保留位 3B
    // 空中鉴权密钥 每个字符均可为0～F
    airAuthKey: {
      type: 'string',
      offset: 24,
      len: 16,
      default: '',
    },
  },
}

export class DmrConfigSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 40
    this.proto = DmrConfigSettingsProto[this.version]
  }
}

// 加密基础配置
const EncryptionConfigSettingsProto = {
  0: {
    // 加密使能 是否开启加密
    enable: {
      type: 'u8',
      offset: 0,
      bool,
      default: false,
    },
    // 保留位 3B
  },
}

export class EncryptionConfigSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 4
    this.proto = EncryptionConfigSettingsProto[this.version]
  }
}

// 加密列表配置
const EncryptionListSettingsProto = {
  0: {
    // 密钥ID "0xFF：无效 0~31：密钥ID"
    keyId: {
      type: 'u8',
      offset: 0,
      default: 0xff,
    },
    // 密钥编码ID	1~255
    encodeId: {
      type: 'u8',
      offset: 1,
      default: 0xff,
    },
    // 密钥值 10B+1B 设置具体的密钥值，长度受密钥长度参数控制。 默认值:0591AH287X
    keyValue: {
      type: 'string',
      offset: 2,
      len: 10,
      default: '0591AH287X',
    },
    // 保留位 3B
    // 密钥名称 默认值:加密密钥1
    keyName: {
      type: 'stringU16',
      offset: 16,
      len: 34,
      default: '',
    },
    // 保留位 2B
  },
}

export class EncryptionListSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 52
    this.proto = EncryptionListSettingsProto[this.version]
  }
}

// 高级加密ARC4列表
const EncryptionARC4ListProto = {
  0: {
    // 密钥ID "0xFF：无效 0~31：密钥ID"
    keyId: {
      type: 'u8',
      offset: 0,
      default: 0xff,
    },
    // 密钥编码ID	1~255
    encodeId: {
      type: 'u8',
      offset: 1,
      default: 0xff,
    },
    // 密钥值 5B+1B 设置具体的密钥值，长度受密钥长度参数控制。采用BCD码显示
    keyValue: {
      type: 'bcd',
      offset: 2,
      len: 5,
      default: '',
    },
    // 密钥名称 默认值:加密密钥1
    keyName: {
      type: 'stringU16',
      offset: 8,
      len: 34,
      default: '',
    },
    // 保留位 2B
  },
}

export class EncryptionARC4List extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 44
    this.proto = EncryptionARC4ListProto[this.version]
  }
}

// 高级加密ARS列表
const EncryptionARSListProto = {
  0: {
    // 密钥ID "0xFF：无效 0~31：密钥ID"
    keyId: {
      type: 'u8',
      offset: 0,
      default: 0xff,
    },
    // 密钥编码ID	1~255
    encodeId: {
      type: 'u8',
      offset: 1,
      default: 0xff,
    },
    // 密钥值 32B+1B 设置具体的密钥值，长度受密钥长度参数控制。采用BCD码显示
    keyValue: {
      type: 'bcd',
      offset: 2,
      len: 32,
      default: '',
    },
    // 保留位 1B
    // 密钥名称 默认值:加密密钥1
    keyName: {
      type: 'stringU16',
      offset: 36,
      len: 34,
      default: '',
    },
    // 保留位 2B
  },
}

export class EncryptionARSList extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 72
    this.proto = EncryptionARSListProto[this.version]
  }
}

// DMR联系人
const DMRContactsProto = {
  0: {
    // 通讯录 ID
    id: {
      type: 'u16',
      offset: 0,
      default: 0,
    },
    // 名称排序ID
    sortId: {
      type: 'u16',
      offset: 2,
      default: 0,
    },
    // ch: '号码',
    number: {
      type: 'u32',
      offset: 4,
      default: 1,
    },
    // ch: '名称',
    name: {
      type: 'stringU16',
      len: 34,
      offset: 8,
      default: '',
    },
    // 该联系人是否正被使用
    isUsed: {
      type: 'u8',
      offset: 42,
      bool,
      default: true,
    },
    // 该联系人不允许编辑使能
    noEditingAllowed: {
      type: 'u8',
      offset: 43,
      bool,
      default: false,
    },
  },
}
export const CallType = generateEnumObject({
  // 0：无类型
  NONE: 0,
  // 1：组呼
  GROUP: 1,
  // 2：单呼
  SINGLE: 2,
  // 3：全呼
  BROADCAST: 3,
})

export class DMRContactsSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 44
    this.proto = DMRContactsProto[this.version]
  }
}

// 接收组列表
const ReceiveGroupProto = {
  0: {
    // 接收组列表ID
    groupId: {
      type: 'u16',
      offset: 0,
      default: 0,
    },
    // 保留位 3B
    // 接收组ID个数 0~15
    count: {
      type: 'u8',
      offset: 5,
      default: 0,
    },
    // 接收组列表 32B，单个通讯录ID为2B,最大为16个成员
    listenGroup: {
      type: 'u16',
      repeated: 16,
      offset: 6,
      default: [],
    },
    // 分组名称 支持最长16个unicode字符
    groupName: {
      type: 'stringU16',
      len: 34,
      offset: 38,
      default: '',
    },
  },
}

export class ReceiveGroupSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 72
    this.proto = ReceiveGroupProto[this.version]
  }
}

// 预制短信
const PreMakeSmsProto = {
  0: {
    // 预制短信ID
    msgId: {
      type: 'u8',
      offset: 0,
      default: 0,
    },
    // 保留位 1B
    // 短信长度
    length: {
      type: 'u16',
      offset: 2,
      default: 0,
    },
    // 预制短信内容
    msgContent: {
      type: 'stringU16',
      offset: 4,
      len: 512,
      default: '',
    },
  },
}

export class PreMakeSmsSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 516
    this.proto = PreMakeSmsProto[this.version]
  }
}

// 电话本
const PhoneBookProto = {
  0: {
    // 电话本ID
    phoneId: {
      type: 'u16',
      offset: 0,
      default: 0,
    },
    // 名称排序ID
    sortId: {
      type: 'u16',
      offset: 2,
      default: 0,
    },
    // 电话ID 以BCD码形式保存，如135 0933 2766，保存为0xFF 0xFF 0xF1 0x35 0x09 0x33 0x27 0x66
    phoneNo: {
      type: 'bcd',
      len: 8,
      offset: 4,
      default: 0,
    },
    // 名称
    phoneName: {
      type: 'stringU16',
      offset: 12,
      len: 34,
      default: '',
    },
    // 保留位 2B
  },
}

export class PhoneBookSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 48
    this.proto = PhoneBookProto[this.version]
  }
}

// 巡逻系统
const PatrolSystemProto = {
  0: {
    // 应答超时 2500~10000ms 间隔 500 默认 5000
    responseTimeout: {
      type: 'u32',
      offset: 0,
      default: 5000,
    },
    /*// 时钟差数
    hourDiff: {
      type: 'u8',
      offset: 4,
      default: 0,
    },
    // 分钟差数
    minuteDiff: {
      type: 'u8',
      offset: 5,
      default: 0,
    },
    // 时区ID 0~97
    timezoneId: {
      type: 'u8',
      offset: 6,
      default: 8,
    },
    // GMT标准 "0：晚 1：早" 比GMT标准时早(为1)或晚(为0）
    gmtStandard: {
      type: 'u8',
      offset: 7,
      default: 0,
    },*/
    // 紧急警报 "0：关 1：开"
    emergencyAlarm: {
      type: 'u8',
      offset: 8,
      bool,
      default: true,
    },
    // 报警类型 "0:常规 1:静默"
    alarmType: {
      type: 'u8',
      offset: 9,
      default: 0,
    },
    // 呼叫联系人 通讯录ID
    callContact: {
      type: 'u16',
      offset: 10,
      default: 0xfffe,
    },
    // 发送次数 0~14次数,0xff:不停
    sendCount: {
      type: 'u8',
      offset: 12,
      default: 3,
    },
    // 警报后自动开启监听 0~99（秒）默认 20
    autoOpenMonitor: {
      type: 'u8',
      offset: 13,
      default: 20,
    },
    // 警报后自动开启跟踪定位时间 0~99（秒）默认 30
    autoOpenLocateTrack: {
      type: 'u8',
      offset: 14,
      default: 30,
    },
    // 跟踪监控使能 "0：关 1：开"
    enableMonitorTrack: {
      type: 'u8',
      offset: 15,
      bool,
      default: true,
    },
    // 跟踪间隔 0~995（秒）间隔	5 默认 300
    trackInterval: {
      type: 'u16',
      offset: 16,
      // interval: 5,
      default: 300,
    },
    // 最小距离 0~495（秒）间隔	5 默认 50
    minDistance: {
      type: 'u16',
      offset: 18,
      // interval: 5,
      default: 50,
    },
    rfidSettings: {
      type: 'u8',
      offset: 20,
      bits: {
        // 有源RFID使能
        enable: {
          offset: 0,
          len: 1,
          bool,
          default: false,
        },
        // 有源RFID模式 "0：到位自动读卡 1：按键触发读卡"
        mode: {
          offset: 1,
          len: 1,
          default: 0,
        },
        // 功率 "0(-18dbm) 1(-12dbm)  2(-6dbm)  3(0dbm)"
        power: {
          offset: 2,
          len: 2,
          default: 0,
        },
        // 应答 "0：数传指令应答 1：采用芯片自动应答机制"
        reply: {
          offset: 4,
          len: 1,
          default: 0,
        },
      },
    },
    // 信道 2~125
    channel: {
      type: 'u8',
      offset: 21,
      default: 2,
    },
    // 保留位 2B
    // 数据通道x地址 x:[0,5] 0x00~0xFFFFFFFFFF
    channelAddress: {
      type: 'u64',
      repeated: 6,
      offset: 24,
      default: [
        0xe7d3f03577, 0xc2c2c2c2c2, 0xc2c2c2c2c3, 0xc2c2c2c2c4, 0xc2c2c2c2c5,
        0xc2c2c2c2c6,
      ],
    },
  },
}

export class PatrolSystemSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 72
    this.proto = PatrolSystemProto[this.version]
  }
}

// 录音
const RecordProto = {
  0: {
    // 呼叫记录ID
    recordId: {
      type: 'u32',
      offset: 0,
      default: 0xffffffff,
    },
    // 会话呼叫ID
    callId: {
      type: 'u32',
      offset: 4,
      default: 0,
    },
    // 会话开始时间戳
    startTime: {
      type: 'u32',
      offset: 8,
      default: 0,
    },
    // 录音文件存储起始flash扇区地址
    fileStartAddr: {
      type: 'u32',
      offset: 12,
      default: 0,
    },
    // 录音文件解析设置
    recordParse: {
      type: 'u8',
      offset: 16,
      bits: {
        // 录音文件类型 0:半双工呼叫 1:数字全双工呼出
        type: {
          offset: 0,
          len: 1,
          default: 0,
        },
        // 录音呼叫方向 0:语音呼入 1:语音呼出
        direction: {
          offset: 1,
          len: 1,
          default: 0,
        },
        // 保留位 bit2-bit3
        // 声码器类型 0:AMBE声码器 1:SELP声码器
        codec: {
          offset: 4,
          len: 4,
          default: 0,
        },
      },
    },
    // 通话次数
    callCount: {
      type: 'u8',
      offset: 17,
      default: 0,
    },
    // 语音负载帧数 每帧60ms,存32字节,实际有效27字节
    frameCount: {
      type: 'u16',
      offset: 18,
      default: 0,
    },
  },
}

export class RecordSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 20
    this.proto = RecordProto[this.version]
  }
}

// 区域
const ZoneProto = {
  0: {
    // 区域ID
    zoneId: {
      type: 'u8',
      offset: 0,
      default: 0,
    },
    // 信道个数 0~15
    count: {
      type: 'u8',
      offset: 1,
      default: 0,
    },
    // 信道列表 "0xFFFF:无  0~511：指定已配置信道"
    list: {
      type: 'u16',
      offset: 2,
      repeated: 64,
      default: [],
    },
    // 名称
    name: {
      type: 'stringU16',
      offset: 130,
      len: 34,
      default: '',
    },
  },
}

export class ZoneSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 164
    this.proto = ZoneProto[this.version]
  }
}

// 信道
const ChannelProto = {
  0: {
    /* 信道公共配置 */
    // 信道ID
    chId: {
      type: 'u16',
      offset: 0,
      default: 0,
    },
    // 信道类型 "0:数字信道 1:模拟信道 2:数字模拟信道"
    chType: {
      type: 'u8',
      offset: 2,
      default: 0,
    },
    // 模拟呼叫挂起时间 0~255
    analogCallHangTime: {
      type: 'u8',
      offset: 3,
      default: 0,
    },
    // 接收频率 BCD小端存储 单位Hz；设置值范围，与设备工作频段有关
    rxFreq: {
      type: 'u32',
      offset: 4,
      default: 400000000,
    },
    // 发射频率 BCD小端存储 单位Hz；设置值范围，与设备工作频段有关
    txFreq: {
      type: 'u32',
      offset: 8,
      default: 400000000,
    },
    // 扫描/漫游列表 "0:表示扫描列表 1:表示漫游列表" 该参数在写频软件上面不需要显示，且在模拟信道时默认为0
    scanRoamListType: {
      type: 'u8',
      offset: 12,
      default: 0xff,
    },
    // 扫描/漫游列表ID "0xFF:无 0~31：扫描/漫游ID" 默认0xFF 该参数在模拟信道时，只显示“扫描列表”
    scanList: {
      type: 'u8',
      offset: 13,
      default: 0xff,
    },
    // 信道名称
    chName: {
      type: 'stringU16',
      offset: 14,
      len: 34,
      default: 'Channel',
    },
    // 基础参数配置
    baseSettings: {
      type: 'u8',
      offset: 48,
      bits: {
        // 自动扫描
        autoScan: {
          offset: 0,
          len: 1,
          bool,
          default: false,
        },
        // 只接收
        onlyReceive: {
          offset: 1,
          len: 1,
          bool,
          default: false,
        },
        // 允许脱网
        allowOffline: {
          offset: 2,
          len: 1,
          bool,
          default: false,
        },
        // 紧急警报指示
        emergencyAlarmIndication: {
          offset: 3,
          len: 1,
          bool,
          default: false,
        },
        // 紧急警报确认
        emergencyAlarmConfirm: {
          offset: 4,
          len: 1,
          bool,
          default: false,
        },
        // 紧急呼叫提示
        emergencyCallTip: {
          offset: 5,
          len: 1,
          bool,
          default: false,
        },
        // SDC 联网模式下拉框中选择“SDC”则该参数置1
        // 选择“无”或“SVT”则该参数置0
        networking: {
          offset: 6,
          len: 1,
          default: 0,
        },
        // 本地呼叫
        localCall: {
          offset: 7,
          len: 1,
          bool,
          default: false,
        },
      },
    },
    // 数字/模拟紧急警报系统 0~3 默认0xFF	对应数字/模拟紧急警报系统ID，取决于信道类型
    emergencySysId: {
      type: 'u8',
      offset: 49,
      default: 0xff,
    },
    // 发射功率	"0:低功率 1:高功率" 默认0
    txPower: {
      type: 'u8',
      offset: 50,
      default: 1,
    },
    // 发射准许条件 "0:始终 1:信道空闲 2:可用彩色码(CTCSS/CDCSS)"
    permitConditions: {
      type: 'u8',
      offset: 51,
      default: 0,
    },
    // 发射限时器 15~495（秒）间隔15 默认495
    sendTimeLimiter: {
      type: 'u16',
      offset: 52,
      default: 495,
    },
    // TOT秘钥更新延迟时间 0~255
    totKeyUpdateDelay: {
      type: 'u8',
      offset: 54,
      default: 0,
    },
    // 默认发射信道网络 "0：数字 1：模拟"
    defaultTxChannelNetwork: {
      type: 'u8',
      offset: 55,
      default: 0,
    },

    /* 数字信道参数 */
    // 默认通信地址 "0xFFFF:无 0~511：指定的已配置通讯录"
    defaultTarget: {
      type: 'u16',
      offset: 56,
      default: 0xffff,
    },
    // 彩色码 0~15
    colorCode: {
      type: 'u8',
      offset: 58,
      default: 0,
    },
    // 时隙选择 "0:时隙1 1:时隙2 2:虚拟集群"
    timeSlot: {
      type: 'u8',
      offset: 59,
      default: 0,
    },
    // 指定发射时隙 "0:无 1:时隙1 2:时隙2" 该参数只有在“时隙选择”为双时隙时，才允许配置
    specifyTxTimeSlot: {
      type: 'u8',
      offset: 60,
      default: 0,
    },
    // 呼叫中发射准许条件 "0:同发射准许条件一致 1:始终  2:可用彩色码" 其中，只有在“时隙选择”为双时隙时，该参数才允许配置为“优先打断”
    callingTxPermitCondition: {
      type: 'u8',
      offset: 61,
      default: 1,
    },
    // 接收组列表ID "0xFFFF:无  0~255：指定的已配置接收组列表"
    rxGroupId: {
      type: 'u16',
      offset: 62,
      default: 0xffff,
    },
    // 呼叫功能的一些配置
    callingSettings: {
      type: 'u8',
      offset: 64,
      bits: {
        // TDMA直通模式
        throughMode: {
          offset: 0,
          len: 1,
          bool,
          default: false,
        },
        // 数据呼叫确认 只有当勾选“数据IP”后才支持配置
        dataCallConfirm: {
          offset: 1,
          len: 1,
          bool,
          default: false,
        },
        // 信道时隙校准器 0: 不合格， 1: 合格， 2: 首选
        channelSlotCalibrator: {
          offset: 2,
          len: 2,
          default: 0,
        },
        // 优先打断使能
        priorityInterrupt: {
          offset: 4,
          len: 1,
          bool,
          default: false,
        },
        // 自动漫游
        autoRoam: {
          offset: 5,
          len: 1,
          bool,
          default: false,
        },
        // IP站点互连
        ipSiteConnect: {
          offset: 6,
          len: 1,
          bool,
          default: false,
        },
        // 单呼确认
        singleCallConfirm: {
          offset: 7,
          len: 1,
          bool,
          default: false,
        },
      },
    },
    // 加密的一些配置
    encryptionSettings: {
      type: 'u8',
      offset: 65,
      bits: {
        // 加密使能
        enable: {
          offset: 0,
          len: 1,
          bool,
          default: false,
        },
        // 加密类型
        // 0：异或 1：增强型异或 2：ARC4 3：AES256 4：动态加密 5：Moto Arc4 6：Moto AES256 7：不加密
        type: {
          offset: 1,
          len: 3,
          default: 0,
        },
        // 多密钥解密	"0：禁用 1：启用"
        multiKeyDecryption: {
          offset: 4,
          len: 1,
          bool,
          default: false,
        },
        // 随机密钥加密	"0：禁用 1：启用"
        randomKeyEncryption: {
          offset: 5,
          len: 1,
          bool,
          default: false,
        },
        // 双工模式 "0:单工 1:双工"
        duplexModel: {
          offset: 6,
          len: 1,
          default: 0,
        },
        // SVT 0：禁用1：启用 联网模式下拉框中选择SVT”则该参数置1 选择“无”或“SDC”则该参数置0 仅限SVT的机型使用
        virtualCluster: {
          offset: 7,
          len: 1,
        },
      },
    },
    // 加密密钥
    encryptKey: {
      type: 'u8',
      offset: 66,
      default: 0,
    },
    // 呼叫优先等级 "0:无 1~3:呼叫等级" 勾选联网，默认呼叫等级范围1~3(没有无选项)
    voicePriority: {
      type: 'u8',
      offset: 67,
      default: 1,
    },
    // SVT站点信息 0xFF:无 0~15：站点信息ID
    svtSiteInfo: {
      type: 'u8',
      offset: 68,
      default: 0xff,
    },
    // 语音呼叫设置
    voiceCallSettings: {
      type: 'u8',
      offset: 69,
      bits: {
        // 语音呼叫嵌入别名
        aliasEmbedding: {
          offset: 0,
          len: 1,
          bool,
          default: true,
        },
      },
    },
    // 保留位 uint8[2]

    /* 模拟信道参数 */
    /* // 信令类型 "0:无 1:两音 2:五音 3:DTMF"
     signalingType: {
       type: 'u8',
       offset: 72,
     },
     // 特性列表 0~5
     featureList: {
       type: 'u8',
       offset: 73,
     }, */
    // 信道带宽 "0:12.5K 1:25K"
    channelBandwidth: {
      type: 'u8',
      offset: 74,
      default: 0,
    },
    // 模拟信道基础配置
    analogSettings: {
      type: 'u8',
      offset: 75,
      bits: {
        /* // 自动复位模式时发射权限无效
         invalidTxPermit: {
           offset: 0,
           len: 1,
           bool,
         }, */
        // 加重标志
        weightingMark: {
          offset: 1,
          len: 1,
          bool,
          default: true,
        },
        // 压扩使能
        compandEnable: {
          offset: 2,
          len: 1,
          bool,
          default: false,
        },
        /* // 扰频使能
         scrambleEnable: {
           offset: 3,
           len: 1,
           bool,
         }, */
        // 尾音消除使能
        tailCancellation: {
          offset: 4,
          len: 1,
          bool,
          default: true,
        },
      },
    },
    /* // 扰频频率 0~6000
    scramblingFreq: {
      type: 'u16',
      offset: 76,
    }, */
    // 静噪等级  "0:常开 1:正常 2:加强"
    squelchLevel: {
      type: 'u8',
      offset: 78,
      default: 1,
    },
    /* // 自动复位时间 1~255（秒）
    autoResetTime: {
      type: 'u8',
      offset: 79,
    },
    // 呼叫响应时间 1~255（秒）
    callResponseTime: {
      type: 'u8',
      offset: 80,
    }, */
    // 接收亚音类型 "0:无 1:亚音频 2:亚音数码 3:反向亚音数码"
    receiveToneType: {
      type: 'u8',
      offset: 81,
      default: 1,
    },
    // 发射亚音类型 "0:无 1:亚音频 2:亚音数码 3:反向亚音数码"
    transmitToneType: {
      type: 'u8',
      offset: 82,
      default: 1,
    },
    /* // 接收信令系统 0~3或0~7	默认0xFF
     rxSignallingSystem: {
       type: 'u8',
       offset: 83,
     }, */
    // 接收模拟亚音码 模拟亚音码表	默认"67"
    rxAnalogToneCode: {
      type: 'u16',
      offset: 84,
      default: 670,
    },
    // 接收数字亚音码 模拟亚音码表	默认无
    rxDigitalToneCode: {
      type: 'u16',
      offset: 86,
      default: 23,
    },
    // 发射模拟亚音码 模拟亚音码表	默认"67"
    txAnalogToneCode: {
      type: 'u16',
      offset: 88,
      default: 670,
    },
    // 发射数字亚音码 模拟亚音码表	默认无
    txDigitalToneCode: {
      type: 'u16',
      offset: 90,
      default: 23,
    },
    // 接收静噪模式 "0:载波 1:亚音  2:可选信令 3:亚音与可选信令 4:亚音或可选信令"
    rxSquelchMode: {
      type: 'u8',
      offset: 92,
      default: 1,
    },
    // 监听静噪模式 "0:载波 1:亚音"
    monitorSquelchMode: {
      type: 'u8',
      offset: 93,
      default: 1,
    },
    // 切换信道静噪模式 "0:接收静噪模式 1:监听静噪模式"
    switchChannelSquelchMode: {
      type: 'u8',
      offset: 94,
      default: 0,
    },
    /* // 自动复位模式 "0:立即复位 1:载波延时 2:载波无关 3:手动复位"
     autoResetMode: {
       type: 'u8',
       offset: 95,
     }, */
    // 尾音选择 "0:标准 1:非标准"
    endingSelection: {
      type: 'u8',
      offset: 96,
      default: 1,
    },
    // 爆破音 "0:标准 1:非标准"
    plosive: {
      type: 'u8',
      offset: 97,
      default: 1,
    },
    // 信道繁忙锁定 "0:始终  1:信道空闲  2:CTCSS/CDCSS"
    busyChannelLock: {
      type: 'u8',
      offset: 98,
      default: 0,
    },
    // 保留位 99
  },
}

export class ChannelSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 100
    this.proto = ChannelProto[this.version]
  }
}

// 附件设置
const AttachmentSettingsProto = {
  0: {
    attachmentSettings: {
      type: 'u8',
      offset: 0,
      bits: {
        // 蓝牙模式 0：关闭 1：蓝牙写频 2：蓝牙音频
        // 备注： 屏蔽蓝牙写频
        bluetoothMode: {
          offset: 0,
          len: 2,
          default: 0,
        },
        // 录音功能 0：关闭 1：启用
        recordEnable: {
          offset: 2,
          len: 1,
          bool,
          default: false,
        },
      },
    },
    // 后续保留三个字节
  },
}

export class AttachmentSettings extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 4
    this.proto = AttachmentSettingsProto[this.version]
  }
}

// 虚拟集群
const VirtualClusterProto = {
  0: {
    // 所属归属组
    vcGroupId: {
      type: 'u16',
      offset: 0,
      default: 0,
    },
    // 测机信号间隔
    testMachineInterval: {
      type: 'u16',
      offset: 2,
      default: 3600,
    },
    // RSSI阀值(dBm)
    rssiValue: {
      type: 'u8',
      offset: 4,
      default: 0,
    },
    // 鉴权 暂不使用
    authentication: {
      type: 'u8',
      offset: 5,
      default: 0,
    },
    // 鉴权秘钥
    // 密钥值,0~9,A~F,没填满默认补F
    authKey: {
      type: 'string',
      len: 32,
      offset: 6,
      default: ''.padEnd(32, 'F'),
    },
    // 信道列表 "0xFFFF:无  0~511：指定已配置信道"
    channelList: {
      type: 'u16',
      offset: 38,
      repeated: 16,
      default: [0xfffe],
    },
  },
}

export class VirtualCluster extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 70
    this.proto = VirtualClusterProto[this.version]
  }
}

const SiteInfoProto = {
  0: {
    // 站点信息ID	DBID_C_SITE	范围: 0~15
    id: {
      type: 'u8',
      offset: 0,
      default: 0,
    },
    // 保留位	uint8
    // 站点名称	uint16[16+1]	"支持最长16个unicode字符"
    name: {
      type: 'stringU16',
      offset: 2,
      len: 34,
      default: '',
    },
    frequency: {
      type: 'byteArray',
      subFields: {
        // 接收频点
        rxFreq: {
          type: 'u32',
          offset: 0,
          default: 0,
        },
        // 发射频点
        txFreq: {
          type: 'u32',
          offset: 4,
          default: 0,
        },
      },
      len: 8,
      repeated: 16,
      offset: 36,
    },
  },
}

export class SiteInfo extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 164
    this.proto = SiteInfoProto[this.version]
  }
}

// 系统参数表ID及结构体定义
export const TableIndex = generateEnumObject({
  // 生产信息	生产相关的设备信息，包括 型号，频段，版本信息
  DeviceInfo: 0x01,
  // 身份信息	包括 序列号，区域密文，区域签名
  IdentityInfo: 0x02,
  // 写频读密码	写频读模式使用的密码
  WriteFreqReadPassword: 0x03,
  // 写频写密码  写频写模式使用的密码
  WriteFreqWritePassword: 0x04,
  // 常规设置
  GeneralSettings: 0x05,
  // UI设置
  UISettings: 0x06,
  // 按键设置
  KeysSettings: 0x07,
  // 菜单设置
  MenuSettings: 0x08,
  // GPS设置
  GpsSettings: 0x09,
  // 巡逻系统
  PatrolSystemSettings: 0x0a,
  // 录音
  RecordSettings: 0x0b,
  // 倒放
  UpsideDownSettings: 0x0e,
  // 单独工作设置
  AloneWorkSettings: 0x0f,
  // 数字警报
  DigitalAlarmSettings: 0x10,
  // 模拟警报
  AnalogAlarmSettings: 0x11,
  // 扫描基础设置
  ScanConfigSettings: 0x12,
  // 扫描列表
  ScanListSettings: 0x13,
  // 漫游设置
  RoamConfigSettings: 0x14,
  // 漫游列表
  RoamListSettings: 0x15,
  // DMR设置
  DmrConfigSettings: 0x16,
  // 加密基础配置
  EncryptionConfigSettings: 0x17,
  // 加密列表
  EncryptionListSettings: 0x18,
  // 高级加密ARC4列表
  EncryptionARC4List: 0x19,
  // 高级加密ARS列表
  EncryptionARSList: 0x1a,
  // 联系人列表
  DMRContactsSettings: 0x1c,
  // 接收组列表
  ReceiveGroupSettings: 0x1d,
  // 预制短信
  PreMakeSmsSettings: 0x1e,
  // 区域列表
  ZoneSettings: 0x2a,
  // 信道列表
  ChannelSettings: 0x2b,
  // 电话本
  PhoneBookSettings: 0x2c,
  // 虚拟集群
  VirtualCluster: 0x2d,
  // 站点信息
  SiteInfo: 0x2e,
  // 附件
  AttachmentSettings: 0x2f,
})

// 各结构类型索引
export const StructIndex = {
  // 生产信息	生产相关的设备信息，包括 型号，频段，版本信息
  0x01: TD930DeviceInfo,
  // 身份信息	包括 序列号，区域密文，区域签名
  0x02: IdentityInfo,
  // 写频读密码	写频读模式使用的密码
  0x03: Password,
  // 写频写密码  写频写模式使用的密码
  0x04: Password,
  // 常规设置
  0x05: GeneralSettings,
  // UI设置
  0x06: UISettings,
  // 按键设置
  0x07: KeysSettings,
  // 菜单设置
  0x08: MenuSettings,
  // GPS设置
  0x09: GpsSettings,
  // 巡逻系统
  0x0a: PatrolSystemSettings,
  // 录音
  0x0b: RecordSettings,
  // 倒放设置
  0x0e: UpsideDownSettings,
  // 单独工作设置
  0x0f: AloneWorkSettings,
  // 数字警报
  0x10: DigitalAlarmSettings,
  // 模拟警报
  0x11: AnalogAlarmSettings,
  // 扫描基础设置
  0x12: ScanConfigSettings,
  // 扫描列表
  0x13: ScanListSettings,
  // 漫游设置
  0x14: RoamConfigSettings,
  // 漫游列表
  0x15: RoamListSettings,
  // DMR设置
  0x16: DmrConfigSettings,
  // 加密基础配置
  0x17: EncryptionConfigSettings,
  // 加密列表
  0x18: EncryptionListSettings,
  // 高级加密ARC4列表
  0x19: EncryptionARC4List,
  // 高级加密ARS列表
  0x1a: EncryptionARSList,
  // 联系人列表
  0x1c: DMRContactsSettings,
  // 接收组列表
  0x1d: ReceiveGroupSettings,
  // 预制短信
  0x1e: PreMakeSmsSettings,
  // 区域列表
  0x2a: ZoneSettings,
  // 信道列表
  0x2b: ChannelSettings,
  // 电话本
  0x2c: PhoneBookSettings,
  // 虚拟集群
  0x2d: VirtualCluster,
  // 站点信息
  0x2e: SiteInfo,
  // 附件
  0x2f: AttachmentSettings,
}

export function getClassInstance(options) {
  const opts = {
    ...BaseOptions,
    ...options,
  }
  const DataClass = StructIndex[opts.type]
  if (!DataClass) {
    return null
  }

  return new DataClass(opts)
}

// 生成协议字段默认参数
function createBitsData(bits) {
  const result = {}
  for (const k in bits) {
    result[k] = bits[k].default
  }

  return result
}

function createSubFieldsData(subFields, repeated) {
  const result = []
  for (let i = 0; i < repeated; i++) {
    const data = {}
    for (const k in subFields) {
      data[k] = subFields[k].default
    }
    result.push(data)
  }

  return result
}

export function createDefaultArgs({ type, version = 0 }) {
  const result = {}
  const ctx = getClassInstance({
    type,
    version,
  })
  if (!ctx || !ctx.proto) {
    return result
  }

  for (const key in ctx.proto) {
    const field = ctx.proto[key]

    if (field.bits) {
      result[key] = createBitsData(field.bits)
      continue
    }

    if (field.subFields) {
      result[key] = createSubFieldsData(field.subFields, field.repeated)
      continue
    }

    result[key] = field.default
  }

  return result
}

// 模拟亚音 实际下发值比显示值大10倍，例如显示“67.0”，下发值为670
export const AnalogCode = {
  '67.0': 670,
  69.3: 693,
  71.9: 719,
  74.4: 744,
  '77.0': 770,
  79.7: 797,
  82.5: 825,
  85.4: 854,
  88.5: 885,
  91.5: 915,
  94.8: 948,
  97.4: 974,
  '100.0': 1000,
  103.5: 1035,
  107.2: 1072,
  110.9: 1109,
  114.8: 1148,
  118.8: 1188,
  '123.0': 1230,
  127.3: 1273,
  131.8: 1318,
  136.5: 1365,
  141.3: 1413,
  146.2: 1462,
  151.4: 1514,
  156.7: 1567,
  159.8: 1598,
  162.2: 1622,
  165.5: 1655,
  167.9: 1679,
  171.3: 1713,
  173.8: 1738,
  177.3: 1773,
  179.9: 1799,
  183.5: 1835,
  186.2: 1862,
  189.9: 1899,
  192.8: 1928,
  196.6: 1966,
  199.5: 1995,
  203.5: 2035,
  206.5: 2065,
  210.7: 2107,
  218.1: 2181,
  225.7: 2257,
  229.1: 2291,
  233.6: 2336,
  241.8: 2418,
  250.3: 2503,
  254.1: 2541,
}

// 数字亚音
export const DigitalCode = {
  '023': 23,
  '025': 25,
  '026': 26,
  '031': 31,
  '032': 32,
  '043': 43,
  '047': 47,
  '051': 51,
  '054': 54,
  '065': 65,
  '071': 71,
  '072': 72,
  '073': 73,
  '074': 74,
  114: 114,
  115: 115,
  116: 116,
  125: 125,
  131: 131,
  132: 132,
  134: 134,
  143: 143,
  152: 152,
  155: 155,
  156: 156,
  162: 162,
  165: 165,
  172: 172,
  174: 174,
  205: 205,
  223: 223,
  226: 226,
  243: 243,
  244: 244,
  245: 245,
  251: 251,
  261: 261,
  263: 263,
  265: 265,
  271: 271,
  306: 306,
  311: 311,
  315: 315,
  331: 331,
  343: 343,
  346: 346,
  351: 351,
  364: 364,
  365: 365,
  371: 371,
  411: 411,
  412: 412,
  413: 413,
  423: 423,
  431: 431,
  432: 432,
  445: 445,
  464: 464,
  465: 465,
  466: 466,
  503: 503,
  506: 506,
  516: 516,
  532: 532,
  546: 546,
  565: 565,
  606: 606,
  612: 612,
  624: 624,
  627: 627,
  631: 631,
  632: 632,
  654: 654,
  662: 662,
  664: 664,
  703: 703,
  712: 712,
  723: 723,
  731: 731,
  732: 732,
  734: 734,
  743: 743,
  754: 754,
}

// 需要先清除表再INSERT
export const InsertDataAfterClear = [
  TableIndex.DigitalAlarmSettings,
  TableIndex.AnalogAlarmSettings,
  TableIndex.ScanListSettings,
  TableIndex.RoamListSettings,
  TableIndex.EncryptionListSettings,
  TableIndex.EncryptionARC4List,
  TableIndex.EncryptionARSList,
  TableIndex.DMRContactsSettings,
  TableIndex.ReceiveGroupSettings,
  TableIndex.PreMakeSmsSettings,
  TableIndex.ZoneSettings,
  TableIndex.ChannelSettings,
  TableIndex.PhoneBookSettings,
  TableIndex.SiteInfo,
]

// 写频读、写结构顺序
export const OperationOrderTable = [
  TableIndex.DeviceInfo,
  TableIndex.IdentityInfo,
  TableIndex.WriteFreqReadPassword,
  TableIndex.WriteFreqWritePassword,
  TableIndex.GeneralSettings,
  TableIndex.UISettings,
  TableIndex.KeysSettings,
  TableIndex.MenuSettings,
  TableIndex.GpsSettings,
  TableIndex.PatrolSystemSettings,
  // TableIndex.RecordSettings,
  TableIndex.UpsideDownSettings,
  TableIndex.AloneWorkSettings,
  TableIndex.DigitalAlarmSettings,
  TableIndex.AnalogAlarmSettings,
  TableIndex.ScanConfigSettings,
  TableIndex.ScanListSettings,
  TableIndex.RoamConfigSettings,
  TableIndex.RoamListSettings,
  TableIndex.DmrConfigSettings,
  TableIndex.EncryptionConfigSettings,
  TableIndex.EncryptionListSettings,
  TableIndex.EncryptionARC4List,
  TableIndex.EncryptionARSList,
  TableIndex.DMRContactsSettings,
  TableIndex.ReceiveGroupSettings,
  TableIndex.PreMakeSmsSettings,
  TableIndex.ZoneSettings,
  TableIndex.ChannelSettings,
  TableIndex.PhoneBookSettings,
  TableIndex.VirtualCluster,
  TableIndex.SiteInfo,
  TableIndex.AttachmentSettings,
]
