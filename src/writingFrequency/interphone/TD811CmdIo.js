/*
3.2 数据格式规定
本协议所涉及的所有数据 均默认采用 小端存储.
  为方便 终端编码开发(主要针对结构体对齐),所有 涉及的系统参数、射频参数等，其数据块必须为4的倍数。
3.3 CPS帧格式定义

字段	     帧前缀	CRC值	指令类型	加密类型	参数长度	参数
长度(字节)   2       2      1       1       2     N

帧前缀：固定值为 0xA55A
CRC值： 对指令类型、加密类型、参数长度、参数 进行CRC计算获取的值。若加密，先加密后进行 CRC。
指令类型：参考 指令类型表。
加密类型：值为0 时候表示不加密，值为1时候表示进行AES加密。若为AES加密，针对参数部分 进行加密。
参数长度：参数的长度。
参    数：参数长度范围为 4-480字节.

指令类型表
指令名                     指令类型	         应答指令                     备注
CC_IND_SET_WORK_MODE        0x01      CC_RSP_COMMON               设置交互业务模式。
CC_IND_SIGNATURE_VERIFY     0x02      CC_RSP_SIGNATURE_VERIFY     上位机向终端发送区域签名。
CC_IND_DB_OPER              0x03      CC_RSP_DB_OPER              读/写数据库表
CC_IND_READ_FILE            0x04      CC_RSP_READ_FILE            读取指定的文件
CC_IND_READ_FILE_BLOCK      0x05      CC_RSP_READ_FILE_BLOCK      读取文件指定的数据块
CC_IND_WRITE_FILE           0x06      CC_RSP_COMMON               下载指定文件
CC_IND_WRITE_FILE_BLOCK     0x07      CC_RSP_COMMON               下载文件的数据块
CC_IND_RF_TX_CONTROL        0x08      CC_RSP_COMMON             	控制终端语音发射开启关闭
CC_IND_DEVICE_REBOOT        0x09      CC_RSP_COMMON	              指示终端设备重启
CC_IND_LOG_CONTROL          0x10      CC_RSP_COMMON             	控制终端日志开关
CC_IND_RESET_RUNTIME_PARA   0x11      CC_RSP_COMMON	              重置终端内部的运行变量
CC_RSP_COMMON               0x81    	无                         	终端的通用应答。
CC_RSP_SIGNATURE_VERIFY     0x82    	无                         	终端对上位机区域验证的应答。
CC_RSP_DB_OPER              0x83    	无                         	终端返回数据库表结构体
CC_RSP_READ_FILE            0x84    	无                         	终端返回待读取的文件信息
CC_RSP_READ_FILE_BLOCK      0x85    	无                         	终端返回待读取的文件数据块
*/

import { Decrypt, Encrypt, encryptedDataTo16Byte } from './aesEncrypt'
import {
  Base,
  BaseOptions2 as BaseOptions,
  DefModel,
  generateEnumObject,
  getProtoSize,
  marshalProto,
  readU16LE,
  writeU16LE,
} from './common'
import { CRC16 } from './crc'

const Head = 0xa55a
export const EncryptEnum = {
  // 不加密
  None: 0,
  // 写密码加密
  WAes: 1,
  // 读密码加密
  RAes: 2,
  // 默认密码加密
  NAes: 3,
}
export const CmdTable = generateEnumObject({
  // 中心写频下发的指令
  CC_IND_SET_WORK_MODE: 0x01,
  CC_IND_SIGNATURE_VERIFY: 0x02,
  CC_IND_DB_OPER: 0x03,
  CC_IND_READ_FILE: 0x04,
  CC_IND_READ_FILE_BLOCK: 0x05,
  CC_IND_WRITE_FILE: 0x06,
  CC_IND_WRITE_FILE_BLOCK: 0x07,
  CC_IND_RF_TX_CONTROL: 0x08,
  CC_IND_DEVICE_REBOOT: 0x09,
  // CC_IND_LOG_CONTROL: 0x10,
  CC_IND_RESET_RUNTIME_PARA: 0x11,
  // 获取录音语音帧
  CC_IND_READ_RECORD_BLOCK: 0x10,
  // 获取ui版本信息
  CC_IND_UI_INFO_DATA: 0x11,

  // 终端响应的指令类型
  CC_RSP_COMMON: 0x81,
  CC_RSP_SIGNATURE_VERIFY: 0x82,
  CC_RSP_DB_OPER: 0x83,
  CC_RSP_READ_FILE: 0x84,
  CC_RSP_READ_FILE_BLOCK: 0x85,
  // 设备回复录音语音帧
  CC_RSP_READ_RECORD_BLOCK: 0x88,
  // 设备回复ui版本信息
  CC_RSP_UI_INFO_DATA: 0x89,
})

export function concatenate(resultConstructor, ...arrays) {
  let totalLength = 0
  for (const arr of arrays) {
    totalLength += arr.length
  }
  const result = new resultConstructor(totalLength)
  let offset = 0
  for (const arr of arrays) {
    result.set(arr, offset)
    offset += arr.length
  }
  return result
}

export function concatUint8Array(...arrays) {
  return concatenate(Uint8Array, ...arrays)
}

export function checkDataIsBuffer(data) {
  return !(!(data instanceof Array) && !(data instanceof Uint8Array))
}

export class TD811CmdIo {
  constructor() {
    // 指令头
    this.head = Head
    // CRC 校验和
    this.crc = 0x0000
    // 指令类型
    this.type = CmdTable.CC_IND_SET_WORK_MODE
    // 加密类型，默认万能密码加密
    this.encrypt = EncryptEnum.None
    // 指令参数
    this.data = []
    // 指令参数长度
    this.len = this.data.length
    // 整个指令数据容器, 最大548字节，依此计算多个数据项的结构单次可编码几个数据项
    this.bytes = []
  }

  decode(bytes, model = DefModel, pwd = '') {
    this.bytes = bytes
    this.crc = readU16LE(bytes, 2)
    this.type = bytes[4]
    this.encrypt = bytes[5]
    this.len = readU16LE(bytes, 6)
    this.data = bytes.subarray(8, this.len + 8)
    // 解密
    if (this.encrypt !== EncryptEnum.None) {
      // 默认密码加密，则清除密码，使用默认的万能密码解密
      if (this.encrypt === EncryptEnum.NAes) {
        pwd = ''
      }
      this.data = Decrypt(this.data, model, pwd)
    }

    // 根据响应的指令类型解码响应的指令类型数据
    decodeResponseData(this)

    return this
  }

  // dataLen:数据长度 data:数据区 checkSum:数据区校验和
  encode(model = DefModel, pwd = '') {
    // 加密类型：值为0 时候表示不加密，值为1时候表示进行AES加密。若为AES加密，针对参数部分 进行加密。
    // 加密时，补齐参数长度为16的倍数
    let data = this.data
    if (this.encrypt !== EncryptEnum.None) {
      data = Encrypt(encryptedDataTo16Byte(data), model, pwd)
    }
    this.len = data.length
    // 缓存指令类型、加密类型、参数长度、参数
    const bytes = concatUint8Array(
      [this.type, this.encrypt],
      writeU16LE(this.len),
      data,
    )
    // CRC检验值，对指令类型、加密类型、参数长度、参数 进行CRC计算获取的值
    // 若加密，先加密后进行 CRC
    this.crc = CRC16(bytes)
    // 拼接 CPS 帧
    this.bytes = concatUint8Array(
      writeU16LE(this.head),
      writeU16LE(this.crc),
      bytes,
    )

    return this.bytes
  }
}

function getDecoder(type, model = DefModel) {
  let deCoder = null
  switch (type) {
    case CmdTable.CC_RSP_COMMON:
      deCoder = new COMMON_CODER()
      break
    case CmdTable.CC_RSP_SIGNATURE_VERIFY:
      deCoder = new SIGNATURE_VERIFY_CODER()
      break
    case CmdTable.CC_RSP_DB_OPER:
      deCoder = new DB_OPER_CODER()
      break
    case CmdTable.CC_RSP_READ_FILE:
      break
    case CmdTable.CC_RSP_READ_FILE_BLOCK:
      break
    case CmdTable.CC_RSP_READ_RECORD_BLOCK:
      deCoder = new READ_RECORD_BLOCK_CODER()
      break
    case CmdTable.CC_RSP_UI_INFO_DATA:
      deCoder = new UI_INFO_DATA_CODER()
      break
  }

  return deCoder
}

function decodeResponseData(ctx) {
  let deCoder = getDecoder(ctx.type)
  if (deCoder == null) {
    console.warn('not found decoder for response %d cmd. ', ctx.type, ctx)
    return
  }
  deCoder.getDbCoder = ctx.getDbCoder
  ctx.data = deCoder.decode(ctx.data)
  // 释放解码类结构
  deCoder = null
  bfglob.console.log('decodeResponseData', ctx)
}

// 区域鉴权结果枚举对象
export const VerifyResponse = generateEnumObject({
  // 执行成功
  Success: 0x00,
  // 执行失败
  Fail: 0x01,
})

const CC_RSP_SIGNATURE_VERIFY = {
  0: {
    statusCode: {
      type: 'u8',
      offset: 0,
    },
    // 保留位 3B
    signature: {
      type: 'byteArray',
      offset: 4,
      len: 128,
    },
  },
}

export class SIGNATURE_VERIFY_CODER extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 132
    this.proto = CC_RSP_SIGNATURE_VERIFY[this.version]
  }

  decode(bytes) {
    const data = super.decode(bytes)
    this.data = data[0] || {}
    return this.data
  }
}

// 通用响应码枚举对象
export const CommonResponse = generateEnumObject({
  // 执行成功
  Success: 0x00,
  // 执行失败
  Fail: 0x01,
  // 帧数据接收不完整
  FrameDataIncomplete: 0x02,
  // 帧解密失败
  FrameDecryptFail: 0x03,
  // CRC校验失败
  CRCCheckFail: 0x04,
  // 指令类型错误
  CmdTypeErr: 0x05,
  // 参数格式错误
  ArgsFormatErr: 0x06,
  // 未先执行区域验证，无权限
  NotPermission: 0x07,
  // 收到指令时所处的工作模式不对
  WorkingModeErr: 0x08,
})

const CC_RSP_COMMON = {
  0: {
    resCmd: {
      type: 'u8',
      offset: 0,
    },
    statusCode: {
      type: 'u8',
      offset: 1,
    },
    // 保留位 2B
  },
}

export class COMMON_CODER extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.structLen = options.structLen || 4
    this.proto = CC_RSP_COMMON[this.version]
  }

  decode(bytes) {
    const data = super.decode(bytes)
    this.data = data[0] || {}
    return this.data
  }
}

const CC_RSP_DB_OPER = {
  0: {
    // 表结构ID
    tableId: {
      type: 'u8',
      offset: 0,
    },
    // 操作类型
    operation: {
      type: 'u8',
      offset: 1,
    },
    // 参数项总数
    structCount: {
      type: 'u16',
      offset: 2,
    },
    // 参数长度
    structLen: {
      type: 'u16',
      offset: 4,
    },
    // 数据表起始索引
    startIndex: {
      type: 'u16',
      offset: 6,
    },
    // 后续字节为具体数据参数
  },
}

export class DB_OPER_CODER extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.version = options.version
    this.bytes = []
    this.data = []
    this.proto = CC_RSP_DB_OPER[this.version]
    this.structLen = getProtoSize(this.proto)
  }

  encode() {
    if (!this.proto || this.structLen < 1) {
      return new Uint8Array(0)
    }

    this.bytes = this.data
      .map(data => {
        const buf = marshalProto(data, this.proto, this.structLen)
        return checkDataIsBuffer(data.subData) ? [...buf, ...data.subData] : buf
      })
      .reduce((p, c) => [...p, ...c], [])

    return this.bytes
  }

  decode(bytes) {
    // 表中指定字节数据数据
    super.decode(bytes.subarray(0, this.structLen))
    // 具体数据参数
    const subData = bytes.subarray(this.structLen, bytes.length)

    this.data = this.data[0] || {}
    const dbCoder = this.getDbCoder({
      type: this.data.tableId,
      structLen: this.data.structLen,
    })
    if (dbCoder) {
      Object.assign(this.data, {
        subData: dbCoder.decode(subData),
        coder: dbCoder,
      })
    }

    this.bytes = bytes
    return this.data
  }

  fromJson(source) {
    const data = super.fromJson(source)
    if (checkDataIsBuffer(source.subData)) {
      data.subData = [...source.subData]
    }
    return data
  }
}

const CC_RSP_READ_RECORD_BLOCK = {
  0: {
    frameDataLen: {
      type: 'u32',
      offset: 0,
    },
    dataOffset: {
      type: 'u32',
      offset: 4,
    },
    nData: {
      type: 'u8',
      offset: 8,
      repeated: 32,
    },
  },
}

export class READ_RECORD_BLOCK_CODER extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.data = []
    this.structLen = options.structLen || 40
    this.proto = CC_RSP_READ_RECORD_BLOCK[this.version]
  }

  decode(bytes) {
    const data = super.decode(bytes)
    this.data = data[0] || {}
    return this.data
  }
}

const CC_IND_READ_RECORD_BLOCK = {
  0: {
    recordId: {
      type: 'u32',
      offset: 0,
    },
    dataOffset: {
      type: 'u32',
      offset: 4,
    },
  },
}

export class IND_READ_RECORD_BLOCK_CODER extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.data = []
    this.structLen = options.structLen || 8
    this.proto = CC_IND_READ_RECORD_BLOCK[this.version]
  }

  encode() {
    if (!this.proto || this.structLen < 1) {
      return new Uint8Array(0)
    }

    this.bytes = this.data
      .map(data => {
        const buf = marshalProto(data, this.proto, this.structLen)
        return checkDataIsBuffer(data.subData) ? [...buf, ...data.subData] : buf
      })
      .reduce((p, c) => [...p, ...c], [])

    return this.bytes
  }
}

const CC_RSP_IND_UI_INFO_DATA = {
  0: {
    uiInfoData: {
      type: 'password',
      offset: 0,
      len: 128,
    },
  },
}

export class UI_INFO_DATA_CODER extends Base {
  constructor(options = BaseOptions) {
    super(options)
    this.data = []
    this.structLen = options.structLen || 128
    this.proto = CC_RSP_IND_UI_INFO_DATA[this.version]
  }

  decode(bytes) {
    console.log('CC_IND_UI_INFO_DATA', bytes)
    const data = super.decode(bytes)
    this.data = data[0] || {}
    return this.data
  }
}

// 终端工作模式枚举对象
export const WorkingMode = generateEnumObject({
  IDLE: 0,
  SIGNATURE_VERIFY: 1,
  READ: 2,
  WRITE: 3,
  FILE_READ: 4,
  FILE_WRITE: 5,
  RF_DEBUG: 6,
  PRODUCT_DOWNLOAD: 7,
  DEVICE_BACKUP: 8,
  DEVICE_UPDATE: 9,
  DEVICE_DEBUG: 10,
})

// 数据库操作类型定义（CPS_IND_DB_OPT_E）
export const IndDbOpt = generateEnumObject({
  INSERT: 1,
  DELETE: 2,
  UPDATE: 3,
  SELECT: 4,
  CLEAR: 5,
  GET_COUNT: 6,
})

// 数据库操作返回类型定义（CPS_RSP_DB_FORM_E）
export const RspDbFrom = generateEnumObject({
  RECORD: 1,
  RESULT: 2,
  COUNT: 3,
})
