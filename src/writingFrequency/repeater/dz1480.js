import i18n from '@/modules/i18n'
import bfproto, { kcpPackageName } from '@/modules/protocol'
import { toHexDmrId } from '@/utils/bfutil'
import bfnotify from '@/utils/notify'
import {
  readStringU16,
  writeStringU16,
} from '@/writingFrequency/interphone/common'
import { cloneDeep } from 'lodash'
import {
  enumObject,
  getReplySubject,
  parseDmrId,
  PbSubject,
  Timeout,
} from './common'

const DefaultCMD = enumObject({
  /**无**/
  NONE: 0,
  /**服务器获取从机中继信息 **/
  CLIENT_INFO: 1,
  /**设置信道信息**/
  CH_CONFIG: 2,
  /**切换信道**/
  CH_SWITCH: 3,
  /**切换发射功率**/
  TX_POWER_SWITCH: 4,
  /**重启中继**/
  DEVICE_REBOOT: 5,
  /**加入同频同播系统**/
  CFCB_SYS_ADD: 6,
  /**移除同频同播系统**/
  CFCB_SYS_REMOVE: 7,
})
export const DefModel = 'DZ1480'
export const Models = ['DZ1480']
export const CLIENT_CMD = (function () {
  const RepeatorConfig = bfproto.bfdx_proto_msg_T(
    'EPDB_CTRL_CLIENT_CMD',
    kcpPackageName,
  )
  if (!RepeatorConfig) {
    return DefaultCMD
  }
  return RepeatorConfig.values
})()

function clearWriteTimeout(timers) {
  timers && clearTimeout(timers)
}

function decodeRepeatorConfig(rpc_cmd_obj) {
  // 解码 RepeatorConfig
  const RepeatorConfig = bfproto.bfdx_proto_msg_T(
    'RepeatorConfig',
    kcpPackageName,
  )
  rpc_cmd_obj.body = RepeatorConfig.decode(rpc_cmd_obj.body)

  return rpc_cmd_obj
}

function writeResponse(rpc_cmd, options) {
  return new Promise((resolve, reject) => {
    // 清除超时处理
    clearWriteTimeout(options.timers)

    // 解析出 bfkcp.rpc_cmd
    const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(
      rpc_cmd,
      options.packageName,
    )
    // 取消命令主题订阅
    const subSid =
      typeof options.subSid === 'undefined'
        ? getReplySubject(rpc_cmd_obj.paraInt)
        : options.subSid

    switch (rpc_cmd_obj.res) {
      case -10000:
        // 服务器无法发送到中继
        reject(rpc_cmd_obj.paraInt)
        bfnotify.messageBox(i18n.global.t('msgbox.targetNotOnline'), 'error')
        bfglob.server.unsubscribe(subSid)
        return
      case 10000:
        // 服务器发送命令成功
        // bfnotify.messageBox(i18n.global.t("msgbox.sendSuccess"), 'success');
        return
    }

    bfglob.server.unsubscribe(subSid)

    // 解析出body内容
    decodeRepeatorConfig(rpc_cmd_obj)
    bfglob.console.log('[DZ1480] writeResponse:', rpc_cmd_obj)

    switch (rpc_cmd_obj.res) {
      case 1:
      case 2:
        // 中继应答，指令操作成功
        resolve(rpc_cmd_obj)
        break
      case -4:
        // -4:失败,参数有误
        reject(rpc_cmd_obj)
        bfnotify.messageBox(
          i18n.global.t('msgbox.repeaterParametersError'),
          'error',
        )
        break
      case -1:
        // -1:失败,其它原因情况
        // this.showOperationFailedMessage(data)
        reject(rpc_cmd_obj)
        break
      default:
        // 默认失败
        reject(rpc_cmd_obj)
    }
  })
}

function wrapperResOptions(res, options) {
  // 缓存指令操作的中继的id
  res.deviceId = options.deviceId

  return res
}

function write(data, options) {
  return new Promise((resolve, reject) => {
    const opts = Object.assign(
      {
        rpcCmdFields: {},
        packageName: kcpPackageName,
        stream: false,
      },
      options,
    )
    const replyTo = getReplySubject(opts.rpcCmdFields.paraInt)
    bfglob.console.log('[DZ1480] write:', data, opts)

    opts.subSid = bfglob.server.subscribe(replyTo, rpc_cmd_obj => {
      writeResponse(rpc_cmd_obj, opts).then(resolve).catch(reject)
    })

    const unsubscribe = err => {
      const subSid = typeof opts.subSid === 'undefined' ? replyTo : opts.subSid
      bfglob.server.unsubscribe(subSid)
      reject(err || opts)
    }
    const timeoutUnsubscribe = () => {
      unsubscribe('timeout')
      bfnotify.messageBox(i18n.global.t('msgbox.selError'), 'error')
    }

    opts.timers = setTimeout(timeoutUnsubscribe, Timeout * 1000)

    bfproto
      .sendMessage(5, data, opts.decodeMsgType, PbSubject, opts)
      .catch(res => {
        // cmd=5,没有直接的应答，故总是会超时，需要过滤掉命令的超时的处理
        if (typeof res === 'object' && res.replyTo && res.subject) {
          return
        }
        unsubscribe(res)
      })
  })
}

export async function writeInData(data, options) {
  const opts = {
    packageName: options.packageName || kcpPackageName,
    decodeMsgType: 'RepeatorConfig',
    rpcCmdFields: {},
    ...options,
  }
  // sid: 同播控制器的dmrId
  opts.rpcCmdFields.sid = parseDmrId(opts.rpcCmdFields.sid)

  // deviceId: 被操作的同播中继的dmrId
  const res = await write(
    {
      deviceId: parseDmrId(opts.deviceId),
      ConfigDataBody: data,
    },
    opts,
  )

  return wrapperResOptions(res, opts)
}

/**上报从机注册（注销）信息**/
export function saveSimulcastInfo(data) {
  const dmrId = toHexDmrId(data.deviceId, false)
  const controller = bfglob.gcontrollers.getDataByIndex(dmrId)
  if (!controller) {
    return
  }
  // 能收到中继102，就表示中继已经上线
  data.ctrlStats = controller.ctrlStats = data.isReg === 0 ? -1 : 1
  controller.simulcastInfo = data
  bfglob.emit('simulcastInfo', data)

  // 拷贝上级控制器的functions，以便判断是否支持状态监控
  const parentController = bfglob.gcontrollers.get(controller.simulcastParent)
  if (parentController) {
    controller.functions = cloneDeep(parentController.functions)
  }
  bfglob.emit('update_controller_stats', controller)
}

export function processInfoReport(rpc_cmd) {
  // 解码 ClientRepeatorInfoReport
  const data = (rpc_cmd.body = bfproto.decodeMessage(
    rpc_cmd.body,
    'ClientRepeatorInfoReport',
    kcpPackageName,
  ))
  bfglob.console.log('processInfoReport:', rpc_cmd)

  // 将同播中继注册的信息缓存到对应的设备中
  saveSimulcastInfo(data)
}

// 查询当前已经注册的同播中继当前信息
export async function querySimulcastRepeaterInfo(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.CLIENT_INFO,
    res: 1,
  })

  const rpcCmd = await writeInData([], opts)

  return bfproto.decodeMessage(
    rpcCmd.body.ConfigDataBody,
    'ClientRepeatorInfoReport',
    opts.packageName || kcpPackageName,
  )
}

/**控制从机 发射功率切换**/
export async function switchPower(data, options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.TX_POWER_SWITCH,
  })

  const RepeatorConfigTxPowerSwitch = bfproto.bfdx_proto_msg_T(
    'RepeatorConfigTxPowerSwitch',
    opts.packageName || kcpPackageName,
  )
  const body = RepeatorConfigTxPowerSwitch.encode(
    RepeatorConfigTxPowerSwitch.create(data),
  ).finish()

  return writeInData(body, opts)
}

/**控制从机 信道切换**/
export async function switchChannel(data, options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.CH_SWITCH,
  })

  const RepeatorConfigChSwitch = bfproto.bfdx_proto_msg_T(
    'RepeatorConfigChSwitch',
    opts.packageName || kcpPackageName,
  )
  const body = RepeatorConfigChSwitch.encode(
    RepeatorConfigChSwitch.create(data),
  ).finish()

  return writeInData(body, opts)
}

/**控制从机 信道配置**/
export async function setChannelConfig(data, options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.CH_CONFIG,
  })

  // 需要编码信道名称,writeStringU16
  const channelConfig = {
    ...data,
    chName: data.chName ? writeStringU16(data.chName) : [],
  }

  const RepeatorConfigChInfo = bfproto.bfdx_proto_msg_T(
    'RepeatorConfigChInfo',
    opts.packageName || kcpPackageName,
  )
  const body = RepeatorConfigChInfo.encode(
    RepeatorConfigChInfo.create(channelConfig),
  ).finish()

  return writeInData(body, opts)
}

// 查询信道配置
export async function queryChannelConfig(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.CH_CONFIG,
    res: 1,
  })

  const rpcCmd = await writeInData([], opts)
  const channelConfig = bfproto.decodeMessage(
    rpcCmd.body.ConfigDataBody,
    'RepeatorConfigChInfo',
    opts.packageName || kcpPackageName,
  )
  // 需要解码信道名称,readStringU16
  channelConfig.chName = channelConfig.chName.length
    ? readStringU16(channelConfig.chName, 0, channelConfig.chName.length)
    : ''
  // 返回信道的配置
  return channelConfig
}

/**控制从机 重启从机中继**/
export async function restartSimulcastRepeater(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.DEVICE_REBOOT,
  })

  return writeInData([], opts)
}

/**控制从机 加入同频同播系统**/
export async function registrySimulcastRepeater(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.CFCB_SYS_ADD,
  })

  return writeInData([], opts)
}

/**控制从机 移除同频同播系统**/
export async function removeSimulcastRepeater(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.CFCB_SYS_REMOVE,
  })

  return writeInData([], opts)
}

export default {
  writeInData,
  processInfoReport,
  switchPower,
  switchChannel,
  setChannelConfig,
  restartSimulcastRepeater,
  registrySimulcastRepeater,
  removeSimulcastRepeater,
  queryChannelConfig,
  querySimulcastRepeaterInfo,
}
