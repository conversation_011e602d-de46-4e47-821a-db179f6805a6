import i18n from '@/modules/i18n'
import bfproto, { kcpPackageName } from '@/modules/protocol'
import bfnotify from '@/utils/notify'
import { getReplySubject, parseDmrId, PbSubject, Timeout } from './common'
import * as Models from '@/writingFrequency/interphone/models'
import { SnNumber } from '@/writingFrequency/SnNmber'

export const DefModel = Models.TR805005
export const TR925Models = [
  Models.TR900M,
  Models.TR90FR,
  Models.TR925D,
  Models.TR925M,
  Models.TR850M,
  Models.RR850M,
  Models.RR900M,
  Models.BR105M,
]

export default {
  // 创建中继写频rpcCmd相关字段方法
  getDefaultParaBinData() {
    return {
      operation: 5,
      tableId: 1,
      objIndex: 0,
      objNum: 0,
    }
  },
  createParaBinData(paraBin, packageName = kcpPackageName) {
    const operationInfo = bfproto.bfdx_proto_msg_T(
      'RepeatorOperationInfo',
      packageName,
    )
    const operationInfoObj = operationInfo.create()

    Object.assign(operationInfoObj, this.getDefaultParaBinData(), paraBin)

    return operationInfo.encode(operationInfoObj).finish()
  },
  createRpcCmdFields({ sid, paraInt, paraBin, packageName }) {
    return {
      // 将16进制的dmrId转成10进制数字
      sid: parseDmrId(sid),
      paraInt: paraInt,
      paraBin: this.createParaBinData(paraBin, packageName),
    }
  },

  // 中继写频读、写命令
  ioRepeaterSetting(data, options) {
    return new Promise((resolve, reject) => {
      const opts = Object.assign(
        {
          rpcCmdFields: {},
          packageName: kcpPackageName,
          stream: false,
        },
        options,
      )
      const replyTo = getReplySubject(opts.rpcCmdFields.paraInt)
      bfglob.console.log('ioRepeaterSetting:', data, opts)

      opts.subSid = bfglob.server.subscribe(replyTo, rpc_cmd_obj => {
        // 修正this指向
        typeof options.resFunc === 'function' &&
          options.resFunc.call(this, resolve, reject, rpc_cmd_obj, opts)
      })
      const unsubscribe = err => {
        const subSid =
          typeof opts.subSid === 'undefined' ? replyTo : opts.subSid
        bfglob.server.unsubscribe(subSid)
        reject(err || opts)
      }
      const timeoutUnsubscribe = () => {
        unsubscribe()
        bfnotify.messageBox(i18n.global.t('msgbox.selError'), 'error')
      }

      opts.timers = setTimeout(timeoutUnsubscribe, Timeout * 1000)

      bfproto
        .sendMessage(5, data, opts.decodeMsgType, PbSubject, opts)
        .catch(res => {
          // cmd=5,没有直接的应答，故总是会超时，需要过滤掉命令的超时的处理
          if (typeof res === 'object' && res.replyTo && res.subject) {
            return
          }
          unsubscribe(res)
        })
    })
  },
  clearTimeoutOfRepeaterCmd(timers) {
    timers && clearTimeout(timers)
  },

  // 查询中继相关配置信息
  decodeRpcParaBin(bytes, packageName = kcpPackageName) {
    const operationInfo = bfproto.bfdx_proto_msg_T(
      'RepeatorOperationInfo',
      packageName,
    )
    return operationInfo.decode(bytes)
  },
  queryRepeaterSettingResponse(resolve, reject, rpc_cmd_obj, opts) {
    rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(rpc_cmd_obj, opts.packageName)

    // 服务器的响应
    switch (rpc_cmd_obj.res) {
      case -10000:
        // 服务器无法发送到中继
        reject(rpc_cmd_obj.paraInt)
        bfnotify.messageBox(i18n.global.t('msgbox.targetNotOnline'), 'error')
        return
      case 10000:
        // 服务器发送命令成功
        // bfnotify.messageBox(i18n.global.t("msgbox.sendSuccess"), 'success');
        return
    }

    // 正常接收中继响应
    // 清除超时处理
    this.clearTimeoutOfRepeaterCmd(opts.timers)

    switch (rpc_cmd_obj.res) {
      case -1:
        // -1:失败,其它原因情况
        reject(rpc_cmd_obj)
        bfnotify.messageBox(i18n.global.t('msgbox.selError'), 'error')
        break
      case -2:
        // -2:失败,对象不存在(查询/添加/修改/删除)
        reject(rpc_cmd_obj)
        bfnotify.messageBox(
          i18n.global.t('msgbox.repeaterQueryFailed'),
          'error',
        )
        break
      case -4:
        // -4:失败,参数有误
        reject(rpc_cmd_obj)
        bfnotify.messageBox(
          i18n.global.t('msgbox.repeaterParametersError'),
          'error',
        )
        break
      case 1:
        // 1:应答/操作成功
        !opts.stream &&
          bfnotify.messageBox(i18n.global.t('msgbox.selSuccess'), 'success')
        break
    }

    // 解码 RepeatorOperationInfo
    rpc_cmd_obj.paraBin = this.decodeRpcParaBin(
      rpc_cmd_obj.paraBin,
      opts.packageName,
    )
    // 解码body数据
    rpc_cmd_obj.body = bfproto.decodeMessage(
      rpc_cmd_obj.body,
      opts.decodeMsgType,
      opts.packageName,
    )

    // 中继多次响应数据流，需要检测是否全部接收，如查询全部信道
    const subject = getReplySubject(rpc_cmd_obj.paraInt)
    const unselectable = () => {
      bfglob.server.unsubscribe(opts.subSid)
      bfglob.off(subject)
      bfglob.off(`failed:${subject}`)
    }
    const checkPacketIsLoss = () => {
      const flag = Date.now()
      opts.flag = flag

      setTimeout(() => {
        // 被新的响应数据重置flag，停止执行
        if (opts.flag !== flag) {
          return
        }

        // 最后一个包时没有重置flag，判断是否全部接收完毕
        if (rpc_cmd_obj.paraBin.objNum === opts.count) {
          return
        }

        unselectable()
        // 没有全部接收数据，向用户提示
        bfglob.emit(`failed:${subject}`, rpc_cmd_obj, opts)
        bfnotify.warningBox(
          this.$t('msgbox.tryAgainQueryChannelData'),
          'warning',
        )
      }, 3000)
    }

    if (opts.stream) {
      // 对象引用，可记录接收到的数据包的次数
      if (typeof opts.count !== 'number') {
        opts.count = 0
      }
      // 标记接收的次数
      opts.count++

      // 发布接收到数据的消息事件
      bfglob.emit(
        subject,
        rpc_cmd_obj,
        rpc_cmd_obj.paraBin.objNum === opts.count,
      )

      // 完整接收所有数据，结束相关订阅事件
      if (rpc_cmd_obj.paraBin.objNum === opts.count) {
        bfnotify.messageBox(i18n.global.t('msgbox.selSuccess'), 'success')
        unselectable()
      } else {
        checkPacketIsLoss()
      }
    } else {
      // 单次查询，向上传递数据并取消消息事件
      resolve(rpc_cmd_obj)
      bfglob.server.unsubscribe(opts.subSid)
    }
  },
  queryRepeaterSetting(options) {
    const opts = {
      ...options,
      rpcCmdFields: this.createRpcCmdFields(options),
      resFunc: this.queryRepeaterSettingResponse,
      packageName: options.packageName || kcpPackageName,
    }

    return this.ioRepeaterSetting(null, opts)
  },

  // 中继写频写入数据命令
  decodeResponseRpcCmdObj(rpc_cmd_obj, opts) {
    // 解码 RepeatorOperationInfo
    rpc_cmd_obj.paraBin = this.decodeRpcParaBin(
      rpc_cmd_obj.paraBin,
      opts.packageName,
    )
    bfglob.console.log('writer repeater response:', rpc_cmd_obj, opts)

    const subject = this.repeaterReplyPrefix + rpc_cmd_obj.paraInt
    const subSid = typeof opts.subSid === 'undefined' ? subject : opts.subSid
    bfglob.server.unsubscribe(subSid)

    return rpc_cmd_obj
  },
  showOperationSuccessMessage(rpc_cmd_obj) {
    switch (rpc_cmd_obj.paraBin.operation) {
      case 8:
      case 9:
        break
      case 11:
        bfnotify.messageBox(
          i18n.global.t('msgbox.switchRepeaterChannelSuccess'),
          'success',
        )
        break
      case 12:
        bfnotify.messageBox(
          i18n.global.t('msgbox.switchRepeaterFrequencySuccess'),
          'success',
        )
        break
      default:
        bfnotify.messageBox(
          i18n.global.t('msgbox.repeaterWriteSuccess'),
          'success',
        )
    }
  },
  showOperationFailedMessage(rpc_cmd_obj) {
    switch (rpc_cmd_obj.paraBin.operation) {
      case 11:
        bfnotify.messageBox(
          i18n.global.t('msgbox.switchRepeaterChannelFailed'),
          'warning',
        )
        break
      case 12:
        bfnotify.messageBox(
          i18n.global.t('msgbox.switchRepeaterFrequencyFailed'),
          'warning',
        )
        break
      default:
        bfnotify.messageBox(
          i18n.global.t('msgbox.repeaterWriteFail'),
          'warning',
        )
    }
  },
  writeRepeaterSettingResponse(resolve, reject, rpc_cmd_obj, opts) {
    rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(rpc_cmd_obj, opts.packageName)

    switch (rpc_cmd_obj.res) {
      case -10000:
        // 服务器无法发送到中继
        reject(rpc_cmd_obj.paraInt)
        bfnotify.messageBox(i18n.global.t('msgbox.targetNotOnline'), 'error')
        return
      case 10000:
        // 服务器发送命令成功
        // bfnotify.messageBox(i18n.global.t("msgbox.sendSuccess"), 'success');
        return
    }

    // 清除超时处理
    this.clearTimeoutOfRepeaterCmd(opts.timers)

    rpc_cmd_obj = this.decodeResponseRpcCmdObj(rpc_cmd_obj, opts)

    switch (rpc_cmd_obj.res) {
      case -2:
        // -2:失败,对象不存在(查询/添加/修改/删除)
        reject(rpc_cmd_obj)
        bfnotify.messageBox(
          i18n.global.t('msgbox.repeaterQueryFailed'),
          'error',
        )
        break
      case -3:
        // -3:失败,删除对象为当前信道
        reject(rpc_cmd_obj)
        bfnotify.messageBox(
          i18n.global.t('msgbox.deleteChannelFailed'),
          'warning',
        )
        break
      case -4:
        // -4:失败,参数有误
        reject(rpc_cmd_obj)
        bfnotify.messageBox(
          i18n.global.t('msgbox.repeaterParametersError'),
          'error',
        )
        break
      case -1:
        // -1:失败,其它原因情况
        this.showOperationFailedMessage(rpc_cmd_obj)
        reject(rpc_cmd_obj)
        break
      case 1:
        // 中继应答，指令操作成功
        resolve(rpc_cmd_obj)
        this.showOperationSuccessMessage(rpc_cmd_obj)
        break
    }
  },

  // 通用写入数据方法
  writeInData(data, options) {
    const opts = {
      ...options,
      rpcCmdFields: this.createRpcCmdFields(options),
      resFunc: this.writeRepeaterSettingResponse,
      packageName: options.packageName || kcpPackageName,
    }

    return this.ioRepeaterSetting(data, opts)
  },

  // 通用查询中继配置方法，返回解码后rpc_cmd_obj.body
  queryConfig(options) {
    return this.queryRepeaterSetting(options).then(res => {
      return bfproto.copyFieldsFromProto(
        res.body,
        options.packageName || kcpPackageName,
      )
    })
  },

  // 解码中继设备信息中的sn码
  snDecode(uint8Arr) {
    const sn = Array.from(uint8Arr)
      .map(int => {
        return int.toString(16).padStart(2, '0')
      })
      .join('')

    return SnNumber.TryCovertToSnNumberString(sn)
  },
}
