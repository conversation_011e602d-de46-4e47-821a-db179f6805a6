// 模拟枚举对象
export function enumObject(source) {
  const ValuesById = {}
  const Values = Object.create(ValuesById)
  Object.keys(source).forEach(k => {
    const v = source[k]
    Values[(ValuesById[v] = k)] = v
  })

  return Values
}

// 中继写频，与服务器交互的主题
export const PbSubject = `repeater.${bfglob.sysId}`

// 接收中继的响应结果的主题为: `repeater${rpcCmd.paraInt}`
const ReplyToPrefix = 'repeater'

export function getReplySubject(paraInt) {
  return `${ReplyToPrefix}${paraInt}`
}

// 默认的交互超时时长，60s
export const Timeout = 60

// 16进制dmrId转换为10进制
export function parseDmrId(dmrId) {
  return typeof dmrId === 'string' ? parseInt(dmrId, 16) : dmrId
}
