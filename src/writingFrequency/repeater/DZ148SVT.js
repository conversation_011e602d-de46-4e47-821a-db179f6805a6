import i18n from '@/modules/i18n'
import bfproto, { kcpPackageName } from '@/modules/protocol'
import { toHexDmrId } from '@/utils/bfutil'
import bfnotify from '@/utils/notify'
import { cloneDeep } from 'lodash'
import {
  enumObject,
  getReplySubject,
  parseDmrId,
  PbSubject,
  Timeout,
} from './common'

const DefaultCMD = enumObject({
  /**无**/
  NONE: 0,
  /**服务器获取从机中继信息 **/
  CLIENT_INFO: 1,
  /**设置信道信息**/
  CH_CONFIG: 2,
  /**切换信道**/
  CH_SWITCH: 3,
  /**切换发射功率**/
  TX_POWER_SWITCH: 4,
  /**重启中继**/
  DEVICE_REBOOT: 5,
  /**加入同频同播系统**/
  CFCB_SYS_ADD: 6,
  /**移除同频同播系统**/
  CFCB_SYS_REMOVE: 7,
  /**虚拟集群中继信息**/
  SVT_RELAY_INFO: 8,
  /**虚拟集群中继配置**/
  SVT_RELAY_CFG: 9,
  /**虚拟集群中继状态**/
  SVT_RELAY_STATUS: 10,
})
export const DefModel = 'DZ148SVT'
export const CLIENT_CMD = (function () {
  const RepeatorConfig = bfproto.bfdx_proto_msg_T(
    'EPDB_CTRL_CLIENT_CMD',
    kcpPackageName,
  )
  if (!RepeatorConfig) {
    return DefaultCMD
  }
  return RepeatorConfig.values
})()

function clearWriteTimeout(timers) {
  timers && clearTimeout(timers)
}

function decodeRepeatorConfig(rpc_cmd_obj) {
  // 解码 RepeatorConfig
  const RepeatorConfig = bfproto.bfdx_proto_msg_T(
    'RepeatorConfig',
    kcpPackageName,
  )
  rpc_cmd_obj.body = RepeatorConfig.decode(rpc_cmd_obj.body)

  return rpc_cmd_obj
}

function writeResponse(rpc_cmd, options) {
  return new Promise((resolve, reject) => {
    // 清除超时处理
    clearWriteTimeout(options.timers)

    // 解析出 bfkcp.rpc_cmd
    const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(
      rpc_cmd,
      options.packageName,
    )
    // 取消命令主题订阅
    const subSid =
      typeof options.subSid === 'undefined'
        ? getReplySubject(rpc_cmd_obj.paraInt)
        : options.subSid

    switch (rpc_cmd_obj.res) {
      case -10000:
        // 服务器无法发送到中继
        reject(rpc_cmd_obj.paraInt)
        bfnotify.messageBox(i18n.global.t('msgbox.targetNotOnline'), 'error')
        bfglob.server.unsubscribe(subSid)
        return
      case 10000:
        // 服务器发送命令成功
        // bfnotify.messageBox(i18n.global.t("msgbox.sendSuccess"), 'success');
        return
    }

    // 需要等中继返回再取消订阅
    bfglob.server.unsubscribe(subSid)

    // 解析出body内容
    decodeRepeatorConfig(rpc_cmd_obj)
    bfglob.console.log('[DZ1480] writeResponse:', rpc_cmd_obj)

    switch (rpc_cmd_obj.res) {
      case 1:
      case 2:
        // 中继应答，指令操作成功
        resolve(rpc_cmd_obj)
        break
      case -4:
        // -4:失败,参数有误
        reject(rpc_cmd_obj)
        bfnotify.messageBox(
          i18n.global.t('msgbox.repeaterParametersError'),
          'error',
        )
        break
      case -1:
        // -1:失败,其它原因情况
        // this.showOperationFailedMessage(data)
        reject(rpc_cmd_obj)
        break
      default:
        // 默认失败
        reject(rpc_cmd_obj)
    }
  })
}

function wrapperResOptions(res, options) {
  // 缓存指令操作的中继的id
  res.deviceId = options.deviceId

  return res
}

function write(data, options) {
  return new Promise((resolve, reject) => {
    const opts = Object.assign(
      {
        rpcCmdFields: {},
        packageName: kcpPackageName,
        stream: false,
      },
      options,
    )
    const replyTo = getReplySubject(opts.rpcCmdFields.paraInt)
    bfglob.console.log('[DZ1480] write:', data, opts)

    opts.subSid = bfglob.server.subscribe(replyTo, rpc_cmd_obj => {
      writeResponse(rpc_cmd_obj, opts).then(resolve).catch(reject)
    })

    const unsubscribe = err => {
      const subSid = typeof opts.subSid === 'undefined' ? replyTo : opts.subSid
      bfglob.server.unsubscribe(subSid)
      reject(err || opts)
    }
    const timeoutUnsubscribe = () => {
      unsubscribe('timeout')
      bfnotify.messageBox(i18n.global.t('msgbox.selError'), 'error')
    }

    opts.timers = setTimeout(timeoutUnsubscribe, Timeout * 1000)

    bfproto
      .sendMessage(5, data, opts.decodeMsgType, PbSubject, opts)
      .catch(res => {
        // cmd=5,没有直接的应答，故总是会超时，需要过滤掉命令的超时的处理
        if (typeof res === 'object' && res.replyTo && res.subject) {
          return
        }
        unsubscribe(res)
      })
  })
}

// int32 data_mark = 3; 	   /** 标识 0=控制器 1=服务器源 2=CPS源**/
export const RepeaterConfigDataMark = {
  Controller: 0,
  ServiceSource: 1,
  CPS: 2,
}

export async function writeInData(data, options) {
  const opts = {
    packageName: options.packageName || kcpPackageName,
    decodeMsgType: 'RepeatorConfig',
    rpcCmdFields: {},
    ...options,
  }
  // sid: 同播控制器的dmrId
  opts.rpcCmdFields.sid = parseDmrId(opts.rpcCmdFields.sid)

  // deviceId: 被操作的同播中继的dmrId
  const res = await write(
    {
      deviceId: parseDmrId(opts.deviceId),
      ConfigDataBody: data,
      dataMark: RepeaterConfigDataMark.ServiceSource,
    },
    opts,
  )

  return wrapperResOptions(res, opts)
}

/**上报从机注册（注销）信息**/
export function saveSvtRepeaterInfo(data) {
  const dmrId = toHexDmrId(data.deviceId, false)
  const controller = bfglob.gcontrollers.getDataByIndex(dmrId)
  if (!controller) {
    return
  }
  // 能收到中继103，就表示中继已经上线
  data.ctrlStats = controller.ctrlStats = data.isReg === 0 ? -1 : 1
  data.deviceCode = Array.isArray(data.deviceCode)
    ? String.fromCharCode.apply(null, data.deviceCode)
    : data.deviceCode
  controller.svtRepeaterInfo = data
  bfglob.emit('svtRepeaterInfo', data)

  // 拷贝上级控制器的functions，以便判断是否支持状态监控
  const parentController = bfglob.gcontrollers.get(controller.simulcastParent)
  if (parentController) {
    controller.functions = cloneDeep(parentController.functions)
  }
  bfglob.emit('update_controller_stats', controller)
}

// 查询当前已经注册的同播中继当前信息
export async function querySvtRepeaterInfo(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.SVT_RELAY_INFO,
    res: 1,
  })

  const rpcCmd = await writeInData([], opts)

  return bfproto.decodeMessage(
    rpcCmd.body.ConfigDataBody,
    'SvtRepeatorInfoReport',
    opts.packageName || kcpPackageName,
  )
}

// 查询中继设备状态信息
export async function querySvtRepeaterStateInfo(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.SVT_RELAY_STATUS,
    res: 1,
  })

  const rpcCmd = await writeInData([], opts)

  return bfproto.decodeMessage(
    rpcCmd.body.ConfigDataBody,
    'SvtRepeatorDevStatReport',
    opts.packageName || kcpPackageName,
  )
}

// 查询中继当前功率信息
export async function querySvtRepeaterPowerInfo(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.TX_POWER_SWITCH,
    res: 1,
  })

  const rpcCmd = await writeInData([], opts)

  return bfproto.decodeMessage(
    rpcCmd.body.ConfigDataBody,
    'RepeatorConfigTxPowerSwitch',
    opts.packageName || kcpPackageName,
  )
}

/**控制从机 发射功率切换**/
export async function switchPower(data, options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.TX_POWER_SWITCH,
  })

  const RepeatorConfigTxPowerSwitch = bfproto.bfdx_proto_msg_T(
    'RepeatorConfigTxPowerSwitch',
    opts.packageName || kcpPackageName,
  )
  const body = RepeatorConfigTxPowerSwitch.encode(
    RepeatorConfigTxPowerSwitch.create(data),
  ).finish()

  return writeInData(body, opts)
}

// 查询常规配置
export async function queryCommonSetting(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.SVT_RELAY_CFG,
    res: 1,
  })

  const rpcCmd = await writeInData([], opts)
  const config = bfproto.decodeMessage(
    rpcCmd.body.ConfigDataBody,
    'SvtRepeatorCfgPara',
    opts.packageName || kcpPackageName,
  )

  return config
}

// 设置常规配置
export async function writeCommonSetting(data, options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.SVT_RELAY_CFG,
  })

  const SvtRepeatorCfgParaT = bfproto.bfdx_proto_msg_T(
    'SvtRepeatorCfgPara',
    opts.packageName || kcpPackageName,
  )
  const body = SvtRepeatorCfgParaT.encode(
    SvtRepeatorCfgParaT.create(data),
  ).finish()

  return writeInData(body, opts)
}

/**控制从机 重启从机中继**/
export async function restartSvtRepeater(options) {
  const opts = cloneDeep(options)
  Object.assign(opts.rpcCmdFields, {
    paraInt: CLIENT_CMD.DEVICE_REBOOT,
  })

  return writeInData([], opts)
}

// 虚拟集群控制器103命令解析
export function processSvtRepeaterInfoReport(rpc_cmd) {
  // 解码 ClientRepeatorInfoReport
  const data = (rpc_cmd.body = bfproto.decodeMessage(
    rpc_cmd.body,
    'SvtRepeatorInfoReport',
    kcpPackageName,
  ))
  bfglob.console.log('processSvtRepeaterInfoReport:', rpc_cmd)
  saveSvtRepeaterInfo(data)
}

export default {
  writeInData,
  switchPower,
  querySvtRepeaterPowerInfo,
  restartSvtRepeater,
  querySvtRepeaterInfo,
  querySvtRepeaterStateInfo,
  processSvtRepeaterInfoReport,
}
