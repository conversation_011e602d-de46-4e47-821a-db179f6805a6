<template>
  <el-dialog
    id="bfalarmHistory"
    v-model="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    class="alarm_dialog drag-dialog"
    modal-class="drag-dialog-modal"
    :fullscreen="fullscreen"
    @close="closeDlgFn"
    @open="openDlgFn"
  >
    <template #header>
      <div v-bfdrag>
        {{ $t('dataTable.emergency') }}
      </div>
    </template>
    <el-form :model="emergencyForm" class="alarm" :label-width="labelWidth">
      <div v-if="isSimpleDevice" class="userImageFile">
        <img :src="emergencyForm.userImageContent" />
      </div>
      <el-form-item :label="$t('dataTable.alarmTime')">
        <div class="time" v-text="emergencyForm.alarmTime" />
      </el-form-item>
      <el-form-item
        v-if="isSimpleDevice"
        :label="$t('dataTable.alarmUserName')"
      >
        <div class="userName" v-text="emergencyForm.userInfo" />
      </el-form-item>
      <el-form-item :label="$t('dataTable.alarmDevice')">
        <div class="deviceSelfId" v-text="emergencyForm.deviceSelfId" />
      </el-form-item>
      <el-form-item :label="$t('dataTable.alarmConditions')">
        <el-input
          v-model="emergencyForm.alarmConditions"
          type="textarea"
          :autosize="{
            minRows: 2,
            maxRows: 5,
          }"
          :placeholder="$t('dataTable.inputcontent')"
        />
      </el-form-item>
      <el-form-item :label="$t('dataTable.processContext')">
        <el-input
          v-model="emergencyForm.deallerResult"
          type="textarea"
          :autosize="{
            minRows: 2,
            maxRows: 5,
          }"
          :placeholder="$t('dataTable.inputcontent')"
        />
      </el-form-item>
      <el-form-item :label="$t('dataTable.processor')">
        <el-input v-model="emergencyForm.deallerName" name="deallerName" />
      </el-form-item>
      <el-form-item label-width="0" class="footer">
        <el-button
          type="primary"
          @click="cancelTheEmergencyAlarm"
          v-text="$t('dialog.clearAlarm')"
        />
        <el-button
          type="primary"
          @click="stopAlarmVoice"
          v-text="$t('dialog.stopAlarmSound')"
        />
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { SupportedLang } from '@/modules/i18n'
import bfprocess from '@/utils/bfprocess'
import { getIotCmdLabel, IotDeviceTypeList } from '@/utils/iot'
import bfTime from '@/utils/time'
import defaultUserPng from '@/images/sysImg/default_user.png'

export default {
  props: {
    cmdTime: {
      type: String,
      required: true,
    },
    dbRid: {
      type: String,
      required: true,
    },
    device: {
      type: Object,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      visible: true,
      emergencyForm: {},
    }
  },
  methods: {
    openDlgFn() {},
    closeDlgFn() {
      this.destroyDialog()
    },
    AfterCancelAlarmFn(name) {
      if (this.name !== name) return

      // 取消报警订阅事件
      this.clearCancelAlarmEvent()

      // 停止声音播放
      bfglob.emit('play_alarm_voice', false)

      // 添加报警历史记录
      const alarm_result = {
        alarmConditions: this.emergencyForm.alarmConditions,
        deallerResult: this.emergencyForm.deallerResult,
        deallerName: this.emergencyForm.deallerName,
      }
      bfprocess.add_alarm_history(
        this.dbRid,
        JSON.stringify(alarm_result),
        this.emergencyForm.alarmTime,
      )
    },
    cancelCb10Alarm() {
      const target = {
        groud: [],
        device: [this.device.dmrId],
      }
      bfprocess.cb10(target, this.cancelAlarmCount > 0)
    },
    clearCancelAlarmEvent() {
      this.cancelAlarmCount = 0
      if (this.cancelCb10AlarmTimer) {
        clearTimeout(this.cancelCb10AlarmTimer)
        this.cancelCb10AlarmTimer = undefined
      }
      bfglob.off('destroy_alarm_dialog_of_bc00', this.AfterCancelAlarmFn)
    },
    cancelTheEmergencyAlarm() {
      if (this.isIotDevice) {
        // todo 物联终端解除报警
        return
      }

      // 先清除之前解析报警的定时器和订阅的事件
      this.clearCancelAlarmEvent()

      // 订阅解除报警成功消息
      bfglob.on('destroy_alarm_dialog_of_bc00', this.AfterCancelAlarmFn)

      // 解除报警
      const timeout = 5 * 1000
      const cancelAlarm = () => {
        this.cancelCb10Alarm()
        this.cancelAlarmCount++

        // 定时器检查是否已经解除报警
        this.cancelCb10AlarmTimer = setTimeout(() => {
          if (!this.visible || this.cancelAlarmCount > 3) {
            this.clearCancelAlarmEvent()
            return
          }

          cancelAlarm()
        }, timeout)
      }
      cancelAlarm()
    },
    stopAlarmVoice() {
      bfglob.emit('play_alarm_voice', false)
    },
    destroyDialog() {
      this.visible = false
      // 发布销毁消息，清除app vue实例中的相关数据
      bfglob.emit('alarm_destroyDialog', this.name)
    },
  },
  mounted() {
    this.emergencyForm = {
      alarmTime: bfTime.getLocalTimeString(
        bfTime.utcTimeToLocalTime(this.cmdTime),
      ),
      userInfo: this.device.userName
        ? this.device.orgShortName + '/' + this.device.userName
        : this.device.orgShortName,
      deviceSelfId: this.isIotDevice ? this.device.devName : this.device.selfId,
      alarmConditions: this.isIotDevice
        ? getIotCmdLabel(this.device.lastCmd)
        : '',
      deallerResult: '',
      deallerName: bfglob.userInfo.name,
      userImageContent: this.device.userImageFile || defaultUserPng,
    }
    bfglob.on(
      'destroy_alarm_dialog_of_bc00',
      function (name) {
        this.name === name && this.destroyDialog()
      }.bind(this),
    )
    this.openDlgFn()
  },
  watch: {
    cmdTime: {
      handler(newVal) {
        this.emergencyForm.alarmTime = bfTime.getLocalTimeString(
          bfTime.utcTimeToLocalTime(newVal),
        )
      },
    },
    device: {
      deep: true,
      handler(newVal) {
        if (IotDeviceTypeList.includes(newVal.devType)) {
          // this.emergencyForm.userInfo = newVal.orgId
          this.emergencyForm.deviceSelfId = newVal.devName
          return
        }
        this.emergencyForm.userInfo = newVal.userName
          ? newVal.orgShortName + '/' + newVal.userName
          : newVal.orgShortName
        this.emergencyForm.deviceSelfId = newVal.selfId
        this.emergencyForm.userImageContent =
          newVal.userImageFile || defaultUserPng
      },
    },
  },
  computed: {
    fullscreen() {
      return !(this.$root.layoutLevel > 0)
    },
    isSimpleDevice() {
      return !this.isIotDevice
    },
    isIotDevice() {
      return IotDeviceTypeList.includes(this.device.devType)
    },
    locale() {
      return this.$i18n.locale
    },
    isFR() {
      return this.locale === SupportedLang.fr
    },
    isEN() {
      return this.locale === SupportedLang.enUS
    },
    labelWidth() {
      return this.isFR ? '140px' : this.isEN ? '120px' : '100px'
    },
  },
}
</script>

<style lang="scss">
.el-dialog.alarm_dialog {
  transform: initial;
  width: 460px;
  left: 0;

  .el-dialog__body {
    .alarm.el-form {
      position: relative;

      .el-form-item {
        margin-bottom: 8px;

        &.footer {
          .el-form-item__content {
            display: flex;
            justify-content: center;
            line-height: 36px;
          }
        }
      }

      .userImageFile {
        width: 60px;
        height: 60px;
        position: absolute;
        top: 0;
        right: 0;
        overflow: hidden;

        img {
          width: 100%;
          border-radius: 6px;
        }
      }
    }
  }
}
</style>
