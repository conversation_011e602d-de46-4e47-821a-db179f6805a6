<template>
  <el-dialog
    v-model="visible"
    :title="$t('nav.version')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    class="versionInfo"
  >
    <div
      v-for="(item, i) in versions"
      :key="i"
      :class="['version-box', locale]"
    >
      <div class="version-box-item">
        <span class="version-label" v-text="$t(`nav.${i}Version`) + ':'" />
        <span class="version-info" v-text="item.version" />
      </div>
      <div class="version-box-item">
        <span class="version-label" v-text="$t('nav.versionBuildTime') + ':'" />
        <span class="version-info" v-text="item.buildTime" />
      </div>
      <div class="version-box-item">
        <span class="version-label" v-text="$t('nav.versionGitTag') + ':'" />
        <span class="version-info" v-text="item.gitTag.slice(0, 8)" />
      </div>
      <div v-show="item.startRuningTime" class="version-box-item">
        <span class="version-label" v-text="$t('nav.startRuningTime') + ':'" />
        <span class="version-info" v-text="item.startRuningTime" />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import bfTime from '@/utils/time'
import pkgJson from '~/package.json'
import vueMixin from '@/utils/vueMixin'

const version = pkgJson.version

export default {
  name: 'BfVersion',
  mixins: [vueMixin],
  data() {
    return {
      visible: false,
      // CLIENT_BUILD_TIME,CLIENT_GIT_TAG, webpack.DefinePulgin define
      versions: {
        client: {
          version: version || '',
          buildTime: CLIENT_BUILD_TIME || '',
          gitTag: CLIENT_GIT_TAG || '',
          startRuningTime: '',
        },
        server: {
          version: '',
          buildTime: '',
          gitTag: '',
          startRuningTime: '',
        },
      },
    }
  },
  methods: {
    reloadServerVersion() {
      fetch('/server_version')
        .then(res => res.text())
        .then(data => {
          const res = data.match(
            /ver:(?<version>[0-9.].*?)\s?,?\s?build:(?<buildTime>\w*\S.*?),?\s?Git:(?<gitTag>\w*),?\s?Start:(?<startRuningTime>\w*\S.*?),?\s?UTC/,
          )
          // IE，EDGE等浏览器不支持正则分组命名，生成对象
          if (!res.groups) {
            res.groups = {
              version: res[1],
              buildTime: res[2],
              gitTag: (res[3] || '').slice(0, 8),
              startRuningTime: res[4],
            }
          }
          this.versions.server = { ...res.groups }
          this.versions.server.buildTime = bfTime.localToUtcTime(
            this.versions.server.buildTime,
          )
          this.versions.server.buildTime += ' UTC'
          this.versions.server.startRuningTime += ' UTC'
        })
        .catch(err => {
          console.error(err)
        })
    },
    serverReConnect() {
      this.reloadServerVersion()
    },
  },
  beforeMount() {
    // 在窗口打开时请求系统服务器的版本信息
    this.reloadServerVersion()
    bfglob.on('server.reconnect', this.serverReConnect)
  },
  beforeUnmount() {
    bfglob.off('server.reconnect', this.serverReConnect)
  },
}
</script>

<style lang="scss">
.versionInfo {
  width: 450px;

  .el-dialog__body {
    .version-box {
      line-height: 30px;

      &:not(:last-child) {
        margin-bottom: 18px;
      }

      .version-box-item {
        text-align: left;

        .version-label {
          width: 160px;
          display: inline-block;
          text-align: right;
          padding-right: 10px;
        }
      }

      &.fr {
        .version-box-item .version-label {
          width: 180px;
        }
      }
    }
  }
}
</style>
