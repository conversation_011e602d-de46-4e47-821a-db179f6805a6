<template>
  <div class="debug-logs-wrap">
    <!-- 设置minItemSize为66（3行），systemLogs添加频率过快，导致部分log渲染时高度还未计算出来，下一条就开始渲染了，导致下一条第一行的上一条的第二行重叠了-->
    <DynamicScroller
      ref="systemLogsContainer"
      class="scroller debug-logs-container"
      :items="systemLogs"
      :minItemSize="66"
    >
      <template #default="{ item, index, active }">
        <DynamicScrollerItem
          :item="item"
          :active="active"
          :data-index="index"
          :size-dependencies="[item.text]"
        >
          {{ item.text }}
        </DynamicScrollerItem>
      </template>
    </DynamicScroller>
    <div class="debug-logs-btns">
      <el-button
        type="danger"
        :disabled="!systemLogs.length"
        @click="cleanAllLogs"
      >
        {{ $t('dialog.cleanLogs') }}
      </el-button>
      <el-button
        type="primary"
        :disabled="!systemLogs.length"
        icon="download"
        @click="downloadLogs"
      >
        {{ $t('dialog.export') }}
      </el-button>
    </div>
  </div>
</template>

<script>
import base64js from 'base64-js'
import bfutil from '@/utils/bfutil'
import { nowLocalTime } from '@/utils/time'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { throttle } from 'lodash'

// const logSubject = bfglob.sysId + '.debug.*'
const logSubject = 'bflog'

export default {
  name: 'SystemLog',
  data() {
    return {
      bfmaxi: false,
      bfmini: {
        tooltip: 'nav.systemLog',
        icon: 'icon-notes',
        subject: 'open_vsystemLog',
      },
      systemLogs: [],
      logId: 0,
      nats_ssid: undefined,
      isKeepEnd: true,
    }
  },
  mounted() {
    this.getLogs()
  },
  methods: {
    nextLogId() {
      if (this.logId >= Number.MAX_SAFE_INTEGER) {
        this.logId = 0
      }
      return this.logId++
    },
    getLogs() {
      if (!this.nats_ssid) {
        bfglob.server.subscribe(logSubject, this.onDebugLogs)
      }
    },
    close() {
      this.nats_ssid && bfglob.server.unsubscribe(this.nats_ssid)
      this.nats_ssid = undefined
      this.cleanAllLogs()
    },
    scrollToBottom() {
      this.$nextTick(() => {
        // 滚动条不需要保持到底部
        if (!this.isKeepEnd) {
          return
        }

        if (!this.$refs.systemLogsContainer) {
          return
        }
        // this.$refs.systemLogsContainer.$el.scrollTop = this.$refs.systemLogsContainer.$el.scrollHeight
        this.$refs.systemLogsContainer.scrollToBottom()
      })
    },
    onDebugLogs(msg_data, msg_reply, msg_subject, nats_ssid) {
      try {
        // 判断滚动条是否保持到底部
        const el = this.$refs.systemLogsContainer?.ctx?.$el
        if (el) {
          this.isKeepEnd = el.clientHeight + el.scrollTop + 4 >= el.scrollHeight
        }

        // 解码日志消息
        const logMsg = bfutil.uint8array2string(base64js.toByteArray(msg_data))
        this.nats_ssid = nats_ssid
        const log = {
          id: this.nextLogId(),
          time: Date.now(),
          text: logMsg,
        }

        this.systemLogs.push(Object.freeze(log))
        // 滚动条保持到底部
        this.scrollToBottom()
      } catch (e) {
        bfglob.console.log('onDebugLogs err:', e)
      }
    },
    cleanAllLogs() {
      this.systemLogs = []
      this.logId = 0
    },
    downloadLogs() {
      const systemLogs = this.systemLogs
        .map(log => {
          return log.text
        })
        .join('')
      bfutil.saveAsFile(
        `systemLogs-${nowLocalTime('YYYYMMDD_HHmmss')}.log`,
        systemLogs,
        'text/plain',
      )
    },
    // 定时清除日志，只保留最后的200条记录
    checked_notes_logs() {
      const len = this.systemLogs.length
      if (len > 400) {
        this.systemLogs.splice(0, len - 200)
      }
    },
  },
  computed: {
    fullscreen() {
      return this.bfmaxi ? true : !(this.$root.layoutLevel > 0)
    },
  },
  beforeMount() {
    this.scrollToBottom = throttle(this.scrollToBottom, 120)
    // 3分钟检测一次日志数量
    setInterval(this.checked_notes_logs, 3 * 60 * 1000)
  },
  beforeUnmount() {
    this.nats_ssid && bfglob.server.unsubscribe(this.nats_ssid)
    this.close()
  },
  components: {
    DynamicScroller,
    DynamicScrollerItem,
  },
}
</script>

<style lang="scss">
.debug-logs-wrap {
  height: 90%;

  .debug-logs-container {
    height: 100%;
    overflow: auto;
    text-align: left;
    padding: 10px 15px;
    flex: auto;
    line-height: 22px;
    top: 0 !important;

    .vue-recycle-scroller__item-view {
      &:not(:last-child) {
        border-bottom: 1px solid #ebeef5;
      }

      & > div {
        white-space: normal;
        word-wrap: break-word;
        word-break: break-all;
      }
    }
  }

  .debug-logs-btns {
    border-top: 1px solid #ebeef5;
    padding: 10px 15px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  &.is-fullscreen .debug-logs-container {
    height: 100%;
  }
}
</style>
