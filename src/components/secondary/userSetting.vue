<template>
  <el-dialog
    v-model="visible"
    :title="$t('header.setting')"
    :fullscreen="fullscreen"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    class="flex flex-col setting-dialog"
    @open="onOpen"
  >
    <el-tabs type="border-card">
      <el-tab-pane
        :label="$t('header.userSet')"
        class="flex flex-wrap justify-between personal-setting-tab-pane"
      >
        <div
          v-for="(item, index) in settings"
          :key="index"
          class="personal-setting-item"
        >
          <el-checkbox
            v-model="userSetting[item.key]"
            :label="item.label"
            :disabled="item.disabled"
            @change="confirm_userSettings(item)"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('header.sysSet')" class="system-setting-tab-pane">
        <el-form ref="sysSet" :model="sysSet" label-width="100px">
          <el-form-item :label="$t('header.logo')" class="upload-logo">
            <el-upload
              class="upload-input"
              action=""
              :multiple="false"
              :before-upload="before_upload"
              :show-file-list="false"
            >
              <el-button type="primary">
                <span v-text="$t('dialog.selectImage')" />
              </el-button>
              <template #tip>
                <div class="el-upload__tip sys_title_prompt">
                  <p v-text="$t('dialog.picSize')" />
                  <p v-text="$t('dialog.selImgPrompt')" />
                </div>
              </template>
            </el-upload>
            <div class="sys_logo_img">
              <img :src="sysSet.clientLogo || defaultLogo" />
            </div>
          </el-form-item>
          <el-form-item :label="$t('header.moveText')" prop="clientTitle">
            <el-input
              v-model="sysSet.clientTitle"
              :maxlength="32"
              :autofocus="true"
              type="textarea"
              :rows="3"
              resize="none"
              show-word-limit
            />
          </el-form-item>
          <div class="data_btns">
            <el-button
              type="success"
              @click="save_sys_logo_and_title"
              v-text="$t('dialog.confirm')"
            />
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane
        :label="$t('header.accountManager')"
        class="account-setting-tab-pane"
      >
        <el-form
          ref="accountSet"
          :model="account"
          :label-width="accountFormLabelWidth"
          :rules="accountRule"
        >
          <el-form-item :label="$t('header.oldPwd')" prop="oldPwd">
            <el-input
              v-model="account.oldPwd"
              type="password"
              show-password
              @keyup.enter="resetPassword"
            />
          </el-form-item>
          <el-form-item :label="$t('header.newPwd')" prop="newPwd">
            <el-input
              v-model="account.newPwd"
              type="password"
              show-password
              @keyup.enter="resetPassword"
            />
          </el-form-item>
          <el-form-item :label="$t('header.newPwd2')" prop="newPwd2">
            <el-input
              v-model="account.newPwd2"
              type="password"
              show-password
              @keyup.enter="resetPassword"
            />
          </el-form-item>
          <div class="data_btns">
            <el-button
              type="primary"
              @click="resetPassword"
              v-text="$t('dialog.confirm')"
            />
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>
import i18n, { SupportedLang } from '@/modules/i18n'
import dbCmd from '@/modules/protocol/db.pb.cmd'
import bfproto from '@/modules/protocol/index'
import bfprocess, { updateGlobalUserSettingEffect } from '@/utils/bfprocess'
import bfCrypto from '@/utils/crypto'

import bfNotify, { warningBox } from '@/utils/notify'
import { cloneDeep } from 'lodash'
import validateRules from '@/utils/validateRules'

export default {
  name: 'BfSetting',
  data() {
    return {
      visible: false,
      userSetting: cloneDeep(bfglob.userInfo.setting),
      sysSet: {
        clientLogo: bfglob.systemSetting.clientLogo,
        clientTitle: bfglob.systemSetting.clientTitle,
      },
      account: {
        oldPwd: '',
        newPwd: '',
        newPwd2: '',
      },
      defaultLogo: `/logo.${bfglob.siteConfig?.logoExt || 'jpg'}`,
    }
  },
  methods: {
    onOpen() {
      Object.assign(this.sysSet, {
        clientLogo: bfglob.systemSetting.clientLogo,
        clientTitle: bfglob.systemSetting.clientTitle,
      })
    },
    before_upload(file) {
      if (file.size >= 102400) {
        warningBox(this.$t('msgbox.selImgError'), 'error')
        return false
      }
      var that = this
      var reader = new FileReader()
      reader.onload = function (e) {
        that.sysSet.clientLogo = reader.result
      }
      reader.readAsDataURL(file)
      return false
    },
    confirm_userSettings(settingItem) {
      this.userSetting.ispUdateVoipSpeakInfo = false
      const oldSettings = cloneDeep(bfglob.userInfo.setting)
      bfprocess
        .updateUserSetting(
          JSON.stringify(this.userSetting),
          dbCmd.DB_USER_PUPDATE,
        )
        .then(() => {
          bfglob.userInfo.setting = Object.assign(
            {},
            bfglob.userInfo.setting,
            this.userSetting,
          )
          updateGlobalUserSettingEffect(bfglob.userInfo.setting, oldSettings)
          bfglob.emit(
            'update_user_settings',
            bfglob.userInfo.setting,
            oldSettings,
          )
        })
        .catch(() => {
          // 恢复选择前状态
          this.userSetting[settingItem.key] = !this.userSetting[settingItem.key]
        })
    },
    save_sys_logo_and_title() {
      var that = this
      this.$refs.sysSet.validate(function (valid) {
        if (valid) {
          // 规则验证成功，可以提交数据
          if (bfglob.systemSetting.logoRid === '') {
            // 添加LOGO
            bfprocess.saveSysLogoAndTitle(
              that.sysSet.clientLogo,
              dbCmd.DB_SYS_CONFIG_INSERT,
              'clientLogo',
            )
          } else {
            // 更新LOGO
            bfprocess.saveSysLogoAndTitle(
              that.sysSet.clientLogo,
              dbCmd.DB_SYS_CONFIG_UPDATE,
              'clientLogo',
              bfglob.systemSetting.logoRid,
            )
          }
          // 去掉滚动标题的换行符
          that.sysSet.clientTitle = that.sysSet.clientTitle.replace(/\n/g, '')
          if (bfglob.systemSetting.titleRid === '') {
            // 添加标题
            bfprocess.saveSysLogoAndTitle(
              that.sysSet.clientTitle,
              dbCmd.DB_SYS_CONFIG_INSERT,
              bfglob.systemSetting.titleConfKey,
            )
          } else {
            // 更新标题
            bfprocess.saveSysLogoAndTitle(
              that.sysSet.clientTitle,
              dbCmd.DB_SYS_CONFIG_UPDATE,
              bfglob.systemSetting.titleConfKey,
              bfglob.systemSetting.titleRid,
            )
          }

          bfNotify.messageBox(i18n.global.t('msgbox.sysSetSuccess'), 'success')
        }
      })
    },
    updateUserLoginPass() {
      const msgObj = {
        orgId: bfglob.userInfo.orgId,
        rid: bfglob.userInfo.rid,
        userLoginPass: bfCrypto.sha256(
          bfglob.userInfo.origData.userLoginName + this.account.newPwd,
        ),
      }
      const msgOpts = {
        rpcCmdFields: {
          origReqId: 'rid',
          resInfo: 'org_id,rid,user_login_pass',
        },
      }
      bfproto
        .sendMessage(
          dbCmd.DB_USER_PUPDATE,
          msgObj,
          'db_user',
          `db.${bfglob.sysId}`,
          msgOpts,
        )
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo === '+OK') {
            bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
            bfglob.userInfo.origData.userLoginPass = msgObj.userLoginPass
          } else {
            bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
          }
        })
        .catch(err => {
          bfglob.console.warn('updateUserLoginPass timeout', err)
          bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
        })
    },
    resetPassword() {
      this.$refs.accountSet.validate(valid => {
        if (valid) {
          // 难旧密码是否正确
          var pass_hash = bfCrypto.sha256(
            bfglob.userInfo.origData.userLoginName + this.account.oldPwd,
          )
          if (bfglob.userInfo.origData.userLoginPass !== pass_hash) {
            warningBox(this.$t('msgbox.oldPwdError'), 'error')
            return false
          }
          // 更新密码
          this.updateUserLoginPass()
        } else {
          return false
        }
      })
    },
    syncUserSettings(userSetting) {
      this.userSetting = cloneDeep(userSetting)
    },
  },
  mounted() {
    bfglob.on('update_user_settings', this.syncUserSettings)
  },
  beforeUnmount() {
    bfglob.off('update_user_settings', this.syncUserSettings)
  },
  watch: {
    'userSetting.showOnlyLocateTerminals'(val) {
      if (val) {
        this.userSetting.showOnlyOnlineTerminals = true
      }
    },
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    isFR() {
      return this.locale === SupportedLang.fr
    },
    isEN() {
      return this.locale === SupportedLang.enUS
    },
    isCN() {
      return this.locale === SupportedLang.zhCN
    },
    fullscreen() {
      return !(this.$root.layoutLevel > 0)
    },
    accountRule() {
      return {
        oldPwd: [validateRules.required()],
        newPwd: [validateRules.required()],
        newPwd2: [
          validateRules.required(),
          {
            validator: (rule, value, callback) => {
              if (this.account.newPwd !== value) {
                callback(new Error(this.$t('msgbox.resetPwdError')))
              } else {
                callback()
              }
            },
            trigger: 'blur',
          },
        ],
      }
    },
    accountFormLabelWidth() {
      if (this.isFR) {
        return '180px'
      }
      if (this.isEN) {
        return '150px'
      }
      return '100px'
    },
    settings() {
      const settings = [
        {
          label: this.$t('header.onAndOffTips'),
          key: 'deviceOnoff',
        },
        {
          label: this.$t('header.startAndEndWorkTips'),
          key: 'changeShifts',
        },
        {
          label: this.$t('header.callingTips'),
          key: 'calling',
        },
        {
          label: this.$t('header.emergencyAlarmTips'),
          key: 'alarmPrompt',
        },
        {
          label: this.$t('header.EGAlarmOnMapCenterTips'),
          key: 'showAlarmOnMapCenter',
        },
        {
          label: this.$t('header.openEGAlarmSound'),
          key: 'alarmVoice',
        },
        {
          label: this.$t('header.InspectionNotice'),
          key: 'showInsNotifi',
        },
        {
          label: this.$t('header.InsOnMapTips'),
          key: 'InsOnMapToolips',
        },
        {
          label: this.$t('header.onMapDisplayLinePointName'),
          key: 'showLinePointName',
        },
        {
          label: this.$t('header.showActivePointBattery'),
          key: 'showActivePointBattery',
        },
        {
          label: this.$t('header.newDevOrCtrlData'),
          key: 'showNewDeviceData',
        },
        {
          label: this.$t('header.showCtrollerStats'),
          key: 'showCtrlOnlineInfo',
        },
        {
          label: this.$t('header.showCtrlMarker'),
          key: 'showCtrlMarker',
        },
        {
          label: this.$t('header.fancytreeSortType'),
          key: 'fancytreeSortType',
        },
        {
          label: this.$t('header.scrollMoveTitle'),
          key: 'scrollMoveTitle',
        },
        {
          label: this.$t('header.showSmsResNotify'),
          key: 'showSmsResNotify',
        },
        {
          label: this.$t('header.showCrossBorderAlarm'),
          key: 'showCrossBorderAlarm',
        },
        {
          label: this.$t('header.showOnlyOnlineTerminals'),
          key: 'showOnlyOnlineTerminals',
          disabled: this.userSetting.showOnlyLocateTerminals,
        },
        {
          label: this.$t('header.showOnlyLocateTerminals'),
          key: 'showOnlyLocateTerminals',
        },
        {
          label: this.$t('header.checkChannelListenGroup'),
          key: 'checkChannelListenGroup',
        },
      ]

      if (bfglob.sysIniConfig.iotEnable) {
        settings.push({
          label: this.$t('header.showIotDeviceMarkers'),
          key: 'showIotDeviceMarkers',
        })
      }

      return settings
    },
  },
}
</script>

<style lang="scss">
.el-dialog.setting-dialog {
  width: 600px;

  .el-dialog__body {
    .el-tabs .el-tabs__content {
      padding: 10px 16px;
    }
  }

  .el-tab-pane.personal-setting-tab-pane {
    .personal-setting-item {
      flex-basis: 50%;
      line-height: 28px;
    }
  }

  .el-tab-pane.system-setting-tab-pane {
    .el-form-item.upload-logo .el-form-item__content {
      display: flex;
      justify-content: space-between;

      & > * {
        flex: auto;
      }

      .sys_logo_img {
        width: 120px;
        height: 40px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        flex: none;

        img {
          height: 100%;
          width: 100%;
        }
      }

      .sys_title_prompt {
        line-height: 1.5;
      }
    }
  }

  .el-tab-pane.account-setting-tab-pane {
    .el-form-item .el-form-item__label {
      //line-height: 1.2;
    }
  }

  &.is-fullscreen {
    .el-tab-pane.system-setting-tab-pane {
      .el-form-item.upload-logo .el-form-item__content {
        flex-direction: column;
      }
    }
  }
}
</style>
