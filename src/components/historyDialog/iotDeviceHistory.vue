<template>
  <historyCommon
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :device="queryProps.device"
    :deviceDmrId="deviceDmrId"
    :iot-device="queryProps.iotDevice"
    :user="queryProps.user"
    :head="dthead"
    :name="dataTableName"
    :exportNamePrefix="dlgTitle"
    :parse-request-data="parseRequestdata"
    @remove-data-table-data="removeDataTableData"
  >
    <template #optionsFormItem>
      <el-form-item :label="$t('dialog.terminalName')" prop="deviceRid">
        <el-select
          v-model="deviceDmrId"
          filterable
          clearable
          :placeholder="$t('dialog.select')"
          :no-match-text="$t('dialog.noMatchText')"
        >
          <el-option
            v-for="item in iotDeviceRids"
            :key="item.rid"
            :label="item.label"
            :value="item.devId"
          />
        </el-select>
      </el-form-item>
    </template>
  </historyCommon>
</template>

<script>
import { getIotCmdLabel, getIotDeviceTypeName } from '@/utils/iot'
import bfTime from '@/utils/time'
import vueMixin from '@/utils/vueMixin'
import historyCommon from '@/components/common/historyCommon.vue'
import { useRouteParams } from '@/router'

const { getRouteParams } = useRouteParams()

export default {
  name: 'IOTDeviceHistory',
  mixins: [vueMixin],
  data() {
    return {
      queryProps: {
        dbListName: 'db_iot_data_history_list',
        cmd: 59,
        isRid: false,
        device: false,
        iotDevice: true,
        user: false,
      },
      dataTableName: 'IotDeviceHistoryTable',
      deviceDmrId: '',
      iotDeviceRids: bfglob.giotDevices.getList(),
    }
  },
  methods: {
    parseRequestdata(item) {
      const device = bfglob.giotDevices.getDataByIndex(item.devId)
      if (typeof device === 'undefined') {
        bfglob.console.error('没有此物联网设备', item.devId)
        return
      }

      const pOrg = bfglob.gorgData.get(device.orgId)
      if (typeof pOrg === 'undefined') {
        bfglob.console.error('没有此上级单位', device.orgId)
        return
      }

      const pointItem = bfglob.glinePoints.getDataByIndex(item.recvStationId)
      if (!pointItem) {
        bfglob.console.error('没有此巡查点', item.recvStationId)
        return
      }

      const controller = bfglob.gcontrollers.getDataByIndex(item.receiver)
      if (!controller) {
        bfglob.console.error('没有此控制器', item.receiver)
        return
      }
      return item
    },
    removeDataTableData() {
      this.deviceDmrId = ''
    },
  },
  components: {
    historyCommon,
  },
  computed: {
    contentClass() {
      return this.isMobile ? 'is-mobile ' : ''
    },
    dthead() {
      return [
        {
          title: this.$t('dialog.parentOrg'),
          data: 'orgShortName',
          width: '100px',
          render: (data, type, row, meta) => {
            const device = bfglob.giotDevices.getDataByIndex(row.devId)
            if (typeof device === 'undefined') {
              return '-'
            }

            const pOrg = bfglob.gorgData.get(device.orgId)
            if (typeof pOrg === 'undefined') {
              return '-'
            }
            return pOrg.orgShortName + ''
          },
        },
        {
          title: this.$t('dialog.terminalName'),
          data: 'deviceSelfId',
          width: this.isFR ? '120px' : '100px',
          render: (data, type, row, meta) => {
            const device = bfglob.giotDevices.getDataByIndex(row.devId)
            if (typeof device === 'undefined') {
              return '-'
            }
            return device.devName + ''
          },
        },
        {
          title: this.$t('dialog.deviceType'),
          data: 'devType',
          width: this.isFR ? '120px' : '100px',
          render: data => {
            return getIotDeviceTypeName(data) ?? ''
          },
        },
        {
          title: this.$t('iotDevHistory.cmdHex'),
          data: 'cmd',
          width: '100px',
          render: data => {
            return getIotCmdLabel(data)
          },
        },
        {
          title: this.$t('iotDevHistory.cmdTime'),
          data: '_cmdTime',
          width: '130px',
          render: (data, type, row, meta) => {
            return bfTime.getUtcTimeString(row.cmdTime) + ''
          },
        },
        {
          title: this.$t('iotDevHistory.recvTime'),
          data: '_recvTime',
          width: '130px',
          render: (data, type, row, meta) => {
            return bfTime.getUtcTimeString(row.recvTime) + ''
          },
        },
        {
          title: this.$t('dialog.pointName'),
          data: 'pointName',
          width: '160px',
          render: (data, type, row, meta) => {
            const pointItem = bfglob.glinePoints.getDataByIndex(
              row.recvStationId,
            )
            if (!pointItem) {
              return '-'
            }
            return pointItem.pointName + ''
          },
        },
        {
          title: this.$t('dataTable.pointNo'),
          data: 'pointNo',
          width: this.isEN ? '150px' : '100px',
          render: (data, type, row, meta) => {
            const pointItem = bfglob.glinePoints.getDataByIndex(
              row.recvStationId,
            )
            if (!pointItem) {
              return '-'
            }
            return pointItem.pointId + ''
          },
        },
        {
          title: this.$t('dialog.deviceName'),
          data: 'deviceName',
          width: this.isFR ? '150px' : '120px',
          render: (data, type, row, meta) => {
            const controller = bfglob.gcontrollers.getDataByIndex(row.receiver)
            if (!controller) {
              return '-'
            }
            return controller.selfId + ''
          },
        },
      ]
    },
    dlgTitle() {
      return this.$t('nav.iotDeviceHistory')
    },
  },
  activated() {
    this.$route.params = getRouteParams(this.$route.name)
    if (this.$route.params.getMoreIotDeviceHistory) {
      const _time = new Date()
      const hisCom = this.$refs.hisCom
      if (hisCom) {
        hisCom.query.startTime = bfTime.getDayBeforeTheSpecifiedTime(
          _time,
          24 * 7,
        )
        hisCom.query.endTime = _time
        this.deviceDmrId = this.$route.params.deviceDmrId
        this.$nextTick(() => {
          hisCom.queryFunc()
        })
      }
    }
  },
}
</script>

<style></style>
