<template>
  <div
    class="relative flex h-[100px] w-[280px] items-center justify-center border-4 border-[#94CCE8] font-sans text-white bg-[radial-gradient(circle_at_50%_105%,_rgba(11,162,231,0.56)_0%,_rgba(11,162,231,0)_40%)]"
  >
    <div class="relative z-10 flex w-full items-center justify-between px-5">
      <img src="@/assets/images/common/minus-button.svg" class="h-[50px] w-[50px] cursor-pointer select-none" @click="decrement" alt="minus" />
      <input
        type="text"
        class="w-[80px] min-w-[40px] border-none bg-transparent p-0 text-center font-sans text-[32px] font-medium text-white focus:outline-none"
        v-model="inputValue"
        @blur="handleBlur"
        @input="handleInput"
        pattern="\d*"
      />
      <img src="@/assets/images/common/plus-button.svg" class="h-[50px] w-[50px] cursor-pointer select-none" @click="increment" alt="plus" />
    </div>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  const props = defineProps({
    modelValue: {
      type: Number,
      required: true,
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: 99,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const inputValue = ref(props.modelValue)

  watch(
    () => props.modelValue,
    newValue => {
      inputValue.value = newValue
    }
  )

  const increment = () => {
    if (props.modelValue < props.max) {
      emit('update:modelValue', props.modelValue + 1)
    }
  }

  const decrement = () => {
    if (props.modelValue > props.min) {
      emit('update:modelValue', props.modelValue - 1)
    }
  }

  const handleInput = event => {
    inputValue.value = event.target.value.replace(/\D/g, '')
  }

  const handleBlur = () => {
    let value = parseInt(inputValue.value, 10)

    if (isNaN(value)) {
      value = props.min
    }
    if (value > props.max) {
      value = props.max
    }
    if (value < props.min) {
      value = props.min
    }

    inputValue.value = value
    if (value !== props.modelValue) {
      emit('update:modelValue', value)
    }
  }
</script>
