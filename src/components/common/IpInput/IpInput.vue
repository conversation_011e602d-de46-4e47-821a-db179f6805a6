<template>
  <div class="w-full ip-input-wrap">
    <el-input v-model="ipv4" :disabled="disabled" class="ip-input" />
  </div>
</template>

<script>
import { encodeStringIp, decodeInt32Ip } from '@/utils/bfutil'

export default {
  name: 'IpInput',
  emits: ['update:modelValue'],
  props: {
    modelValue: {
      type: [String, Number],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      uint32Ip: 0,
      // 标准 v4 IP 正则
      ipReg:
        /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      ipv4: '',
    }
  },
  methods: {
    encodeIpv4(val) {},
  },
  watch: {
    uint32Ip(val) {
      this.$emit('update:modelValue', val)

      if (val === 0) {
        this.ipv4 = ''
        return
      }

      const ipv4 = decodeInt32Ip(val)
      if (ipv4 !== this.ipv4) {
        this.ipv4 = ipv4
      }
    },
    modelValue: {
      immediate: true,
      handler(val) {
        if (this.ipReg.test(val)) {
          this.uint32Ip = encodeStringIp(val)
        } else if (typeof val === 'number') {
          this.uint32Ip = val
        } else {
          this.uint32Ip = 0
        }
      },
    },
    ipv4(val) {
      // 替换除了数字和"."之外的所有字符
      let reg = /[^0-9.]/gi
      if (reg.test(val)) {
        val = val.replace(reg, '')
      }
      // 替换连续的点号"."
      reg = /\.+/gi
      if (reg.test(val)) {
        val = val.replace(reg, '.')
      }

      // 过滤后的值为空，则结束
      if (val === '') {
        this.uint32Ip = 0
        return
      }

      // 如果输入的ip段大于4段，则取前4段数字
      let ipv4Arr = val.split('.')
      if (ipv4Arr.length > 4) {
        ipv4Arr = ipv4Arr.slice(0, 4)
      }

      if (ipv4Arr.length === 4) {
        // 检验每一段ip值是否大于255
        if (!this.ipReg.test(val)) {
          val = ipv4Arr
            .map(v => {
              return v > 255 ? 255 : v
            })
            .join('.')
        }

        // 检验是否为完整的ip地址
        if (this.ipReg.test(val)) {
          if (val !== this.ipv4) {
            this.ipv4 = val
            return
          }
          this.uint32Ip = encodeStringIp(val)
        }
      }
    },
  },
}
</script>

<style></style>
