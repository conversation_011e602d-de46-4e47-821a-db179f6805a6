<template>
  <el-dialog
    v-model="prochatGatewayVisible"
    :title="$t('dialog.prochatGatewayConfig')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    class="header-border footer-border prochat-gateway-setting-dialog"
    @close="onClose"
  >
    <el-form
      ref="formData"
      :model="formData"
      :rules="rules"
      label-width="160px"
      :validate-on-rule-change="false"
      class="grid grid-cols-1"
    >
      <el-form-item
        ref="hostItem"
        :label="$t('dialog.prochatDomain')"
        prop="host"
      >
        <el-input v-model="formData.host" :maxlength="56" />
      </el-form-item>
      <el-form-item
        ref="portItem"
        :label="$t('dialog.prochatPort')"
        prop="port"
      >
        <el-input-number
          v-model.number="formData.port"
          :min="0x01"
          :max="0xffff"
          step-strictly
        />
      </el-form-item>
      <el-form-item :label="$t('dialog.prochatNo')" prop="prochatNo">
        <el-input v-model="formData.prochatNo" :maxlength="16" />
      </el-form-item>
      <el-form-item :label="$t('dialog.prochatUserNo')" prop="userNo">
        <el-input v-model="formData.userNo" :maxlength="16" />
      </el-form-item>
      <el-form-item :label="$t('dialog.prochatPassword')" prop="password">
        <el-input v-model="formData.password" type="password" :maxlength="16" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="text-center actions">
        <el-button
          type="primary"
          class="w-32"
          @click="onConfirm"
          v-text="$t('dialog.confirm')"
        />
        <el-button
          type="warning"
          class="w-32"
          @click="onReset"
          v-text="$t('dialog.reset')"
        />
      </div>
    </template>
  </el-dialog>
</template>

<script>
import validateRules from '@/utils/validateRules'

/**
 * @param prochatNo - 公网网关的号码
 * @param host - 公网网关的 ip 或域名
 * @param port - 公网网关的端口
 * @param userNo - 专网网关(本系统)的号码
 * @param password - 专网网关的密码
 *
 * 所有的配置项存放在 controller.setting 中，controller.sipNo 为 prochatNo
 */
export const DefaultFormData = {
  prochatNo: '',
  host: '',
  port: 5060,
  userNo: '',
  password: '',
}

export default {
  name: 'ProchatGatewaySetting',
  emits: ['update:modelValue', 'update:visible'],
  props: {
    modelValue: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      formData: {
        ...DefaultFormData,
      },
    }
  },
  computed: {
    fullscreen() {
      return this.$root.layoutLevel === 0
    },
    prochatGatewayVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    rules() {
      return {
        prochatNo: [validateRules.required(), validateRules.maxLen(12)],
        host: [validateRules.required(), validateRules.checkHost()],
        port: [validateRules.required()],
        userNo: [validateRules.required(), validateRules.maxLen(12)],
        password: [validateRules.required(), validateRules.maxLen(8)],
      }
    },
  },
  methods: {
    onClose() {
      this.prochatGatewayVisible = false
    },
    async validate() {
      return await this.$refs.formData.validate().catch(() => false)
    },
    async onConfirm() {
      const valid = await this.validate()
      if (!valid) {
        return
      }

      this.$emit('update:modelValue', this.formData)
      this.onClose()
    },
    onReset() {
      this.formData = {
        ...DefaultFormData,
      }
      this.$nextTick(() => {
        this.$refs.formData?.clearValidate()
      })
    },
  },
  watch: {
    modelValue: {
      handler(val) {
        this.formData = val
      },
      immediate: true,
      deep: true,
    },
  },
}
</script>

<style lang="scss">
.el-dialog.prochat-gateway-setting-dialog {
  width: 520px;
}
</style>
