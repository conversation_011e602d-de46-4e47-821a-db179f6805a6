<template>
  <section class="dynamic-group-tree--wrapper">
    <div class="dynamic-group-tree--select-info">
      {{ selectLength }}/{{ maxSelectSize }}
    </div>
    <TableTree
      ref="dynamicGroupTree"
      :treeId="treeId"
      :option="treeOptions"
      :contextmenuOption="contextmenuOption"
      in-dialog
      class="dynamic-group-tree"
      @loaded="treeLoaded"
      @select="select"
      @click="clickNode"
    />
  </section>
</template>

<script>
import TableTree from '@/components/common/tableTree'
import {
  addOneDeviceNode,
  addOneOrgNode,
  createOrgNodeData,
  defaultTreeId,
  getDeviceNodeData,
  getNodeByKey,
  getOrgNodeTitle,
  getRootNode,
  getTree,
  isRootNode,
  redrawViewport,
  removeNode,
  selectAll,
} from '@/utils/bftree'
import { DeviceTypes, getDynamicGroupOrgType } from '@/utils/bfutil'
import {
  MemberType,
  requestTargetCanJoinDynamicGroup,
} from '@/utils/dynamicGroup/api'
import fancytreeMixin from '@/utils/fancytreeMixin'
import bfNotify, { messageBox } from '@/utils/notify'
import { throttle } from 'lodash'

const dynamicGroupOrgType = getDynamicGroupOrgType()
// 不支持动态组的终端类型
const NotSupportDynamicGroupDeviceTypes = [
  DeviceTypes.PhoneRepeater,
  DeviceTypes.VirtualRepeater,
  DeviceTypes.UserCard,
  DeviceTypes.SipGatewayDevice,
  DeviceTypes.MeshGateway,
  DeviceTypes.MeshDevice,
  DeviceTypes.GeneralDmr,
  DeviceTypes.ProchatDevice,
  DeviceTypes.ProchatGatewayDevice,
]

export default {
  name: 'DynamicGroupTree',
  mixins: [fancytreeMixin],
  emits: ['select-length', 'select-member', 'loaded'],
  props: {
    treeId: {
      type: String,
      default: 'dynamic-group-tree__' + Date.now(),
    },
    dynamicGroupType: {
      type: Number,
      required: true,
    },
    maxSelectSize: {
      type: Number,
      default: 128,
    },
    dynamicGroup: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      devices: new Map(),
      groups: new Map(),
      selectLength: 0,
      lastDetailRids: [],
      detailList: [],
      treeIsLoad: false,
    }
  },
  methods: {
    getDynamicGroupDetails() {
      this.detailList = bfglob.gdynamicGroupDetail.getDataByGroupRid(
        this.dynamicGroup.rid,
      )
      return this.detailList
    },
    addDynamicGroupDetail(detail) {
      // 先过滤掉非当前动态组成员
      const detailList = this.getDynamicGroupDetails()
      if (!detailList.some(item => item.rid === detail.rid)) {
        return
      }

      const source = bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)
      if (!source) {
        return
      }

      // 先判断树上是否有节点，没有则添加，有则更新
      const node = getNodeByKey(this.treeId, source.rid)
      if (node) {
        node.setSelected(true)
        return
      }

      if (detail.isDeviceGroup === 2) {
        this.addOneNoPermGroupDetailNode(detail, source)
        if (!bfglob.gorgData.get(detail.groupRid)) {
          this.lastDetailRids.push(detail.groupRid)
        }
      } else {
        this.addOneNoPermDeviceDetailNode(detail, source)
        if (!bfglob.gdevices.get(detail.deviceRid)) {
          this.lastDetailRids.push(detail.deviceRid)
        }
      }
    },
    removeDynamicGroupDetail(detail) {
      const source = bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)
      if (!source) {
        return
      }

      // 判断详情是否为无权限的组成员，如果无权限，则删除该节点
      if (
        bfglob.noPermOrgData.get(detail.groupRid) ||
        bfglob.noPermDevData.get(detail.deviceRid)
      ) {
        removeNode(this.treeId, source.rid)
      }
    },
    // Vue 无法响应Map/Set对象的数据变化
    setCurrentSelectLength() {
      this.$emit('select-length', this.selectLength)
      this.$emit('select-member', this.$getSelectTarget())
    },
    setGroupDetail(key, data, isGroup = false) {
      if (isGroup) {
        this.groups.set(key, data)
      } else {
        this.devices.set(key, data)
      }
      this.selectLength = this.devices.size + this.groups.size
      this.setCurrentSelectLength()
    },
    deleteGroupDetail(key, isGroup = false) {
      if (isGroup) {
        this.groups.delete(key)
      } else {
        this.devices.delete(key)
      }
      this.selectLength = this.devices.size + this.groups.size
      this.setCurrentSelectLength()
    },
    processCanJoinDynamicGroupError(resInfo) {
      if (resInfo.includes('no such dynamic group')) {
        bfNotify.warningBox(this.$t('dynamicGroup.notFoundDynamicGroup'))
        return
      }
      if (resInfo.includes('no login info for this session')) {
        bfNotify.warningBox(this.$t('msgbox.loginSessionExpired'))
        return true
      }
    },
    cancelSelectedGroupDeviceNodeStatus(node) {
      if (!node) {
        return
      }
      // 查找节点对应的详情数据源
      const detailChildren = this.listenGroupMemberIndex[node.key]
      detailChildren &&
        detailChildren.forEach(data => {
          const node = getNodeByKey(this.treeId, data.deviceRid)
          this.setNodeUnSelectable(node, false)
          node?.setSelected(false)
        })
    },
    syncSelectedGroupDeviceNodes(groupMemberNode) {
      if (!groupMemberNode) {
        return
      }
      // 查找节点对应的详情数据源
      const detailChildren = this.listenGroupMemberIndex[groupMemberNode.key]
      detailChildren &&
        detailChildren.forEach(data => {
          const node = getNodeByKey(this.treeId, data.deviceRid)
          node?.setSelected(true)
          this.setNodeUnSelectable(node, true)
          this.setGroupDetail(node.key, data, false)
        })
    },
    select(_, data) {
      // 只对当前操作的节点进行处理
      if (!data.node) {
        return
      }

      const node = data.node
      const extraData = (node.data = node.data || {})
      const clearJoinedIcon = node => {
        if (!node || !node.data?.title) {
          return
        }

        node.title = node.data?.title
        delete node.data?.title
        node.render()
      }
      // 取消选择节点，如果是组成员节点，需要同步取消该组下的终端成员
      if (!node.isSelected()) {
        // 已经保存的数据对象中删除取消选择的数据
        this.deleteGroupDetail(node.key, extraData.isOrg)
        // 清除已加入动态组icon标记
        clearJoinedIcon(node)
        // 需要同步取消该组下的终端成员
        this.cancelSelectedGroupDeviceNodeStatus(node)
        return
      }

      // 如果超出选择上限，则禁止选择
      if (this.selectLength >= this.maxSelectSize) {
        node.setSelected(false)
        clearJoinedIcon(node)
        messageBox(this.$t('dynamicGroup.groupMemberUpperLimit'), 'warning')
        return
      }

      // 同步当前动态组，设置节点选择状态时设置，以取消查询过程
      if (this.notNeedRequest) {
        return
      }

      let target
      let parentKey = 'orgId'
      if (extraData.isOrg) {
        target = bfglob.gorgData.getDataMaybeNoPerm(node.key)
        // 单位的权限是独立的，组成员的上级设置为自己
        parentKey = 'rid'
      } else {
        target = bfglob.gdevices.getDataMaybeNoPerm(node.key)
      }

      // 无法查找目标节点源数据
      if (!target) {
        node.setSelected(false)
        clearJoinedIcon(node)
        return
      }

      const groupDetail = {
        memberOrgId: target[parentKey],
        dynamicGroupType: this.dynamicGroupType,
      }

      Object.assign(
        groupDetail,
        extraData.isOrg
          ? {
              isDeviceGroup: 2,
              groupRid: target.rid,
              groupDmrid: target.dmrId,
            }
          : {
              isDeviceGroup: 1,
              deviceRid: target.rid,
              deviceDmrid: target.dmrId,
            },
      )

      this.setGroupDetail(node.key, groupDetail, extraData.isOrg)

      const memberCanAdd = {
        userPriority: this.voipSpeakInfo.priority ?? 2,
        member: groupDetail,
      }

      // 如果已经拥有该成员信息，则不需要查询
      const localDetail = this.detailList.find(item => {
        // 如果选择的树节点是单位，但详情不是组成员则跳过
        if (extraData.isOrg && item.isDeviceGroup !== MemberType.Group) {
          return false
        }
        return item.groupRid === node.key || item.deviceRid === node.key
      })
      if (localDetail) {
        // 如果是组成员，则把组下的终端也要选择上
        if (localDetail.isDeviceGroup === MemberType.Group) {
          this.setGroupDetail(node.key, localDetail, true)
          this.syncSelectedGroupDeviceNodes(node)
          return
        } else if (localDetail.isDeviceGroup === MemberType.ListenGroupDevice) {
          // 如果是组下终端成员，如果上级接收组不存在，则需要添加
          const parentMember = this.detailList.find(
            item =>
              item.isDeviceGroup === MemberType.Group &&
              item.groupRid === localDetail.groupRid,
          )
          const parentNode = getNodeByKey(this.treeId, localDetail.groupRid)
          // 上级接收组成员还在选中状态，不进行操作，由上级成员来处理
          if (parentMember && parentNode && parentNode.isSelected()) {
            return
          }
        } else {
          this.setGroupDetail(node.key, localDetail, false)
          return
        }
      }

      // 选中，查询是否可以加入动态组，response: member_can_add
      // 只要用户选择该节点，则可以拉人进组，查询只是通知用户目标节点当前的动态组状态
      requestTargetCanJoinDynamicGroup(memberCanAdd)
        .then(rpcCmd => {
          // 已经取消选择该节点，则不处理响应结果
          if (!node.isSelected()) {
            return
          }
          // rpc_cmd.ResInfo '+OK'可以，其他为抢占临时组的组名
          if (rpcCmd.resInfo === '+OK') {
            return
          }

          // 服务器返回错误信息
          if (rpcCmd.resInfo.startsWith('Err:')) {
            this.processCanJoinDynamicGroupError(rpcCmd.resInfo)
            return
          }

          // 需要判断是否已经加入自己的组
          if (rpcCmd.resInfo === this.dynamicGroupName) {
            return
          }

          // 更新目标节点title，显示已加入的动态组名称
          const title = (node.data.title = node.title)
          node.title = `${title} <span class="dynamic-group-joined-icon"><i class="joined-icon mdi mdi-alert-circle-outline"></i>${rpcCmd.resInfo}</span>`
          node.render()

          // 通知用户目标节点动态组状态
          const isGroup = groupDetail.isDeviceGroup === 2
          const target = isGroup
            ? bfglob.gorgData.get(groupDetail.groupRid)
            : bfglob.gdevices.get(groupDetail.deviceRid)
          messageBox(
            this.$t('dynamicGroup.alreadyJoined', {
              member: isGroup
                ? (target?.orgShortName ?? groupDetail.groupDmrId)
                : (target?.selfId ?? groupDetail.deviceDmrid),
              group: rpcCmd.resInfo,
            }),
            'warning',
          )
        })
        .catch(err => {
          bfglob.console.error('[requestTargetCanJoinDynamicGroup] err:', err)
        })
    },
    clickNode(_, data) {
      const excludeList = ['expander', 'prefix', 'checkbox']
      if (excludeList.includes(data.targetType)) {
        return true
      }

      const node = data.node
      if (!node) {
        return false
      }

      node.setActive()
      node.setSelected(!node.isSelected())
    },
    clearLastDynamicGroupDetailNodes() {
      for (let i = 0; i < this.lastDetailRids.length; i++) {
        const key = this.lastDetailRids[i]
        removeNode(this.treeId, key)
      }
      this.lastDetailRids = []
    },
    addOneNoPermGroupDetailNode(detail, detailSource) {
      const parentNode =
        getNodeByKey(detail.memberOrgId) || getRootNode(this.treeId)
      const orgNodeData = createOrgNodeData(detailSource)
      orgNodeData.selected = true
      orgNodeData.title = `<span class='org-title'>${detailSource.orgShortName}</span>`
      const orgNode = parentNode.addChildren(orgNodeData)
      orgNode?.addClass('no-permission')
    },
    addOneNoPermDeviceDetailNode(detail, detailSource) {
      const parentNode =
        getNodeByKey(detail.memberOrgId) || getRootNode(this.treeId)
      const deviceNodeData = getDeviceNodeData(detailSource)
      deviceNodeData.selected = true
      deviceNodeData.title = `
          <i class='device_status device_status_none'></i>
          <span class='device_selfId'>${detailSource.selfId}</span>
        `
      const deviceNode = parentNode.addChildren(deviceNodeData)
      deviceNode?.addClass('no-permission')
    },
    loadDynamicGroupDetailNodes(details) {
      // 先清除上个动态组的无权限成员
      this.clearLastDynamicGroupDetailNodes()

      const detailRids = []
      for (let i = 0; i < details.length; i++) {
        const detail = details[i]
        const detailSource = bfglob.gdynamicGroupDetail.getDetailSource(
          detail.rid,
        )
        // 如果动态组成员详情数据源在无权限的数据容器中，则没有权限，需要加载树节点
        // 终端，直属动态组，如果有上级数据权限，则挂载到上级节点，如果没有
        switch (detail.isDeviceGroup) {
          case 1:
          case 3:
            const device = bfglob.gdevices.get(detail.deviceRid)
            if (!device) {
              detailRids.push(detail.deviceRid)
              this.addOneNoPermDeviceDetailNode(detail, detailSource)
            }
            break
          case 2:
            const org = bfglob.gorgData.get(detail.groupRid)
            if (!org) {
              detailRids.push(detail.groupRid)
              this.addOneNoPermGroupDetailNode(detail, detailSource)
            }
            break
        }
      }

      this.lastDetailRids = detailRids
    },
    toDictTree() {
      return this.$refs.dynamicGroupTree?.toDictTree(defaultTreeId, dict => {
        if (isRootNode(dict)) return true

        const extraData = dict.data || {}
        // 过滤掉所有的动态组
        if (
          extraData.isOrg &&
          dynamicGroupOrgType.includes(extraData.orgIsVirtual)
        ) {
          return false
        }
        // 过滤不支持动态组功能的终端类型节点
        if (
          !extraData.isOrg &&
          NotSupportDynamicGroupDeviceTypes.includes(extraData.deviceType)
        ) {
          return false
        }
        // 如果是虚拟单位下的节点，则过滤掉
        if (!extraData.isOrg && extraData.virtual) {
          return false
        }
        // 过滤掉动态组下成员
        if (extraData.isDynamicGroupMember) return false

        // 更新节点，重新生成节点title属性，主要去除计数标记
        const org = bfglob.gorgData.get(dict.key)
        if (org) {
          dict.title = getOrgNodeTitle(org, { showCounter: false })
        }

        return true
      })
    },
    treeLoaded() {
      // 树节点只能单选
      this.$refs.dynamicGroupTree?.setOption('selectMode', 2)
      this.toDictTree().then(() => {
        this.$refs.dynamicGroupTree.updateViewport(
          this.$refs.dynamicGroupTree.getLocalTree(),
        )
        this.treeIsLoad = true
        this.$emit('loaded')
      })
    },
    setNodeUnSelectable(node, status = undefined) {
      if (!node) {
        return
      }

      node.unselectable = status
      node.unselectableStatus = status
      node.renderStatus()
    },

    // 供父组件使用的方法
    $getSelectTarget() {
      return {
        devices: Array.from(this.devices.values()),
        groups: Array.from(this.groups.values()),
      }
    },
    $updateViewport() {
      this.$refs.dynamicGroupTree?.updateViewport()
    },
    $redrawViewport() {
      redrawViewport(this.treeId, true)
    },
    $selectAll(status) {
      selectAll(this.treeId, status)
    },
    awaitTreeLoaded() {
      return new Promise(resolve => {
        if (this.treeIsLoad) {
          return resolve()
        }
        return this.awaitTreeLoaded()
      })
    },
    async $syncSelectNode() {
      await this.awaitTreeLoaded()
      // 清空已经选择的数据
      this.groups.clear()
      this.devices.clear()
      this.setCurrentSelectLength()

      // 加载没有权限的动态组成员节点
      const dynamicGroupDetails = this.getDynamicGroupDetails()
      this.loadDynamicGroupDetailNodes(dynamicGroupDetails)

      // 先清空之前选择的节点的状态
      const tree = getTree(this.treeId)
      if (tree) {
        const selected = tree.getSelectedNodes()
        selected &&
          selected.forEach(node => {
            this.setNodeUnSelectable(node)
            node.setSelected(false)
          })
      }
      // 在下轮事件周期同步节点选中状态，避免出现节点无法选中问题
      this.$nextTick(() => {
        // 同步当前组的节点时，不需要重新查询状态，设置标示
        this.notNeedRequest = true
        const groupDetails = dynamicGroupDetails.filter(
          item => item.isDeviceGroup === MemberType.Group,
        )
        // 根据当前的节点，重新选择上
        for (let i = 0; i < dynamicGroupDetails.length; i++) {
          const data = dynamicGroupDetails[i]
          const isGroup = data.isDeviceGroup === 2
          const key = isGroup ? data.groupRid : data.deviceRid
          // 根据详情类型，查找节点
          const node = getNodeByKey(this.treeId, key)
          if (!node) {
            continue
          }

          // 选择节点
          node.setSelected(true)
          // 保存数据
          this.setGroupDetail(key, data, isGroup)
          // 如果这个节点是因接收组加入动态组，则禁用该节点的点击操作
          // 如果上级接收组已经删除右已退出任务组，则允许操作该节点
          if (data.isDeviceGroup === MemberType.ListenGroupDevice) {
            if (groupDetails.find(item => item.groupRid === data.groupRid)) {
              node.unselectable = true
              node.unselectableStatus = true
            }
          }
          node.render()
        }
        this.notNeedRequest = false
      })
    },
    $setActive(key, status = true) {
      getNodeByKey(this.treeId, key)?.setActive(status)
    },
    $setSelected(key, status = true) {
      getNodeByKey(this.treeId, key)?.setSelected(status)
    },

    // 重写Mixin方法
    addOneOrgNode(data) {
      addOneOrgNode(this.treeId, data, { selected: false })
    },
    addOneDeviceNode(device) {
      addOneDeviceNode(this.treeId, device, { selected: false })
    },
  },
  computed: {
    contextmenuOption() {
      return {
        menu: [
          {
            title: this.$t('tree.online'),
            cmd: 'displayOnline',
          },
          {
            title: this.$t('tree.displayAllDev'),
            cmd: 'displayAll',
          },
        ],
        select: (event, ui) => {
          switch (ui.cmd) {
            case 'displayOnline':
              this.$refs.dynamicGroupTree?.showOnlineDevices()
              break
            case 'displayAll':
              this.$refs.dynamicGroupTree?.showAllDevices()
          }
        },
      }
    },
    listenGroupMemberIndex() {
      const listenGroupList = []
      const groupList = []
      for (let i = 0; i < this.detailList.length; i++) {
        const detail = this.detailList[i]
        if (detail.isDeviceGroup === MemberType.Device) {
          continue
        }

        if (detail.isDeviceGroup === MemberType.Group) {
          groupList.push(detail)
        } else {
          listenGroupList.push(detail)
        }
      }

      // 索引数据，源数据的RID->组下成员的详情
      const index = {}
      while (listenGroupList.length) {
        const data = listenGroupList.shift()
        index[data.groupRid] = index[data.groupRid] || []
        index[data.groupRid].push(data)
      }
      return index
    },
    voipSpeakInfo() {
      return bfglob.userInfo.setting.voipSpeakInfo
    },
    treeOptions() {
      return {
        selectMode: 2,
      }
    },
    dynamicGroupName() {
      return this.dynamicGroup.orgShortName ?? ''
    },
  },
  components: {
    TableTree,
  },
  mounted() {
    this.setCurrentSelectLength = throttle(this.setCurrentSelectLength, 350)
    bfglob.on('add_one_dynamic_group_detail', this.addDynamicGroupDetail)
    bfglob.on('delete_one_dynamic_group_detail', this.removeDynamicGroupDetail)
  },
  beforeUnmount() {
    bfglob.off('add_one_dynamic_group_detail', this.addDynamicGroupDetail)
    bfglob.off('delete_one_dynamic_group_detail', this.removeDynamicGroupDetail)
  },
}
</script>

<style lang="scss">
.dynamic-group-tree--wrapper {
  .dynamic-group-tree--select-info {
    text-align: right;
    font-size: 14px;
    height: 28px;
    line-height: 28px;
  }

  .dynamic-group-tree {
    height: calc(100% - 28px);

    .dynamic-group-joined-icon {
      color: #e6a23c;
      padding-left: 6px;

      & > .joined-icon {
        margin-right: 4px;
      }
    }

    .no-permission .fancytree-node .fancytree-title {
      font-style: italic;
      color: #e6a23c !important;
    }
  }
}
</style>
