<template>
  <section v-show="mapVisible" class="base-map-container">
    <!--地图缩放级别-->
    <div class="maplibregl-ctrl-mapLevel">
      <span class="mapLevelLabel" v-text="$t('map.mapLevel')" />
      <span class="mapLevel" v-text="mapLevel" />
    </div>

    <!--地图中心十字架-->
    <div class="map_coordinates" />

    <!--自定义控件按钮-->
    <button ref="satellite" class="satellite_btn" :title="$t('map.satellite')" @click="toggleSapStyle(MapStyleName.satellite)" />
    <button ref="streets" class="streets_btn" :title="$t('map.streets')" @click="toggleSapStyle(MapStyleName.streets)" />
    <button ref="quitMap" class="quitMap_btn" :title="$t('map.stop')" @click="close" />

    <!-- 地图顶部居中的自定义内容 -->
    <div class="maplibregl-custom-top-container">
      <slot name="topCenter" />
    </div>
  </section>
</template>

<script>
  import { MapLibreProtoColName, MapStyleName, registerCustomProtocol, rpcUrl } from '@/utils/map'
  import { SupportedLang } from '@/modules/i18n'
  import bfStorage from '@/utils/storage'
  import maplibregl from 'maplibre-gl'
  import 'maplibre-gl/dist/maplibre-gl.css'
  import { cloneDeep } from 'lodash'
  import { BASE_URL } from '@/envConfig'

  const defaultCenter = [118.62188333333333, 25.003556666666668]

  class CustomCtrlIcon {
    constructor(el) {
      this._container = el ?? document.createElement('div')
    }

    onAdd(map) {
      this._map = map
      return this._container
    }

    onRemove() {
      this._container.parentNode.removeChild(this._container)
      this._map = undefined
      this._container = undefined
    }
  }

  export default {
    name: 'BaseMap',
    emits: ['update:visible', 'close', 'init'],
    props: {
      /**
       * 控件列表
       * @example [{position: 'top-right', control: any}]
       */
      controls: {
        type: Array,
      },
      mapId: {
        type: String,
        default: 'base-map-' + Date.now(),
      },
      visible: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        mapLevel: 15,
        // mapStyle: {}, // 不需要vue响应式,不显示声明
        personalMapSettings: {
          center: null,
          style: 'streets',
          zoom: 16,
        },
      }
    },
    methods: {
      close() {
        this.$emit('close')
        // 主要用于向控件传递地图窗口关闭信号
        bfglob.emit('map-close', this.mapId)
      },
      toggleSapStyle(styleName) {
        // 判断是否需要切换地图
        // roadmap-tiles 街道图，hybrid-tiles 卫星图
        const currentStyle = this.map.getStyle()
        if (currentStyle.name === styleName) return

        const newStyles = cloneDeep(this.mapStyle[styleName] ?? this.mapStyle[MapStyleName.satellite])

        // 拷贝原有的图层
        const sources = currentStyle.sources
        const layers = currentStyle.layers
        for (const layer of layers) {
          if (layer.type === 'raster') {
            continue
          }
          // 跳过没有源的图层
          const sourceId = layer.source
          const source = sources[sourceId]
          if (!source) {
            continue
          }

          newStyles.layers.push(layer)
          newStyles.sources[sourceId] = source
        }

        this.map.setStyle(newStyles)
      },

      // 修复切换地图图层语言后，缩放控件没有切换title属性问题
      resetMapboxCtrlTitle() {
        this.$el?.querySelector('.maplibregl-ctrl-zoom-in')?.setAttribute('title', this.$t('map.zoomIn'))
        this.$el?.querySelector('.maplibregl-ctrl-zoom-out')?.setAttribute('title', this.$t('map.zoomOut'))
        this.$el?.querySelector('.maplibregl-ctrl-compass')?.setAttribute('title', this.$t('map.resetBearingToNorth'))
      },
      loadCustomCtrlIcon() {
        const satellite_btn = new CustomCtrlIcon(this.$refs.satellite).onAdd(this.map)
        const streets_btn = new CustomCtrlIcon(this.$refs.streets).onAdd(this.map)
        const quitMap_btn = new CustomCtrlIcon(this.$refs.quitMap).onAdd(this.map)

        $(this.$el).find('.maplibregl-ctrl-top-right .maplibregl-ctrl-group').append(satellite_btn, streets_btn, quitMap_btn)
      },

      // 初始化地图逻辑
      setMapStyle() {
        // 获取地图语言类型
        const uiLang2MapLang = (() => {
          try {
            // zh-cn => zh_CN
            if (this.$i18n.locale === SupportedLang.zhCN) return 'zh_CN'
            return this.$i18n.locale
          } catch (_e) {
            // no-empty
          }

          return 'zh_CN'
        })()
        const roadmap_tiles = [`${MapLibreProtoColName}://${rpcUrl}/gmap?lang=` + uiLang2MapLang + '&mtype=roadmap&x={x}&y={y}&z={z}']
        const hybrid_tiles = [`${MapLibreProtoColName}://${rpcUrl}/gmap?lang=` + uiLang2MapLang + '&mtype=hybrid&x={x}&y={y}&z={z}']

        // 本地化资源，在开发环境下无法使用代理服务器
        // let glyphs = "mapbox://fonts/mapbox/{fontstack}/{range}.pbf";
        const path = `${window.location.origin}${BASE_URL}localMapbox`
        const glyphs = `${path}/glyphs/{fontstack}/{range}.pbf`
        const sprite = `${path}/sprite/sprite`

        const getMinzoom = () => {
          if (typeof bfglob.mapConfig.tilesMinzoom === 'number' && bfglob.mapConfig.tilesMinzoom >= 0 && bfglob.mapConfig.tilesMinzoom < 18) {
            return bfglob.mapConfig.tilesMinzoom
          }

          return 0
        }
        const getMaxzoom = () => {
          if (typeof bfglob.mapConfig.tilesMaxzoom === 'number' && bfglob.mapConfig.tilesMaxzoom > 0 && bfglob.mapConfig.tilesMaxzoom <= 18) {
            return bfglob.mapConfig.tilesMaxzoom
          }

          return 18
        }

        this.mapStyle = {
          [MapStyleName.streets]: {
            name: MapStyleName.streets,
            version: 8,
            sources: {
              'roadmap-tiles': {
                type: 'raster',
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                tiles: roadmap_tiles,
                tileSize: 256,
                minzoom: getMinzoom(),
                maxzoom: getMaxzoom(),
              },
            },
            layers: [
              {
                id: 'roadmap-tiles',
                type: 'raster',
                source: 'roadmap-tiles',
              },
            ],
            glyphs: glyphs,
            sprite: sprite,
          },
          [MapStyleName.satellite]: {
            name: MapStyleName.satellite,
            version: 8,
            sources: {
              'hybrid-tiles': {
                type: 'raster',
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                tiles: hybrid_tiles,
                tileSize: 256,
                minzoom: getMinzoom(),
                maxzoom: getMaxzoom(),
              },
            },
            layers: [
              {
                id: 'hybrid-tiles',
                type: 'raster',
                source: 'hybrid-tiles',
              },
            ],
            glyphs: glyphs,
            sprite: sprite,
          },
        }
      },
      getMapStyle() {
        const styleName = this.personalMapSettings.style || MapStyleName.streets
        return this.mapStyle[styleName]
      },
      getLocalCenter() {
        return this.personalMapSettings.center || bfglob.mapConfig.mapCenter || defaultCenter
      },
      getLocalZoom() {
        return this.personalMapSettings.zoom || 16
      },
      getMapConfigMaxZoom() {
        if (typeof bfglob.mapConfig.maxZoom === 'number' && bfglob.mapConfig.maxZoom > 0 && bfglob.mapConfig.maxZoom <= 23) {
          return bfglob.mapConfig.maxZoom
        }
        return bfglob.mapSetting.maxZoom || 23
      },
      getMapConfigMinZoom() {
        if (typeof bfglob.mapConfig.minZoom === 'number' && bfglob.mapConfig.minZoom >= 0 && bfglob.mapConfig.minZoom < 23) {
          return bfglob.mapConfig.minZoom
        }
        return bfglob.mapSetting.minZoom || 1.5
      },
      initPersonalMapSettings() {
        const saveMapData = bfStorage.getItem('bfdx_saveMapData')
        if (saveMapData) {
          this.personalMapSettings = JSON.parse(saveMapData)
        }
      },
      onMapZoom() {
        const zoom = this.map.getZoom()
        // 更新地图级别的显示
        this.mapLevel = zoom.toFixed(2)
      },
      mapOnload() {
        this.map.on('zoom', this.onMapZoom)
      },
      initBaseMap() {
        this.map = new maplibregl.Map({
          container: this.$el,
          style: this.getMapStyle(),
          center: this.getLocalCenter(),
          zoom: this.getLocalZoom(),
          attributionControl: false,
          maxZoom: this.getMapConfigMaxZoom(),
          minZoom: this.getMapConfigMinZoom(),
          transformRequest: (url, resourceType) => {
            // 请求瓦片地图数据时，添加上token
            // headers会覆盖原有请求的headers，暂不可用
            if (resourceType === 'Tile') {
              return {
                url: `${url}&bftk=${bfglob.bftk}&bfsid=${bfglob.sessionId}`,
              }
            }
            return { url }
          },
        })
        this.map.mapId = this.mapId
        this.$emit('init', this.map)

        this.mapLevel = bfglob.map.getZoom().toFixed(2)
        this.map.addControl(new maplibregl.NavigationControl())
        this.resetMapboxCtrlTitle()
        this.loadCustomCtrlIcon()
        this.controls?.forEach(item => {
          this.map.addControl(item.control, item.position || 'top-right')
        })

        // 监听地图事件程序
        this.map.on('load', this.mapOnload)
      },
      // 绘制设备标记点和经纬度范围
      // drawPointAndRange(lonlatObj) {
      //
      // }
    },
    computed: {
      MapStyleName() {
        return MapStyleName
      },
      mapVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
    },
    watch: {
      visible(newVal) {
        if (!newVal || !this.map) {
          return
        }
        this.$nextTick(() => {
          this.map.resize()
        })
      },
    },
    beforeMount() {
      this.setMapStyle()
      this.initPersonalMapSettings()
    },
    mounted() {
      registerCustomProtocol()
      this.initBaseMap()
    },
  }
</script>

<style scoped lang="scss">
  .base-map-container {
    width: 100%;
    height: 100%;

    .quitMap_btn {
      background: url(@/images/mapImg/map_close.png) no-repeat center center;
    }

    .maplibregl-custom-top-container {
      position: absolute;
      z-index: 100;
      top: 12px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: #fff;
      padding: 6px 10px;
      border-radius: 6px;
      font-size: 1rem;
      max-width: 80%;
      word-break: break-word;

      & > * {
        line-height: 1.5;
      }
    }
  }
</style>
