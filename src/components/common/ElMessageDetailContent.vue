<template>
  <div
    class="bf-message-error"
    style="padding-right: 24px"
  >
    <p
      :class="isOpen ? 'mdi mdi-menu-down' : 'mdi mdi-menu-right'"
      class="cursor-pointer bf-message-error-title"
      @click="toggle"
    >
      {{ message }}
    </p>
    <div
      v-show="isOpen"
      class="italic mt-1 bf-message-error-detail"
    >
      {{ detailMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

//eslint-disable-next-line  vue/require-prop-types
defineProps(['message', 'detailMessage'])

const isOpen = ref(false)
function toggle() {
  isOpen.value = !isOpen.value
}
</script>
