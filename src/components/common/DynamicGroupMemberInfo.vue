<template>
  <div class="dynamic-group-members">
    <div class="member-title">
      {{ $t('dynamicGroup.memberDetails') }}
    </div>
    <div :class="['members', { 'is-disabled': disabled }]">
      <div
        v-for="member in members"
        :key="member.rid"
        class="member-item"
        :title="member.dmrIdLabel"
        @click="clickMemberInfo(member)"
      >
        <span class="member-item-name">
          {{ getMemberOrgName(member) + ' / ' + member.name }}
        </span>
        <el-icon
          v-if="!disabled"
          class="remove-member-action"
          @click.stop="removeMember(member)"
        >
          <CircleClose />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { MemberType } from '@/utils/dynamicGroup/api'

// Props
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  members: {
    type: Array,
    required: true,
  },
})

const emit = defineEmits(['click-member', 'remove-member'])

const getMemberOrgShortName = function (detail) {
  // 组成员，memberOrgId===rid，需要先查找到自己的数据，再读取上级名称
  let memberOrgId = detail.memberOrgId
  if (detail.isDeviceGroup === MemberType.Group) {
    memberOrgId =
      bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)?.parentOrgId ?? ''
  }
  return bfglob.gorgData.getDataMaybeNoPerm(memberOrgId)?.orgShortName ?? '-'
}

const clickMemberInfo = member => {
  if (props.disabled) return
  emit('click-member', member)
}

const removeMember = member => {
  if (props.disabled) return
  emit('remove-member', member)
}

const getMemberOrgName = member => {
  const detail = bfglob.gdynamicGroupDetail.get(member.detailRid)
  if (!detail) {
    if (!member.isOrg) {
      const dev = bfglob.gdevices.get(member.rid)
      if (!dev) return '-'
      return bfglob.gorgData.getShortName(dev.orgId) ?? '-'
    }
    const group = bfglob.gorgData.get(member.rid)
    if (!group) return '-'
    return bfglob.gorgData.get(group.parentOrgId)?.orgShortName ?? '-'
  }
  return getMemberOrgShortName(detail)
}
</script>

<style lang="scss">
.dynamic-group-members {
  .member-title {
    font-size: 16px;
    font-weight: bold;
    line-height: 28px;
  }

  .members {
    padding-left: 16px;
    width: 100%;
    max-height: 330px;
    overflow: auto;

    .member-item {
      line-height: 20px;
      flex-basis: 100%;
      display: flex;
      align-items: center;
      cursor: pointer;

      &:hover {
        .member-item-name,
        .member-item-dmrId {
          color: #20a0ff;
        }
      }

      .member-item-name {
        flex: auto;
        margin-right: 10px;
      }

      .remove-member-action {
        margin-left: 6px;
        cursor: pointer;
        color: #f56c6c;
        font-size: 16px;
      }
    }

    &.is-disabled {
      .member-item {
        cursor: default;

        &:hover {
          .member-item-name,
          .member-item-dmrId {
            color: inherit;
          }
        }

        .remove-member-action {
          display: none;
        }
      }
    }
  }
}
</style>
