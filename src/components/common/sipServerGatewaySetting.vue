<template>
  <el-dialog
    v-model="sipServerGatewayVisible"
    :title="$t('dialog.sipServerGatewayConfig')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    class="header-border footer-border sip-server-setting-dialog"
    @close="onClose"
  >
    <el-form
      ref="formData"
      :model="formData"
      :rules="rules"
      label-width="160px"
      :validate-on-rule-change="false"
      class="grid grid-cols-1"
    >
      <el-form-item prop="host" class="form-item-ellipsis">
        <template #label>
          <span
            class="form-item-label"
            :title="$t('dialog.sipServerGatewayDomain')"
            >{{ $t('dialog.sipServerGatewayDomain') }}</span
          >
        </template>
        <el-input v-model="formData.host" :maxlength="56" />
      </el-form-item>
      <el-form-item
        :label="$t('dialog.sipServerGatewayListenPort')"
        prop="port"
      >
        <el-input-number
          v-model.number="formData.port"
          :min="0x01"
          :max="0xffff"
          step-strictly
        />
      </el-form-item>
      <el-form-item class="form-item-ellipsis listen-range">
        <template #label>
          <span
            class="form-item-label"
            :title="$t('dialog.sipServerGatewayRTPPortRange')"
            >{{ $t('dialog.sipServerGatewayRTPPortRange') }}</span
          >
        </template>
        <el-form-item prop="rtpStart">
          <el-input-number
            v-model="formData.rtpStart"
            :min="0x01"
            :max="0xffff"
            @change="handleMinChange"
          />
        </el-form-item>
        <div class="line">—</div>
        <el-form-item prop="rtpEnd">
          <el-input-number
            v-model="formData.rtpEnd"
            :min="0x01"
            :max="0xffff"
            @change="handleMaxChange"
          />
        </el-form-item>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="text-center actions">
        <el-button
          type="primary"
          class="w-32"
          @click="onConfirm"
          v-text="$t('dialog.confirm')"
        />
        <el-button
          type="warning"
          class="w-32"
          @click="onReset"
          v-text="$t('dialog.reset')"
        />
      </div>
    </template>
  </el-dialog>
</template>

<script>
import validateRules from '@/utils/validateRules'

/**
 * @param host - 公开的 ip 或域名
 * @param port - 监听端口
 * @param rtpStart - rtp端口范围起始
 * @param rtpEnd - rtp端口范围结束
 *
 * 所有的配置项存放在 controller.setting 中
 */
export const DefaultFormData = {
  host: '',
  port: 5060,
  rtpStart: 20000,
  rtpEnd: 20080,
}

export default {
  name: 'SipServerGatewaySetting',
  emits: ['update:modelValue', 'update:visible'],
  props: {
    modelValue: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      formData: {
        ...DefaultFormData,
      },
    }
  },
  computed: {
    fullscreen() {
      return this.$root.layoutLevel === 0
    },
    sipServerGatewayVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    rules() {
      return {
        host: [validateRules.required(), validateRules.checkHost()],
        port: [validateRules.required()],
        rtpStart: [validateRules.required(), this.validateMin()],
        rtpEnd: [validateRules.required(), this.validateMax()],
      }
    },
  },
  methods: {
    onClose() {
      this.sipServerGatewayVisible = false
    },
    async onConfirm() {
      const valid = await this.validate()
      if (!valid) {
        return
      }

      this.$emit('update:modelValue', this.formData)
      this.onClose()
    },
    async validate() {
      return await this.$refs.formData.validate().catch(() => false)
    },
    onReset() {
      this.formData = {
        ...DefaultFormData,
      }
      this.$nextTick(() => {
        this.$refs.formData?.clearValidate()
      })
    },
    handleMinChange() {
      this.$refs.formData.validateField('rtpEnd')
    },
    handleMaxChange() {
      this.$refs.formData.validateField('rtpStart')
    },
    validateMin(trigger = 'blur', message) {
      message = message || this.$t('msgbox.validMinNumber')

      return {
        validator: (rule, value, callback) => {
          const one = Number(value)
          const max = Number(this.formData.rtpEnd)
          if (!max || one < max) {
            return callback()
          }
          return callback(message)
        },
        trigger: trigger,
      }
    },
    validateMax(trigger = 'blur', message) {
      message = message || this.$t('msgbox.validMaxNumber')

      return {
        validator: (rule, value, callback) => {
          const one = Number(value)
          const min = Number(this.formData.rtpStart)
          if (!min || one > min) {
            return callback()
          }
          return callback(message)
        },
        trigger: trigger,
      }
    },
  },
  watch: {
    modelValue: {
      handler(val) {
        this.formData = val
      },
      immediate: true,
      deep: true,
    },
  },
}
</script>

<style lang="scss">
.el-dialog.sip-server-setting-dialog {
  width: 520px;

  .listen-range .el-form-item__content {
    display: flex;

    .line {
      padding: 0 10px;
    }
  }
}
</style>
