<template>
  <div class="flex cursor-pointer select-none items-center mb-4 font-sans text-2xl font-medium" @click="toggle">
    <div class="relative mr-5 flex h-[50px] w-[50px] items-center justify-center">
      <img v-if="props.modelValue" src="@/assets/images/common/checked.svg" alt="checked" class="absolute top-0 left-0 h-full w-full" />
      <img v-else src="@/assets/images/common/unchecked.svg" alt="unchecked" class="absolute top-0 left-0 h-full w-full" />
      <div v-if="props.modelValue" class="z-10 flex h-[28px] w-[28px] items-center justify-center rounded-full border border-[#1e1e1e]">
        <img src="@/assets/images/common/checkmark.svg" alt="checkmark" class="h-[11px] w-[15px]" />
      </div>
    </div>
    <label class="transition-colors duration-300 ease-in-out" :class="props.modelValue ? 'text-white' : 'text-[#1ddbff]'">
      {{ props.label }}
    </label>
  </div>
</template>

<script setup>
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      required: true,
    },
  })
  const emit = defineEmits(['update:modelValue'])
  const toggle = () => {
    emit('update:modelValue', !props.modelValue)
  }
</script>
