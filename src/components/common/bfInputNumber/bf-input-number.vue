<template>
  <el-input
    v-model="formatValue"
    :disabled="disabled"
    class="bf-input-number"
    @change="formatValueChanged"
  >
    <template #prepend>
      <el-button
        icon="minus"
        :disabled="disabled_minus"
        class="bf-input-number-btn"
        @click="minus"
      />
    </template>
    <template #append>
      <el-button
        icon="plus"
        :disabled="disabled_plus"
        class="bf-input-number-btn"
        @click="plus"
      />
    </template>
  </el-input>
</template>

<script>
export default {
  name: 'BfInputNumber',
  emits: ['update:modelValue', 'cusChange', 'change'],
  props: {
    modelValue: {
      type: Number,
      default: 0,
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: Number.MAX_SAFE_INTEGER,
    },
    step: {
      type: Number,
      default: 1,
    },
    formatter: {
      type: Function,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    stepStrictly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      internalValue: 0,
      formatValue: '',
    }
  },
  methods: {
    minus() {
      this.internalValue -= this.step
    },
    plus() {
      this.internalValue += this.step
    },
    updateValue(val = this.internalValue) {
      // 当值发生变更后，向父组件触发更新值事件
      this.$nextTick(() => {
        this.$emit('update:modelValue', val)
      })
    },
    setFormatValue() {
      // 检验最大、最小值
      if (this.internalValue > this.max) {
        this.internalValue = this.max
      }
      if (this.internalValue < this.min) {
        this.internalValue = this.min
      }

      // 如果有格式化方法，则执行格式化方法
      let res
      if (typeof this.formatter === 'function') {
        res = this.formatter(this.internalValue)
      } else {
        // 默认转换为十六进制字符串
        res = this.internalValue.toString(16).toUpperCase()
        if (res.length % 2 !== 0) {
          res = '0' + res
        }
        res = '0x' + res
      }

      this.updateValue()

      this.formatValue = res
    },
    formatValueChanged(val) {
      this.$emit('cusChange', val)
      // 使用parseFloat，因为输入可能为小数
      const v = parseFloat(val)
      if (isNaN(v)) {
        this.internalValue = this.min
      } else {
        // 必须为step的倍数
        const m = v % this.step
        if (this.stepStrictly && m !== 0) {
          // 取余数，余数小于步进值的一半，则向下取值，反之向上取值
          const mod =
            m >= this.step / 2
              ? Math.ceil(v / this.step)
              : Math.floor(v / this.step)
          this.internalValue = mod * this.step
        } else {
          this.internalValue = v
        }
      }

      this.setFormatValue()
      this.$emit('change', this.internalValue)
    },
  },
  watch: {
    modelValue: {
      immediate: true,
      handler(val) {
        this.internalValue = val
      },
    },
    internalValue: {
      immediate: true,
      handler(val, oldVal) {
        this.setFormatValue()
      },
    },
  },
  computed: {
    disabled_minus() {
      return this.disabled || this.internalValue === this.min
    },
    disabled_plus() {
      return this.disabled || this.internalValue === this.max
    },
  },
}
</script>

<style lang="scss">
$bf-input-number-height: 32px;

.bf-input-number {
  min-height: $bf-input-number-height;

  .el-input__inner {
    text-align: center;
    height: $bf-input-number-height;
    line-height: $bf-input-number-height;
  }

  .el-input__wrapper {
    height: $bf-input-number-height;
    line-height: $bf-input-number-height;
  }
}

.bf-input-number .el-input-group__prepend,
.bf-input-number .el-input-group__append {
  padding: 0 14px;
  overflow: hidden;
  height: $bf-input-number-height;
  /*border-color: transparent;*/
}

.bf-input-number .el-button.bf-input-number-btn {
  color: #606266;
}

.bf-input-number .el-button.bf-input-number-btn:focus,
.bf-input-number .el-button.bf-input-number-btn:hover {
  color: #409eff;
}

.bf-input-number .el-button.bf-input-number-btn.is-disabled:focus,
.bf-input-number .el-button.bf-input-number-btn.is-disabled:hover {
  background-color: unset;
  border-color: transparent;
  color: #e4e7ed;
}
</style>
