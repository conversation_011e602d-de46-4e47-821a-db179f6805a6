<template>
  <div class="flex w-full lonLat-wrapper">
    <el-form-item prop="lon" class="lon">
      <el-input v-model="wrapLon" :placeholder="$t('dialog.lon')" />
    </el-form-item>
    <el-form-item prop="lat" class="lat">
      <el-input v-model="wrapLat" :placeholder="$t('dialog.lat')" />
    </el-form-item>
    <el-form-item label-width="0" class="action-container">
      <el-tooltip placement="bottom" :content="$t('dialog.getLngLat')">
        <el-button icon="location" type="primary" @click="get_lonLat_func" />
      </el-tooltip>
    </el-form-item>
    <!-- 获取经纬度和范围的地图 -->
    <el-dialog
      v-model="mapVisible"
      fullscreen
      append-to-body
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <base-map
        ref="baseMap"
        v-model:visible="mapVisible"
        class="lonLat-map-container"
        @init="onInitMap"
        @close="onCloseMap"
      >
        <template #topCenter>
          <div class="get-coordinate-tips">
            {{ $t('map.clickMapGetCoordinates') }}
          </div>
        </template>
      </base-map>
    </el-dialog>
  </div>
</template>

<script>
import baseMap from '@/components/common/BaseMap.vue'
import { deferred } from '@/utils/bfutil'
import { SelectLngLatControl } from '@/utils/map'
import eventBus from '@/utils/eventBus'

let lngLatMap
const lngLatMapReady = deferred()
export default {
  name: 'LonLat',
  emits: ['update:modelValue', 'update:lon', 'update:lat'],
  components: { baseMap },
  props: {
    modelValue: {
      type: Object,
    },
    lon: {
      type: [Number, String],
    },
    lat: {
      type: [Number, String],
    },
  },
  data() {
    return {
      mapVisible: false,
      selectLngLatControl: new SelectLngLatControl(),
    }
  },
  methods: {
    get_lonLat_func() {
      this.mapVisible = true
      this.selectLngLatControl.enable(false)

      const finish = () => {
        this.mapVisible = false
        lngLatMap.getCanvas().style.cursor = ''
      }
      const setLngLat = lngLat => {
        this.wrapLon = lngLat.lng
        this.wrapLat = lngLat.lat
      }
      const mapOnClick = evt => {
        setLngLat(evt.lngLat)
        onMapClose()
      }

      const onMapClose = () => {
        lngLatMap.off('click', mapOnClick)
        finish()
      }

      lngLatMapReady.then(() => {
        eventBus.once('map-close', onMapClose)
        lngLatMap.getCanvas().style.cursor = 'crosshair'
        lngLatMap.on('click', mapOnClick)
      })
    },
    onInitMap(map) {
      lngLatMap = map
      lngLatMapReady.resolve(true)
      this.$nextTick(() => {
        map.resize()
      })
    },
    onCloseMap() {
      this.mapVisible = false
      eventBus.emit('map-close')
    },
  },
  computed: {
    wrapLon: {
      get() {
        return this.lon ?? this.lonLat.lon
      },
      set(value) {
        this.$emit('update:lon', +value)
        if (this.lonLat) {
          this.lonLat.lon = +value
        }
      },
    },
    wrapLat: {
      get() {
        return this.lat ?? this.lonLat.lat
      },
      set(value) {
        this.$emit('update:lat', +value)
        if (this.lonLat) {
          this.lonLat.lat = +value
        }
      },
    },
    lonLat: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      },
    },
  },
}
</script>

<style lang="scss">
.lonLat-wrapper {
  .el-form-item {
    margin-bottom: 0 !important;

    &.lon .el-input__wrapper {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    &.lat .el-input__wrapper {
      border-radius: 0;
    }

    &.action-container .el-button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.lonLat-map-container {
  position: fixed;
  inset: 0;
  z-index: 1000;
  background-color: #fff;
}
</style>
