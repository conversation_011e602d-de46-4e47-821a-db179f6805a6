<template>
  <div class="tree-operator-wrap">
    <el-button
      :type="type"
      :size="size"
      icon="menu"
      class="tree-operator-menu-btn"
      @click="showMenus = !showMenus"
    />
    <transition name="el-zoom-in-center">
      <el-button-group v-show="showMenus" class="tree-operator-btns">
        <el-button
          :type="type"
          :size="size"
          :title="$t('tree.expandAll')"
          @click="expandAll(true)"
        >
          <i class="iconfont icon-expandAll" />
        </el-button>
        <el-button
          :type="type"
          :size="size"
          :title="$t('tree.collapseAll')"
          @click="expandAll(false)"
        >
          <i class="iconfont icon-collapseAll" />
        </el-button>
        <el-button
          :type="type"
          :size="size"
          :title="$t('tree.selectAll')"
          @click="selectAll(true)"
        >
          <i class="iconfont icon-selectedAll" />
        </el-button>
        <el-button
          :type="type"
          :size="size"
          :title="$t('tree.deselectAll')"
          @click="selectAll(false)"
        >
          <i class="iconfont icon-deselectAll" />
        </el-button>
      </el-button-group>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'TreeToolbar',
  props: {
    size: {
      type: String,
      default: 'small',
    },
    type: {
      type: String,
      default: 'primary',
    },
  },
  data() {
    return {
      showMenus: false,
    }
  },
  methods: {
    expandAll(bool) {
      this.$parent.expandAll(bool)
    },
    selectAll(bool) {
      this.$parent.selectAll(bool)
    },
  },
}
</script>

<style>
.tree-operator-wrap {
  position: absolute;
  bottom: 0;
  right: 16px;
  z-index: 1;
  display: flex;
}

.tree-operator-wrap .el-button {
  padding: 6px;
}

.tree-operator-wrap .tree-operator-menu-btn {
  order: 1;
  margin: 0 1px;
  padding: 8px;
}
</style>
