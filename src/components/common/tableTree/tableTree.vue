<template>
  <section class="w-full h-full overflow-hidden fancytree-grid-wrapper">
    <el-input
      v-if="filter"
      ref="filterInput"
      v-model="filterValue"
      :placeholder="$t('tree.filter')"
      clearable
      prefix-icon="search"
      class="w-full search_input_box"
      @input="filterOnChange"
      @keydown.enter="filterOnChange"
    />
    <div ref="fancytreeGridContainer" class="relative w-full fancytree-grid-container" :class="{ 'has-filter': filter }">
      <table :id="treeId" ref="tableTree" class="w-full bf-tree">
        <!-- <caption>Loading&hellip;</caption> -->
        <colgroup>
          <col width="*" />
        </colgroup>
        <thead>
          <tr>
            <th />
          </tr>
        </thead>
      </table>

      <div ref="scrollBar" class="fancytree-ext-table-scroll-bar" />

      <treeToolbar v-if="toolbar" class="treeOptBtns" />
    </div>
  </section>
</template>

<script>
  import 'jquery'
  import 'jquery-ui/dist/jquery-ui'
  import 'ui-contextmenu'
  import 'jquery.fancytree'
  import 'jquery.fancytree/dist/modules/jquery.fancytree.filter'
  import 'jquery.fancytree/dist/modules/jquery.fancytree.grid'
  import 'jquery.fancytree/dist/skin-win8/ui.fancytree.css'
  import { debounce } from 'lodash'
  import bftree, { defaultTreeId, LocalTree, sortChildren, toDictTree } from '@/utils/bftree'
  import './plugins/plain-scrollbar.css'
  import PlainScrollbar from './plugins/plain-scrollbar.js'
  import { defineAsyncComponent } from 'vue'

  const ContextmenuOption = {
    delegate: 'span.fancytree-node',
    autoFocus: true,
    menu: [],
    select: (_event, _ui) => {
      return false
    },
  }

  export default {
    name: 'TableTree',
    emits: ['filterValueChange', 'loaded', 'select', 'dblclick', 'click', 'isFilterOnlineChange'],
    props: {
      toolbar: {
        type: Boolean,
        default: false,
      },
      treeId: {
        type: String,
        default: 'ft_' + Date.now(),
      },
      option: {
        type: Object,
      },
      selected: {
        type: Array,
        default: () => {
          return []
        },
      },
      inDialog: {
        type: Boolean,
        default: false,
      },
      contextmenuOption: {
        type: Object,
      },
      filter: {
        type: Boolean,
        default: true,
      },
      filterOption: {
        type: Object,
        default: () => {
          return {}
        },
      },
    },
    data() {
      return {
        oneNodeHeight: 22,
        isFilterOnline: false,
        filterValue: '',
      }
    },
    methods: {
      // 缓存Tree
      getLocalTree() {
        return LocalTree[this.treeId]
      },
      setLocalTree(tree) {
        LocalTree[this.treeId] = tree
      },
      getNodeByKey(key) {
        return this.getLocalTree().getNodeByKey(key)
      },
      getTree() {
        return this.getLocalTree()
      },
      getRootNode() {
        return this.getLocalTree().getRootNode()
      },
      clearTree() {
        const tree = this.getLocalTree()
        tree.clear()
        tree.render(true, true)
      },
      removeNode(key) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }
        node.remove()
        this.updateViewport()
      },
      removeNodeChildren(key) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }
        node.removeChildren()
        this.updateViewport()
      },
      addNodeChildren(parentNode, children, pos) {
        if (!parentNode) {
          return
        }
        this.$nextTick(() => {
          this.updateViewport()
        })
        return parentNode.addChildren(children, pos)
      },
      sortChildren(sortMethod) {
        sortChildren(this.treeId, sortMethod)
      },
      renderTitle(key, title) {
        const node = this.getTree().getNodeByKey(key)
        if (!node) {
          return
        }
        node.title = title
        node.renderTitle()
        this.updateViewport()
      },
      toDictTree(srcTreeId = defaultTreeId, toDictCb) {
        return toDictTree(this.treeId, srcTreeId, toDictCb)
      },
      unselectableNodeByKey(key, status, redraw = false) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }

        node.unselectable = status || undefined
        node.unselectableStatus = status || undefined
        if (redraw) {
          this.updateViewport()
        }
      },

      setNodeSelected(key, status, redraw = false) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }
        node.setSelected(status)
        if (redraw) {
          this.$nextTick(() => {
            this.updateViewport()
          })
        }
      },
      selectAll(status = true) {
        this.getTree().selectAll(status)
      },
      expandAll(flag = true, opts = {}) {
        this.getTree().expandAll(flag, opts)
      },

      showOnlineDevices() {
        this.filterValue = ''
        this.isFilterOnline = true
        bftree.showOnlineDevices(this.treeId)
      },
      showAllDevices() {
        this.filterValue = ''
        this.isFilterOnline = false
        bftree.showAllDevices(this.treeId)
      },
      reFilterOnline() {
        // 如果fancytree处于文本过虑中，则按filter重新过滤
        if (this.filterValue) {
          this.filterNodes()
          return
        }

        // 处于在线过滤中，则重新过滤在线终端
        if (this.isFilterOnline) {
          this.showOnlineDevices()
        } else {
          this.showAllDevices()
        }
      },
      clearFilter() {
        this.filterValue = ''
        const tree = this.getLocalTree()
        tree && tree.clearFilter()
      },
      filterNodes() {
        const tree = this.getLocalTree()
        tree?.filterNodes(this.filterValue, tree.options.filter)
      },
      filterOnChange() {
        if (this.filterValue) {
          this.filterNodes()
        } else {
          this.clearFilter()
        }
        this.$nextTick(() => {
          this.$emit('filterValueChange', this.filterValue)
        })
      },
      showEntry(cmd, status = true) {
        $(this.tableTree).contextmenu('showEntry', cmd, status)
      },
      enableEntry(cmd, status = true) {
        $(this.tableTree).contextmenu('enableEntry', cmd, status)
      },
      replaceMenu(menu = []) {
        $(this.tableTree).contextmenu('replaceMenu', menu)
      },
      loadContextmenu() {
        if (!this.contextmenuOption) {
          return
        }
        $(this.tableTree).contextmenu({
          beforeOpen: this.inDialog
            ? (event, ui) => {
                // 找到dialog元素的z-index
                let el = event.target
                while (el) {
                  if (el.classList.contains('el-dialog__wrapper')) {
                    break
                  }
                  el = el.parentNode
                }
                const zIndex = +(el?.style.zIndex ?? 6000) + 2
                // 修正右键菜单的z-index，避免被dialog遮住
                if (ui.menu[0]) {
                  ui.menu[0].style.zIndex = zIndex
                }
              }
            : undefined,
          ...ContextmenuOption,
          ...this.contextmenuOption,
        })
      },
      getViewportCount() {
        if (!this.treeContainer) {
          return 0
        }
        return Math.floor(this.treeContainer.offsetHeight / this.oneNodeHeight)
      },
      updateTreeScrollbars(tree) {
        const scrollbar = tree.verticalScrollbar
        if (!scrollbar) {
          return
        }

        scrollbar.set(
          {
            start: tree.viewport.start,
            total: tree.visibleNodeList.length,
            visible: tree.viewport.count,
          },
          true
        )
      },
      async updateViewport(tree = this.getLocalTree(), resize = true) {
        if (!tree) {
          return
        }

        let start = tree.viewport.start
        if (isNaN(start)) {
          start = 0
        }
        const getCount = () => {
          return new Promise(resolve => {
            const entries = new Array(10).entries()
            const main = it => {
              if (it.done) {
                return 10
              }
              const count = this.getViewportCount()
              if (count > 0) {
                return resolve(count)
              }

              setTimeout(() => {
                main(entries.next())
              }, 100)
            }
            main(entries.next())
          })
        }
        const count = await getCount().catch(() => {})
        const data = {
          count,
          start,
        }
        this.setOption('viewport', data)
        tree.setViewport(data)
        if (resize) {
          this.$nextTick(() => {
            tree.redrawViewport(true)
          })
        }
      },
      initTree() {
        $(this.$refs.tableTree).fancytree({
          extensions: ['filter', 'grid'],
          checkbox: true,
          quicksearch: true,
          autoScroll: true,
          selectMode: 3,
          debugLevel: 0,
          // Defines what happens, when the user click a folder node.
          // 1:activate, 2:expand, 3:activate and expand, 4:activate/dblclick expands (default: 4)
          clickFolderMode: 1,
          filter: {
            autoApply: false, // 打开时是否自动加载数据
            autoExpand: true, // 父、子节点筛选
            counter: false, // 在图标处显示有几个匹配节点
            fuzzy: false, //  输入'fb' 匹配'FooBar'
            hideExpandedCounter: true, // 隐藏徽章
            hideExpanders: true, // 如果所有子节点都通过筛选隐藏，隐藏扩展
            highlight: true, // 高亮
            leavesOnly: false, // 只显示符合条件的节点
            nodata: true, // 没有符合条件的节点，不显示 nodata
            mode: 'hide', // "hide" or "dimm"
            ...this.filterOption,
          },
          sortChildren: true,
          strings: {
            noData: this.$t('dialog.noMatchText'),
          },
          nodata: false,
          source: [],
          table: {
            indentation: 20,
            nodeColumnIdx: 0,
          },
          viewport: {
            enabled: true,
            count: this.getViewportCount(),
          },
          preInit: (event, data) => {
            const tree = data.tree
            tree.verticalScrollbar = new PlainScrollbar({
              numberOfItems: {
                start: 0,
                total: 100,
                visible: 10,
              },
              orientation: 'vertical',
              onSet: numberOfItems => {
                tree.setViewport({
                  start: Math.round(numberOfItems.start),
                  count: this.getViewportCount(),
                })
              },
              scrollbarElement: this.scrollBar,
            })
          },
          init: (event, data) => {
            data.tree.$container.addClass('fancytree-connectors')
            this.setLocalTree(data.tree)
            data.tree.adjustViewportSize()
            this.loadContextmenu()
            this.selectDefaultNode(this.selected)
            setTimeout(() => {
              this.updateTreeScrollbars(data.tree)
              this.$emit('loaded')
            }, 0)
          },
          updateViewport: (event, data) => {
            this.updateTreeScrollbars(data.tree)
          },
          select: (event, data) => {
            this.$emit('select', event, data)
          },
          dblclick: (event, data) => {
            this.$emit('dblclick', event, data)
          },
          click: (event, data) => {
            this.$emit('click', event, data)
          },
          ...this.option,
        })
      },
      setOption(optionName, value) {
        const tableTree = this.getTree()
        if (!tableTree) {
          return
        }

        tableTree.setOption(optionName, value)
      },
      selectDefaultNode(keys) {
        if (!Array.isArray(keys) || keys.length === 0) {
          return
        }
        for (let i = 0; i < keys.length; i++) {
          this.setNodeSelected(keys[i], true)
        }
        this.updateViewport()
      },
    },
    beforeMount() {
      this.filterOnChange = debounce(this.filterOnChange, 200)
    },
    mounted() {
      this.initTree()
      bfglob.on('refilterOnlineDev', this.reFilterOnline)
      this.$nextTick(() => {
        this.updateViewport()
      })
    },
    beforeUnmount() {
      bfglob.off('refilterOnlineDev', this.reFilterOnline)
    },
    watch: {
      filterValue(val) {
        if (val) {
          this.isFilterOnline = false
        }
      },
      isFilterOnline(val) {
        this.$emit('isFilterOnlineChange', val)
      },
      selected: {
        deep: true,
        handler(val) {
          this.selectDefaultNode(val)
        },
      },
      '$i18n.locale'() {
        this.loadContextmenu()
      },
      contextmenuOption: {
        deep: true,
        handler() {
          this.loadContextmenu()
        },
      },
    },
    computed: {
      tableTree() {
        return this.$refs.tableTree
      },
      treeContainer() {
        return this.$refs.fancytreeGridContainer
      },
      scrollBar() {
        return this.$refs.scrollBar
      },
      filterInput() {
        return this.$refs.filterInput
      },
      scrollBarHeight() {
        if (this.filter && this.filterInput) {
          return this.filterInput.offsetHeight
        }

        return 0
      },
    },
    components: {
      treeToolbar: defineAsyncComponent(() => import('./treeToolbar')),
    },
  }
</script>

<style lang="scss">
  @import url('jquery-ui/themes/base/all.css');

  .fancytree-grid-wrapper {
    .fancytree-grid-container {
      height: 100%;
      padding-right: 10px;

      &.has-filter {
        height: calc(100% - 32px);
      }
    }

    .fancytree-node mark,
    .fancytree-node .mark {
      padding: unset;
      background-color: #ffeb3b;
    }

    table.fancytree-container.fancytree-ext-table {
      outline: none;
      width: 100%;
      height: 100%;
      display: flex;
      position: relative;
      overflow-x: auto;
      overflow-y: hidden;

      thead tr th,
      tbody tr td {
        text-align: left;
      }

      tbody {
        padding-right: 8px;
      }

      tbody tr {
        &.fancytree-exp-n span.fancytree-node:before {
          width: 16px;
          height: 22px;
          content: '';
          position: absolute;
          background-image: url('/node_modules/jquery.fancytree/dist/skin-win8/vline.gif');
        }

        &:hover,
        &.fancytree-active {
          background-color: transparent;
          outline: none;
        }

        & td {
          border: none;
          padding: 0;

          span.fancytree-node {
            line-height: 20px;
            white-space: nowrap;
          }

          span.fancytree-node:hover span.fancytree-title {
            background-color: #eff9fe;
            border-color: #70c0e7;
          }

          span.fancytree-title {
            border-radius: 3px;
            margin: 0;
            padding: 0;
            display: inline-flex;
          }

          span.fancytree-title .iconfont {
            color: inherit;
            margin-right: 2px;

            &.icon-virtual-org {
              color: #673ab7;
            }

            &.icon-task-group.init,
            &.icon-temp-group.init {
              color: #212121;
            }

            &.icon-task-group.delete,
            &.icon-temp-group.delete {
              color: #c62828;
            }

            &.icon-task-group.valid,
            &.icon-temp-group.valid {
              color: #388e3c;
            }

            &.icon-temp-group.invalid {
              color: #ffa726;
            }
          }
        }

        &.fancytree-selected {
          background-color: transparent;
        }

        &.fancytree-active:hover,
        &.fancytree-selected:hover {
          background-color: transparent;
          outline: none;
        }

        &.fancytree-focused span.fancytree-title {
          outline: none;
        }

        &.fancytree-active span.fancytree-title:hover,
        &.fancytree-selected .fancytree-title:hover,
        &.fancytree-focused span.fancytree-title {
          border-color: #719acb !important;
          color: inherit;
          background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #f2f9fd), to(#c4e8fa));
          background: linear-gradient(180deg, #f2f9fd 0, #c4e8fa);
        }
      }
    }

    .fancytree-ext-table-scroll-bar.scrollbar-vertical {
      width: 8px;

      &.plain-scrollbar[data-scrollable='false'] {
        visibility: hidden;
      }

      &:hover,
      &:active,
      &:focus {
        box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.4);
        background-color: rgba(0, 0, 0, 0.01);
      }

      .arrow-up,
      .arrow-down {
        display: none;
      }

      .slider-area {
        top: 0;
        bottom: 0;
        width: 100%;

        .slider {
          width: 100%;
          border-radius: 4px;
          background-color: rgba(0, 0, 0, 0.2);
          box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.1);

          &:hover,
          &:focus {
            background-color: rgba(0, 0, 0, 0.35);
            box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.1);
          }

          &:active {
            background: rgba(0, 0, 0, 0.5);
          }
        }
      }
    }
  }
</style>
