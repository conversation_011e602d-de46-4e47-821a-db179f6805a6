<template>
  <div class="menu-item-wrapper h-[72px] mb-[13px] flex items-center">
    <div
      :class="[
        'menu-button relative flex items-center cursor-pointer select-none pl-[15px]',
        'transition-all duration-300 ease-in-out',
        'active:scale-98',
        { active: isActive },
        isActive ? 'w-[252px] h-[72px]' : 'w-[210px] h-[60px]',
      ]"
      @click="handleClick"
    >
      <div
        :style="{ backgroundImage: `url(${iconUrl})` }"
        :class="[
          'icon-display',
          'relative z-10 w-[41px] h-[31px] mr-[15px] transition-all duration-300 ease-in-out',
          { 'transform -translate-y-[4.5px]': isActive },
        ]"
      />
      <EllipsisText
        :class="[
          'relative z-10 text-white font-bold [text-shadow:1px_1px_2px_rgba(0,0,0,0.5)] transition-all duration-300 ease-in-out !mb-0',
          isActive ? 'text-[26px] !w-[160px]' : 'text-[21px] !w-[120px]',
          { 'transform -translate-y-[4.5px]': isActive },
        ]"
        :content="navItem.label"
      ></EllipsisText>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { useI18n } from 'vue-i18n'
  import EllipsisText from './EllipsisText.vue'

  const { t } = useI18n()
  const props = defineProps({
    navItem: {
      type: Object,
      required: true,
    },
    isActive: {
      type: Boolean,
      required: true,
    },
  })

  const emit = defineEmits(['click'])

  const getImageUrl = (path: string) => {
    if (!path) return ''
    // Note: The path is relative to the assets directory in the final build.
    // This assumes a similar structure as the original component.
    return new URL(`../../assets/${path}`, import.meta.url).href
  }

  const iconPath = computed(() => {
    return props.isActive ? props.navItem.activeIconPath : props.navItem.inactiveIconPath
  })

  const iconUrl = computed(() => {
    return getImageUrl(iconPath.value)
  })

  const handleClick = () => {
    emit('click', props.navItem)
  }
</script>

<style scoped>
  .menu-button {
    background-image: url('@/assets/images/manage/menu_btn.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-color: transparent;
    /* 明确告诉浏览器哪些属性需要过渡效果 */
    transition-property: width, height, transform;
  }
  .menu-button.active {
    background-image: url('@/assets/images/manage/menu_btn_selected.svg');
  }

  .icon-display {
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    background-color: transparent;
  }
</style>
