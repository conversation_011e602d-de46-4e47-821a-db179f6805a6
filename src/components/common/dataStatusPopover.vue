<template>
  <section class="repeater-status-wrapper">
    <el-descriptions v-if="showBasicStatus" title="" border :column="2">
      <el-descriptions-item label="DMRID">
        {{ dmrIdLabel }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dialog.channel')">
        {{ $t('dialog.channel') }}
        {{ repeaterStatus.channelId + 1 }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dialog.receiveFrequency')">
        {{ rxFrequency }} Mhz
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dialog.transFrequency')">
        {{ txFrequency }} Mhz
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dialog.power')">
        {{ repeaterStatus.powerValue }} W
      </el-descriptions-item>
      <el-descriptions-item :label="$t('dataTable.IPAddress')">
        {{ ipAddr }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('repeaterStatus.voltage')">
        {{ volValue }} V
      </el-descriptions-item>
      <el-descriptions-item :label="$t('repeaterStatus.temperature')">
        {{ tmpValue }} ℃
      </el-descriptions-item>
    </el-descriptions>

    <section class="repeater-status-container">
      <div
        v-for="status in repeaterStatusList"
        :key="status.label"
        class="repeater-status-item"
      >
        <span class="status-icon" :class="status.color"
          ><i :class="status.icon"
        /></span>
        <div class="repeater-status-item-lable">
          {{ status.label }}
        </div>
      </div>
    </section>
  </section>
</template>
<script>
import { formatDmrIdLabel, frequencyHz2Mhz, toHexDmrId } from '@/utils/bfutil'

export default {
  name: 'RepeaterDataStatus',
  props: {
    showBasicStatus: {
      type: Boolean,
      default: true,
    },
    repeaterStatus: {
      type: Object,
      required: true,
    },
  },
  computed: {
    dmrId() {
      return toHexDmrId(this.repeaterStatus.deviceDmrid, false)
    },
    dmrIdLabel() {
      return formatDmrIdLabel(this.dmrId)
    },
    rxFrequency() {
      return frequencyHz2Mhz(this.repeaterStatus.rxFrequency) || 0
    },
    txFrequency() {
      return frequencyHz2Mhz(this.repeaterStatus.txFrequency) || 0
    },
    ipAddr() {
      return `${this.repeaterStatus.ipAddr >>> 24}.${(this.repeaterStatus.ipAddr & 0xff0000) >>> 16}.${(this.repeaterStatus.ipAddr & 0xff00) >>> 8}.${this.repeaterStatus.ipAddr & 0xff}`
    },
    volValue() {
      if (this.repeaterStatus.volValue === 0) return '0'
      // 将毫伏转换为伏 1V=10**3mV
      const vol = this.repeaterStatus.volValue / 1000
      return `${vol.toFixed(1)}`
    },
    tmpValue() {
      if (this.repeaterStatus.volValue === 0) return '0'

      // 转换正常值，被放大了10倍，摄氏度
      const vol = this.repeaterStatus.tmpValue / 10
      return `${vol.toFixed(1)}`
    },

    commonErrLabel() {
      return {
        0: this.$t('repeaterWriteFreq.normal'),
        1: this.$t('repeaterWriteFreq.abnormal'),
      }
    },
    volStatus() {
      const icons = {
        0: 'mdi mdi-battery',
        1: 'mdi mdi-battery-arrow-up',
        2: 'mdi mdi-battery-arrow-down-outline',
      }
      const colors = {
        0: 'text-success',
        1: 'text-danger',
        2: 'text-warning',
      }
      const labels = {
        0:
          this.$t('repeaterStatus.voltage') +
          this.$t('repeaterWriteFreq.normal'),
        1:
          this.$t('repeaterStatus.voltage') +
          this.$t('repeaterWriteFreq.tooHigh'),
        2:
          this.$t('repeaterStatus.voltage') +
          this.$t('repeaterWriteFreq.tooLow'),
      }
      return {
        icon: icons[this.repeaterStatus.volErr] ?? icons[0],
        color: colors[this.repeaterStatus.volErr] ?? colors[0],
        label: labels[this.repeaterStatus.volErr] ?? labels[0],
      }
    },
    tmpStatus() {
      const icons = {
        0: 'mdi mdi-thermometer',
        1: 'mdi mdi-thermometer-chevron-up',
        2: 'mdi mdi-thermometer-chevron-down',
      }
      const colors = {
        0: 'text-success',
        1: 'text-danger',
        2: 'text-warning',
      }
      const labels = {
        0:
          this.$t('repeaterStatus.temperature') +
          this.$t('repeaterWriteFreq.normal'),
        1:
          this.$t('repeaterStatus.temperature') +
          this.$t('repeaterWriteFreq.tooHigh'),
        2:
          this.$t('repeaterStatus.temperature') +
          this.$t('repeaterWriteFreq.tooLow'),
      }
      return {
        icon: icons[this.repeaterStatus.tmpErr] ?? icons[0],
        color: colors[this.repeaterStatus.tmpErr] ?? colors[0],
        label: labels[this.repeaterStatus.tmpErr] ?? labels[0],
      }
    },
    gpsStatus() {
      const icons = {
        0: 'mdi mdi-crosshairs-off',
        1: 'mdi mdi-crosshairs',
        2: 'mdi mdi-crosshairs-gps',
      }
      const colors = {
        0: 'text-info',
        1: 'text-warning',
        2: 'text-success',
      }
      const labels = {
        0:
          this.$t('repeaterStatus.gps') +
          this.$t('repeaterStatus.notInstalled'),
        1: this.$t('repeaterStatus.gps') + this.$t('repeaterStatus.notSynced'),
        2: this.$t('repeaterStatus.gps') + this.$t('repeaterStatus.synced'),
      }
      return {
        icon: icons[this.repeaterStatus.gpsErr] ?? icons[0],
        color: colors[this.repeaterStatus.gpsErr] ?? colors[0],
        label: labels[this.repeaterStatus.gpsErr] ?? labels[0],
      }
    },
    fanStatus() {
      const icons = {
        0: 'mdi mdi-fan',
        1: 'mdi mdi-fan-alert',
      }
      const colors = {
        0: 'text-success',
        1: 'text-danger',
      }
      const labels = {
        0: this.$t('repeaterStatus.fan') + this.$t('repeaterWriteFreq.normal'),
        1:
          this.$t('repeaterStatus.fan') + this.$t('repeaterWriteFreq.abnormal'),
      }
      return {
        icon: icons[this.repeaterStatus.fanErr] ?? icons[0],
        color: colors[this.repeaterStatus.fanErr] ?? colors[0],
        label: labels[this.repeaterStatus.fanErr] ?? labels[0],
      }
    },
    rxPllStatus() {
      const icons = {
        0: 'mdi mdi-message',
        1: 'mdi mdi-message-lock',
      }
      const colors = {
        0: 'text-success',
        1: 'text-danger',
      }
      const labels = {
        0:
          this.$t('repeaterStatus.rxPll') + this.$t('repeaterWriteFreq.normal'),
        1:
          this.$t('repeaterStatus.rxPll') +
          this.$t('repeaterWriteFreq.abnormal'),
      }
      return {
        icon: icons[this.repeaterStatus.rxPllErr] ?? icons[0],
        color: colors[this.repeaterStatus.rxPllErr] ?? colors[0],
        label: labels[this.repeaterStatus.rxPllErr] ?? labels[0],
      }
    },
    txPllStatus() {
      const icons = {
        0: 'mdi mdi-send',
        1: 'mdi mdi-send-lock',
      }
      const colors = {
        0: 'text-success',
        1: 'text-danger',
      }
      const labels = {
        0:
          this.$t('repeaterStatus.txPll') + this.$t('repeaterWriteFreq.normal'),
        1:
          this.$t('repeaterStatus.txPll') +
          this.$t('repeaterWriteFreq.abnormal'),
      }
      return {
        icon: icons[this.repeaterStatus.txPllErr] ?? icons[0],
        color: colors[this.repeaterStatus.txPllErr] ?? colors[0],
        label: labels[this.repeaterStatus.txPllErr] ?? labels[0],
      }
    },
    antStatus() {
      const icons = {
        0: 'mdi mdi-antenna',
        1: 'mdi mdi-antenna',
      }
      const colors = {
        0: 'text-success',
        1: 'text-danger',
      }
      const labels = {
        0: this.$t('repeaterStatus.ant') + this.$t('repeaterWriteFreq.normal'),
        1:
          this.$t('repeaterStatus.ant') + this.$t('repeaterWriteFreq.abnormal'),
      }
      return {
        icon: icons[this.repeaterStatus.antErr] ?? icons[0],
        color: colors[this.repeaterStatus.antErr] ?? colors[0],
        label: labels[this.repeaterStatus.antErr] ?? labels[0],
      }
    },
    signalStatus() {
      const icons = {
        0: 'mdi mdi-signal',
        1: 'mdi mdi-signal-off',
      }
      const colors = {
        0: 'text-success',
        1: 'text-danger',
      }
      const labels = {
        0:
          this.$t('dialog.nothing') +
          this.$t('repeaterStatus.signalInterference'),
        1: this.$t('repeaterStatus.signalInterference'),
      }

      return {
        icon: icons[this.repeaterStatus.signal] ?? icons[0],
        color: colors[this.repeaterStatus.signal] ?? colors[0],
        label: labels[this.repeaterStatus.signal] ?? labels[0],
      }
    },
    repeaterStatusList() {
      return [
        this.volStatus,
        this.tmpStatus,
        this.gpsStatus,
        this.fanStatus,
        this.rxPllStatus,
        this.txPllStatus,
        this.antStatus,
        this.signalStatus,
      ]
    },
  },
}
</script>

<style lang="scss">
.repeater-status-wrapper {
  .repeater-status-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    justify-content: center;
    margin-top: 10px;

    .repeater-status-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 20%;

      .repeater-status-item-lable {
        word-break: break-word;
        white-space: normal;
      }

      .status-icon {
        font-size: 1.5rem;
      }
    }
  }
}
</style>
