<template>
  <el-header class="shadow-md flex flex-col bf-header">
    <section class="flex items-center shadow-sm px-2 base-header">
      <div v-if="layout === 0" ref="navbtn" class="nav-btn">
        <el-button
          type="primary"
          :title="$t('header.navBtn')"
          @click="quickShowNavBar"
        >
          <i class="iconfont icon-nav" />
          <span v-text="$t('header.navBtn')" />
        </el-button>
      </div>
      <div v-if="layout >= 1" ref="logo" class="logo">
        <img :src="logoImage" />
      </div>

      <el-menu
        v-show="showNav"
        :mode="menuMode"
        unique-opened
        class="md:ml-2 nav-wrap bf-menu"
        :class="{ 'mobile-nav-wrap': mobileNav }"
        :ellipsis="false"
        :default-active="$route.name"
      >
        <template v-for="(item, idx) in navMenu">
          <template v-if="item.submenu">
            <el-sub-menu
              v-if="item.submenu.length > 0 && showNav"
              :key="idx"
              :index="item.index"
              popper-class="header-top-submenu"
            >
              <template #title>
                <span v-text="item.text()" />
              </template>
              <el-menu-item
                v-for="(nav, index) in item.submenu"
                :key="index"
                :index="nav.index"
                @click="menuItemOnClick(nav)"
              >
                <i class="iconfont pr-1" :class="nav.iconClass" />
                <span v-text="nav.text()" />
              </el-menu-item>
            </el-sub-menu>
          </template>
          <el-menu-item
            v-else
            :key="idx"
            :index="item.index"
            :class="item.classes"
            @click="menuItemOnClick(item)"
          >
            <i
              v-if="item.iconClass"
              :class="item.iconClass"
              class="iconfont pr-1"
            />
            <span v-text="item.text()" />
          </el-menu-item>
        </template>
      </el-menu>

      <scrollingText
        v-if="layout >= 1"
        ref="scrollingText"
        class="text-white scrolling-container"
        :scrollStatus="userSetting.scrollMoveTitle"
        :title="moveTitle"
      />

      <div ref="setting" class="dropdown-wrap">
        <el-dropdown
          :class="['user-drop-down', layout === 0 ? 'mobile-drop-down' : '']"
          ref="userDropdown"
          :hide-on-click="false"
          trigger="click"
          @command="settingCommand"
        >
          <el-button type="primary">
            <p class="max-w-32 m-0 p-0 truncate">
              {{ loginName }}
            </p>
            <el-icon class="el-icon--right">
              <Setting />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu class="user-dorp-down-menu">
              <template v-for="item in headDropDownMenu">
                <el-dropdown-item
                  v-if="item.subMenus?.length > 0"
                  :key="'subMenu-' + item.menuId"
                  :command="item.command"
                >
                  <el-dropdown
                    :visible-arrow="false"
                    placement="left-start"
                    trigger="click"
                    class="submenu-dropdown"
                    @command="item.onCommand"
                  >
                    <div class="submenu-title">
                      <span
                        :id="item.menuId"
                        class="userDroItem"
                        :class="item.menuClass || ''"
                        v-text="item.text"
                      />
                      <span class="space" />
                      <el-icon>
                        <ArrowRight />
                      </el-icon>
                    </div>
                    <template #dropdown>
                      <el-dropdown-menu :visible-arrow="false">
                        <el-dropdown-item
                          v-for="subItem in item.subMenus"
                          :key="subItem.menuId"
                          :command="subItem.command"
                        >
                          <i
                            class="lang_icon"
                            :class="subItem.iconClass || ''"
                          />
                          <span
                            :id="subItem.menuId"
                            class="userDroItem"
                            :class="subItem.menuClass || ''"
                            v-text="subItem.text"
                          />
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </el-dropdown-item>
                <el-dropdown-item
                  v-else
                  :key="item.menuId"
                  :command="item.command"
                >
                  <span
                    :id="item.menuId"
                    class="userDroItem"
                    :class="item.menuClass || ''"
                    v-text="item.text"
                  />
                </el-dropdown-item>
              </template>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <div v-if="layout >= 1" ref="time" class="text-white time-wrap">
        <p class="nav-date" v-text="serverTime" />
      </div>
      <div v-if="layout === 0" ref="orgbtn" class="org-btn">
        <el-button
          type="primary"
          :title="$t('header.orgList')"
          @click="toggleTree"
        >
          <i class="iconfont icon-list" />
          <span v-text="$t('header.orgList')" />
        </el-button>
      </div>
      <div ref="serverStats" class="state-wrap">
        <i
          v-if="serverStats"
          class="iconfont icon-connect success"
          :title="$t('loginDlg.connected')"
        />
        <i
          v-else
          class="iconfont icon-disconnect danger"
          :title="$t('loginDlg.disconnect')"
        />
      </div>
      <audio ref="audio" hidden :src="alarmAudio" />
      <div v-if="mobileNav" class="nav-mask" @click="closeMenuWrap" />
    </section>

    <!-- 快速导航tag，记录打开过的路由 -->
    <quick-routing-tags
      v-show="routingTagsNotOnlyOne"
      ref="quickRoutingTags"
      :tags="routingTags"
      class="flex-auto"
      @remove="removeQuickRoutingTag"
    />
  </el-header>
</template>

<script>
import { defineAsyncComponent } from 'vue'
import scrollingText from '@/components/common/scrollingText.vue'
import screenfull from 'screenfull'
import {
  getDisplayLabel,
  loadLanguageAsync,
  SupportedLang,
  SupportedLangList,
} from '@/modules/i18n'
import bfutil, {
  notRepeaterWfPermission,
  notSendCmdPermission,
} from '@/utils/bfutil'
import login from '@/utils/login'
import bfNotify, { messageBox, Types } from '@/utils/notify'
import bfStorage from '@/utils/storage'
import bfTime from '@/utils/time'
import vueMixin from '@/utils/vueMixin'
import {
  checkLicenseModuleAuth,
  findModuleNameByMenuIndex,
  getAuthModuleI18nKey,
  getLicense,
} from '@/utils/bfAuth'
import { cloneDeep } from 'lodash'
import { BASE_URL, Enable_License } from '@/envConfig'

const BfdxUrl = 'http://www.bfdx.com'
const BelfoneUrl = 'https://www.belfone.com/'
const AboutOfficialWebsiteUrls = {
  [SupportedLang.zhCN]: BfdxUrl,
  [SupportedLang.enUS]: BelfoneUrl,
  [SupportedLang.fr]: BelfoneUrl,
}

// 开启授权校验配置，默认开启
const enableLicense = (() => Enable_License)()
bfglob.console.log('enable license:', enableLicense)

const RouterAuthMethods = {
  sendcmd: notSendCmdPermission,
  repeaterWriteFrequency: notRepeaterWfPermission,
  // "interphoneWriteFrequency": notIntercomWfPermission
}

export default {
  name: 'BfHead',
  mixins: [vueMixin],
  emits: ['openMenuItem', 'head-height'],
  data() {
    return {
      layout: bfglob.layout,
      serverTime: bfTime.nowLocalTime(),
      loginName: bfglob.userInfo.name,
      serverStats: false,
      showNav: bfglob.layout > 0,
      logoImage: `${BASE_URL}logo.${bfglob.siteConfig?.logoExt || 'jpg'}`,
      moveTitle: '',
      userSetting: cloneDeep(bfglob.userInfo.setting),
      menuMode: 'horizontal',
      routingTags: [],
      routingTagsSet: new Set(),
    }
  },
  methods: {
    /**
     * 通过路由名称查找导航菜单选项
     * @param {string} name 路由名称
     * @returns {Record<string, any> & {index: string} | undefined} 菜单选项
     */
    findNavMenuWithName(name) {
      const stacks = [...this.navMenu]
      while (stacks.length > 0) {
        const menuItem = stacks.shift()
        if (menuItem.index === name) return menuItem

        if (menuItem.submenu) {
          stacks.push(...menuItem.submenu)
        }
      }
      return undefined
    },
    filterAffixRoutes(routes) {
      let affixRoutes = []
      routes.forEach(route => {
        if (route.meta && route.meta.affix) {
          affixRoutes.push(route)
        }

        if (route.children) {
          const tempTags = this.filterAffixRoutes(route.children, route.path)
          if (tempTags.length >= 1) {
            affixRoutes = [...affixRoutes, ...tempTags]
          }
        }
      })
      return affixRoutes
    },
    initQuickRoutingTags() {
      const routes = this.$router.options.routes
      const mainRoutes = routes.find(r => r.path === '/')
      if (!mainRoutes) return
      const affixRoutes = this.filterAffixRoutes(routes)
      for (const route of affixRoutes) {
        // Must have tag name
        if (!route.name) continue

        const nav = this.findNavMenuWithName(route.name)
        if (!nav) continue

        this.addQuickRoutingTag(nav, route)
      }
    },
    syncUserSettings(userSetting) {
      this.userSetting = cloneDeep(userSetting)
    },
    /**
     * 检查导航菜单是否有授权
     * @param index 忽略不检查的菜单
     * @param excludeList 忽略不检查的菜单
     * @returns {boolean} 有授权则返回true
     */
    checkMenuIsHasLicenseAuth(index, excludeList) {
      // 跳过忽略的菜单项
      if (excludeList.includes(index)) {
        return true
      }

      // 检查菜单对应的模块的授权信息
      const lic = getLicense()
      const authOk = checkLicenseModuleAuth(lic, index)
      if (!authOk) {
        const moduleName = findModuleNameByMenuIndex(index)
        const i18nKey = getAuthModuleI18nKey(moduleName)
        messageBox(
          this.$t('auth.noSpecifiedModuleAuth', { module: this.$t(i18nKey) }),
          Types.error,
        )
      }

      return authOk
    },
    /**
     * 添加快速导航标签
     * @param {Record<string, any> & {index: string}} nav
     * @param {Record<string, any>} route
     */
    addQuickRoutingTag(nav, route) {
      if (this.routingTagsSet.has(nav.index)) return

      route = route ?? this.findRouteWithName(nav.index)
      if (!route) return

      const tag = {
        route,
        nav,
      }
      this.routingTagsSet.add(nav.index)
      this.routingTags.push(tag)
    },
    /**
     * 删除一个快速导航标签
     * @param {{nav: Record<string, any> & {index: string}, route: Record<string, any>}} tag
     */
    removeQuickRoutingTag(tag) {
      this.routingTagsSet.delete(tag.route.name)
      this.routingTags = this.routingTags.filter(t => t !== tag)
      if (tag.route.name === this.$route.name) {
        const lastTag =
          this.routingTags[this.routingTags.length - 1] ??
          this.findRouteWithName('main')
        lastTag && this.$router.push({ name: lastTag.route.name })
      }
      //触发删除路由标签时清空对应的历史页面的表格数据
      bfglob.emit('removeQuickRoutingTag', tag)
    },
    /**
     * 通过路由名称来检查对应的菜单是否有授权
     * @param {string} name 路由名称
     * @returns {boolean} 默认true
     */
    checkMenuAuthWithName(name) {
      // 没有开启授权验证
      if (!enableLicense) {
        return true
      }

      // 忽略不检查的菜单项
      const excludeList = [
        'systemFile',
        'aboutBF',
        'notes',
        'version',
        'authorization',
        'relatedSoftware',
      ]

      return this.checkMenuIsHasLicenseAuth(name, excludeList)
    },
    menuItemOnClick(nav) {
      // 忽略当前路由的点击
      if (nav.index === this.$route.name) return
      // 如果是移动端，则关闭菜单
      this.mobileNav && this.closeMenuWrap()
      // 开启了授权，才检查是否有授权相关的模块
      if (!this.checkMenuAuthWithName(nav.index)) {
        return
      }

      // 页面跳转
      const isOk = this.selectMenuItem(nav.index)
      if (isOk) {
        // 添加快速导航tag
        this.addQuickRoutingTag(nav)
      }
    },
    // 向父组件传递打开对话框的消息
    selectMenuItem(index) {
      switch (index) {
        case 'systemFile':
          this.openHelpFile()
          break
        case 'aboutBF':
          this.openAbout()
          break
        case 'contacts':
          this.exportContacts()
          break
        // 指定页面为对话框
        case 'version':
        case 'authorization':
        case 'systemLog':
        case 'bfSpeaking':
          // case 'sendcmd':
          this.openDialog(index)
          break
        // 默认走路由
        default:
          return this.goToRoute(index)
      }

      return true
    },
    openDialog(index) {
      this.$emit('openMenuItem', this.menuItemComponentPaths[index])
    },
    goToRoute(name) {
      if (name === this.$route.name) return false

      // 验证权限
      if (RouterAuthMethods[name] && RouterAuthMethods[name]()) {
        return false
      }
      if (name.includes('sendcmd') && this.isSuper()) {
        return false
      }

      this.$router.push({ name })
      return true
    },
    isSuper() {
      // 如果是root下的账户，不允许发送命令,并弹出警告框
      if (bfglob.userInfo.isSuper) {
        bfNotify.warningBox(this.$t('msgbox.superAdminWarning'))
        return true
      }
      return false
    },
    findRouteWithName(name) {
      const mainRoutes = this.$router.options.routes.find(r => r.path === '/')
      if (!mainRoutes) return
      const stacks = [...mainRoutes.children]
      while (stacks.length > 0) {
        const r = stacks.shift()
        if (r.name === name) return r
        if (r.children) {
          stacks.push(...r.children)
        }
      }
      return undefined
    },
    // 打开关于窗口
    openAbout() {
      const url =
        AboutOfficialWebsiteUrls[this.locale] ||
        AboutOfficialWebsiteUrls[SupportedLang.enUS]
      window.open(url)
    },
    openHelpFile() {
      window.open(this.helpFile)
    },
    // 导出通讯录
    exportContacts() {
      // 处理单位通讯录
      var org_contacts_arr = []
      var g_org = bfglob.gorgData.getAll()
      for (var i in g_org) {
        const item = g_org[i]
        const parentOrg = bfglob.gorgData.getParent(item.parentOrgId)
        const orgObj = {
          parentOrgDmrId: parentOrg ? parentOrg.dmrId : '',
          orgShortName: item.orgShortName,
          dmrId: item.dmrId,
          orgIsVirtual: item.orgIsVirtual,
        }
        org_contacts_arr.push(orgObj)
      }
      var __AscOrgSortValue = items => {
        return items.sort(function (a, b) {
          return bfutil.sortByProps(a, b, { orgSortValue: 'asc' })
        })
      }
      org_contacts_arr = __AscOrgSortValue(org_contacts_arr)

      // 处理对讲机通讯录
      var device_contacts_arr = []
      var g_device = bfglob.gdevices.getAll()
      for (var k in g_device) {
        const item = g_device[k]
        const orgItem = bfglob.gdevices.getParent(item.orgId)
        let orgDmrId = ''
        const virOrgsDmrId = []
        if (orgItem) {
          orgDmrId = orgItem.dmrId
        }
        if (item.virOrgs) {
          var virOrgsArr = item.virOrgs.split(',')
          for (var x in virOrgsArr) {
            var v_orgItem = bfglob.gdevices.getParent(virOrgsArr[x])
            if (v_orgItem) {
              virOrgsDmrId.push(v_orgItem.dmrId)
            }
          }
        }
        var deviceObj = {
          selfId: item.selfId,
          orgDmrId: orgDmrId,
          virOrgsDmrId: virOrgsDmrId,
          dmrId: item.dmrId,
          channels: bfutil.sortChannels(item.channels ?? []),
        }
        device_contacts_arr.push(deviceObj)
      }
      var __AscSelfId = items => {
        return items.sort(function (a, b) {
          return bfutil.sortByProps(a, b, { selfId: 'asc' })
        })
      }
      device_contacts_arr = __AscSelfId(device_contacts_arr)

      // 合成通讯录，并导出数据
      var contactsObj = {
        orgs: org_contacts_arr,
        devices: device_contacts_arr,
        exportUser: bfglob.userInfo.name,
        exportTime: bfTime.nowLocalTime(),
      }
      var contactsObj_json = JSON.stringify(contactsObj)
      bfNotify
        .warningBox(this.$t('msgbox.exportContacts'), 'info')
        .then(res => {
          bfutil.saveAsFile(
            'bfdx_contacts_' + bfTime.nowLocalTime() + '.json',
            contactsObj_json,
          )
        })
        .catch(err => {
          bfglob.console.error(err)
        })
    },

    settingCommand(cmd) {
      // 语言子级菜单，不响应命令
      if (cmd === 'language') {
        return
      }

      if (cmd) {
        this.$refs.userDropdown?.handleClose()
      }

      switch (cmd) {
        case 'set':
          this.openDialog('setting')
          break
        case 'fullScreen':
          screenfull.toggle()
          break
        case 'reload':
          window.location.reload(true)
          break
        // case 'switchNewUi':
        //   warningBox(this.$t('header.switchnewUiTips')).then(() => {
        //     const uiVersionKey = 'bf8100::ui_version'
        //     window.localStorage.setItem(uiVersionKey, 'v2')
        //     window.location.href = '/new-ui/'
        //   }).catch((err) => {
        //     bfglob.console.log('Cancel switch new version of the page', err)
        //   })
        //   break
        // case 'switchOldUi':
        //   warningBox(this.$t('header.switchOldUiTips')).then(() => {
        //     const uiVersionKey = 'bf8100::ui_version'
        //     window.localStorage.setItem(uiVersionKey, 'v1')
        //     window.location.href = '/'
        //   }).catch((err) => {
        //     bfglob.console.log('Cancel switch old version of the page', err)
        //   })
        // break
      }
    },
    languageCommand(lang) {
      if (this.locale === lang) return
      loadLanguageAsync(lang).then(bfutil.saveLang)
    },

    // 快速显示导航、设备列表树快捷功能
    quickShowNavBar() {
      this.showNav = !this.showNav
    },
    toggleTree() {
      bfglob.emit('toggleTree')
    },
    closeMenuWrap() {
      this.showNav = false
    },

    serverStatusChanged(data) {
      this.serverStats = data
      bfglob.isLogin = data

      const interval = 300
      let islogin = false
      let breakLogin = false

      const loginAgain = () => {
        login
          .loginBySid({
            sysId: bfglob.sysId,
            userName: bfglob.userInfo.name,
            password: '',
          })
          .then(res => {
            bfglob.console.log('server reconnect, so login again:', res)
            // 100:成功 0:不成功(未知原因) 1:无此用户 2:密码检验不通过 3:无此sid
            switch (res.responseCode) {
              case 100:
                islogin = true
                login.saveLoginResponse(res)

                // 重新保存sessionId到localStorage
                try {
                  let account = bfStorage.getItem('bfdx_account')
                  if (account) {
                    account = {
                      ...JSON.parse(account),
                      sessionId: bfglob.sessionId,
                    }
                    account = JSON.stringify(account)
                    bfStorage.setItem('bfdx_account', account)
                  }
                } catch (e) {
                  // no-empty
                }

                break
              case 0:
              case 1:
              case 2:
              case 3:
              default:
                breakLogin = true
                // 向用户提示无法重新连接服务器
                bfNotify.warningBox(
                  this.$t('msgbox.serverRestartAndConnectFailed'),
                  'warn',
                )
                break
            }
          })
          .catch(err => {
            if (islogin) {
              return
            }
            bfglob.console.warn(
              'sessionId login failed for server reconnect',
              err,
            )
          })

        setTimeout(() => {
          if (!islogin && !breakLogin) {
            loginAgain()
          }
        }, interval)
      }

      if (data) {
        setTimeout(loginAgain, interval)
      }
    },
  },
  mounted() {
    // 头部加载完成，触发 height 事件，返回当前容器的高度
    this.$emit('head-height', $(this.$el).height())
    // 监听服务器连接状态
    this.serverStats = bfglob.server.wasConnected
    bfglob.on('serverStatus', this.serverStatusChanged)
    // 监听界面布局级别
    bfglob.on('bflayout', level => {
      if (this.layout === level) {
        return
      }
      this.layout = level
      this.showNav = level > 0
    })

    // 订阅服务器时间
    bfglob.on(
      'serverTime',
      function (date) {
        this.serverTime = date
      }.bind(this),
    )

    // 订阅紧急报警声音播放消息
    bfglob.on('play_alarm_voice', (bool = true) => {
      const audio = this.$refs.audio
      if (!audio) {
        return
      }
      if (bool) {
        // 播放声音
        audio.play()
      } else {
        // 停止播放
        audio.pause()
        audio.currentTime = 0
      }
    })
  },
  watch: {
    $route() {
      const nav = this.findNavMenuWithName(this.$route.name)
      if (!nav) return

      this.addQuickRoutingTag(nav, this.$route)
    },
    mobileNav(val) {
      if (val) {
        this.menuMode = 'vertical'
      } else {
        this.menuMode = 'horizontal'
      }
    },
    routingTagsNotOnlyOne() {
      // 快速导航栏菜单隐藏或者展示时,首页地图resize
      bfglob.map?.resize()
    },
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    defaultTitle() {
      return bfglob.siteConfig.moveTitle || this.$t('header.moveTitle')
    },
    mobileNav() {
      return this.showNav && this.layout === 0
    },
    helpFile() {
      return `${BASE_URL}assets/document/BF8100_help_${this.locale}.pdf`
    },
    alarmAudio() {
      return `${BASE_URL}assets/audio/emergency_${this.locale}.wav`
    },
    languagesMenus() {
      return SupportedLangList.map(lang => {
        return {
          command: lang,
          iconClass: {
            'mdi mdi-check-circle-outline': this.locale === lang,
          },
          menuId: lang,
          text: getDisplayLabel(lang),
        }
      })
    },
    headDropDownMenu() {
      return [
        {
          command: 'set',
          menuId: 'user_set',
          text: this.$t('header.setting'),
        },
        {
          command: 'language',
          menuId: 'language',
          text: this.$t('header.language'),
          subMenus: this.languagesMenus,
          onCommand: this.languageCommand.bind(this),
        },
        {
          command: 'fullScreen',
          menuId: 'fullScreen',
          text: this.$t('header.fullScreen'),
        },
        {
          command: 'reload',
          menuId: 'clearCache',
          text: this.$t('header.clearCache'),
        },
        // {
        //   command: 'switchNewUi',
        //   menuId: 'switchNewUi',
        //   text: this.$t('header.switchNewUi'),
        // },
        // {
        //   command: 'switchOldUi',
        //   menuId: 'switchOldUi',
        //   text: this.$t('header.switchOldUi'),
        // },
      ]
    },
    navMenu() {
      return [
        {
          index: 'main',
          text: () => this.$t('nav.home'),
          classes: 'firstMenuItem',
        },
        {
          index: 'data',
          text: () => this.$t('nav.data'),
          submenu: this.navDataMenu,
        },
        {
          index: 'history',
          text: () => this.$t('nav.enquiry'),
          submenu: this.navEnquiryMenu,
        },
        {
          index: 'helper',
          text: () => this.$t('nav.help'),
          submenu: this.navHelpMenu,
        },
        {
          index: 'command',
          text: () => this.$t('nav.command'),
          submenu: this.navCommandMenu,
        },
        {
          index: 'bfSpeaking',
          // iconClass: 'icon-organize',
          text: () => this.$t('dialog.networkSpeaking'),
          classes: 'firstMenuItem',
        },
      ]
    },
    navDataMenu() {
      const dataMenus = [
        {
          index: 'orgs',
          iconClass: 'icon-organize',
          text: () => this.$t('nav.orgData'),
        },
        {
          index: 'jobs',
          iconClass: 'icon-job',
          text: () => this.$t('nav.postData'),
        },
        {
          index: 'users',
          iconClass: 'icon-set-user',
          text: () => this.$t('nav.userData'),
        },
        {
          index: 'linePoint',
          iconClass: 'icon-patrol-point',
          text: () => this.$t('nav.linePointData'),
        },
        {
          index: 'lines',
          iconClass: 'icon-line-master',
          text: () => this.$t('dialog.lineTitle'),
        },
        {
          index: 'rules',
          iconClass: 'icon-rules',
          text: () => this.$t('dialog.ruleTitle'),
        },
        {
          index: 'mapPoints',
          iconClass: 'icon-mapmarker',
          text: () => this.$t('nav.mapPointData'),
        },
        {
          index: 'devices',
          iconClass: 'icon-interphone',
          text: () => this.$t('dialog.deviceDataTitle'),
        },
        {
          index: 'controllers',
          iconClass: 'icon-controller',
          text: () => this.$t('nav.ctrlData'),
        },
        {
          index: 'shortNumberMapping',
          iconClass: 'icon-mapping',
          text: () => this.$t('dialog.shortNumberMapping'),
        },
        {
          index: 'gatewayFilter',
          iconClass: 'icon-rules',
          text: () => this.$t('dialog.phoneBlackWhiteList'),
        },
        {
          index: 'gatewayPermission',
          iconClass: 'icon-rules',
          text: () => this.$t('dialog.gatewayPermission'),
        },
        {
          index: 'predefinedPhoneBook',
          iconClass: 'icon-phone-book',
          text: () => this.$t('dialog.predefinedPhoneBook'),
        },
        {
          index: 'dynamicGroup',
          iconClass: 'icon-task-group',
          text: () => this.$t('dynamicGroup.title'),
        },
      ]
      if (bfglob.sysIniConfig.iotEnable) {
        dataMenus.push({
          index: 'IOT',
          iconClass: 'icon-iot',
          text: () => this.$t('iot.terminal'),
        })
      }

      return dataMenus
    },
    navEnquiryMenu() {
      const queryMenus = [
        {
          index: 'alarmHistory',
          iconClass: 'icon-jingbaojilu',
          text: () => this.$t('nav.alarmHistory'),
        },
        {
          index: 'rfidBatteryAlarm',
          iconClass: 'icon-low-battery-alarm',
          text: () => this.$t('nav.activePatrolPointAlarm'),
        },
        {
          index: 'InspectionHistory',
          iconClass: 'icon-xunchajilu',
          text: () => this.$t('nav.InspectionHistory'),
        },
        {
          index: 'soundHistory',
          iconClass: 'icon-sound-history',
          text: () => this.$t('nav.soundHistory'),
        },
        {
          index: 'shiftHistory',
          iconClass: 'icon-work-shift',
          text: () => this.$t('nav.readerCardHistory'),
        },
        {
          index: 'gpstraceHistory',
          iconClass: 'icon-gps-track',
          text: () => this.$t('nav.GPSpathHistory'),
        },
        {
          index: 'InsRulesHistory',
          iconClass: 'icon-rules-tongji',
          text: () => this.$t('dialog.insRules'),
        },
        {
          index: 'dispatchHistory',
          iconClass: 'icon-dispatch-history',
          text: () => this.$t('dialog.dispatch'),
        },
        {
          index: 'onlineHistory',
          iconClass: 'icon-user-online',
          text: () => this.$t('nav.switchHistory'),
        },
        {
          index: 'ctrlonlineHistory',
          iconClass: 'icon-controller-online',
          text: () => this.$t('dialog.ctrlHistory'),
        },
        {
          index: 'smsHistory',
          iconClass: 'icon-sms',
          text: () => this.$t('nav.smsHistory'),
        },
        {
          index: 'crudHistory',
          iconClass: 'icon-cz-jl',
          text: () => this.$t('nav.crudHistory'),
        },
      ]
      if (bfglob.sysIniConfig.iotEnable) {
        queryMenus.push({
          index: 'iotDeviceHistory',
          iconClass: 'icon-iot-history',
          text: () => this.$t('nav.iotDeviceHistory'),
        })
      }

      return queryMenus
    },
    navHelpMenu() {
      const { helpMenu = {} } = bfglob.menuControl ?? {}
      const helpMenuOrder = [
        'systemFile',
        'aboutBF',
        'notes',
        'version',
        'authorization',
        'software',
      ]
      const helpMenuItems = {
        systemFile: {
          index: 'systemFile',
          iconClass: 'icon-instruction-book',
          text: () => this.$t('nav.helpContent'),
        },
        aboutBF: {
          index: 'aboutBF',
          iconClass: 'icon-about',
          text: () => this.$t('nav.BFWebsite'),
        },
        notes: {
          index: 'notes',
          iconClass: 'icon-notes',
          text: () => this.$t('nav.runNotes'),
        },
        version: {
          index: 'version',
          iconClass: 'icon-version',
          text: () => this.$t('nav.version'),
        },
        authorization: {
          index: 'authorization',
          iconClass: 'icon-authenticationbg',
          text: () => this.$t('nav.authorization'),
        },
        software: {
          index: 'relatedSoftware',
          iconClass: 'icon-software',
          text: () => this.$t('nav.relatedSoftware'),
        },
      }

      // helpMenu中设置为false的不显示菜单选项
      return helpMenuOrder
        .filter(key => {
          // 如果是授权信息菜单，又没有开启授权功能，则隐藏菜单
          if (key === 'authorization' && !enableLicense) {
            return false
          }

          return !(helpMenu[key] === false)
        })
        .map(key => helpMenuItems[key])
    },
    navCommandMenu() {
      const { commandMenu = {} } = bfglob.menuControl ?? {}
      const commandMenuOrder = [
        'sendCommand',
        'contacts',
        'repeaterCommand',
        'terminalCommand',
      ]
      const commandMenuItems = {
        sendCommand: {
          index: 'sendcmd',
          iconClass: 'icon-command',
          text: () => this.$t('nav.sendCommand'),
        },
        contacts: {
          index: 'contacts',
          iconClass: 'icon-address-book',
          text: () => this.$t('nav.contacts'),
        },
        repeaterCommand: {
          index: 'repeaterWriteFrequency',
          iconClass: 'icon-write-data',
          text: () => this.$t('nav.repeaterWriteFrequency'),
        },
        terminalCommand: {
          index: 'interphoneWf',
          iconClass: 'icon-write-data',
          text: () => this.$t('nav.interphoneWriteFrequency'),
        },
      }

      return commandMenuOrder
        .filter(key => {
          return !(commandMenu[key] === false)
        })
        .map(key => commandMenuItems[key])
    },
    dataMenuIndex() {
      return [
        'orgs',
        'jobs',
        'users',
        'linePoint',
        'lines',
        'rules',
        'mapPoints',
        'devices',
        'controllers',
        'shortNumberMapping',
        'gatewayFilter',
        'gatewayPermission',
        'predefinedPhoneBook',
        'dynamicGroup',
        'IOT',
      ]
    },
    historyMenuIndex() {
      return [
        'alarmHistory',
        'rfidBatteryAlarm',
        'InspectionHistory',
        'soundHistory',
        'shiftHistory',
        'gpstraceHistory',
        'InsRulesHistory',
        'dispatchHistory',
        'onlineHistory',
        'ctrlonlineHistory',
        'smsHistory',
        'crudHistory',
        'iotDeviceHistory',
      ]
    },
    helpMenuPaths() {
      return {
        systemFile: '',
        aboutBF: '',
        notes: 'layout/notes',
        version: 'secondary/systemVersion',
        authorization: 'secondary/authorization',
        relatedSoftware: 'secondary/relatedSoftware',
      }
    },
    commandMenuPaths() {
      return {
        sendcmd: 'command/sendcmd',
        contacts: '',
        repeaterWriteFrequency: 'repeaterWf/repeaterWriteFrequency',
        interphoneWf: 'interphoneWf/interphoneWf',
      }
    },
    /**
     * 导航菜单选项的index与组件的路径索引
     * index -> component path
     * @returns {{[p: string]: string}}
     */
    menuItemComponentPaths() {
      return {
        ...this.dataMenuIndex
          .map(index => ({ [index]: `dataDialog/${index}` }))
          .reduce((p, c) => Object.assign(p, c), {}),
        ...this.historyMenuIndex
          .map(index => ({ [index]: `historyDialog/${index}` }))
          .reduce((p, c) => Object.assign(p, c), {}),
        ...this.helpMenuPaths,
        ...this.commandMenuPaths,
        bfSpeaking: 'command/bfSpeaking',
        setting: 'secondary/userSetting',
      }
    },
    routingTagsNotOnlyOne() {
      return this.routingTags.length !== 1
    },
  },
  beforeMount() {
    const displayUserName = userName => {
      if (bfglob.userInfo.name !== userName) {
        this.loginName = `${bfglob.userInfo.name}(${userName})`
      }
    }
    bfglob.on('userInfo.origData.userName', displayUserName)
    if (bfglob.userInfo.origData) {
      displayUserName(bfglob.userInfo.origData.userName)
    }

    // 订阅系统滚动标题、logo数据
    const displayClientTitle = title => {
      this.moveTitle = title || this.defaultTitle
      this.$nextTick(() => {
        this.$refs.scrollingText?.stop()
        this.$refs.scrollingText?.start()
      })
    }
    bfglob.on('moveTitle', displayClientTitle)
    if (bfglob.systemSetting.clientTitle) {
      displayClientTitle(bfglob.systemSetting.clientTitle)
    } else if (!this.moveTitle) {
      displayClientTitle(this.defaultTitle)
    }

    const displayLogo = logo => {
      this.logoImage = logo || this.logoImage
    }
    bfglob.on('logoImage', displayLogo)
    if (bfglob.systemSetting.clientLogo) {
      displayLogo(bfglob.systemSetting.clientLogo)
    }

    bfglob.on('update_user_settings', this.syncUserSettings)

    // 初始化固定的路由的快速导航标签
    this.initQuickRoutingTags()
  },
  beforeUnmount() {
    bfglob.off('update_user_settings', this.syncUserSettings)
  },
  components: {
    scrollingText,
    QuickRoutingTags: defineAsyncComponent(
      () => import('@/components/layout/QuickRoutingTags.vue'),
    ),
  },
}
</script>

<style lang="scss">
$header-bg-color: #20a0ff;
$header-text-color: #fff;
$header-height: 40px;

.el-header.bf-header {
  height: auto !important;
  padding: 0;
  max-height: 70px;

  .base-header {
    background-color: $header-bg-color;
    height: $header-height;
    justify-content: space-between;

    .logo img {
      max-width: 120px;
      height: $header-height;
      vertical-align: top;
    }

    .el-menu.bf-menu {
      &.el-menu--horizontal,
      &.el-menu--horizontal > .el-menu-item,
      &.el-menu--horizontal > .el-sub-menu .el-sub-menu__title {
        border-bottom: none;
      }
    }
  }
}

@media (min-width: 2560px) {
  .el-header.bf-header {
    max-height: 78px;
  }
}

.header-top-submenu.el-menu--horizontal .el-menu {
  padding: 0;

  .el-menu-item {
    i.iconfont {
      color: #909399;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #e3e8ee;
    }
  }
}

.mobile-nav-wrap.bf-menu.el-menu {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  background: #545c64;
  width: 80vw;
  z-index: 10001;
  overflow: auto;
  height: 100%;

  .el-menu-item {
    .is-active {
      color: inherit !important;
    }

    &:not(.firstMenuItem):not(:last-child) {
      border-bottom: 1px solid #696969;
    }

    & i {
      color: inherit;
    }
  }
}

.nav-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.5);
  z-index: 10000;
}

.scrolling-container {
  order: 4;
  overflow: hidden;
  min-width: 0;
  line-height: 40px;
  height: 40px;
  flex-grow: 1;
}

.state-wrap {
  order: 5;
  flex-shrink: unset;
  flex-grow: unset;
  margin: 0 10px;
  background-color: #fff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  align-self: center;

  .iconfont {
    font-size: 32px;
    display: block;
    width: 100%;
    height: 100%;
    transform: translate(-1px, -8px);
  }

  .success {
    color: #13ce66;
  }

  .danger {
    color: #ff4949;
  }
}

.time-wrap {
  order: 6;
  min-width: 140px;
}

.nav-date {
  margin: 0;
}

.org-btn {
  order: 7;
}

.dropdown-wrap {
  order: 8;
  margin-left: 10px;
  min-width: 80px;
  flex: none;
}

.dropdown-wrap button {
  border: none;
}

.dropdown-wrap button > span {
  min-height: 14px;
  display: flex;
}

.mobile-drop-down button {
  background-color: #79bbff;
}

.el-dropdown-menu.user-dorp-down-menu {
  .el-dropdown-menu__item,
  .el-dropdown.submenu-dropdown {
    line-height: 28px;
  }
}

.space {
  flex: auto;
}

.el-dropdown-menu .submenu-dropdown.el-dropdown {
  width: 100%;

  .submenu-title {
    width: 100%;
    display: flex;
    align-items: center;

    &:hover {
      background-color: #ecf5ff;
      color: #66b1ff;
    }

    i {
      margin-right: unset;
    }
  }
}

.fixedTitle {
  position: unset !important;
  left: unset !important;
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
}

.lang_icon {
  width: 16px;
  color: #20a0ff;
}

.el-dropdown-menu {
  padding: 0;
  margin: 0;
  text-align: left;
}

.el-dropdown-menu__item {
  line-height: 28px;
  border-bottom: 1px solid #e3e8ee;
  padding: 0 8px 0 8px;
}

.el-badge__content.is-fixed.is-dot {
  right: 20px;
  top: 12px;
  width: 14px;
  height: 14px;
}
</style>
