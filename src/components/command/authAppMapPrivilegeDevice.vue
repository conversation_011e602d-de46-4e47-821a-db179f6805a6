<template>
  <el-dialog
    v-model="visible"
    :title="$t('dialog.mobileDevReqDevGps')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    :fullscreen="fullscreen"
    :before-close="beforeClose"
    append-to-body
    top="0"
    class="privilege-info drag-dialog"
    modal-class="drag-dialog-modal"
    @open="openDlgFn"
    @close="closeDlgFn"
  >
    <template #header>
      <div v-bfdrag>
        {{ $t('dialog.mobileDevReqDevGps') }}
      </div>
    </template>
    <el-table
      ref="respTable"
      class="resp-table"
      :data="privilegeDeviceFromAppReqData"
      tooltip-effect="dark resp-tooltip"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column
        :label="$t('dialog.reqDevice')"
        :width="reqDeviceThWidth"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ getSelfId(scope.row.appDmrid) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('dialog.targetDevice')"
        width="122"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ getSelfId(scope.row.grantDeviceDmrid) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('dialog.grantUser')"
        width="120"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.grantUserName }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('auth.expireTime')" width="160">
        <template #default="scope">
          <el-date-picker
            v-model="scope.row.expireTime"
            popper-class="expire-time-picker"
            type="date"
            size="small"
            :placeholder="$t('dialog.chooseDate')"
            :picker-options="pickerOptions"
            :class="{ 'is-error': !scope.row.expireTime }"
            value-format="YYYY-MM-DD"
            @change="dateChange(scope.row, scope.row.expireTime)"
          />
          <p v-show="!scope.row.expireTime" class="error-message">
            {{ $t('auth.expireTime') }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="#" width="150" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip
            effect="dark"
            :content="$t('dialog.agree')"
            placement="top"
          >
            <el-button
              type="success"
              circle
              icon="check"
              @click="agreeItem(scope.row)"
            />
          </el-tooltip>
          <el-tooltip
            effect="dark"
            :content="$t('dialog.refuse')"
            placement="top"
          >
            <el-button
              type="danger"
              circle
              icon="close"
              @click="denyItem(scope.row)"
            />
          </el-tooltip>
          <el-tooltip
            effect="dark"
            :content="$t('dialog.neglect')"
            placement="top"
          >
            <el-button
              type="info"
              circle
              icon="minus"
              @click="ignoreItem(scope.row)"
            />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="show-privilege-btns">
      <el-button
        type="success"
        size="small"
        :disabled="selected.length === 0"
        @click="agreeAll"
      >
        {{ $t('dialog.agree') }}
      </el-button>
      <el-button
        type="danger"
        size="small"
        :disabled="selected.length === 0"
        @click="denyAll"
      >
        {{ $t('dialog.refuse') }}
      </el-button>
      <el-button
        type="info"
        size="small"
        :disabled="selected.length === 0"
        @click="ignoreAll"
      >
        {{ $t('dialog.neglect') }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import vueMixin from '@/utils/vueMixin'

import { addTime, nowUtcTime, timeIsBefore } from '@/utils/time'
import { SupportedLang } from '@/modules/i18n'
import bfproto from '@/modules/protocol'
import { cloneDeep } from 'lodash'

export default {
  name: 'AuthAppMapPrivilegeDevice',
  mixins: [vueMixin],
  data() {
    return {
      visible: true,
      privilegeDeviceFromAppReqData: [],
      grantUserName: bfglob.userInfo.name,
      selected: [],
    }
  },
  computed: {
    isFR() {
      return this.locale === SupportedLang.fr
    },
    isEN() {
      return this.locale === SupportedLang.enUS
    },
    reqDeviceThWidth() {
      return this.isFR ? '162' : this.isEN ? '140' : '120'
    },
    pickerOptions() {
      return {
        // 该配置用于日历的日期禁用
        disabledDate(time) {
          return timeIsBefore(time, new Date().setHours(0, 0, 0, 0))
        },
        shortcuts: [
          {
            text: this.$t('dialog.3day'),
            onClick(picker) {
              const date = addTime(new Date(), 3, 'day').toDate()
              picker.$emit('pick', date)
            },
          },
          {
            text: this.$t('dialog.1week'),
            onClick(picker) {
              const date = addTime(new Date(), 1, 'week').toDate()
              picker.$emit('pick', date)
            },
          },
          {
            text: this.$t('dialog.1month'),
            onClick(picker) {
              const date = addTime(new Date(), 1, 'month').toDate()
              picker.$emit('pick', date)
            },
          },
          {
            text: this.$t('dialog.1year'),
            onClick(picker) {
              const date = addTime(new Date(), 1, 'year').toDate()
              picker.$emit('pick', date)
            },
          },
        ],
      }
    },
  },
  methods: {
    getSelfId(dmrid) {
      return bfglob.gdevices.getDataByIndex(dmrid)?.selfId ?? dmrid
    },
    beforeClose(done) {
      done()
    },
    agreeItem(item) {
      item.grantTime = nowUtcTime()
      bfproto
        .sendMessage(
          8182,
          item,
          'db_app_map_privilege_device',
          `radio.${bfglob.sysId}`,
        )
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo === '+OK') {
            this.privilegeDeviceFromAppReqData =
              this.privilegeDeviceFromAppReqData.filter(
                i =>
                  !(
                    i.grantDeviceDmrid === item.grantDeviceDmrid &&
                    i.appDmrid === item.appDmrid
                  ),
              )
            bfglob.gapppMapPivilegeDevice.set(item.rid, item)
            var selOpt = {
              rid: item.rid,
              label:
                item.appDmrid +
                ' / ' +
                item.grantDeviceDmrid +
                '/' +
                item.expireTime,
            }
            bfglob.gapppMapPivilegeDevice.setList(item.rid, selOpt)
          }
        })
        .catch(err => {
          bfglob.console.warn('agree db_app_map_privilege_device fail', err)
        })
    },
    agreeAll() {
      for (let i = 0; i < this.selected.length; i++) {
        const item = this.selected[i]
        if (item.expireTime) continue
        this.agreeItem(item)
      }
    },
    denyItem(item) {
      const newData = cloneDeep(item)
      newData.isSetExpire = 0
      newData.expireTime = '2000-01-01 00:00:00'
      newData.grantTime = 'deny'
      bfproto
        .sendMessage(
          8182,
          newData,
          'db_app_map_privilege_device',
          `radio.${bfglob.sysId}`,
        )
        .then(rpc_cmd_obj => {
          if (rpc_cmd_obj.resInfo === '+OK') {
            this.privilegeDeviceFromAppReqData =
              this.privilegeDeviceFromAppReqData.filter(
                i =>
                  !(
                    i.grantDeviceDmrid === item.grantDeviceDmrid &&
                    i.appDmrid === item.appDmrid
                  ),
              )
          }
        })
        .catch(err => {
          bfglob.console.warn('refuse db_app_map_privilege_device fail', err)
        })
    },
    denyAll() {
      for (let i = 0; i < this.selected.length; i++) {
        this.denyItem(this.selected[i])
      }
    },
    ignoreItem(item) {
      this.privilegeDeviceFromAppReqData =
        this.privilegeDeviceFromAppReqData.filter(
          i =>
            !(
              i.grantDeviceDmrid === item.grantDeviceDmrid &&
              i.appDmrid === item.appDmrid
            ),
        )
    },
    ignoreAll() {
      this.selected.forEach(d => {
        this.ignoreItem(d)
      })
    },
    dateChange(row, val) {
      if (val) {
        row.expireTime = val + ' 23:59:59'
      }
    },
    handleSelectionChange(val) {
      this.selected = val
    },
  },
  watch: {
    visible(val) {
      if (!val) {
        this.privilegeDeviceFromAppReqData = []
      }
    },
    privilegeDeviceFromAppReqData(val) {
      if (val.length === 0) {
        this.visible = false
      }
    },
  },
  mounted() {
    bfglob.on('deleteItemPrivilege', data => {
      this.privilegeDeviceFromAppReqData =
        this.privilegeDeviceFromAppReqData.filter(
          i =>
            !(
              i.appDmrid === data.appDmrid &&
              i.grantDeviceDmrid === data.grantDeviceDmrid
            ),
        )
    })
  },
}
</script>

<style lang="scss">
@use '@/css/common.scss' as *;

.el-dialog.privilege-info {
  width: 742px;
  height: auto;
  margin: 0;

  .el-dialog__body {
    display: flex;
    flex-direction: column;
    padding: 10px;
    gap: 20px;
    justify-content: space-around;
    align-items: center;

    .auth-actions {
      margin-bottom: 10px;
      text-align: center;

      .el-button {
        min-width: 30%;
      }
    }

    .resp-table {
      .error-message {
        position: absolute;
        top: 10px;
        right: 18px;
        font-size: 10px;
        color: red;
      }

      .el-date-editor {
        width: 100%;
      }

      .is-error .el-input__inner {
        border-color: red;
      }
    }
  }

  .show-privilege-btns {
    display: flex;
    justify-content: center;
    padding-bottom: 10px;
    min-width: 60%;

    .el-button {
      min-width: 40%;
    }
  }
}

.el-tooltip__popper.resp-tooltip {
  z-index: 5001 !important;
}

.el-date-picker.expire-time-picker {
  z-index: 5001 !important;
}
</style>
