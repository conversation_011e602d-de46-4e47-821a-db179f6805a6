import { warningBox } from '@/utils/notify'
import i18n from '@/modules/i18n'

async function detectNewUi() {
  const res = await fetch('/new-ui/index.html')
  return res.ok
}

// 尝试新版UI
async function tryLoadNewUi() {
  // 尝试请求新版UI,提示是否使用
  const hasNewUi = await detectNewUi().catch(() => false)
  if (!hasNewUi) return

  const uiVersionKey = 'bf8100::ui_version'
  const switchNewUi = () => {
    window.localStorage.setItem(uiVersionKey, 'v2')
    window.location.href = '/new-ui/'
  }
  // 判断缓存使用的UI版本，根据版本进行切换
  const uiVersion = window.localStorage.getItem(uiVersionKey)
  if (!uiVersion) {
    warningBox(i18n.global.t('header.detectNewUiTips'))
      .then(switchNewUi)
      .catch(() => {
        bfglob.console.log('Cancel switch new UI')
      })
    return
  }

  if (uiVersion === 'v2' && !window.location.href.includes('/new-ui/')) {
    switchNewUi()
  }
}

// 在浏览器空闲时才检测新版
window.requestIdleCallback(() => {
  tryLoadNewUi()
})
