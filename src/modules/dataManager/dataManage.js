import { getDynamicGroupOrgType } from '@/utils/bfutil'
import { v1 as uuid } from 'uuid'
import { MemberType } from '@/utils/dynamicGroup/api'
import eventBus from '@/utils/eventBus'
import { reactive } from 'vue'

// 创建一个vue实例，通过vue.$set添加动态响应式对象属性，同时监听属性变化做全局数据状态同步更新
// datas 储存所有数据，indexs 储存所有索引，selects 储存下拉列表数据
const state = reactive({
  datas: {},
  indexs: {},
  selects: {},
  markers: {},
  names: {},
  privelege: {},
  dmrIdAddr: {},
  noAccessPoints: {},
  set(target, protopety, value) {
    // this.$set.apply(this, arguments)
    Reflect.set(target, protopety, value)
  },
  emit(subject, ...params) {
    eventBus.emit(subject, ...params)
  },
  on(subject, callback) {
    eventBus.on(subject, callback)
  },
  once(subject, callback) {
    eventBus.once(subject, callback)
  },
  off(subject, callback) {
    eventBus.off(subject, callback)
  },
})

// 缓存各数据管理器名称，以便跨管理器获取数据
var __orgsManageName = ''
var __jobsManageName = ''
var __usersManageName = ''

var __imagesManageName = ''
var __devicesManageName = ''
var __mapPointsManageName = ''
var __linePointsManageName = ''
var __lineMastersManageName = ''
var __rulesManageName = ''
var __controllersManageName = ''
var __phoneShortNoManageName = ''
var __phoneGatewayFilterManageName = ''
var __phoneGatewayPermissionManageName = ''
var __controllerGatewayManageName = ''
var __phoneNoListManageName = ''

var __DynamicGroupDetailManageName = ''
var __appMapPrivilegeDeviceManageName = ''

// 数据的基类
var __initName = function (name) {
  if (typeof name !== 'string') {
    name = uuid()
  }
  return name
}

class DataManage {
  constructor(name) {
    this.name = __initName(name)
    state.names[this.name] = this.name
    state.datas[this.name] = {}
    // state.set(state.datas, this.name, {})
    state.indexs[this.name] = {}
    state.selects[this.name] = {}
    state.markers[this.name] = {}
  }

  get(key = '') {
    const __key = 'x' + key
    return state.datas[this.name][__key]
  }

  set(key = 'x', val = {}, index) {
    const __key = 'x' + key
    state.set(state.datas[this.name], __key, val)
    typeof index !== 'undefined' && this.setIndex(index, key)
    state.emit(this.name, key, val)
    return this
  }

  delete(key = 'x') {
    // 删除数据前，先删除掉索引和下拉列表数据
    // 单位管理器中有虚拟、真实单位下拉列表之分，需要重写 delete 事件
    this.deleteIndexByVal(key)
    this.deleteList(key)

    delete state.datas[this.name]['x' + key]
    return this
  }

  getAll() {
    return state.datas[this.name]
  }

  getParent(key = 'x') {
    const __key = 'x' + key
    return state.datas[__orgsManageName][__key]
  }

  getOrgNameByKey(key = 'x', bool = false) {
    // bool 如果为true则返回全称，为false则返回简称
    const __jobData = this.getParent(key)
    if (__jobData) {
      if (bool) {
        return __jobData.orgFullName
      } else {
        return __jobData.orgShortName
      }
    } else {
      return ''
    }
  }

  getList() {
    return state.selects[this.name]
  }

  setList(key = 'x', val = {}) {
    const _key = 'x' + key
    state.set(state.selects[this.name], _key, val)
    return this
  }

  deleteList(key = 'x') {
    const __key = 'x' + key
    delete state.selects[this.name][__key]
    return this
  }

  // 删除所有指向val的索引
  deleteIndexByVal(val = 'x') {
    const _index = state.indexs[this.name]
    const _val = 'x' + val
    for (var k in _index) {
      if (_index[k] === _val) {
        delete state.indexs[this.name][k]
      }
    }
  }

  // 通过索引名称删除
  deleteIndexByKey(key = '') {
    const _index = state.indexs[this.name]
    key = 'x' + key
    if (_index[key]) {
      delete _index[key]
    }
  }

  getDataByIndex(index) {
    const key = this.getIndex(index)
    return state.datas[this.name][key]
  }

  getIndex(key) {
    if (typeof key === 'undefined') {
      return state.indexs[this.name]
    }
    const _key = 'x' + key
    return state.indexs[this.name][_key]
  }

  setIndex(key = 'x', val = 'x') {
    // val 支持字符串和数组设置多个索引
    const __val = 'x' + val
    if (typeof key === 'string' || typeof key === 'number') {
      const _key = 'x' + key
      state.set(state.indexs[this.name], _key, __val)
    } else if (key instanceof Array) {
      for (let i = 0; i < key.length; i++) {
        const __item = key[i]
        if (__item === null || typeof __item === 'undefined') {
          continue
        }
        const __key = 'x' + __item.toString()
        state.set(state.indexs[this.name], __key, __val)
      }
    }
    return this
  }

  getMarkerAll() {
    return state.markers[this.name]
  }

  getMarker(key = 'x') {
    const _key = 'x' + key
    return state.markers[this.name][_key]
  }

  setMarker(key = 'x', marker) {
    const _key = 'x' + key
    state.set(state.markers[this.name], _key, marker)

    return this
  }

  deleteMarker(key = 'x') {
    const _key = 'x' + key
    const marker = state.markers[this.name][_key]
    if (marker) {
      marker.remove()
      delete state.markers[this.name][_key]
    }
    return this
  }

  clear() {
    state.datas[this.name] = {}
    state.indexs[this.name] = {}
    state.selects[this.name] = {}
    state.markers[this.name] = {}
  }
}

// 监听单位数据的变化，更新单位下辖各数据的上级单位名称
var updateOrgShortName = (name, key, val, type) => {
  const target = state.datas[name]
  for (const k in target) {
    const item = target[k]
    // 找到上级单位rid为key的用户数据
    let orgId = 'orgId'
    let shortName = 'orgShortName'
    if (type === 1) {
      orgId = 'parentOrgId'
      shortName = 'parentOrgName'
    }
    if (item[orgId] === key) {
      // 判断item上级单位名称是否与更新的单位数据简称相同,不相同则更新
      if (item[shortName] !== val.orgShortName) {
        item[shortName] = val.orgShortName
        state.set(state.datas[name], k, item)
      }
    }
    if (type === 2) {
      // 为对讲机数据,同步更新虚拟单位名称
      if (item.virOrgs) {
        var virOrgs = item.virOrgs.split(',')
        var virOrgsNames = item.virOrgsName.split(',')
        for (var z in virOrgs) {
          if (virOrgs[z] === key) {
            virOrgsNames[z] = val.orgShortName
          }
        }
        item.virOrgsName = virOrgsNames.join(',')
      }
    }
  }
}
var updateUserTitleName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const item = target[k]
    if (item.userTitle === key) {
      if (item.userTitleName !== val.titleName) {
        item.userTitleName = val.titleName
        state.set(state.datas[name], k, item)
      }
    }
  }
}
var updateUserName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const item = target[k]
    // 更新指定用户名称
    if (item.deviceUser === key) {
      if (item.deviceUserName !== val.userName) {
        item.deviceUserName = val.userName
        state.set(state.datas[name], k, item)
      }
    }
    // 更新最后使用人员名称
    if (item.lastRfidPerson === key) {
      if (item.userName !== val.userName) {
        item.userName = val.userName
        state.set(state.datas[name], k, item)
      }
    }
  }
}
var updateLinePointName = (name, key, val) => {
  const target = state.datas[name]
  for (const i in target) {
    const lineData = target[i]
    for (const k in lineData.pointData) {
      const item = lineData.pointData[k]
      if (item.rid === key) {
        // 更新线路下辖巡查点编号
        if (item.pointId !== val.pointId) {
          item.pointId = val.pointId
          state.set(lineData.pointData, k, item)
        }
        // 更新线路下辖巡查点名称
        if (item.pointName !== val.pointName) {
          item.pointName = val.pointName
          state.set(lineData.pointData, k, item)
        }
      }
    }
  }
}
var updateLineMasterName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const item = target[k]
    if (item.ruleLineRid === key) {
      if (item.lineName !== val.lineName) {
        item.lineName = val.lineName
        state.set(state.datas[name], k, item)
      }
    }
  }
}
var updateShortNoMappingTargetName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const data = target[k]
    if (data.refDevId === key) {
      data.mappingTargetName = val.selfId
      state.set(state.datas[name], k, data)
    }
    if (data.refOrgId === key) {
      data.mappingTargetName = val.orgShortName
      state.set(state.datas[name], k, data)
    }
  }
}
var updateGatewayBlackWhiteListLabel = (name, _key, _val) => {
  const target = state.datas[name]
  // let resetListNames = (rids, names) => {
  //   for (let i = 0; i < rids.length; i++) {
  //     let rid = rids[i]
  //     if (rid !== key) {
  //       continue
  //     }
  //
  //     names[i] = val.selfId ? val.selfId : val.orgShortName ? val.orgShortName : ''
  //   }
  //
  //   return [...names]
  // }

  for (const k in target) {
    const data = target[k]
    // data.inBlackNames = resetListNames(data.inBlack, data.inBlackNames);
    // data.inWhiteNames = resetListNames(data.inWhite, data.inWhiteNames);
    // data.outBlackNames = resetListNames(data.outBlack, data.outBlackNames);
    // data.outWhiteNames = resetListNames(data.outWhite, data.outWhiteNames);

    state.set(state.datas[name], k, data)
  }
}
var updateDeviceGatewayName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const data = target[k]
    if (data.gatewayFilterRid === key) {
      data.gatewayFilterName = val.name

      state.set(state.datas[name], k, Object.assign({}, data))
    }
  }
}
var updateGatewayPermissionOrgName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const data = target[k]
    if (data.permOrgId === key) {
      data.permOrgName = val.orgShortName

      state.set(state.datas[name], k, Object.assign({}, data))
    }
  }
}
var updateGatewayPermissionDeviceName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const data = target[k]
    if (data.permDevId === key) {
      data.permDevName = val.selfId

      state.set(state.datas[name], k, Object.assign({}, data))
    }
  }
}
var updateGatewayPermissionGatewayName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const data = target[k]
    if (data.gatewayRid === key) {
      data.gatewayName = val.name

      state.set(state.datas[name], k, Object.assign({}, data))
    }
  }
}
var updateControllerGatewayRefControllerName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const data = target[k]
    if (data.refControllerId === key) {
      data.refControllerName = val.selfId

      state.set(state.datas[name], k, Object.assign({}, data))
    }
  }
}
var updateControllerGatewayRefDevName = (name, key, val) => {
  const target = state.datas[name]
  for (const k in target) {
    const data = target[k]
    if (data.refDevId === key) {
      data.refDevName = val.selfId

      state.set(state.datas[name], k, Object.assign({}, data))
    }
  }
}

// 图片数据管理
class ImagesData extends DataManage {
  constructor() {
    super(uuid())

    __imagesManageName = this.name
  }

  getFileContentBykey(key = 'x') {
    const __imageData = this.get(key)
    if (__imageData) {
      return __imageData.fileContent
    } else {
      return ''
    }
  }

  getFileNameBykey(key = 'x') {
    const __imageData = this.get(key)
    if (__imageData) {
      return __imageData.fileName
    } else {
      return ''
    }
  }

  getHashBykey(key = 'x') {
    const __imageData = this.get(key)
    if (__imageData) {
      return __imageData.hash
    } else {
      return ''
    }
  }
}

// 单位数据管理
var delete_data_for_delete_org = function (globData, orgId, deleSubject = 'x') {
  if (!globData || !orgId) {
    return
  }
  var __globData = globData.getAll()
  for (var k in __globData) {
    var item = __globData[k]
    if (item.orgId === orgId) {
      bfglob.emit(deleSubject, item)
    }
  }
}
var delete_orgData_for_delete_org = function (orgId, deleSubject = 'x') {
  if (!orgId) {
    return
  }
  var __globData = bfglob.gorgData.getAll()
  for (var k in __globData) {
    var item = __globData[k]
    if (item.parentOrgId === orgId) {
      bfglob.emit(deleSubject, item)
    }
  }
}

var __getVirtualName = function (virtual) {
  let __name = 'real'
  switch (virtual) {
    case 1:
      __name = 'virtual'
      break
    case 101:
      __name = 'taskGroup'
      break
    case 100:
      __name = 'tempGroup'
      break
    case 2:
    default:
      __name = 'real'
  }
  return __name
}

const dynamicGroupOrgType = getDynamicGroupOrgType()

function deleteDynamicGroupDetails(dbOrgKey) {
  const details = bfglob.gdynamicGroupDetail.getDataByGroupRid(dbOrgKey)
  details.length && bfglob.emit('remove_global_dynamic_group_detail', details, true)
}

class OrgsData extends DataManage {
  constructor() {
    super(uuid())
    __orgsManageName = this.name
    state.selects[this.name].virtual = {}
    state.selects[this.name].real = {}
    state.selects[this.name].taskGroup = {}
    state.selects[this.name].tempGroup = {}
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val, 1)
    })
  }

  //will return an lonlat array [lon,lat]
  getOrgMapMakerPointLonlatInfo(orgRid) {
    const tMapPoint = this.getOrgMapMakerPoint(orgRid)
    if (!tMapPoint) {
      return {}
    }
    return {
      lonlat: [tMapPoint.lon, tMapPoint.lat],
      showLevel: tMapPoint.startShowLevel,
    }
  }

  getOrgMapMakerPoint(orgRid) {
    //org rid will same as map point
    return bfglob.gmapPoints.get(orgRid)
  }

  // 重置单位的下拉列表管理功能 virtual =1 为虚拟单位，virtual =2 为真实单位
  getList(virtual = 2) {
    const __name = __getVirtualName(virtual)
    return state.selects[this.name][__name]
  }

  setList(key = 'x', val = {}, virtual = 2) {
    //  先将旧的下拉选项删除
    this.deleteList(key, 1)
    this.deleteList(key, 2)

    // 再添加新的选项
    const __name = __getVirtualName(virtual)
    const __key = 'x' + key
    state.set(state.selects[this.name][__name], __key, val)
    return this
  }

  deleteList(key = 'x', virtual = 2) {
    const __name = __getVirtualName(virtual)
    const __key = 'x' + key
    delete state.selects[this.name][__name][__key]

    return this
  }

  deleteAllData(key = 'x') {
    delete_data_for_delete_org(bfglob.guserData, key, 'delete_global_userData')
    delete_data_for_delete_org(bfglob.gdevices, key, 'delete_global_deviceData')
    delete_data_for_delete_org(bfglob.glinePoints, key, 'delete_global_linePointData')
    delete_data_for_delete_org(bfglob.glineMaster, key, 'delete_global_lineMasterData')
    delete_data_for_delete_org(bfglob.gruleMaster, key, 'delete_global_ruleMasterData')
    delete_data_for_delete_org(bfglob.gmapPoints, key, 'delete_global_mapPointData')
    delete_data_for_delete_org(bfglob.gcontrollers, key, 'delete_global_controllerData')
    delete_data_for_delete_org(bfglob.gshortNo, key, 'delete_global_db_phone_short_no')
    delete_data_for_delete_org(bfglob.gatewayFilter, key, 'delete_global_db_phone_gateway_filter')
    delete_data_for_delete_org(bfglob.gatewayPermission, key, 'delete_global_db_phone_gateway_permission')
    delete_data_for_delete_org(bfglob.gcontrollerGateway, key, 'delete_global_db_controller_gateway')
    delete_data_for_delete_org(bfglob.gphoneBook, key, 'delete_global_db_phone_no_list')
    deleteDynamicGroupDetails(key)

    delete_orgData_for_delete_org(key, 'delete_global_orgData')
  }

  // 重写 delete 事件
  delete(key = 'x') {
    // 删除索引和下拉列表
    this.deleteIndexByVal(key)
    const data = this.get(key)
    if (!data) {
      return this
    }
    this.deleteList(key, data.orgIsVirtual)

    // 虚拟单位下只挂载对讲机节点，不需要删除其他类型的数据
    if (data.orgIsVirtual === 2) {
      // 为真实单位，需要删除该单位下所有数据
      this.deleteAllData(key)
    }

    const _key = 'x' + key
    // state.emit(_key, _key);
    delete state.datas[this.name][_key]
    return this
  }

  getShortName(key = 'x') {
    const data = this.get(key)
    return data ? data.orgShortName : ''
  }

  getFullName(key = 'x') {
    const data = this.get(key)
    return data ? data.orgFullName : ''
  }

  getDynamicGroup() {
    const dataStore = state.datas[this.name]

    return Object.keys(dataStore)
      .map(key => dataStore[key])
      .filter(data => dynamicGroupOrgType.includes(data.orgIsVirtual))
  }

  // 查找本地或无权限的单位数据
  getDataMaybeNoPerm(rid = 'x') {
    return this.get(rid) || bfglob.noPermOrgData.get(rid)
  }
}

class DynamicGroupDetail extends DataManage {
  constructor() {
    super(uuid())

    __DynamicGroupDetailManageName = this.name
  }

  // 通过动态组rid查找所有的组成员信息
  getDataByGroupRid(groupRid) {
    const allData = this.getAll()
    const result = []
    for (const k in allData) {
      const data = allData[k]
      if (data.orgId === groupRid) {
        result.push(data)
      }
    }

    return result
  }

  deleteDataByGroupRid(groupRid) {
    const dataList = this.getDataByGroupRid(groupRid)
    for (let i = 0; i < dataList.length; i++) {
      const data = dataList[i]
      this.delete(data.rid)
    }

    return this
  }

  getDetailSource(rid) {
    const data = this.get(rid)
    if (!data) {
      return undefined
    }

    if (data.isDeviceGroup === 2) {
      return bfglob.gorgData.getDataMaybeNoPerm(data.groupRid)
    }

    return bfglob.gdevices.getDataMaybeNoPerm(data.deviceRid)
  }

  getDetailParentSource(memberOrgId) {
    return bfglob.gorgData.get(memberOrgId) || bfglob.noPermOrgData.get(memberOrgId)
  }

  getDynamicGroup(orgId) {
    return bfglob.gorgData.get(orgId) || bfglob.noPermOrgData.get(orgId)
  }

  sortDetails(dataList) {
    if (!Array.isArray(dataList)) {
      return []
    }
    const deviceDetails = []
    const groupDetails = []
    let groupDeviceDetails = []
    for (let i = 0; i < dataList.length; i++) {
      const data = dataList[i]
      switch (data.isDeviceGroup) {
        case MemberType.Device:
          deviceDetails.push(data)
          break
        case MemberType.Group:
          groupDetails.push(data)
          break
        case MemberType.ListenGroupDevice:
          groupDeviceDetails.push(data)
      }
    }

    // 动态组成员排序，终端->组呼->组呼下终端
    const result = [...deviceDetails]
    for (let j = 0; j < groupDetails.length; j++) {
      const data = groupDetails[j]
      result.push(data)
      const children = groupDeviceDetails.filter(item => item.groupRid === data.groupRid)
      groupDeviceDetails = groupDeviceDetails.filter(item => item.groupRid !== data.groupRid)
      // 找到该组成员下所有终端，按正常、被抢占、未应答加入、未应答退出、失效进行排序
      result.push(
        ...children
        // .sort((a, b) => {
        //   if (a.memberState === MemberState.Normal && b.memberState !== MemberState.Normal) { return -1 }
        //   // if (a.memberState === MemberState.Normal && b.memberState === MemberState.Normal ) { return 0 }
        //   return a.memberState - b.memberState
        // }),
      )
    }
    result.push(...groupDeviceDetails)

    return result
  }

  getSortDataByGroupRid(groupRid) {
    const datalist = this.getDataByGroupRid(groupRid)
    return this.sortDetails(datalist)
  }
}

// 职位数据管理
class JobsData extends DataManage {
  constructor() {
    super(uuid())
    __jobsManageName = this.name
  }

  getJobNameByKey(key = 'x') {
    const __jobData = this.get(key)
    if (__jobData) {
      return __jobData.titleName
    } else {
      return ''
    }
  }
}

// 用户数据管理
var __getjobData = function (key) {
  const _key = 'x' + key
  return state.datas[__jobsManageName][_key]
}

class UsersData extends DataManage {
  constructor() {
    super(uuid())
    __usersManageName = this.name
    state.privelege[this.name] = {}
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
    })
    state.on(__jobsManageName, (key, val) => {
      updateUserTitleName(this.name, key, val)
    })
  }

  getJobNameByKey(key = 'x') {
    const __jobData = __getjobData(key)
    if (__jobData) {
      return __jobData.titleName
    } else {
      return ''
    }
  }

  getUserNameByKey(key = 'x') {
    const __userData = this.get(key)
    if (__userData) {
      return __userData.userName
    } else {
      return ''
    }
  }

  getUserImageByKey(key = 'x') {
    const __userData = this.get(key)
    let __fileContent = ''
    if (__userData) {
      __fileContent = __userData.fileContent
    }
    return __fileContent
  }

  setPrivelege(key = 'x', val = []) {
    const _key = 'x' + key
    state.set(state.privelege[this.name], _key, val)
    return this
  }

  getPrivelege(key = 'x') {
    const _key = 'x' + key
    return state.privelege[this.name][_key]
  }

  deletePrivelege(key = 'x') {
    const _key = 'x' + key
    delete state.privelege[this.name][_key]
    return this
  }

  delete(key = 'x') {
    this.deleteIndexByVal(key)
    this.deleteList(key)
    this.deletePrivelege(key)

    const _key = 'x' + key
    delete state.datas[this.name][_key]
    return this
  }

  clear() {
    super.clear()
    state.privelege[this.name] = {}
  }
}

// 设备数据管理
var __getUserData = function (key) {
  const _key = 'x' + key
  return state.datas[__usersManageName][_key]
}

const deleteLocalDeviceGatewayFilterRid = (name, data) => {
  const datas = state.datas[name]
  for (const k in datas) {
    const item = datas[k]
    if (item.gatewayFilterRid === data.rid) {
      item.gatewayFilterRid = ''
      item.gatewayFilterName = ''
    }
  }
  state.set(state.datas, name, datas)
}
const asyncLocalDeviceGatewayFilterName = (name, data) => {
  const datas = state.datas[name]
  for (const k in datas) {
    const item = datas[k]
    if (item.gatewayFilterRid === data.rid) {
      item.gatewayFilterName = data.name
    }
  }
  state.set(state.datas, name, datas)
}

// 发射组和收听组都被清空，则返回true
const checkChannelDataIsEmpty = channel => {
  if (!channel.sendGroup && channel.listenGroup.length === 0) {
    return true
  }
  return false
}

const deleteChannelConfigByOrgData = (channel, orgData) => {
  channel.sendGroup = channel.sendGroup === orgData.dmrId ? '' : channel.sendGroup

  // 重置收听组，过滤掉被删除的单位的dmrId
  channel.listenGroup = (channel.listenGroup || []).filter(item => {
    return item !== orgData.dmrId
  })

  return checkChannelDataIsEmpty(channel)
}
const updateChannelConfigByOrgData = (channel, orgData) => {
  channel.sendGroup = channel.sendGroup === orgData.oldDmrId ? orgData.dmrId : channel.sendGroup

  // 重置收听组，过滤掉被删除的单位的dmrId
  channel.listenGroup = (channel.listenGroup || []).map(item => {
    return item === orgData.oldDmrId ? orgData.dmrId : item
  })

  return checkChannelDataIsEmpty(channel)
}
const update_device_channel_config = (name, orgData, status) => {
  const datas = state.datas[name]
  for (const k in datas) {
    const item = datas[k]
    const channels = item.channels ?? []
    for (let i = 0; i < channels.length; i++) {
      const channel = channels[i]
      if (status === 1) {
        updateChannelConfigByOrgData(channel, orgData)
      } else {
        deleteChannelConfigByOrgData(channel, orgData)
      }
    }
    try {
      item.channel = JSON.stringify({ channels: channels })
    } catch (_e) {
      item.channel = JSON.stringify({ channels: [] })
    }
  }
  state.set(state.datas, name, datas)
}

class AppMapPrivilegeDeviceData extends DataManage {
  constructor() {
    super(uuid())
    __appMapPrivilegeDeviceManageName = this.name
    state.on(__appMapPrivilegeDeviceManageName, () => {})
  }
}

class DevicesData extends DataManage {
  constructor() {
    super(uuid())
    __devicesManageName = this.name
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val, 2)
    })
    state.on(__usersManageName, (key, val) => {
      updateUserName(this.name, key, val)
    })
    state.on(__phoneGatewayFilterManageName, (key, val) => {
      updateDeviceGatewayName(this.name, key, val)
    })
    bfglob.on('delete_global_phone_gateway_filter', data => {
      deleteLocalDeviceGatewayFilterRid(this.name, data)
    })
    bfglob.on('update_global_phone_gateway_filter', data => {
      asyncLocalDeviceGatewayFilterName(this.name, data)
    })
    bfglob.on('update_device_channel_config', (data, state) => {
      update_device_channel_config(this.name, data, state)
    })
  }

  getUserNameByKey(key = 'x') {
    const __userData = __getUserData(key)
    if (__userData) {
      return __userData.userName
    } else {
      return ''
    }
  }

  getUserImageFileByKey(key = 'x') {
    const __userData = __getUserData(key)
    if (__userData) {
      return __userData.fileContent
    } else {
      return ''
    }
  }

  getSelfIdByKey(key = 'x') {
    const data = this.get(key)
    return data ? data.selfId : ''
  }

  getDataMaybeNoPerm(rid = 'x') {
    return this.get(rid) || bfglob.noPermDevData.get(rid)
  }
}

// 地图标记点数据管理
class MapPointsData extends DataManage {
  constructor() {
    super(uuid())
    __mapPointsManageName = this.name
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
    })
    state.on(__mapPointsManageName, () => {})
  }
}

// 巡查点数据管理
class LinePointsData extends DataManage {
  constructor() {
    super(uuid())
    __linePointsManageName = this.name
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
    })
  }

  get(key = '', bool = false) {
    const __key = 'x' + key
    let data = null
    data = state.datas[this.name][__key]
    // bool ==true ,则如果在本地巡查点数据管理器中没有数据，将从没有权限的巡查点数据中获取
    if (bool && !data) {
      data = state.noAccessPoints[__key]
    }
    return data
  }
}

// 巡查线路数据管理
class LineMastersData extends DataManage {
  constructor() {
    super(uuid())
    __lineMastersManageName = this.name
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
    })
    state.on(__linePointsManageName, (key, val) => {
      updateLinePointName(this.name, key, val)
    })
  }

  getNoAccessPoint(key = 'x') {
    const _key = 'x' + key
    return state.noAccessPoints[_key]
  }

  setNoAccessPoint(key = 'x', val = {}) {
    const _key = 'x' + key
    state.set(state.noAccessPoints, _key, val)
    return this
  }
}

// 巡查规则数据管理
class RulesData extends DataManage {
  constructor() {
    super(uuid())
    __rulesManageName = this.name
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
    })
    state.on(__lineMastersManageName, (key, val) => {
      updateLineMasterName(this.name, key, val)
    })
    state.on(__rulesManageName, () => {})
  }
}

// 控制器数据管理
const controlUpdateDetailDataMethod = (name, key, val) => {
  // name 控制器数据管理名称
  // key 控制器网关关系表rid
  // val 控制器网关关系数据
  const datas = state.datas[name]
  for (const k in datas) {
    const data = datas[k]
    if (data.rid === val.refControllerId) {
      data.controllerGateway = {
        ...data.controllerGateway,
        [val.rid]: val,
      }
      break
    }
  }
  state.set(state.datas, name, datas)
}
const controlDeleteDetailDataMethod = (name, key) => {
  const datas = state.datas[name]
  for (const k in datas) {
    const data = datas[k]
    if (data.controllerGateway && data.controllerGateway[key]) {
      delete data.controllerGateway[key]
      break
    }
  }
  state.set(state.datas, name, datas)
}
const controlUpdateDetailDataSubject = 'controlUpdateDetailData'
const controlDeleteDetailDataSubject = 'controlDeleteDetailData'
const deleteGlobalControllerDataSubject = 'deleteGlobalControllerData'

class ControllersData extends DataManage {
  constructor() {
    super(uuid())
    __controllersManageName = this.name
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
    })
    state.on(controlUpdateDetailDataSubject, (key, val) => {
      controlUpdateDetailDataMethod(this.name, key, val)
    })
    state.on(controlDeleteDetailDataSubject, key => {
      controlDeleteDetailDataMethod(this.name, key)
    })
  }

  getSelfId(key = 'x') {
    const data = this.get(key)
    return data ? data.selfId : ''
  }

  delete(key = 'x') {
    super.delete(key)

    state.emit(deleteGlobalControllerDataSubject, key)
    return this
  }
}

class PhoneShortNo extends DataManage {
  constructor() {
    super(uuid())
    __phoneShortNoManageName = this.name
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
      updateShortNoMappingTargetName(this.name, key, val)
    })
    state.on(__devicesManageName, (key, val) => {
      updateShortNoMappingTargetName(this.name, key, val)
    })
    state.on(__phoneShortNoManageName, () => {})
  }
}

class PhoneGatewayFilter extends DataManage {
  constructor() {
    super(uuid())
    __phoneGatewayFilterManageName = this.name
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
      // updateGatewayBlackWhiteListLabel(this.name, key, val);
    })
    // state.on(__devicesManageName, (key, val) => {
    //   updateGatewayBlackWhiteListLabel(this.name, key, val);
    // });
  }

  getName(key = 'x') {
    const data = this.get(key)
    return data ? data.name : ''
  }
}

class PhoneGatewayPermission extends DataManage {
  constructor() {
    super(uuid())
    __phoneGatewayPermissionManageName = this.name
    state.on(__phoneGatewayPermissionManageName, () => {})
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
      updateGatewayPermissionOrgName(this.name, key, val)
    })
    state.on(__devicesManageName, (key, val) => {
      updateGatewayPermissionDeviceName(this.name, key, val)
    })
    state.on(__phoneGatewayFilterManageName, (key, val) => {
      updateGatewayPermissionGatewayName(this.name, key, val)
    })
  }

  getName(key = 'x') {
    const data = this.get(key)
    return data ? data.name : ''
  }
}

// 删除控制器，同步网关设备关系数据
const deleteControllerGatewayByDeleteController = (name, key) => {
  const local_data = state.datas[name]
  for (const k in local_data) {
    const data = local_data[k]
    if (data.refControllerId === key) {
      delete local_data[k]
    }
  }

  state.set(state.datas, name, local_data)
}

class ControllerGateway extends DataManage {
  constructor() {
    super(uuid())
    __controllerGatewayManageName = this.name
    state.on(__controllerGatewayManageName, () => {})
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
    })
    state.on(__devicesManageName, (key, val) => {
      updateControllerGatewayRefDevName(this.name, key, val)
    })
    state.on(__controllersManageName, (key, val) => {
      updateControllerGatewayRefControllerName(this.name, key, val)
    })
    state.on(deleteGlobalControllerDataSubject, key => {
      deleteControllerGatewayByDeleteController(this.name, key)
    })
  }

  set(key = 'x', val = {}, index) {
    super.set(key, val, index)
    state.emit(controlUpdateDetailDataSubject, key, val)

    return this
  }

  delete(key = 'x') {
    super.delete(key)
    state.emit(controlDeleteDetailDataSubject, key)

    return this
  }

  getPhonePos(key = 'x') {
    const data = this.get(key)
    return data ? data.phonePos : ''
  }

  getPhoneNo(key = 'x') {
    const data = this.get(key)
    return data ? data.phoneNo : ''
  }

  includesPhonePos(refControllerId = 'x', pos) {
    const datas = state.datas[this.name]
    for (const k in datas) {
      const data = datas[k]
      if (data.refControllerId !== refControllerId) {
        continue
      }
      if (data.phonePos === pos) {
        return true
      }
    }
    return false
  }

  getDataByDevId(refDevId) {
    const datas = state.datas[this.name]
    for (const k in datas) {
      const data = datas[k]
      if (data.refDevId === refDevId) {
        return data
      }
    }

    return undefined
  }

  getDataListByRefControllerId(refControllerId = 'x') {
    const list = []

    const local_data = state.datas[this.name]
    for (const k in local_data) {
      const data = local_data[k]
      if (data.refControllerId === refControllerId) {
        list.push(data)
      }
    }

    return list
  }
}

class PhoneNoList extends DataManage {
  constructor() {
    super(uuid())
    __phoneNoListManageName = this.name
    state.on(__phoneNoListManageName, () => {})
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
    })
  }

  getPhoneNo(key = 'x') {
    const data = this.get(key)
    return data ? data.phoneNo : ''
  }
}

// dmrAddr 调度地址数据管理类
var __DMRIDManageName = ''

class DMRIDManage {
  constructor() {
    __DMRIDManageName = this.name = uuid()
    state.on(__DMRIDManageName, () => {})
  }

  getAll() {
    return state.dmrIdAddr
  }

  get(key = 'x') {
    const _key = 'x' + key
    return state.dmrIdAddr[_key]
  }

  set(key = 'x', val = {}) {
    const _key = 'x' + key
    state.set(state.dmrIdAddr, _key, val)
    return this
  }

  getType(key = 'x') {
    var addr = this.get(key)
    if (addr) {
      return addr.type
    } else {
      return -1
    }
  }

  getTitle(key = 'x') {
    var addr = this.get(key)
    if (addr) {
      return addr.title
    } else {
      return ''
    }
  }

  getGroupCallNo(key = 'x') {
    var addr = this.get(key)
    if (addr) {
      return addr.groupCallNo
    } else {
      return -1
    }
  }
}

// 没有权限的类管理
var __noPerm_org_class_name = ''
var __noPerm_device_class_name = ''

class NoPermOrg extends DataManage {
  constructor() {
    super(uuid())
    __noPerm_org_class_name = this.name
  }

  getParent(key = 'x') {
    const __key = 'x' + key
    return state.datas[__noPerm_org_class_name][__key]
  }
}

class NoPermDevice extends DataManage {
  constructor() {
    super(uuid())
    __noPerm_device_class_name = this.name
    state.on(__noPerm_device_class_name, () => {})
  }

  getParent(key = 'x') {
    const __key = 'x' + key
    return state.datas[__noPerm_org_class_name][__key]
  }
}

let __DeviceChannelZoneManageName = ''

// const DefOrgRid = '00000000-0000-0000-0000-000000000000'

class DeviceChannelZone extends DataManage {
  constructor() {
    super(uuid())

    __DeviceChannelZoneManageName = this.name
    // state.on(__orgsManageName, (key, val) => {
    //   updateOrgShortName(this.name, key, val)
    // })
  }

  getZoneTitleByIndex(zoneNo = 0) {
    const data = this.getDataByIndex(zoneNo)
    return data ? data.zoneTitle : ''
  }

  delete(key = 'x') {
    super.delete(key)
    const channelZones = this.getAll()
    for (const k in channelZones) {
      const data = channelZones[k]
      if (data.zoneParent === key) {
        this.delete(data.rid)
      }
    }
  }
}

let __IOTDevicesManageName = ''

class IOTDevices extends DataManage {
  constructor() {
    super(uuid())

    __IOTDevicesManageName = this.name
    state.on(__orgsManageName, (key, val) => {
      updateOrgShortName(this.name, key, val)
    })
  }
}

export default {
  NoPermOrg,
  NoPermDevice,
  noPermOrgClass() {
    return new NoPermOrg()
  },
  noPermDeviceClass() {
    return new NoPermDevice()
  },
  orgsDataClass() {
    return new OrgsData()
  },
  jobsDataClass() {
    return new JobsData()
  },
  usersDataClass() {
    return new UsersData()
  },
  imagesDataClass() {
    return new ImagesData()
  },
  devicesDataClass() {
    return new DevicesData()
  },
  mapPointsDataClass() {
    return new MapPointsData()
  },
  linePointsDataClass() {
    return new LinePointsData()
  },
  lineMastersDataClass() {
    return new LineMastersData()
  },
  rulesDataClass() {
    return new RulesData()
  },
  controllersDataClass() {
    return new ControllersData()
  },
  DMRIDAddressClass() {
    return new DMRIDManage()
  },
  phoneShortNoClass() {
    return new PhoneShortNo()
  },
  phoneGatewayFilterClass() {
    return new PhoneGatewayFilter()
  },
  phoneGatewayPermissionClass() {
    return new PhoneGatewayPermission()
  },
  controllerGatewayClass() {
    return new ControllerGateway()
  },
  phoneNoListClass() {
    return new PhoneNoList()
  },
  DeviceChannelZoneClass() {
    return new DeviceChannelZone()
  },
  DynamicGroupDetailClass() {
    return new DynamicGroupDetail()
  },
  IOTDevicesClass() {
    return new IOTDevices()
  },
  AppMapPrivilegeDeviceDataClass() {
    return new AppMapPrivilegeDeviceData()
  },
}

export { updateGatewayBlackWhiteListLabel }
