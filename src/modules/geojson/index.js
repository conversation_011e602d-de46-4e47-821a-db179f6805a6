import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import { BASE_URL } from '@/envConfig'

const Cache = new Map()

export function getGeoJson(src) {
  if (typeof src !== 'string') {
    return new Promise(resolve => {
      resolve(undefined)
    })
  }

  // 判断本地缓存是否存在，如果缓存数据在60分钟内，则使用本地缓存数据
  if (Cache.has(src)) {
    const cache = Cache.get(src)
    if (dayjs().isBefore(dayjs(new Date(cache.time)).add(60, 'minute'))) {
      return new Promise(resolve => {
        resolve(cloneDeep(cache.value))
      })
    } else {
      // 超过60分钟，删除该缓存数据
      cache.delete(src)
    }
  }

  return fetch(`${BASE_URL}geojson/${src}.geojson`)
    .then(res => res.json())
    .then(data => {
      // 做缓存，无需每次都请求，减少带宽资源
      Cache.set(src, { time: Date.now(), value: cloneDeep(data) })
      return data
    })
}

export default {
  getGeoJson,
}
