// @generated by protoc-gen-connect-es v1.6.1 with parameter "target=js+dts,import_extension=none"
// @generated from file prochatrpc.proto (package bfdx_proto, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CommonReq, CommonResp } from './mesh_pb'
import { MethodKind } from '@bufbuild/protobuf'

/**
 * @generated from service bfdx_proto.Prochat
 */
export declare const Prochat: {
  readonly typeName: 'bfdx_proto.Prochat'
  readonly methods: {
    /**
     * query prochat device list
     *
     * @generated from rpc bfdx_proto.Prochat.QueryProchatDeviceList
     */
    readonly queryProchatDeviceList: {
      readonly name: 'QueryProchatDeviceList'
      readonly I: typeof CommonReq
      readonly O: typeof CommonResp
      readonly kind: MethodKind.Unary
    }
  }
}
